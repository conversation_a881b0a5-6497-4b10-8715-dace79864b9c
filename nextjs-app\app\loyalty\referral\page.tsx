"use client"

import { useState } from "react"
import Link from "next/link"
import {
  ArrowLeft,
  UserPlus,
  Users,
  TrendingUp,
  Search,
  Calendar,
  CheckCircle,
  XCircle,
  Clock,
  Share2,
  Copy,
  MessageSquare,
  Mail,
  Smartphone,
  Download,
  ChevronRight,
} from "lucide-react"
import { useRouter } from "next/navigation"

import { But<PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog"

// Mock data for referrals
const mockReferrals = {
  stats: {
    totalReferrals: 19,
    activeReferrals: 15,
    conversionRate: 78,
    pointsPerReferral: 250,
    totalPointsEarned: 4750,
  },
  topReferrers: [
    { id: 4, name: "Dewi Anggraini", referrals: 8, points: 2000 },
    { id: 2, name: "Siti Rahayu", referrals: 5, points: 1250 },
    { id: 6, name: "Rina Wijaya", referrals: 3, points: 750 },
    { id: 1, name: "Ahmad Rizki", referrals: 2, points: 500 },
    { id: 5, name: "Rudi Hermawan", referrals: 1, points: 250 },
  ],
  allReferrals: [
    {
      id: 1,
      date: "25/03/2024",
      referrer: { id: 4, name: "Dewi Anggraini", phone: "081234567893" },
      referee: { id: 15, name: "Joko Susanto", phone: "081234567910" },
      status: "completed",
      pointsAwarded: 250,
    },
    {
      id: 2,
      date: "22/03/2024",
      referrer: { id: 2, name: "Siti Rahayu", phone: "081234567891" },
      referee: { id: 16, name: "Andi Pratama", phone: "081234567911" },
      status: "completed",
      pointsAwarded: 250,
    },
    {
      id: 3,
      date: "18/03/2024",
      referrer: { id: 6, name: "Rina Wijaya", phone: "081234567895" },
      referee: { id: 17, name: "Maya Sari", phone: "081234567912" },
      status: "pending",
      pointsAwarded: 0,
    },
    {
      id: 4,
      date: "15/03/2024",
      referrer: { id: 4, name: "Dewi Anggraini", phone: "081234567893" },
      referee: { id: 18, name: "Bima Putra", phone: "081234567913" },
      status: "completed",
      pointsAwarded: 250,
    },
    {
      id: 5,
      date: "12/03/2024",
      referrer: { id: 1, name: "Ahmad Rizki", phone: "081234567890" },
      referee: { id: 19, name: "Dian Lestari", phone: "081234567914" },
      status: "completed",
      pointsAwarded: 250,
    },
    {
      id: 6,
      date: "10/03/2024",
      referrer: { id: 2, name: "Siti Rahayu", phone: "081234567891" },
      referee: { id: 20, name: "Rudi Santoso", phone: "081234567915" },
      status: "completed",
      pointsAwarded: 250,
    },
    {
      id: 7,
      date: "08/03/2024",
      referrer: { id: 4, name: "Dewi Anggraini", phone: "081234567893" },
      referee: { id: 21, name: "Nina Wati", phone: "081234567916" },
      status: "completed",
      pointsAwarded: 250,
    },
    {
      id: 8,
      date: "05/03/2024",
      referrer: { id: 5, name: "Rudi Hermawan", phone: "081234567894" },
      referee: { id: 22, name: "Tono Prasetyo", phone: "081234567917" },
      status: "completed",
      pointsAwarded: 250,
    },
    {
      id: 9,
      date: "02/03/2024",
      referrer: { id: 6, name: "Rina Wijaya", phone: "081234567895" },
      referee: { id: 23, name: "Sinta Dewi", phone: "081234567918" },
      status: "cancelled",
      pointsAwarded: 0,
      reason: "Pelanggan tidak melakukan transaksi",
    },
    {
      id: 10,
      date: "28/02/2024",
      referrer: { id: 4, name: "Dewi Anggraini", phone: "081234567893" },
      referee: { id: 24, name: "Hadi Nugroho", phone: "081234567919" },
      status: "completed",
      pointsAwarded: 250,
    },
  ],
  monthlyStats: [
    { month: "Jan", referrals: 3, conversions: 2 },
    { month: "Feb", referrals: 5, conversions: 4 },
    { month: "Mar", referrals: 11, conversions: 9 },
  ],
}

export default function ReferralPage() {
  const router = useRouter()
  const [searchQuery, setSearchQuery] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [activeTab, setActiveTab] = useState("all")
  const [showShareDialog, setShowShareDialog] = useState(false)
  const [referralMethod, setReferralMethod] = useState("whatsapp")

  // Filter referrals based on search query and status filter
  const filteredReferrals = mockReferrals.allReferrals.filter((referral) => {
    const matchesSearch =
      searchQuery === "" ||
      referral.referrer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      referral.referee.name.toLowerCase().includes(searchQuery.toLowerCase())

    const matchesStatus = statusFilter === "all" || referral.status === statusFilter

    return matchesSearch && matchesStatus
  })

  return (
    <div className="flex flex-col min-h-screen bg-slate-50">
      <header className="p-4 flex justify-between items-center bg-white sticky top-0 z-10 shadow-sm">
        <div className="flex items-center">
          <Link href="/loyalty" className="mr-3">
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <h1 className="text-lg font-semibold">Program Referral</h1>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.push("/loyalty/referral/settings")}
            className="hidden sm:flex"
          >
            Pengaturan
          </Button>
          <Button onClick={() => setShowShareDialog(true)} className="bg-blue-500 hover:bg-blue-600" size="sm">
            <Share2 className="h-4 w-4 mr-1" /> Bagikan
          </Button>
        </div>
      </header>

      <div className="p-4">
        <Card className="p-4 bg-gradient-to-r from-blue-500 to-purple-500 text-white mb-6">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <div>
              <h3 className="text-xl font-semibold">Program Referral</h3>
              <p className="text-white/80 mt-1">
                Ajak teman Anda bergabung dan dapatkan {mockReferrals.stats.pointsPerReferral} poin untuk setiap
                referral yang berhasil!
              </p>
            </div>
            <Button
              variant="secondary"
              className="bg-white text-blue-600 hover:bg-white/90"
              onClick={() => setShowShareDialog(true)}
            >
              <Share2 className="h-4 w-4 mr-2" /> Bagikan Kode Referral
            </Button>
          </div>
        </Card>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <Card className="p-4">
            <div className="flex flex-col items-center text-center">
              <UserPlus className="h-8 w-8 text-blue-500 mb-2" />
              <h3 className="text-2xl font-bold">{mockReferrals.stats.totalReferrals}</h3>
              <p className="text-sm text-gray-500">Total Referral</p>
            </div>
          </Card>
          <Card className="p-4">
            <div className="flex flex-col items-center text-center">
              <Users className="h-8 w-8 text-green-500 mb-2" />
              <h3 className="text-2xl font-bold">{mockReferrals.stats.activeReferrals}</h3>
              <p className="text-sm text-gray-500">Referral Aktif</p>
            </div>
          </Card>
          <Card className="p-4">
            <div className="flex flex-col items-center text-center">
              <TrendingUp className="h-8 w-8 text-purple-500 mb-2" />
              <h3 className="text-2xl font-bold">{mockReferrals.stats.conversionRate}%</h3>
              <p className="text-sm text-gray-500">Tingkat Konversi</p>
            </div>
          </Card>
          <Card className="p-4">
            <div className="flex flex-col items-center text-center">
              <UserPlus className="h-8 w-8 text-amber-500 mb-2" />
              <h3 className="text-2xl font-bold">{mockReferrals.stats.totalPointsEarned}</h3>
              <p className="text-sm text-gray-500">Total Poin Diberikan</p>
            </div>
          </Card>
        </div>

        <div className="mb-6">
          <h3 className="text-lg font-medium mb-3">Top Referrer</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {mockReferrals.topReferrers.slice(0, 3).map((referrer, index) => (
              <Card key={referrer.id} className="p-4">
                <div className="flex items-center gap-3">
                  <div
                    className={`w-10 h-10 rounded-full flex items-center justify-center text-white ${
                      index === 0 ? "bg-yellow-500" : index === 1 ? "bg-gray-400" : "bg-amber-600"
                    }`}
                  >
                    {index + 1}
                  </div>
                  <div>
                    <h4 className="font-medium">{referrer.name}</h4>
                    <div className="flex items-center gap-3 mt-1">
                      <div className="flex items-center gap-1 text-sm">
                        <UserPlus className="h-3 w-3 text-blue-500" />
                        <span>{referrer.referrals} referral</span>
                      </div>
                      <div className="flex items-center gap-1 text-sm text-gray-500">
                        <span>{referrer.points} poin</span>
                      </div>
                    </div>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>

        <div className="mb-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium">Daftar Referral</h3>
            <Button variant="outline" size="sm" onClick={() => router.push("/loyalty/referral/export")}>
              <Download className="h-4 w-4 mr-1" /> Export
            </Button>
          </div>

          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 gap-2">
            <div className="relative flex-1 w-full">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <Input
                placeholder="Cari referral..."
                className="pl-10"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Semua Status</SelectItem>
                <SelectItem value="completed">Selesai</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="cancelled">Dibatalkan</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Tabs defaultValue="all" className="mb-4" onValueChange={setActiveTab}>
            <TabsList className="grid grid-cols-3 w-full">
              <TabsTrigger value="all">Semua</TabsTrigger>
              <TabsTrigger value="completed">Selesai</TabsTrigger>
              <TabsTrigger value="pending">Pending</TabsTrigger>
            </TabsList>
          </Tabs>

          <div className="space-y-4 mb-20">
            {filteredReferrals.length > 0 ? (
              filteredReferrals
                .filter((ref) => activeTab === "all" || ref.status === activeTab)
                .map((referral) => (
                  <Card key={referral.id} className="p-4">
                    <div className="flex justify-between items-start">
                      <div>
                        <div className="flex items-center gap-2">
                          <h3 className="font-medium">{referral.referee.name}</h3>
                          <Badge
                            variant="outline"
                            className={
                              referral.status === "completed"
                                ? "border-green-500 text-green-500 flex items-center gap-1"
                                : referral.status === "pending"
                                  ? "border-amber-500 text-amber-500 flex items-center gap-1"
                                  : "border-red-500 text-red-500 flex items-center gap-1"
                            }
                          >
                            {referral.status === "completed" ? (
                              <>
                                <CheckCircle className="h-3 w-3" /> Selesai
                              </>
                            ) : referral.status === "pending" ? (
                              <>
                                <Clock className="h-3 w-3" /> Pending
                              </>
                            ) : (
                              <>
                                <XCircle className="h-3 w-3" /> Dibatalkan
                              </>
                            )}
                          </Badge>
                        </div>
                        <p className="text-sm text-gray-500">{referral.referee.phone}</p>
                        <div className="mt-2">
                          <div className="flex items-center gap-1 text-sm text-gray-500">
                            <UserPlus className="h-3 w-3" />
                            <span>
                              Direferensikan oleh <span className="font-medium">{referral.referrer.name}</span>
                            </span>
                          </div>
                          <div className="flex items-center gap-1 text-sm text-gray-500 mt-1">
                            <Calendar className="h-3 w-3" />
                            <span>{referral.date}</span>
                          </div>
                          {referral.status === "completed" && (
                            <div className="flex items-center gap-1 text-sm text-green-500 mt-1">
                              <span>+{referral.pointsAwarded} poin diberikan</span>
                            </div>
                          )}
                          {referral.status === "cancelled" && referral.reason && (
                            <div className="flex items-center gap-1 text-sm text-red-500 mt-1">
                              <XCircle className="h-3 w-3" />
                              <span>{referral.reason}</span>
                            </div>
                          )}
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-blue-500"
                        onClick={() => router.push(`/loyalty/referral/${referral.id}`)}
                      >
                        Detail <ChevronRight className="h-4 w-4 ml-1" />
                      </Button>
                    </div>
                  </Card>
                ))
            ) : (
              <div className="text-center py-8 text-gray-500">Tidak ada referral yang ditemukan</div>
            )}
          </div>
        </div>
      </div>

      {/* Share Referral Dialog */}
      <Dialog open={showShareDialog} onOpenChange={setShowShareDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Bagikan Kode Referral</DialogTitle>
            <DialogDescription>Ajak teman Anda bergabung dan dapatkan poin!</DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-2">
            <div className="bg-gray-100 p-3 rounded-md text-center">
              <p className="text-sm text-gray-500 mb-1">Kode Referral Anda</p>
              <p className="text-xl font-bold tracking-wider">FELIS2024</p>
            </div>

            <div className="space-y-2">
              <Label>Bagikan melalui</Label>
              <RadioGroup defaultValue="whatsapp" value={referralMethod} onValueChange={setReferralMethod}>
                <div className="flex flex-col space-y-2">
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="whatsapp" id="whatsapp" />
                    <Label htmlFor="whatsapp" className="flex items-center">
                      <MessageSquare className="h-4 w-4 mr-2 text-green-600" /> WhatsApp
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="email" id="email" />
                    <Label htmlFor="email" className="flex items-center">
                      <Mail className="h-4 w-4 mr-2 text-blue-600" /> Email
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="sms" id="sms" />
                    <Label htmlFor="sms" className="flex items-center">
                      <Smartphone className="h-4 w-4 mr-2 text-purple-600" /> SMS
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="copy" id="copy" />
                    <Label htmlFor="copy" className="flex items-center">
                      <Copy className="h-4 w-4 mr-2 text-gray-600" /> Salin Tautan
                    </Label>
                  </div>
                </div>
              </RadioGroup>
            </div>

            <div className="space-y-2">
              <Label htmlFor="referral-message">Pesan (opsional)</Label>
              <Textarea
                id="referral-message"
                placeholder="Tambahkan pesan personal..."
                defaultValue="Halo! Saya menggunakan layanan Felis Laundry dan sangat puas. Gunakan kode FELIS2024 untuk mendapatkan diskon 10% untuk order pertama Anda!"
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowShareDialog(false)}>
              Batal
            </Button>
            <Button onClick={() => setShowShareDialog(false)}>
              {referralMethod === "copy" ? "Salin Tautan" : "Bagikan"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
