# Integrasi Cashbox untuk Pembayaran Non Tunai

## Deskripsi

Dokumentasi ini menjelaskan integrasi data cashbox yang sebenarnya dari API untuk metode pembayaran non tunai pada halaman pembuatan pesanan.

## Perubahan yang Dilakukan

### 1. Import Hook Cashbox

```typescript
import { useCashboxes } from '@/hooks/useCashbox';
```

### 2. Fetch Data Cashbox

```typescript
const { data: cashboxes } = useCashboxes({ isActive: true });
```

### 3. State Management

Menambahkan state untuk menyimpan cashbox yang dipilih:

```typescript
const [selectedCashboxId, setSelectedCashboxId] = useState<number | null>(null);
```

### 4. Loading State

Menambahkan loading state untuk cashbox di bagian payment method:

```typescript
{cashboxes === undefined ? (
  <div className="text-center py-8">
    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto"></div>
    <p className="text-sm text-muted-foreground mt-2">Memuat cashbox...</p>
  </div>
) : ...}
```

### 5. Dynamic Cashbox List

Mengganti list cashbox statis dengan data dari API:

```typescript
cashboxes
  .filter((cashbox) => cashbox.type === 'NON_TUNAI')
  .map((cashbox) => (
    <Button
      key={cashbox.id}
      variant="outline"
      className="w-full justify-between h-14 text-left"
      onClick={() => {
        setOrderDetails({
          ...orderDetails,
          paymentMethod: 'non-cash',
        });
        setSelectedCashboxId(cashbox.id);
        setIsPaymentSheetOpen(false);
      }}
    >
      <div className="flex items-center gap-3">
        <span className="text-blue-500 font-medium">
          {cashbox.name.substring(0, 3).toUpperCase()}
        </span>
        <span>{cashbox.name}</span>
      </div>
      <div className="flex items-center gap-2">
        <span className="text-sm text-muted-foreground">
          Rp {cashbox.balance.toLocaleString()}
        </span>
        <ChevronRight className="h-4 w-4 text-muted-foreground" />
      </div>
    </Button>
  ));
```

### 6. Empty State

Menambahkan pesan ketika tidak ada cashbox non tunai:

```typescript
<div className="text-center py-8 text-muted-foreground">
  <p className="text-sm">Tidak ada cashbox non tunai tersedia</p>
  <p className="text-xs mt-1">
    Silakan tambahkan cashbox di menu kelola keuangan
  </p>
</div>
```

### 7. Payment Summary

Menampilkan nama cashbox yang dipilih di ringkasan pembayaran:

```typescript
{
  orderDetails.paymentMethod === 'non-cash'
    ? selectedCashboxId && cashboxes
      ? cashboxes.find((c) => c.id === selectedCashboxId)?.name || 'Non Tunai'
      : 'Non Tunai'
    : 'Deposit';
}
```

### 8. State Reset

Menambahkan reset `selectedCashboxId` ketika mengubah metode pembayaran:

```typescript
onClick={() => {
  setOrderDetails({
    ...orderDetails,
    paymentMethod: 'cash', // atau 'deposit', 'later'
  });
  setSelectedCashboxId(null); // Reset cashbox selection
  setIsPaymentSheetOpen(false);
}}
```

## Fitur yang Ditambahkan

### 1. **Data Real dari API**

- Menggunakan hook `useCashboxes` untuk mengambil data cashbox aktif
- Filter otomatis untuk cashbox dengan type `NON_TUNAI`

### 2. **Tampilan Informasi Lengkap**

- Nama cashbox
- Saldo cashbox (Rp format)
- Inisial cashbox (3 huruf pertama)

### 3. **Loading State**

- Spinner loading saat mengambil data cashbox
- Pesan loading yang informatif

### 4. **Empty State**

- Pesan ketika tidak ada cashbox non tunai
- Arahan untuk menambahkan cashbox

### 5. **State Management**

- Tracking cashbox yang dipilih
- Reset selection saat mengubah payment method

## Keuntungan

1. **Data Akurat**: Menggunakan data cashbox sebenarnya dari database
2. **User Experience**: Loading state dan empty state yang informatif
3. **Informasi Lengkap**: Menampilkan nama dan saldo cashbox
4. **Konsistensi**: Sinkronisasi dengan sistem cashbox management

## Testing

Build berhasil tanpa error:

```bash
npm run build
# ✓ Compiled successfully
```

## Troubleshooting

### Issue: Cashbox Data Kosong

**Problem**: API mengembalikan data kosong meskipun halaman cashbox menampilkan data.

**Penyebab**: Parameter `{ isActive: true }` pada `useCashboxes()` mungkin terlalu spesifik atau ada masalah dengan query.

**Solusi**:

1. Menggunakan `useCashboxes()` tanpa parameter untuk mengambil semua data
2. Melakukan filter di frontend: `cashbox.type === 'NON_TUNAI' && cashbox.isActive`
3. Menambahkan debug logging untuk memonitor API response

**Debugging**:

```typescript
// Debug cashbox data
console.log('🚀 ~ CreateOrderDetailsPage ~ cashboxes:', cashboxes);
console.log(
  '🚀 ~ CreateOrderDetailsPage ~ cashboxesLoading:',
  cashboxesLoading
);
console.log('🚀 ~ CreateOrderDetailsPage ~ cashboxesError:', cashboxesError);
console.log('🚀 ~ CreateOrderDetailsPage ~ activeOutlet:', activeOutlet);
```

### Error Handling

Menambahkan error handling yang lebih baik:

```typescript
{cashboxesLoading ? (
  <div className="text-center py-8">
    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto"></div>
    <p className="text-sm text-muted-foreground mt-2">Memuat cashbox...</p>
  </div>
) : cashboxesError ? (
  <div className="text-center py-8 text-muted-foreground">
    <p className="text-sm text-red-500">Error: {cashboxesError.message}</p>
    <p className="text-xs mt-1">Gagal memuat data cashbox</p>
  </div>
) : ...}
```

## Update Terbaru

### Penghapusan Default Perfume Value

**Tanggal**: [Current Date]
**Issue**: Default value "Lavender Fresh" tidak ada di database
**Solusi**: Menghapus default value dan menggunakan placeholder "Pilih parfum"

**Perubahan**:

```typescript
// Sebelum - dengan default value
setOrderDetails((prevState) => ({
  ...prevState,
  customerId: id,
  perfume: 'Lavender Fresh', // ❌ Tidak ada di database
}));

// Sesudah - tanpa default value
setOrderDetails((prevState) => ({
  ...prevState,
  customerId: id, // ✅ User harus memilih parfum dari dropdown
}));
```

**UI Behavior**:

- Dropdown perfume menampilkan placeholder "Pilih parfum"
- User wajib memilih parfum dari daftar yang tersedia di database
- Tidak ada pre-selected value yang tidak valid

### Perbaikan Payment Method Mapping

**Tanggal**: [Current Date]
**Issue**: Error `'NON-CASH' is not a PaymentMethod` saat create order dengan metode non tunai
**Root Cause**: Frontend mengirim `'non-cash'` yang dikonversi menjadi `'NON-CASH'` yang bukan enum value valid di Prisma

**Enum PaymentMethod yang Valid**:

```typescript
enum PaymentMethod {
  CASH
  TRANSFER
  CREDIT_CARD
  DEBIT_CARD
  E_WALLET
  DEPOSIT
}
```

**Solusi - Mapping Payment Method**:

```typescript
// Sebelum ❌
method: orderDetails.paymentMethod.toUpperCase(); // 'non-cash' -> 'NON-CASH' (invalid)

// Sesudah ✅
method: (orderDetails.paymentMethod === 'cash'
  ? 'CASH'
  : orderDetails.paymentMethod === 'non-cash'
  ? 'TRANSFER' // Default non-cash ke TRANSFER
  : orderDetails.paymentMethod === 'deposit'
  ? 'DEPOSIT'
  : 'CASH') as PaymentMethod;
```

**Perubahan**:

1. **Mapping Eksplisit**: Frontend mengirim enum value yang valid
2. **Default Non-Cash**: `'non-cash'` di-map ke `'TRANSFER'`
3. **CashboxId**: Mengirim `selectedCashboxId` untuk payment non-cash
4. **Type Safety**: Proper TypeScript casting untuk PaymentMethod

## Catatan

- Hanya cashbox dengan `type: 'NON_TUNAI'` yang ditampilkan
- Filter `isActive: true` dilakukan di frontend untuk memastikan data muncul
- Saldo cashbox ditampilkan dalam format Rupiah
- State `selectedCashboxId` direset ketika mengubah payment method
- Debug logging ditambahkan untuk monitoring API response
- **Perfume selection tidak memiliki default value** - user wajib memilih dari dropdown
