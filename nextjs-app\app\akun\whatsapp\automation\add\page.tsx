"use client"

import { useState } from "react"
import Link from "next/link"
import { ArrowLeft, Save } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"

// Sample template data for selection
const templates = [
  { id: "1", name: "Konfirmasi Pesanan", category: "order" },
  { id: "2", name: "Status Pesanan", category: "order" },
  { id: "3", name: "Pesanan Selesai", category: "order" },
  { id: "4", name: "Pengingat Pembayaran", category: "payment" },
  { id: "5", name: "<PERSON><PERSON><PERSON>", category: "promotion" },
]

export default function AddAutomationPage() {
  const [automation, setAutomation] = useState({
    name: "",
    status: "active",
    triggerType: "event", // event or schedule
    eventTrigger: "new_order", // for event type
    scheduleType: "daily", // for schedule type
    scheduleTime: "08:00", // for schedule type
    scheduleDay: "1", // for weekly or monthly schedule
    templateId: "",
    customerFilter: "all", // all, segment, individual
    customerSegment: "", // for segment filter
  })

  const handleStatusChange = (checked: boolean) => {
    setAutomation({
      ...automation,
      status: checked ? "active" : "inactive",
    })
  }

  const handleSave = () => {
    if (!automation.name) {
      alert("Nama otomasi tidak boleh kosong!")
      return
    }
    if (!automation.templateId) {
      alert("Template pesan harus dipilih!")
      return
    }
    alert("Otomasi berhasil disimpan!")
  }

  return (
    <div className="flex flex-col min-h-screen bg-slate-50">
      <header className="p-4 flex justify-between items-center bg-white sticky top-0 z-10 shadow-sm">
        <div className="flex items-center">
          <Link href="/akun/whatsapp" className="mr-3">
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <h1 className="text-lg font-semibold">Tambah Otomasi Baru</h1>
        </div>
        <Button onClick={handleSave} className="bg-green-500 hover:bg-green-600">
          <Save className="h-4 w-4 mr-1" /> Simpan
        </Button>
      </header>

      <div className="p-4 pb-20">
        <Card className="mb-4">
          <CardContent className="p-4 space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">Nama Otomasi</Label>
              <Input
                id="name"
                placeholder="Masukkan nama otomasi"
                value={automation.name}
                onChange={(e) => setAutomation({ ...automation, name: e.target.value })}
              />
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="status" className="cursor-pointer">
                Status Otomasi
              </Label>
              <div className="flex items-center gap-2">
                <Switch id="status" checked={automation.status === "active"} onCheckedChange={handleStatusChange} />
                <Badge variant={automation.status === "active" ? "default" : "secondary"} className="text-xs">
                  {automation.status === "active" ? "Aktif" : "Nonaktif"}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="mb-4">
          <CardContent className="p-4">
            <h3 className="font-medium mb-3">Pemicu Otomasi</h3>

            <div className="space-y-4">
              <RadioGroup
                value={automation.triggerType}
                onValueChange={(value) => setAutomation({ ...automation, triggerType: value })}
                className="space-y-2"
              >
                <div className="flex items-start space-x-2">
                  <RadioGroupItem value="event" id="trigger-event" />
                  <Label htmlFor="trigger-event" className="font-normal cursor-pointer">
                    Berbasis Peristiwa
                    <p className="text-xs text-gray-500">Kirim pesan saat terjadi peristiwa tertentu</p>
                  </Label>
                </div>
                <div className="flex items-start space-x-2">
                  <RadioGroupItem value="schedule" id="trigger-schedule" />
                  <Label htmlFor="trigger-schedule" className="font-normal cursor-pointer">
                    Terjadwal
                    <p className="text-xs text-gray-500">Kirim pesan pada waktu yang ditentukan</p>
                  </Label>
                </div>
              </RadioGroup>

              {automation.triggerType === "event" ? (
                <div className="space-y-2 pt-2">
                  <Label htmlFor="eventTrigger">Jenis Peristiwa</Label>
                  <Select
                    value={automation.eventTrigger}
                    onValueChange={(value) => setAutomation({ ...automation, eventTrigger: value })}
                  >
                    <SelectTrigger id="eventTrigger">
                      <SelectValue placeholder="Pilih jenis peristiwa" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="new_order">Pesanan Baru</SelectItem>
                      <SelectItem value="status_change">Perubahan Status Pesanan</SelectItem>
                      <SelectItem value="order_complete">Pesanan Selesai</SelectItem>
                      <SelectItem value="payment_due">Pembayaran Tertunda</SelectItem>
                      <SelectItem value="payment_received">Pembayaran Diterima</SelectItem>
                      <SelectItem value="customer_birthday">Ulang Tahun Pelanggan</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              ) : (
                <div className="space-y-4 pt-2">
                  <div className="space-y-2">
                    <Label htmlFor="scheduleType">Frekuensi</Label>
                    <Select
                      value={automation.scheduleType}
                      onValueChange={(value) => setAutomation({ ...automation, scheduleType: value })}
                    >
                      <SelectTrigger id="scheduleType">
                        <SelectValue placeholder="Pilih frekuensi" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="once">Satu Kali</SelectItem>
                        <SelectItem value="daily">Harian</SelectItem>
                        <SelectItem value="weekly">Mingguan</SelectItem>
                        <SelectItem value="monthly">Bulanan</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {automation.scheduleType === "weekly" && (
                    <div className="space-y-2">
                      <Label htmlFor="scheduleDay">Hari</Label>
                      <Select
                        value={automation.scheduleDay}
                        onValueChange={(value) => setAutomation({ ...automation, scheduleDay: value })}
                      >
                        <SelectTrigger id="scheduleDay">
                          <SelectValue placeholder="Pilih hari" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="1">Senin</SelectItem>
                          <SelectItem value="2">Selasa</SelectItem>
                          <SelectItem value="3">Rabu</SelectItem>
                          <SelectItem value="4">Kamis</SelectItem>
                          <SelectItem value="5">Jumat</SelectItem>
                          <SelectItem value="6">Sabtu</SelectItem>
                          <SelectItem value="0">Minggu</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  )}

                  {automation.scheduleType === "monthly" && (
                    <div className="space-y-2">
                      <Label htmlFor="scheduleDay">Tanggal</Label>
                      <Select
                        value={automation.scheduleDay}
                        onValueChange={(value) => setAutomation({ ...automation, scheduleDay: value })}
                      >
                        <SelectTrigger id="scheduleDay">
                          <SelectValue placeholder="Pilih tanggal" />
                        </SelectTrigger>
                        <SelectContent>
                          {Array.from({ length: 31 }, (_, i) => (
                            <SelectItem key={i} value={(i + 1).toString()}>
                              {i + 1}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  )}

                  <div className="space-y-2">
                    <Label htmlFor="scheduleTime">Waktu</Label>
                    <Input
                      id="scheduleTime"
                      type="time"
                      value={automation.scheduleTime}
                      onChange={(e) => setAutomation({ ...automation, scheduleTime: e.target.value })}
                    />
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        <Card className="mb-4">
          <CardContent className="p-4">
            <h3 className="font-medium mb-3">Template Pesan</h3>

            <div className="space-y-2">
              <Label htmlFor="templateId">Pilih Template</Label>
              <Select
                value={automation.templateId}
                onValueChange={(value) => setAutomation({ ...automation, templateId: value })}
              >
                <SelectTrigger id="templateId">
                  <SelectValue placeholder="Pilih template pesan" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">-- Pilih Template --</SelectItem>
                  {templates.map((template) => (
                    <SelectItem key={template.id} value={template.id}>
                      {template.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <div className="flex justify-end">
                <Link href="/akun/whatsapp/templates/add">
                  <Button variant="link" size="sm" className="h-auto p-0">
                    + Buat template baru
                  </Button>
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <h3 className="font-medium mb-3">Penerima</h3>

            <div className="space-y-4">
              <RadioGroup
                value={automation.customerFilter}
                onValueChange={(value) => setAutomation({ ...automation, customerFilter: value })}
                className="space-y-2"
              >
                <div className="flex items-start space-x-2">
                  <RadioGroupItem value="all" id="customer-all" />
                  <Label htmlFor="customer-all" className="font-normal cursor-pointer">
                    Semua Pelanggan
                    <p className="text-xs text-gray-500">Kirim ke semua pelanggan yang terkait dengan pemicu</p>
                  </Label>
                </div>
                <div className="flex items-start space-x-2">
                  <RadioGroupItem value="segment" id="customer-segment" />
                  <Label htmlFor="customer-segment" className="font-normal cursor-pointer">
                    Segmen Pelanggan
                    <p className="text-xs text-gray-500">Kirim hanya ke pelanggan dalam segmen tertentu</p>
                  </Label>
                </div>
              </RadioGroup>

              {automation.customerFilter === "segment" && (
                <div className="space-y-2 pt-2">
                  <Label htmlFor="customerSegment">Pilih Segmen</Label>
                  <Select
                    value={automation.customerSegment}
                    onValueChange={(value) => setAutomation({ ...automation, customerSegment: value })}
                  >
                    <SelectTrigger id="customerSegment">
                      <SelectValue placeholder="Pilih segmen pelanggan" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="new">Pelanggan Baru</SelectItem>
                      <SelectItem value="regular">Pelanggan Tetap</SelectItem>
                      <SelectItem value="vip">Pelanggan VIP</SelectItem>
                      <SelectItem value="inactive">Pelanggan Tidak Aktif</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
