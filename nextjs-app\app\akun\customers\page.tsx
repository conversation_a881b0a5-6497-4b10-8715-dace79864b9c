'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import {
  ArrowLeft,
  Search,
  Plus,
  Filter,
  MoreVertical,
  Edit,
  Trash2,
  Eye,
  FileSpreadsheet,
  Loader2,
  RefreshCw,
} from 'lucide-react';
import { useRouter } from 'next/navigation';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Separator } from '@/components/ui/separator';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

import {
  useCustomers,
  useDeleteCustomer,
  formatCurrency,
  getCustomerStatusBadge,
  getCustomerTypeLabel,
} from '@/hooks/useCustomers';
import type { GetCustomersParams } from '@/lib/api/customers';
import { ensureAuthSync } from '@/lib/auth-sync';
import { toast } from '@/components/ui/use-toast';

export default function CustomersPage() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState('semua');
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [customerToDelete, setCustomerToDelete] = useState<{
    id: number;
    name: string;
  } | null>(null);
  const [showFilterDialog, setShowFilterDialog] = useState(false);
  const [authError, setAuthError] = useState(false);
  const [filterOptions, setFilterOptions] = useState({
    status: 'semua',
    customerType: 'semua',
    sortBy: 'name:asc',
  });

  // Ensure auth sync on component mount
  useEffect(() => {
    ensureAuthSync();

    // Check if user is properly authenticated
    const checkAuth = () => {
      const user = localStorage.getItem('user');
      const tokens = localStorage.getItem('tokens');

      if (!user || !tokens) {
        console.log('No auth data found');
        setAuthError(true);
        return;
      }

      try {
        const parsedTokens = JSON.parse(tokens);
        const accessToken = parsedTokens?.access?.token;

        if (!accessToken) {
          console.log('No access token found');
          setAuthError(true);
          return;
        }

        // Check if token is expired
        const expiresAt = new Date(parsedTokens.access.expires);
        const now = new Date();

        if (expiresAt <= now) {
          console.log('Access token expired, API will handle refresh');
        }

        setAuthError(false);
      } catch (error) {
        console.error('Error parsing auth data:', error);
        setAuthError(true);
      }
    };

    checkAuth();
  }, [router]);

  // Build API params from current state
  const apiParams: GetCustomersParams = {
    search: searchQuery || undefined,
    status:
      filterOptions.status !== 'semua'
        ? (filterOptions.status as any)
        : undefined,
    customerType:
      filterOptions.customerType !== 'semua'
        ? (filterOptions.customerType as any)
        : undefined,
    sortBy: filterOptions.sortBy,
    limit: 50,
    page: 1,
  };

  // Apply tab filter to status
  if (activeTab !== 'semua') {
    if (activeTab === 'baru') {
      apiParams.status = 'NEW';
    } else if (activeTab === 'aktif') {
      apiParams.status = 'ACTIVE';
    } else if (activeTab === 'nonaktif') {
      apiParams.status = 'INACTIVE';
    }
  }

  const {
    data: customersData,
    isLoading,
    error,
    refetch,
    isRefetching,
  } = useCustomers(apiParams);

  const deleteCustomerMutation = useDeleteCustomer();

  const handleDeleteCustomer = (id: number, name: string) => {
    setCustomerToDelete({ id, name });
    setShowDeleteDialog(true);
  };

  const confirmDelete = async () => {
    if (customerToDelete) {
      try {
        await deleteCustomerMutation.mutateAsync(customerToDelete.id);
        setShowDeleteDialog(false);
        setCustomerToDelete(null);
      } catch (error) {
        // Error sudah ditangani di mutation
      }
    }
  };

  const applyFilters = () => {
    setShowFilterDialog(false);
  };

  const handleCustomerClick = (e: React.MouseEvent, customerId: number) => {
    // Prevent navigation if clicking on dropdown menu
    if ((e.target as HTMLElement).closest('.dropdown-menu')) {
      return;
    }
    router.push(`/akun/customers/${customerId}`);
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  const customers = customersData?.results || [];

  // Calculate tab counts
  const getTabCounts = () => {
    if (!customersData) return { semua: 0, baru: 0, aktif: 0, nonaktif: 0 };

    const counts = customers.reduce(
      (acc, customer) => {
        acc.semua++;
        if (customer.status === 'NEW') acc.baru++;
        if (customer.status === 'ACTIVE') acc.aktif++;
        if (customer.status === 'INACTIVE') acc.nonaktif++;
        return acc;
      },
      { semua: 0, baru: 0, aktif: 0, nonaktif: 0 }
    );

    return counts;
  };

  const tabCounts = getTabCounts();

  // Handle authentication error
  const handleLoginRedirect = () => {
    router.push('/auth/login?callbackUrl=/akun/customers');
  };

  // Check for authentication errors
  useEffect(() => {
    if (error && (error as any)?.response?.status === 401) {
      setAuthError(true);
      toast({
        title: 'Sesi Berakhir',
        description: 'Silakan login kembali untuk melanjutkan.',
        variant: 'destructive',
      });
    }
  }, [error]);

  // If there's an auth error, show login prompt
  if (authError) {
    return (
      <div className="flex flex-col min-h-screen bg-slate-50">
        <header className="p-4 flex justify-between items-center bg-white sticky top-0 z-10 shadow-sm">
          <div className="flex items-center">
            <Link href="/akun" className="mr-3">
              <ArrowLeft className="h-5 w-5" />
            </Link>
            <h1 className="text-lg font-semibold">Pelanggan</h1>
          </div>
        </header>
        <div className="flex-1 flex items-center justify-center p-4">
          <div className="text-center">
            <h2 className="text-lg font-semibold mb-2">Sesi Berakhir</h2>
            <p className="text-gray-500 mb-4">
              Silakan login kembali untuk mengakses data pelanggan.
            </p>
            <Button onClick={handleLoginRedirect}>Login Kembali</Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen bg-slate-50">
      <header className="p-4 flex justify-between items-center bg-white sticky top-0 z-10 shadow-sm">
        <div className="flex items-center">
          <Link href="/akun" className="mr-3">
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <h1 className="text-lg font-semibold">Pelanggan</h1>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="icon"
            onClick={() => refetch()}
            disabled={isRefetching}
          >
            <RefreshCw
              className={`h-4 w-4 ${isRefetching ? 'animate-spin' : ''}`}
            />
          </Button>
          <Button
            variant="outline"
            size="icon"
            onClick={() => setShowFilterDialog(true)}
          >
            <Filter className="h-4 w-4" />
          </Button>
          <Button
            className="bg-blue-500 hover:bg-blue-600"
            onClick={() => router.push('/akun/customers/add')}
          >
            <Plus className="h-4 w-4" />
          </Button>
        </div>
      </header>

      <div className="flex-1 p-4">
        {/* Search Bar */}
        <div className="relative mb-4">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Cari nama, telepon, atau email..."
            value={searchQuery}
            onChange={handleSearchChange}
            className="pl-10"
          />
        </div>

        {/* Tabs */}
        <div className="flex gap-2 mb-4 overflow-x-auto">
          <Badge
            variant={activeTab === 'semua' ? 'default' : 'outline'}
            className="cursor-pointer whitespace-nowrap"
            onClick={() => setActiveTab('semua')}
          >
            Semua ({tabCounts.semua})
          </Badge>
          <Badge
            variant={activeTab === 'baru' ? 'default' : 'outline'}
            className="cursor-pointer whitespace-nowrap"
            onClick={() => setActiveTab('baru')}
          >
            Baru ({tabCounts.baru})
          </Badge>
          <Badge
            variant={activeTab === 'aktif' ? 'default' : 'outline'}
            className="cursor-pointer whitespace-nowrap"
            onClick={() => setActiveTab('aktif')}
          >
            Aktif ({tabCounts.aktif})
          </Badge>
          <Badge
            variant={activeTab === 'nonaktif' ? 'default' : 'outline'}
            className="cursor-pointer whitespace-nowrap"
            onClick={() => setActiveTab('nonaktif')}
          >
            Nonaktif ({tabCounts.nonaktif})
          </Badge>
        </div>

        {/* Loading State */}
        {isLoading && (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin mr-2" />
            <span>Memuat data pelanggan...</span>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="text-center py-8">
            <p className="text-red-500 mb-4">Gagal memuat data pelanggan</p>
            <Button onClick={() => refetch()} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Coba Lagi
            </Button>
          </div>
        )}

        {/* Customer List */}
        {!isLoading && !error && (
          <div className="space-y-3 mb-20">
            {customers.length > 0 ? (
              customers.map((customer) => {
                const statusBadge = getCustomerStatusBadge(customer.status);
                const orderCount =
                  customer._count?.orders || customer.totalOrders || 0;
                const totalSpent = customer.financialData?.totalSpent || 0;

                return (
                  <Card
                    key={customer.id}
                    className="p-3 cursor-pointer hover:shadow-md transition-shadow"
                    onClick={(e) => handleCustomerClick(e, customer.id)}
                  >
                    <div className="flex justify-between items-center">
                      <div className="flex items-center gap-3">
                        <Avatar>
                          <AvatarImage src="" alt={customer.name} />
                          <AvatarFallback>
                            {customer.name.substring(0, 2).toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <h3 className="font-medium">{customer.name}</h3>
                            <Badge
                              variant={statusBadge.variant}
                              className="text-xs"
                            >
                              {statusBadge.label}
                            </Badge>
                            {customer.labels && customer.labels.length > 0 && (
                              <div className="flex flex-wrap gap-1">
                                {customer.labels
                                  .slice(0, 2)
                                  .map((label, index) => (
                                    <Badge
                                      key={index}
                                      variant="secondary"
                                      className="text-xs"
                                    >
                                      {label}
                                    </Badge>
                                  ))}
                                {customer.labels.length > 2 && (
                                  <Badge
                                    variant="secondary"
                                    className="text-xs"
                                  >
                                    +{customer.labels.length - 2}
                                  </Badge>
                                )}
                              </div>
                            )}
                          </div>
                          <div className="text-sm text-gray-500 space-y-1">
                            {customer.phone && <p>📞 {customer.phone}</p>}
                            {customer.email && <p>✉️ {customer.email}</p>}
                            <p className="text-xs">
                              {orderCount} order •{' '}
                              {formatCurrency(Number(totalSpent))} •{' '}
                              {getCustomerTypeLabel(customer.customerType)}
                            </p>
                          </div>
                        </div>
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="dropdown-menu"
                          >
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent
                          align="end"
                          className="dropdown-menu"
                        >
                          <DropdownMenuItem
                            onClick={() =>
                              router.push(`/akun/customers/${customer.id}`)
                            }
                          >
                            <Eye className="h-4 w-4 mr-2" /> Lihat Detail
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() =>
                              router.push(`/akun/customers/edit/${customer.id}`)
                            }
                          >
                            <Edit className="h-4 w-4 mr-2" /> Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            className="text-red-500"
                            onClick={() =>
                              handleDeleteCustomer(customer.id, customer.name)
                            }
                          >
                            <Trash2 className="h-4 w-4 mr-2" /> Hapus
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </Card>
                );
              })
            ) : (
              <div className="text-center py-8 text-gray-500">
                {searchQuery
                  ? `Tidak ada pelanggan yang ditemukan untuk "${searchQuery}"`
                  : 'Belum ada pelanggan yang terdaftar'}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Hapus Pelanggan</DialogTitle>
            <DialogDescription>
              Apakah Anda yakin ingin menghapus pelanggan{' '}
              {customerToDelete?.name}? Tindakan ini tidak dapat dibatalkan dan
              akan menghapus semua data terkait.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowDeleteDialog(false)}
              disabled={deleteCustomerMutation.isPending}
            >
              Batal
            </Button>
            <Button
              variant="destructive"
              onClick={confirmDelete}
              disabled={deleteCustomerMutation.isPending}
            >
              {deleteCustomerMutation.isPending && (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              )}
              Hapus
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Filter Dialog */}
      <Dialog open={showFilterDialog} onOpenChange={setShowFilterDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Filter & Urutkan</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-2">
            <div>
              <Label className="text-base">Status</Label>
              <RadioGroup
                value={filterOptions.status}
                onValueChange={(value) =>
                  setFilterOptions({ ...filterOptions, status: value })
                }
                className="flex flex-col space-y-1 mt-2"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="semua" id="status-semua" />
                  <Label htmlFor="status-semua">Semua Status</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="ACTIVE" id="status-active" />
                  <Label htmlFor="status-active">Aktif</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="INACTIVE" id="status-inactive" />
                  <Label htmlFor="status-inactive">Tidak Aktif</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="NEW" id="status-new" />
                  <Label htmlFor="status-new">Baru</Label>
                </div>
              </RadioGroup>
            </div>

            <Separator />

            <div>
              <Label className="text-base">Tipe Pelanggan</Label>
              <RadioGroup
                value={filterOptions.customerType}
                onValueChange={(value) =>
                  setFilterOptions({ ...filterOptions, customerType: value })
                }
                className="flex flex-col space-y-1 mt-2"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="semua" id="type-semua" />
                  <Label htmlFor="type-semua">Semua Tipe</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="INDIVIDUAL" id="type-individual" />
                  <Label htmlFor="type-individual">Individu</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="CORPORATE" id="type-corporate" />
                  <Label htmlFor="type-corporate">Perusahaan</Label>
                </div>
              </RadioGroup>
            </div>

            <Separator />

            <div>
              <Label className="text-base">Urutkan Berdasarkan</Label>
              <RadioGroup
                value={filterOptions.sortBy}
                onValueChange={(value) =>
                  setFilterOptions({ ...filterOptions, sortBy: value })
                }
                className="flex flex-col space-y-1 mt-2"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="name:asc" id="sort-name-asc" />
                  <Label htmlFor="sort-name-asc">Nama (A-Z)</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="name:desc" id="sort-name-desc" />
                  <Label htmlFor="sort-name-desc">Nama (Z-A)</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem
                    value="totalOrders:desc"
                    id="sort-orders-desc"
                  />
                  <Label htmlFor="sort-orders-desc">
                    Jumlah Order (Tertinggi)
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem
                    value="totalOrders:asc"
                    id="sort-orders-asc"
                  />
                  <Label htmlFor="sort-orders-asc">
                    Jumlah Order (Terendah)
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="createdAt:desc" id="sort-recent" />
                  <Label htmlFor="sort-recent">Terdaftar Terbaru</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="createdAt:asc" id="sort-oldest" />
                  <Label htmlFor="sort-oldest">Terdaftar Terlama</Label>
                </div>
              </RadioGroup>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowFilterDialog(false)}
            >
              Batal
            </Button>
            <Button onClick={applyFilters}>Terapkan</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
