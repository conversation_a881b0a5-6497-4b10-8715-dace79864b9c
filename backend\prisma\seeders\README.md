# Database Seeders

Dokumentasi untuk database seeders aplikasi laundry.

## 📋 Seeders Available

### 1. Admin Seeder

Membuat akun admin default:

- **Email**: `<EMAIL>`
- **Password**: `admin123456`
- **Role**: `ADMIN`
- **Status**: Email dan phone terverifikasi

### 2. Location Seeder

Membuat data lokasi Indonesia lengkap:

- **38 Provinsi** seluruh Indonesia
- **105 Kota/Kabupaten** utama
- Data real-time berdasarkan pemerintah 2024

## 🚀 Cara <PERSON>an

```bash
# Semua seeders
npm run db:seed

# Seeder tertentu
npm run db:seed:admin
npm run db:seed:location
```

## 🔐 Login Admin

Setelah seeder berhasil:

- **Email**: `<EMAIL>`
- **Password**: `admin123456`

## 📍 Data Lokasi

Contoh data yang dibuat:

- DKI Jakarta → Jakarta Pusat, Jakarta Utara, dll
- <PERSON><PERSON> → Bandung, Bekasi, Bogor, dll
- <PERSON><PERSON> → Surabaya, Malang, Kediri, dll
- Dan seterusnya untuk seluruh Indonesia

## 🔄 Reset Data

Location data akan otomatis di-reset setiap kali seeder dijalankan.
Admin akan di-skip jika sudah ada.
