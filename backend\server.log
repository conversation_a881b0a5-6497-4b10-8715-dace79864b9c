yarn run v1.22.22
warning ../../package.json: No license field
$ cross-env NODE_ENV=development nodemon src/index.ts
[33m[nodemon] 2.0.20[39m
[33m[nodemon] to restart at any time, enter `rs`[39m
[33m[nodemon] watching path(s): *.*[39m
[33m[nodemon] watching extensions: ts,json[39m
[32m[nodemon] starting `ts-node src/index.ts`[39m
[33mwarn[39m: Unable to connect to email server. Make sure you have configured the SMTP options in .env
[32minfo[39m: Connected to SQL Database
[32minfo[39m: Listening to port 3000
node:events:485
      throw er; // Unhandled 'error' event
      ^

Error: read EIO
    at TTY.onStreamRead (node:internal/stream_base_commons:216:20)
Emitted 'error' event on ReadStream instance at:
    at emitErrorNT (node:internal/streams/destroy:170:8)
    at emitErrorCloseNT (node:internal/streams/destroy:129:3)
    at process.processTicksAndRejections (node:internal/process/task_queues:90:21) {
  errno: -5,
  code: 'EIO',
  syscall: 'read'
}

Node.js v23.11.0
