import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { useAuth } from '@/lib/auth-context';
import {
  servicesAPI,
  Service,
  CreateServiceRequest,
  UpdateServiceRequest,
  ServiceFilters,
  ServicesResponse,
} from '@/lib/api/services';

// Query keys
const QUERY_KEYS = {
  services: (outletId: number, filters?: Partial<ServiceFilters>) => [
    'services',
    outletId,
    filters,
  ],
  service: (id: number, outletId: number) => ['services', id, outletId],
};

// Hook untuk mendapatkan daftar services
export const useServices = (filters?: Partial<ServiceFilters>) => {
  const { activeOutlet } = useAuth();

  const queryFilters: ServiceFilters = {
    outletId: activeOutlet?.id || 0,
    page: 1,
    limit: 10,
    ...filters,
  };

  return useQuery<ServicesResponse, Error>({
    queryKey: QUERY_KEYS.services(activeOutlet?.id || 0, filters),
    queryFn: () => servicesAPI.getServices(queryFilters),
    enabled: !!activeOutlet?.id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Hook untuk mendapatkan detail service
export const useService = (id: number) => {
  const { activeOutlet } = useAuth();

  return useQuery<Service, Error>({
    queryKey: QUERY_KEYS.service(id, activeOutlet?.id || 0),
    queryFn: () => servicesAPI.getService(id, activeOutlet?.id || 0),
    enabled: !!activeOutlet?.id && !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Hook untuk membuat service baru
export const useCreateService = () => {
  const queryClient = useQueryClient();
  const { activeOutlet } = useAuth();

  return useMutation<Service, Error, CreateServiceRequest>({
    mutationFn: servicesAPI.createService,
    onSuccess: (data) => {
      // Invalidate services list
      queryClient.invalidateQueries({
        queryKey: ['services', activeOutlet?.id],
      });

      // Invalidate service categories to refresh the services within categories
      queryClient.invalidateQueries({
        queryKey: ['serviceCategories'],
      });

      toast.success('Service berhasil dibuat!');
    },
    onError: (error: any) => {
      const message = getServiceErrorMessage(error);
      toast.error(message);
    },
  });
};

// Hook untuk mengupdate service
export const useUpdateService = () => {
  const queryClient = useQueryClient();
  const { activeOutlet } = useAuth();

  return useMutation<
    Service,
    Error,
    { id: number; data: UpdateServiceRequest }
  >({
    mutationFn: ({ id, data }) => servicesAPI.updateService(id, data),
    onSuccess: (data, variables) => {
      // Update cache for specific service
      queryClient.setQueryData(
        QUERY_KEYS.service(variables.id, activeOutlet?.id || 0),
        data
      );

      // Invalidate services list
      queryClient.invalidateQueries({
        queryKey: ['services', activeOutlet?.id],
      });

      // Invalidate service categories to refresh the services within categories
      queryClient.invalidateQueries({
        queryKey: ['serviceCategories'],
      });

      toast.success('Service berhasil diperbarui!');
    },
    onError: (error: any) => {
      const message = getServiceErrorMessage(error);
      toast.error(message);
    },
  });
};

// Hook untuk menghapus service
export const useDeleteService = () => {
  const queryClient = useQueryClient();
  const { activeOutlet } = useAuth();

  return useMutation<void, Error, number>({
    mutationFn: (id) => servicesAPI.deleteService(id, activeOutlet?.id || 0),
    onSuccess: (_, serviceId) => {
      // Remove from cache
      queryClient.removeQueries({
        queryKey: QUERY_KEYS.service(serviceId, activeOutlet?.id || 0),
      });

      // Invalidate services list
      queryClient.invalidateQueries({
        queryKey: ['services', activeOutlet?.id],
      });

      // Invalidate service categories to refresh the services within categories
      queryClient.invalidateQueries({
        queryKey: ['serviceCategories'],
      });

      toast.success('Service berhasil dihapus!');
    },
    onError: (error: any) => {
      const message = getServiceErrorMessage(error);
      toast.error(message);
    },
  });
};

// Helper functions
export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
  }).format(amount);
};

export const getServiceStatusBadge = (isActive: boolean) => {
  return isActive
    ? { label: 'Aktif', variant: 'success' as const }
    : { label: 'Nonaktif', variant: 'secondary' as const };
};

export const getUnitLabel = (unit: string): string => {
  const unitLabels: Record<string, string> = {
    kg: 'Kilogram',
    pcs: 'Pieces',
    'm²': 'Meter Persegi',
    pair: 'Pasang',
    lot: 'Lot',
  };

  return unitLabels[unit] || unit;
};

export const getServiceErrorMessage = (error: any): string => {
  if (error?.response?.data?.message) {
    return error.response.data.message;
  }

  if (error?.response?.status === 400) {
    return 'Data yang dimasukkan tidak valid. Silakan periksa kembali.';
  }

  if (error?.response?.status === 401) {
    return 'Sesi Anda telah berakhir. Silakan login kembali.';
  }

  if (error?.response?.status === 403) {
    return 'Anda tidak memiliki izin untuk melakukan aksi ini.';
  }

  if (error?.response?.status === 404) {
    return 'Service tidak ditemukan.';
  }

  if (error?.response?.status === 409) {
    return 'Service dengan nama tersebut sudah ada.';
  }

  if (error?.response?.status >= 500) {
    return 'Terjadi kesalahan server. Silakan coba lagi nanti.';
  }

  return error?.message || 'Terjadi kesalahan yang tidak diketahui.';
};

export const getServiceFieldErrors = (error: any): Record<string, string> => {
  const fieldErrors: Record<string, string> = {};

  if (error?.response?.data?.details) {
    error.response.data.details.forEach((detail: any) => {
      if (detail.path && detail.message) {
        fieldErrors[detail.path[0]] = detail.message;
      }
    });
  }

  return fieldErrors;
};
