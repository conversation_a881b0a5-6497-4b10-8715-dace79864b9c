import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from '@/components/ui/use-toast';
import { useAuth } from '@/lib/auth-context';
import {
  cashboxAPI,
  type Cashbox,
  type CreateCashboxRequest,
  type UpdateCashboxRequest,
  type GetCashboxesParams,
  type AdjustBalanceRequest,
} from '@/lib/api/cashbox';

// Hook untuk mengambil semua cashboxes untuk outlet
export function useCashboxes(params?: GetCashboxesParams) {
  const { activeOutlet } = useAuth();

  return useQuery({
    queryKey: ['cashboxes', activeOutlet?.id, params],
    queryFn: () =>
      activeOutlet
        ? cashboxAPI.getCashboxes(activeOutlet.id, params)
        : Promise.reject('No active outlet'),
    enabled: !!activeOutlet,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook untuk mengambil cashbox berdasarkan ID
export function useCashbox(id: number | null) {
  const { activeOutlet } = useAuth();

  return useQuery({
    queryKey: ['cashboxes', activeOutlet?.id, id],
    queryFn: () =>
      activeOutlet && id
        ? cashboxAPI.getCashbox(activeOutlet.id, id)
        : Promise.reject('No active outlet or ID'),
    enabled: !!id && !!activeOutlet,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook untuk mengambil balance cashbox
export function useCashboxBalance(id: number | null) {
  const { activeOutlet } = useAuth();

  return useQuery({
    queryKey: ['cashboxes', activeOutlet?.id, id, 'balance'],
    queryFn: () =>
      activeOutlet && id
        ? cashboxAPI.getCashboxBalance(activeOutlet.id, id)
        : Promise.reject('No active outlet or ID'),
    enabled: !!id && !!activeOutlet,
    staleTime: 1 * 60 * 1000, // 1 minute (balance changes frequently)
  });
}

// Hook untuk membuat cashbox baru
export function useCreateCashbox() {
  const queryClient = useQueryClient();
  const { activeOutlet } = useAuth();

  return useMutation({
    mutationFn: (data: CreateCashboxRequest) => {
      if (!activeOutlet) throw new Error('No active outlet');
      return cashboxAPI.createCashbox(activeOutlet.id, data);
    },
    onSuccess: (newCashbox) => {
      // Invalidate dan refetch cashboxes list
      queryClient.invalidateQueries({ queryKey: ['cashboxes'] });

      toast({
        title: 'Berhasil',
        description: `Cashbox ${newCashbox.name} berhasil ditambahkan.`,
      });
    },
  });
}

// Hook untuk update cashbox
export function useUpdateCashbox() {
  const queryClient = useQueryClient();
  const { activeOutlet } = useAuth();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateCashboxRequest }) => {
      if (!activeOutlet) throw new Error('No active outlet');
      return cashboxAPI.updateCashbox(activeOutlet.id, id, data);
    },
    onSuccess: (updatedCashbox) => {
      // Invalidate cashboxes list dan detail
      queryClient.invalidateQueries({ queryKey: ['cashboxes'] });
      queryClient.setQueryData(
        ['cashboxes', activeOutlet?.id, updatedCashbox.id],
        updatedCashbox
      );

      toast({
        title: 'Berhasil',
        description: `Cashbox ${updatedCashbox.name} berhasil diperbarui.`,
      });
    },
  });
}

// Hook untuk delete cashbox
export function useDeleteCashbox() {
  const queryClient = useQueryClient();
  const { activeOutlet } = useAuth();

  return useMutation({
    mutationFn: (id: number) => {
      if (!activeOutlet) throw new Error('No active outlet');
      return cashboxAPI.deleteCashbox(activeOutlet.id, id);
    },
    onSuccess: () => {
      // Invalidate cashboxes list
      queryClient.invalidateQueries({ queryKey: ['cashboxes'] });

      toast({
        title: 'Berhasil',
        description: 'Cashbox berhasil dihapus.',
      });
    },
    onError: (error: any) => {
      const message =
        error.response?.data?.error ||
        error.response?.data?.message ||
        'Gagal menghapus cashbox';
      toast({
        title: 'Error',
        description: message,
        variant: 'destructive',
      });
    },
  });
}

// Hook untuk adjust balance cashbox
export function useAdjustCashboxBalance() {
  const queryClient = useQueryClient();
  const { activeOutlet } = useAuth();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: AdjustBalanceRequest }) => {
      if (!activeOutlet) throw new Error('No active outlet');
      return cashboxAPI.adjustCashboxBalance(activeOutlet.id, id, data);
    },
    onSuccess: (result, { id }) => {
      // Invalidate cashboxes list dan balance
      queryClient.invalidateQueries({ queryKey: ['cashboxes'] });
      queryClient.invalidateQueries({
        queryKey: ['cashboxes', activeOutlet?.id, id, 'balance'],
      });

      toast({
        title: 'Berhasil',
        description: `Saldo cashbox berhasil disesuaikan. Saldo baru: ${formatCurrency(
          result.balance
        )}`,
      });
    },
    onError: (error: any) => {
      const message =
        error.response?.data?.error ||
        error.response?.data?.message ||
        'Gagal menyesuaikan saldo cashbox';
      toast({
        title: 'Error',
        description: message,
        variant: 'destructive',
      });
    },
  });
}

// Helper function untuk extract error message dari API response
export function getCashboxErrorMessage(error: any): string {
  if (error?.response?.data?.error) {
    return error.response.data.error;
  }

  if (error?.response?.data?.message) {
    return error.response.data.message;
  }

  if (error?.message) {
    return error.message;
  }

  return 'Terjadi kesalahan yang tidak diketahui';
}

// Helper function untuk extract field errors dari API response
export function getCashboxFieldErrors(error: any): Record<string, string> {
  const fieldErrors: Record<string, string> = {};

  if (error?.response?.data?.message) {
    const message = error.response.data.message;

    // Check for specific validation patterns
    const patterns = [
      // Required field validation
      {
        pattern: /"name" is required/i,
        field: 'name',
        message: 'Nama cashbox wajib diisi',
      },
      {
        pattern: /"type" is required/i,
        field: 'type',
        message: 'Tipe cashbox wajib dipilih',
      },

      // Length validation
      {
        pattern: /"name" length must be at least \d+ characters long/i,
        field: 'name',
        message: 'Nama cashbox terlalu pendek',
      },
      {
        pattern:
          /"name" length must be less than or equal to \d+ characters long/i,
        field: 'name',
        message: 'Nama cashbox terlalu panjang (maksimal 100 karakter)',
      },

      // Enum validation
      {
        pattern: /"type" must be one of/i,
        field: 'type',
        message: 'Tipe cashbox harus TUNAI atau NON_TUNAI',
      },

      // Number validation
      {
        pattern: /"balance" must be a number/i,
        field: 'balance',
        message: 'Saldo harus berupa angka',
      },
      {
        pattern: /"balance" must be greater than or equal to 0/i,
        field: 'balance',
        message: 'Saldo tidak boleh negatif',
      },

      // Amount validation for balance adjustment
      {
        pattern: /"amount" is required/i,
        field: 'amount',
        message: 'Jumlah penyesuaian wajib diisi',
      },
      {
        pattern: /"amount" must be a number/i,
        field: 'amount',
        message: 'Jumlah penyesuaian harus berupa angka',
      },
      {
        pattern: /"reason" is required/i,
        field: 'reason',
        message: 'Alasan penyesuaian wajib diisi',
      },
      {
        pattern:
          /"reason" length must be less than or equal to \d+ characters long/i,
        field: 'reason',
        message: 'Alasan terlalu panjang (maksimal 500 karakter)',
      },
      {
        pattern:
          /"reference" length must be less than or equal to \d+ characters long/i,
        field: 'reference',
        message: 'Referensi terlalu panjang (maksimal 100 karakter)',
      },
    ];

    // Check each pattern
    for (const { pattern, field, message: msg } of patterns) {
      if (pattern.test(message)) {
        fieldErrors[field] = msg;
      }
    }

    // If no specific field error found but there's a validation error in the message,
    // try to extract field name from Joi error format
    if (Object.keys(fieldErrors).length === 0) {
      const joiFieldMatch = message.match(/"(\w+)"/);
      if (joiFieldMatch) {
        const fieldName = joiFieldMatch[1];
        fieldErrors[fieldName] = message;
      }
    }
  }

  return fieldErrors;
}

// Helper function untuk format currency Indonesia
export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
}

// Helper function untuk format cashbox type
export function getCashboxTypeBadge(type: string) {
  switch (type) {
    case 'TUNAI':
      return {
        label: 'Tunai',
        variant: 'default' as const,
        color: 'bg-green-100 text-green-800',
      };
    case 'NON_TUNAI':
      return {
        label: 'Non Tunai',
        variant: 'secondary' as const,
        color: 'bg-blue-100 text-blue-800',
      };
    default:
      return {
        label: type,
        variant: 'outline' as const,
        color: 'bg-gray-100 text-gray-800',
      };
  }
}

// Helper function untuk format cashbox status
export function getCashboxStatusBadge(isActive: boolean) {
  return isActive
    ? {
        label: 'Aktif',
        variant: 'default' as const,
        color: 'bg-green-100 text-green-800',
      }
    : {
        label: 'Nonaktif',
        variant: 'secondary' as const,
        color: 'bg-gray-100 text-gray-800',
      };
}
