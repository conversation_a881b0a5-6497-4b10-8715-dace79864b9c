# 📧 Email System Quick Start

Email system ini mendukung **automatic switching** antara SMTP dan SendGrid hanya dengan mengubah environment variable.

## 🚀 Quick Setup

### 1. Pilih Provider

**Untuk SMTP (Gmail):**

```bash
cp env-examples/smtp.env.example .env
# Edit .env dan isi kredensial Gmail Anda
```

**Untuk SendGrid:**

```bash
cp env-examples/sendgrid.env.example .env
# Edit .env dan isi kredensial SendGrid Anda
```

### 2. Konfigurasi Environment

**SMTP Setup:**

```env
EMAIL_PROVIDER=smtp
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
EMAIL_FROM=<EMAIL>
```

**SendGrid Setup:**

```env
EMAIL_PROVIDER=sendgrid
SENDGRID_API_KEY=SG.your-api-key
SENDGRID_FROM_EMAIL=<EMAIL>
```

### 3. Gunakan Email Service

```typescript
import emailService from './services/email.service';

// Email akan otomatis menggunakan provider yang dipilih
await emailService.sendEmail(
  '<EMAIL>',
  'Subject',
  'Text content',
  '<h1>HTML content</h1>'
);

// Fungsi pre-built
await emailService.sendWelcomeEmail('<EMAIL>', 'John Doe');
await emailService.sendResetPasswordEmail('<EMAIL>', 'token');
await emailService.sendVerificationEmail('<EMAIL>', 'token');
```

## 🔄 Switching Provider

Untuk beralih provider, cukup ubah satu variable:

```env
# Ubah dari SMTP ke SendGrid
EMAIL_PROVIDER=sendgrid

# Ubah dari SendGrid ke SMTP
EMAIL_PROVIDER=smtp
```

**Tidak perlu mengubah kode aplikasi!** 🎉

## 🧪 Testing

```typescript
// Import test functions
import emailTest from './examples/email-switching-example';

// Test complete system
await emailTest.completeEmailTest();

// Test specific provider
await emailTest.demonstrateAutoSwitching();
```

## 📊 Monitoring

Email service memberikan feedback lengkap:

```typescript
const result = await emailService.sendEmail('<EMAIL>', 'Test', 'Hello');

if (result.success) {
  console.log('✅ Email sent!', result.messageId);
} else {
  console.error('❌ Email failed:', result.error);
}
```

## 🔧 Advanced Configuration

### Environment Variables

| Variable              | Required           | Description                           | Default |
| --------------------- | ------------------ | ------------------------------------- | ------- |
| `EMAIL_PROVIDER`      | No                 | Email provider (`smtp` or `sendgrid`) | `smtp`  |
| `SMTP_HOST`           | Yes (for SMTP)     | SMTP server host                      | -       |
| `SMTP_PORT`           | Yes (for SMTP)     | SMTP port                             | -       |
| `SMTP_USERNAME`       | Yes (for SMTP)     | SMTP username                         | -       |
| `SMTP_PASSWORD`       | Yes (for SMTP)     | SMTP password                         | -       |
| `EMAIL_FROM`          | Yes (for SMTP)     | From email address                    | -       |
| `SENDGRID_API_KEY`    | Yes (for SendGrid) | SendGrid API key                      | -       |
| `SENDGRID_FROM_EMAIL` | Yes (for SendGrid) | Verified sender email                 | -       |

### Provider Detection

```typescript
// Check current provider
const provider = emailService.getCurrentProvider();
console.log(`Using provider: ${provider}`);
```

## 🆘 Troubleshooting

### SMTP Issues

- **Authentication failed**: Pastikan menggunakan App Password, bukan password biasa
- **Connection timeout**: Check SMTP_HOST dan SMTP_PORT
- **Email not sending**: Verify 2FA enabled di Gmail

### SendGrid Issues

- **401 Unauthorized**: Check SENDGRID_API_KEY
- **403 Forbidden**: Verify API key permissions (Mail Send)
- **Email rejected**: Verify sender email di SendGrid dashboard

### General Issues

- **Provider not found**: Check EMAIL_PROVIDER value (`smtp` or `sendgrid`)
- **Config error**: Check all required env variables are set
- **TypeScript errors**: Make sure to install dependencies: `npm install @sendgrid/mail`

## 📚 More Info

- [Complete Setup Guide](./SENDGRID_SETUP.md)
- [Email Examples](./src/examples/)
- [Type Definitions](./src/types/email.types.ts)

---

💡 **Tip**: Start with SMTP untuk development, switch ke SendGrid untuk production!
