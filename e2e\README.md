# 🧺 Laundry Management System

Full-stack aplikasi manajemen laundry dengan NextJS, Express.js, dan testing automation menggunakan Cypress.

## 📁 Struktur Project

```
laundry-app/
├── backend/                 # 🟢 Express.js API Server
├── nextjs-app/             # 🔵 Next.js Frontend App
├── cypress/                # 🧪 E2E Testing Suite (MAIN)
│   ├── e2e/               # Test files
│   ├── fixtures/          # Test data
│   └── support/           # Custom commands
├── cypress.config.ts      # Cypress configuration
└── package.json          # Root package dengan testing scripts
```

## 🚀 Quick Start

### 1. Install Dependencies

```bash
# Root dependencies (untuk Cypress testing)
npm install

# Backend dependencies
cd backend && npm install

# Frontend dependencies
cd ../nextjs-app && npm install --legacy-peer-deps
```

### 2. Environment Setup

```bash
# Setup environment variables
cp backend/.env.example backend/.env
cp nextjs-app/.env.local.example nextjs-app/.env.local
```

### 3. Run Applications

```bash
# Terminal 1: Start Backend API (Port 5000)
npm run dev:backend

# Terminal 2: Start Frontend (Port 3000)
npm run dev:nextjs

# Terminal 3: Run E2E Tests
npm run cypress:open
```

## 📝 Available Scripts

### Root Level (Testing & Project Management)

```bash
# Cypress E2E Testing
npm run cypress:open          # Open Cypress GUI
npm run cypress:run           # Run tests headless
npm run cypress:run:chrome    # Run with Chrome
npm run cypress:run:firefox   # Run with Firefox
npm run test:e2e             # Alias for cypress:run
npm run test:e2e:headed      # Run with browser visible

# Development
npm run dev:backend          # Start Express.js server
npm run dev:nextjs          # Start Next.js app
```

### Backend (cd backend/)

```bash
npm run dev                  # Start development server
npm run build               # Build for production
npm run start               # Start production server
npm run test                # Run backend tests
npm run migrate             # Run database migrations
npm run seed                # Seed database
```

### Frontend (cd nextjs-app/)

```bash
npm run dev                 # Start development server
npm run build              # Build for production
npm run start              # Start production server
npm run lint               # Run ESLint
```

## 🧪 Testing Strategy

### E2E Testing dengan Cypress

Setup Cypress berada di **root level** untuk testing yang comprehensive:

#### ✅ Current Test Coverage:

- **Authentication Flow**
  - Login/Logout functionality
  - Registration process
  - Password reset
  - Session management

#### 🔄 Planned Test Coverage:

- [ ] Order Management (CRUD)
- [ ] Customer Management
- [ ] Inventory Management
- [ ] Reports & Analytics
- [ ] Payment Integration
- [ ] WhatsApp Integration
- [ ] Mobile Responsive Testing

#### 🛠 Custom Commands:

```javascript
// Advanced authentication
cy.loginAs('<EMAIL>', 'password123');
cy.clearAuthData();

// User management
cy.createTestUser(userData);
cy.verifyUserInDatabase(userId);

// Wait helpers
cy.waitForPageLoad();
cy.waitForElement('[data-testid="button"]');

// Form helpers
cy.fillForm({ email: '<EMAIL>', password: '123456' });
```

## 🔧 Tech Stack

### Frontend

- **Framework**: Next.js 15 (App Router)
- **Styling**: Tailwind CSS
- **UI Components**: Radix UI
- **State Management**: React Query (TanStack Query)
- **Forms**: React Hook Form + Zod
- **Authentication**: NextAuth.js

### Backend

- **Framework**: Express.js
- **Database**: PostgreSQL
- **ORM**: Prisma
- **Authentication**: JWT
- **File Upload**: Multer
- **Email**: Brevo (SendinBlue)
- **WhatsApp**: Custom integration

### Testing

- **E2E Testing**: Cypress
- **API Testing**: Cypress (API calls)
- **Database Testing**: Cypress (custom commands)
- **Visual Testing**: Screenshots & Videos

## 🌍 Environments

### Development

- **Frontend**: http://localhost:3000
- **Backend**: http://localhost:5000
- **Database**: Local PostgreSQL

### Testing

- **Cypress Base URL**: http://localhost:3000
- **API URL**: http://localhost:5000/api

## 📊 Features

### Core Features

- ✅ User Authentication & Authorization
- ✅ Customer Management
- ✅ Order Management
- ✅ Service Management
- ✅ Inventory Management
- ✅ Financial Reports
- ✅ WhatsApp Integration
- ✅ Attendance System

### Advanced Features

- ✅ Multi-outlet Support
- ✅ Loyalty Program
- ✅ Automated WhatsApp Notifications
- ✅ Custom Reports
- ✅ Inventory Alerts
- ✅ Staff Management

## 🛡️ Quality Assurance

### Code Quality

- **Linting**: ESLint + TypeScript
- **Formatting**: Prettier
- **Git Hooks**: Husky + Lint-staged
- **Type Safety**: TypeScript throughout

### Testing

- **E2E Coverage**: Critical user journeys
- **API Testing**: All endpoints tested
- **Database Testing**: CRUD operations verified
- **Error Handling**: Comprehensive error scenarios

## 🚀 Deployment

### Prerequisites

- Node.js 18+
- PostgreSQL 14+
- PM2 (untuk production)

### Production Build

```bash
# Build backend
cd backend && npm run build

# Build frontend
cd ../nextjs-app && npm run build

# Run tests
cd .. && npm run test:e2e
```

## 📈 Development Workflow

1. **Feature Development**

   - Create feature branch
   - Develop backend API
   - Develop frontend UI
   - Write E2E tests
   - Code review
   - Merge to main

2. **Testing Strategy**

   - Unit tests (backend)
   - Integration tests (API)
   - E2E tests (user journeys)
   - Manual testing (edge cases)

3. **Deployment Process**
   - Automated testing
   - Build verification
   - Staging deployment
   - Production deployment

## 🔍 Debugging & Monitoring

### Development

```javascript
// Cypress debugging
cy.pause(); // Pause test execution
cy.debug(); // Debug current element
cy.log('Custom message'); // Custom logging
```

### Production Monitoring

- Error tracking
- Performance monitoring
- User analytics
- API monitoring

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/AmazingFeature`)
3. Write tests for your feature
4. Commit changes (`git commit -m 'Add AmazingFeature'`)
5. Push to branch (`git push origin feature/AmazingFeature`)
6. Open Pull Request

## 📄 License

This project is proprietary software. All rights reserved.

---

**Happy Coding! 🎉**

Para developer dan QA engineer silakan refer ke dokumentasi Cypress di `cypress/README.md` untuk detail testing.
