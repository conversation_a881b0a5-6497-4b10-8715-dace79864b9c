version: '3.8'

services:
  node-app:
    build: .
    image: node-app
    ports:
      - '3000:3000'
    depends_on:
      - postgresdb
    volumes:
      - .:/usr/src/node-app
    networks:
      - node-network

  postgresdb:
    image: postgres
    restart: always
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=secret
    ports:
      - '5432:5432'
    volumes:
      - dbdata:/var/lib/postgresql/data
    networks:
      - node-network

volumes:
  dbdata:
    driver: local

networks:
  node-network:
    driver: bridge
