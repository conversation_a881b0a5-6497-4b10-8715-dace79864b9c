import Joi from 'joi';
import { password } from './custom.validation';

const register = {
  body: Joi.object().keys({
    name: Joi.string().required().min(2).max(50),
    email: Joi.string().required().email(),
    phone: Joi.string()
      .required()
      .pattern(/^[0-9+]+$/)
      .min(10)
      .max(15),
    password: Joi.string().required().custom(password)
  })
};

const login = {
  body: Joi.object().keys({
    email: Joi.string().required(),
    password: Joi.string().required()
  })
};

const logout = {
  body: Joi.object().keys({
    refreshToken: Joi.string().required()
  })
};

const refreshTokens = {
  body: Joi.object().keys({
    refreshToken: Joi.string().required()
  })
};

const forgotPassword = {
  body: Joi.object().keys({
    email: Joi.string().email().required()
  })
};

const resetPassword = {
  query: Joi.object().keys({
    token: Joi.string().required()
  }),
  body: Joi.object().keys({
    password: Joi.string().required().custom(password)
  })
};

const sendVerificationEmail = {
  query: Joi.object().keys({
    method: Joi.string().valid('token', 'otp').required()
  })
};

const verifyEmail = {
  body: Joi.object()
    .keys({
      token: Joi.string().optional(),
      code: Joi.string()
        .optional()
        .length(6)
        .pattern(/^\d{6}$/)
    })
    .xor('token', 'code')
    .messages({
      'object.missing': 'Please provide either verification token or code',
      'object.xor': 'Please provide either verification token or code, not both'
    })
};

const sendPhoneVerification = {
  body: Joi.object().keys({
    phone: Joi.string().optional() // Optional, will use user's phone if not provided
  })
};

const verifyPhone = {
  body: Joi.object().keys({
    phone: Joi.string().optional(), // Optional, will use user's phone if not provided
    code: Joi.string().required().length(6)
  })
};

export default {
  register,
  login,
  logout,
  refreshTokens,
  forgotPassword,
  resetPassword,
  sendVerificationEmail,
  verifyEmail,
  sendPhoneVerification,
  verifyPhone
};
