import httpStatus from 'http-status';
import catchAsync from '../utils/catchAsync';
import ApiError from '../utils/ApiError';
import { customerDepositService } from '../services';
import { User } from '@prisma/client';

const depositIn = catchAsync(async (req, res) => {
  if (!req.user) {
    throw new ApiError(httpStatus.UNAUTHORIZED, 'User not authenticated');
  }

  const { customerId, outletId, amount, cashboxId, reference } = req.body;
  const depositTx = await customerDepositService.depositIn(
    customerId,
    outletId,
    amount,
    cashboxId,
    (req.user as User).id,
    reference
  );
  res.status(httpStatus.CREATED).send(depositTx);
});

const depositOut = catchAsync(async (req, res) => {
  if (!req.user) {
    throw new ApiError(httpStatus.UNAUTHORIZED, 'User not authenticated');
  }

  const { customerId, outletId, amount, cashboxId, reference } = req.body;
  const depositTx = await customerDepositService.depositOut(
    customerId,
    outletId,
    amount,
    cashboxId,
    (req.user as User).id,
    reference
  );
  res.status(httpStatus.CREATED).send(depositTx);
});

const payWithDeposit = catchAsync(async (req, res) => {
  if (!req.user) {
    throw new ApiError(httpStatus.UNAUTHORIZED, 'User not authenticated');
  }

  const { customerId, outletId, orderId, amount, reference } = req.body;
  const depositTx = await customerDepositService.payWithDeposit(
    customerId,
    outletId,
    orderId,
    amount,
    (req.user as User).id,
    reference
  );
  res.status(httpStatus.CREATED).send(depositTx);
});

const getDepositHistory = catchAsync(async (req, res) => {
  if (!req.user) {
    throw new ApiError(httpStatus.UNAUTHORIZED, 'User not authenticated');
  }

  const customerId = parseInt(req.params.customerId);
  const outletId = parseInt(req.params.outletId);

  const options = {
    page: req.query.page ? parseInt(req.query.page as string) : undefined,
    limit: req.query.limit ? parseInt(req.query.limit as string) : undefined,
    type: req.query.type as any,
    dateFrom: req.query.dateFrom ? new Date(req.query.dateFrom as string) : undefined,
    dateTo: req.query.dateTo ? new Date(req.query.dateTo as string) : undefined
  };

  const result = await customerDepositService.getDepositHistory(customerId, outletId, options);
  res.send(result);
});

const getDepositBalance = catchAsync(async (req, res) => {
  if (!req.user) {
    throw new ApiError(httpStatus.UNAUTHORIZED, 'User not authenticated');
  }

  const customerId = parseInt(req.params.customerId);
  const outletId = parseInt(req.params.outletId);

  const balance = await customerDepositService.getDepositBalance(customerId, outletId);
  res.send({ balance });
});

export default {
  depositIn,
  depositOut,
  payWithDeposit,
  getDepositHistory,
  getDepositBalance
};
