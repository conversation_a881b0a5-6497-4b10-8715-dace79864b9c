import Joi from 'joi';
import { password } from './custom.validation';

const createEmployee = {
  body: Joi.object().keys({
    email: Joi.string().required().email(),
    password: Joi.string().required().custom(password),
    name: Joi.string().required(),
    phone: Joi.string()
      .required()
      .pattern(/^[0-9+]+$/)
      .min(10)
      .max(15),
    outletId: Joi.number().integer().required()
  })
};

const getEmployees = {
  query: Joi.object().keys({
    name: Joi.string().optional(),
    outletId: Joi.string()
      .pattern(/^\d+$/)
      .optional()
      .custom((value, helpers) => {
        if (value === undefined) return value;
        const num = parseInt(value, 10);
        if (isNaN(num)) {
          return helpers.error('any.invalid');
        }
        return num;
      }),
    sortBy: Joi.string().optional(),
    limit: Joi.string()
      .pattern(/^\d+$/)
      .optional()
      .custom((value, helpers) => {
        if (value === undefined) return value;
        const num = parseInt(value, 10);
        if (isNaN(num)) {
          return helpers.error('any.invalid');
        }
        return num;
      }),
    page: Joi.string()
      .pattern(/^\d+$/)
      .optional()
      .custom((value, helpers) => {
        if (value === undefined) return value;
        const num = parseInt(value, 10);
        if (isNaN(num)) {
          return helpers.error('any.invalid');
        }
        return num;
      })
  })
};

const getEmployee = {
  params: Joi.object().keys({
    employeeId: Joi.number().integer()
  })
};

const updateEmployee = {
  params: Joi.object().keys({
    employeeId: Joi.number().integer()
  }),
  body: Joi.object()
    .keys({
      email: Joi.string().email(),
      password: Joi.string().custom(password),
      name: Joi.string(),
      phone: Joi.string()
        .pattern(/^[0-9+]+$/)
        .min(10)
        .max(15),
      outletId: Joi.number().integer(),
      isActive: Joi.boolean()
    })
    .min(1)
};

const deleteEmployee = {
  params: Joi.object().keys({
    employeeId: Joi.number().integer()
  })
};

export default {
  createEmployee,
  getEmployees,
  getEmployee,
  updateEmployee,
  deleteEmployee
};
