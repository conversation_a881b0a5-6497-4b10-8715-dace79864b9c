'use client';

import {
  Bell,
  Qr<PERSON>ode,
  CheckCircle,
  MapPin,
  ClipboardList,
  RefreshCw,
  Package,
  Truck,
  InboxIcon,
  CalendarCheck,
  CalendarDays,
  CalendarRange,
  AlertCircle,
} from 'lucide-react';
import Image from 'next/image';

import Link from 'next/link';

import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import BottomNavigation from '@/components/bottom-navigation';
import { useOrderCounts, useOrderSummary } from '@/hooks/useOrders';

export default function Home() {
  const orderCounts = useOrderCounts();
  const { data: orderSummary, isLoading: summaryLoading } = useOrderSummary();

  return (
    <div className="flex flex-col min-h-screen bg-slate-50">
      <header className="p-4 flex justify-between items-center">
        <div className="flex items-center">
          <Image
            src="/logo.png"
            alt="Obilas"
            width={120}
            height={40}
            className="h-10 w-auto"
          />
        </div>
        <div className="flex items-center gap-3">
          <Link href="/notifications" className="relative flex items-center">
            <Badge
              variant="outline"
              className="absolute -top-3 -left-4 rounded-full bg-orange-500 text-white border-none text-xs px-1.5 min-w-5 h-5 flex items-center justify-center"
            >
              9+
            </Badge>
            <Bell className="h-6 w-6 text-blue-500" />
          </Link>
        </div>
      </header>

      <main className="flex-1 px-4 pb-20">
        <div className="mb-6 relative">
          <h1 className="text-2xl font-semibold text-gray-800">
            Hai, Felis Laundry
          </h1>
          <p className="text-gray-600">Selamat datang kembali</p>
          <button className="absolute right-0 top-1/2 -translate-y-1/2">
            <QrCode className="h-10 w-10 text-blue-500" />
          </button>
        </div>

        <Card className="rounded-xl mb-8 bg-white shadow-sm border border-gray-100">
          <div className="p-5">
            <h2 className="text-gray-700 font-medium text-base mb-4">
              Ringkasan Pesanan
            </h2>
            <div className="grid grid-cols-5 gap-2">
              <Link
                href="/orders?filter=masuk"
                className="flex flex-col hover:bg-gray-50 p-2 rounded-md transition-colors items-center"
              >
                <InboxIcon className="h-4 w-4 text-gray-400" />
                <div className="flex items-center gap-2 my-1">
                  <span className="text-2xl font-semibold text-gray-800">
                    {summaryLoading ? '-' : orderSummary?.masuk || 0}
                  </span>
                </div>
                <span className="text-xs text-gray-500">Masuk</span>
              </Link>

              <Link
                href="/orders?filter=hari-ini"
                className="flex flex-col hover:bg-gray-50 p-2 rounded-md transition-colors items-center"
              >
                <CalendarCheck className="h-4 w-4 text-gray-400" />
                <div className="flex items-center gap-2 my-1">
                  <span className="text-2xl font-semibold text-gray-800">
                    {summaryLoading ? '-' : orderSummary?.hariIni || 0}
                  </span>
                </div>
                <span className="text-xs text-gray-500">Hari Ini</span>
              </Link>

              <Link
                href="/orders?filter=besok"
                className="flex flex-col hover:bg-gray-50 p-2 rounded-md transition-colors items-center"
              >
                <CalendarDays className="h-4 w-4 text-gray-400" />
                <div className="flex items-center gap-2 my-1">
                  <span className="text-2xl font-semibold text-gray-800">
                    {summaryLoading ? '-' : orderSummary?.besok || 0}
                  </span>
                </div>
                <span className="text-xs text-gray-500">Besok</span>
              </Link>

              <Link
                href="/orders?filter=lusa"
                className="flex flex-col hover:bg-gray-50 p-2 rounded-md transition-colors items-center"
              >
                <CalendarRange className="h-4 w-4 text-gray-400" />
                <div className="flex items-center gap-2 my-1">
                  <span className="text-2xl font-semibold text-gray-800">
                    {summaryLoading ? '-' : orderSummary?.lusa || 0}
                  </span>
                </div>
                <span className="text-xs text-gray-500">Lusa</span>
              </Link>

              <Link
                href="/orders?filter=terlambat"
                className="flex flex-col hover:bg-gray-50 p-2 rounded-md transition-colors items-center"
              >
                <AlertCircle className="h-4 w-4 text-red-400" />
                <div className="flex items-center gap-2 my-1">
                  <span className="text-2xl font-semibold text-red-500">
                    {summaryLoading ? '-' : orderSummary?.terlambat || 0}
                  </span>
                </div>
                <span className="text-xs text-gray-500">Terlambat</span>
              </Link>
            </div>
          </div>
        </Card>

        <div className="grid grid-cols-3 gap-4 mb-8">
          <Link
            href="/orders?tab=konfirmasi"
            className="flex flex-col items-center"
          >
            <div className="relative">
              <div className="bg-gray-100 rounded-full p-3 mb-2 relative">
                <CheckCircle className="h-10 w-10 text-green-500" />
              </div>
              <Badge className="absolute -top-2 -right-2 bg-orange-500 border-none">
                {orderCounts.konfirmasi}
              </Badge>
            </div>
            <span className="text-sm text-center">Konfirmasi</span>
          </Link>
          <Link
            href="/orders?tab=penjemputan"
            className="flex flex-col items-center"
          >
            <div className="relative">
              <div className="bg-gray-100 rounded-full p-3 mb-2">
                <MapPin className="h-10 w-10 text-red-500" />
              </div>
              <Badge className="absolute -top-2 -right-2 bg-orange-500 border-none">
                {orderCounts.pickup}
              </Badge>
            </div>
            <span className="text-sm text-center">Penjemputan</span>
          </Link>
          <Link
            href="/orders?tab=antrian"
            className="flex flex-col items-center"
          >
            <div className="relative">
              <div className="bg-gray-100 rounded-full p-3 mb-2">
                <ClipboardList className="h-10 w-10 text-blue-500" />
              </div>
              <Badge className="absolute -top-2 -right-2 bg-orange-500 border-none">
                {orderCounts.pending}
              </Badge>
            </div>
            <span className="text-sm text-center">Antrian</span>
          </Link>
          <Link
            href="/orders?tab=proses"
            className="flex flex-col items-center"
          >
            <div className="relative">
              <div className="bg-gray-100 rounded-full p-3 mb-2">
                <RefreshCw className="h-10 w-10 text-purple-500" />
              </div>
              <Badge className="absolute -top-2 -right-2 bg-orange-500 border-none">
                {orderCounts.processing}
              </Badge>
            </div>
            <span className="text-sm text-center">Proses</span>
          </Link>
          <Link
            href="/orders?tab=siap-ambil"
            className="flex flex-col items-center"
          >
            <div className="relative">
              <div className="bg-gray-100 rounded-full p-3 mb-2">
                <Package className="h-10 w-10 text-amber-500" />
              </div>
              <Badge className="absolute -top-2 -right-2 bg-orange-500 border-none">
                {orderCounts.ready}
              </Badge>
            </div>
            <span className="text-sm text-center">Siap Ambil</span>
          </Link>
          <Link
            href="/orders?tab=siap-antar"
            className="flex flex-col items-center"
          >
            <div className="relative">
              <div className="bg-gray-100 rounded-full p-3 mb-2">
                <Truck className="h-10 w-10 text-teal-500" />
              </div>
              <Badge className="absolute -top-2 -right-2 bg-orange-500 border-none">
                {orderCounts.ready_for_pickup}
              </Badge>
            </div>
            <span className="text-sm text-center">Siap Antar</span>
          </Link>
        </div>

        <div className="mb-4">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">
            Top 5 Pelanggan Bulan Ini
          </h2>
          <div className="space-y-4">
            {[
              { rank: 1, name: 'royan', transactions: 1 },
              { rank: 2, name: 'bebu', transactions: 1 },
              { rank: 3, name: 'Mimi akbar', transactions: 1 },
              { rank: 4, name: 'iman', transactions: 1 },
            ].map((customer) => (
              <div
                key={customer.rank}
                className="flex items-center justify-between"
              >
                <div className="flex items-center gap-3">
                  <div
                    className={`w-10 h-10 rounded-full flex items-center justify-center text-white font-bold ${
                      customer.rank === 1
                        ? 'bg-yellow-500'
                        : customer.rank === 2
                        ? 'bg-gray-400'
                        : customer.rank === 3
                        ? 'bg-orange-500'
                        : 'bg-gray-300'
                    }`}
                  >
                    {customer.rank}
                  </div>
                  <div>
                    <p className="font-semibold">{customer.name}</p>
                    <p className="text-sm text-gray-500">
                      {customer.transactions} Transaksi
                    </p>
                  </div>
                </div>
                <Link href={`/orders/create?customer=${customer.name}`}>
                  <Button
                    variant="outline"
                    className="rounded-full text-blue-500 border-blue-500"
                  >
                    + Order
                  </Button>
                </Link>
              </div>
            ))}
          </div>
        </div>
      </main>

      <BottomNavigation activePage="beranda" />
    </div>
  );
}
