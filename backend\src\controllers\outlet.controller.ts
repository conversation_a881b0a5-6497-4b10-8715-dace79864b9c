import httpStatus from 'http-status';
import pick from '../utils/pick';
import ApiError from '../utils/ApiError';
import catchAsync from '../utils/catchAsync';
import { outletService } from '../services';
import { User } from '@prisma/client';

const createOutlet = catchAsync(async (req, res) => {
  if (!req.user) {
    throw new ApiError(httpStatus.UNAUTHORIZED, 'User not authenticated');
  }
  const outlet = await outletService.createOutlet(req.body, (req.user as User).id);
  res.status(httpStatus.CREATED).send(outlet);
});

const getOutlets = catchAsync(async (req, res) => {
  if (!req.user) {
    throw new ApiError(httpStatus.UNAUTHORIZED, 'User not authenticated');
  }
  const filter = pick(req.query, ['name', 'city', 'province', 'isActive']);
  const options = pick(req.query, ['sortBy', 'sortType', 'limit', 'page']);
  const result = await outletService.queryOutlets(filter, options, req.user as User);
  res.send(result);
});

const getOutlet = catchAsync(async (req, res) => {
  if (!req.user) {
    throw new ApiError(httpStatus.UNAUTHORIZED, 'User not authenticated');
  }
  const outlet = await outletService.getOutletById(parseInt(req.params.outletId), req.user as User);
  if (!outlet) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Outlet not found');
  }
  res.send(outlet);
});

const updateOutlet = catchAsync(async (req, res) => {
  if (!req.user) {
    throw new ApiError(httpStatus.UNAUTHORIZED, 'User not authenticated');
  }
  const outlet = await outletService.updateOutletById(
    parseInt(req.params.outletId),
    req.body,
    req.user as User
  );
  res.send(outlet);
});

const deleteOutlet = catchAsync(async (req, res) => {
  if (!req.user) {
    throw new ApiError(httpStatus.UNAUTHORIZED, 'User not authenticated');
  }
  await outletService.deleteOutletById(parseInt(req.params.outletId), req.user as User);
  res.status(httpStatus.NO_CONTENT).send();
});

const getOutletServices = catchAsync(async (req, res) => {
  if (!req.user) {
    throw new ApiError(httpStatus.UNAUTHORIZED, 'User not authenticated');
  }
  const services = await outletService.getOutletServices(
    parseInt(req.params.outletId),
    req.user as User
  );
  res.send(services);
});

const copyServicesFromOutlet = catchAsync(async (req, res) => {
  if (!req.user) {
    throw new ApiError(httpStatus.UNAUTHORIZED, 'User not authenticated');
  }
  const { sourceOutletId } = req.body;
  const result = await outletService.copyServicesFromOutlet(
    parseInt(req.params.outletId),
    sourceOutletId,
    req.user as User
  );
  res.send(result);
});

const getOutletStats = catchAsync(async (req, res) => {
  if (!req.user) {
    throw new ApiError(httpStatus.UNAUTHORIZED, 'User not authenticated');
  }
  const stats = await outletService.getOutletStats(parseInt(req.params.outletId), req.user as User);
  res.send(stats);
});

export default {
  createOutlet,
  getOutlets,
  getOutlet,
  updateOutlet,
  deleteOutlet,
  getOutletServices,
  copyServicesFromOutlet,
  getOutletStats
};
