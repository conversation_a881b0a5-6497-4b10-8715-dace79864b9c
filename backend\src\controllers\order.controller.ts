import httpStatus from 'http-status';
import pick from '../utils/pick';
import ApiError from '../utils/ApiError';
import catchAsync from '../utils/catchAsync';
import orderService from '../services/order.service';
import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { Role } from '@prisma/client';

const prisma = new PrismaClient();

// Helper function to check if user has access to order
const checkOrderAccess = async (user: any, orderId: number) => {
  // First check if order exists at all
  const orderExists = await prisma.order.findFirst({
    where: { id: orderId },
    select: { id: true, outletId: true }
  });

  if (!orderExists) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Order not found');
  }

  // ADMIN can access all orders
  if (user.role === Role.ADMIN) {
    return orderExists.outletId;
  }

  // OWNER can access orders from their owned outlets
  if (user.role === Role.OWNER) {
    const outlet = await prisma.outlet.findFirst({
      where: {
        id: orderExists.outletId,
        ownerId: user.id
      }
    });

    if (!outlet) {
      throw new ApiError(httpStatus.FORBIDDEN, 'Access denied to this order');
    }

    return orderExists.outletId;
  }

  // EMPLOYEE can only access orders from their assigned outlet
  if (user.role === Role.EMPLOYEE) {
    if (!user.outletId || orderExists.outletId !== user.outletId) {
      throw new ApiError(httpStatus.FORBIDDEN, 'Access denied to this order');
    }

    return orderExists.outletId;
  }

  // Default: deny access
  throw new ApiError(httpStatus.FORBIDDEN, 'Access denied to this order');
};

const createOrder = catchAsync(async (req: Request, res: Response) => {
  const user = (req as any).user;

  if (!user) {
    throw new ApiError(httpStatus.UNAUTHORIZED, 'User not authenticated');
  }

  const orderData = req.body;

  const order = await orderService.createOrder(orderData, user.id);

  res.status(httpStatus.CREATED).send({
    success: true,
    message: 'Order created successfully',
    data: order
  });
});

const getOrders = catchAsync(async (req: Request, res: Response) => {
  const filter = pick(req.query, [
    'search',
    'paymentStatus',
    'status',
    'customerId',
    'dateFrom',
    'dateTo',
    'outletId',
    'filter',
    'processingStatus'
  ]);
  const options = pick(req.query, ['sortBy', 'limit', 'page']);
  const result = await orderService.queryOrders(filter, options);
  res.send(result);
});

const getOrder = catchAsync(async (req: Request, res: Response) => {
  const user = (req as any).user;
  console.log('🚀 ~ getOrder ~ user:', user);
  const orderId = parseInt(req.params.orderId);

  const outletId = await checkOrderAccess(user, orderId);
  const order = await orderService.getOrderById(orderId, outletId);
  res.send(order);
});

const updateOrder = catchAsync(async (req: Request, res: Response) => {
  const user = (req as any).user;
  const orderId = parseInt(req.params.orderId);

  const outletId = await checkOrderAccess(user, orderId);
  const order = await orderService.updateOrderById(orderId, req.body, outletId);
  res.send(order);
});

const updateOrderItems = catchAsync(async (req: Request, res: Response) => {
  const user = (req as any).user;
  const orderId = parseInt(req.params.orderId);

  const outletId = await checkOrderAccess(user, orderId);
  const order = await orderService.updateOrderItems(orderId, req.body, outletId, user.id);
  res.send(order);
});

const updateOrderStatus = catchAsync(async (req: Request, res: Response) => {
  const user = (req as any).user;
  const orderId = parseInt(req.params.orderId);

  const outletId = await checkOrderAccess(user, orderId);
  const order = await orderService.updateOrderStatus(
    orderId,
    req.body.status,
    outletId,
    user.id,
    req.body.notes
  );
  res.send(order);
});

const deleteOrder = catchAsync(async (req: Request, res: Response) => {
  const user = (req as any).user;
  const orderId = parseInt(req.params.orderId);

  const outletId = await checkOrderAccess(user, orderId);
  await orderService.deleteOrderById(orderId, outletId);
  res.status(httpStatus.NO_CONTENT).send();
});

const addPayment = catchAsync(async (req: Request, res: Response) => {
  const user = (req as any).user;
  const orderId = parseInt(req.params.orderId);

  const outletId = await checkOrderAccess(user, orderId);
  const payment = await orderService.addPaymentToOrder(orderId, req.body, outletId, user.id);
  res.status(httpStatus.CREATED).send(payment);
});

const updateOrderItemStatus = catchAsync(async (req: Request, res: Response) => {
  const user = (req as any).user;
  const orderId = parseInt(req.params.orderId);
  const itemId = parseInt(req.params.itemId);

  const outletId = await checkOrderAccess(user, orderId);
  const orderItem = await orderService.updateOrderItemStatus(
    orderId,
    itemId,
    req.body.status,
    outletId,
    user.id,
    req.body.notes
  );
  res.send(orderItem);
});

const getOrderStatusHistory = catchAsync(async (req: Request, res: Response) => {
  const user = (req as any).user;
  const orderId = parseInt(req.params.orderId);

  const outletId = await checkOrderAccess(user, orderId);
  const history = await orderService.getOrderStatusHistory(orderId, outletId);
  res.send(history);
});

const getOrderItemStatusHistory = catchAsync(async (req: Request, res: Response) => {
  const user = (req as any).user;
  const orderId = parseInt(req.params.orderId);
  const itemId = parseInt(req.params.itemId);

  const outletId = await checkOrderAccess(user, orderId);
  const history = await orderService.getOrderItemStatusHistory(orderId, itemId, outletId);
  res.send(history);
});

const getOrderTimeline = catchAsync(async (req: Request, res: Response) => {
  const user = (req as any).user;
  const orderId = parseInt(req.params.orderId);

  const outletId = await checkOrderAccess(user, orderId);
  const timeline = await orderService.getOrderTimeline(orderId, outletId);
  res.send(timeline);
});

const getOrderSummary = catchAsync(async (req: Request, res: Response) => {
  const user = (req as any).user;

  if (!user) {
    throw new ApiError(httpStatus.UNAUTHORIZED, 'User not authenticated');
  }

  const summary = await orderService.getOrderSummary(user);
  res.send(summary);
});

export default {
  createOrder,
  getOrders,
  getOrder,
  updateOrder,
  updateOrderItems,
  updateOrderStatus,
  deleteOrder,
  addPayment,
  updateOrderItemStatus,
  getOrderStatusHistory,
  getOrderItemStatusHistory,
  getOrderTimeline,
  getOrderSummary
};
