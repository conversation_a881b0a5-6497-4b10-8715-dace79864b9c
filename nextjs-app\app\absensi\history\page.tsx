"use client"

import { useState } from "react"
import Link from "next/link"
import { ArrowLeft, Calendar, Search, MapPin, Clock, Download, ChevronLeft, ChevronRight } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar as CalendarComponent } from "@/components/ui/calendar"

// Mock data for attendance records
const mockAttendanceRecords = [
  {
    id: 1,
    date: "2023-10-25",
    checkIn: "08:05:23",
    checkOut: "17:15:45",
    status: "present",
    location: "Cabang Utama",
    notes: "",
  },
  {
    id: 2,
    date: "2023-10-24",
    checkIn: "08:15:10",
    checkOut: "17:30:22",
    status: "late",
    location: "Cabang Utama",
    notes: "Terlambat karena macet",
  },
  {
    id: 3,
    date: "2023-10-23",
    checkIn: "07:55:33",
    checkOut: "17:05:12",
    status: "present",
    location: "Cabang Utama",
    notes: "",
  },
  {
    id: 4,
    date: "2023-10-22",
    checkIn: null,
    checkOut: null,
    status: "absent",
    location: null,
    notes: "Sakit",
  },
  {
    id: 5,
    date: "2023-10-21",
    checkIn: "08:02:45",
    checkOut: "17:10:18",
    status: "present",
    location: "Cabang Utama",
    notes: "",
  },
  {
    id: 6,
    date: "2023-10-20",
    checkIn: "08:00:12",
    checkOut: "17:05:33",
    status: "present",
    location: "Cabang Utama",
    notes: "",
  },
  {
    id: 7,
    date: "2023-10-19",
    checkIn: "08:10:45",
    checkOut: "17:20:15",
    status: "late",
    location: "Cabang Utama",
    notes: "Terlambat karena ban bocor",
  },
  {
    id: 8,
    date: "2023-10-18",
    checkIn: "07:58:22",
    checkOut: "17:15:40",
    status: "present",
    location: "Cabang Utama",
    notes: "",
  },
  {
    id: 9,
    date: "2023-10-17",
    checkIn: "08:05:10",
    checkOut: "17:10:05",
    status: "present",
    location: "Cabang Utama",
    notes: "",
  },
  {
    id: 10,
    date: "2023-10-16",
    checkIn: null,
    checkOut: null,
    status: "absent",
    location: null,
    notes: "Cuti",
  },
  {
    id: 11,
    date: "2023-10-15",
    checkIn: "08:03:15",
    checkOut: "17:08:30",
    status: "present",
    location: "Cabang Utama",
    notes: "",
  },
  {
    id: 12,
    date: "2023-10-14",
    checkIn: "08:01:45",
    checkOut: "17:05:20",
    status: "present",
    location: "Cabang Utama",
    notes: "",
  },
]

export default function AttendanceHistoryPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [dateFilter, setDateFilter] = useState<Date | undefined>(undefined)
  const [currentPage, setCurrentPage] = useState(1)
  const recordsPerPage = 10

  // Format date as DD/MM/YYYY
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("id-ID", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    })
  }

  // Get day name
  const getDayName = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("id-ID", { weekday: "long" })
  }

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "present":
        return <Badge className="bg-green-500">Hadir</Badge>
      case "late":
        return <Badge className="bg-yellow-500">Terlambat</Badge>
      case "absent":
        return <Badge className="bg-red-500">Tidak Hadir</Badge>
      default:
        return <Badge className="bg-gray-500">Tidak Diketahui</Badge>
    }
  }

  // Filter records based on search query, status, and date
  const filteredRecords = mockAttendanceRecords.filter((record) => {
    // Filter by search query (date or notes)
    const matchesSearch =
      formatDate(record.date).toLowerCase().includes(searchQuery.toLowerCase()) ||
      getDayName(record.date).toLowerCase().includes(searchQuery.toLowerCase()) ||
      (record.notes && record.notes.toLowerCase().includes(searchQuery.toLowerCase()))

    // Filter by status
    const matchesStatus = statusFilter === "all" || record.status === statusFilter

    // Filter by date
    const matchesDate = !dateFilter || record.date === dateFilter.toISOString().split("T")[0]

    return matchesSearch && matchesStatus && matchesDate
  })

  // Pagination
  const indexOfLastRecord = currentPage * recordsPerPage
  const indexOfFirstRecord = indexOfLastRecord - recordsPerPage
  const currentRecords = filteredRecords.slice(indexOfFirstRecord, indexOfLastRecord)
  const totalPages = Math.ceil(filteredRecords.length / recordsPerPage)

  // Handle page change
  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber)
  }

  // Reset filters
  const resetFilters = () => {
    setSearchQuery("")
    setStatusFilter("all")
    setDateFilter(undefined)
    setCurrentPage(1)
  }

  // Export attendance data
  const exportAttendanceData = () => {
    alert("Data absensi akan diunduh sebagai file CSV")
    // In a real app, this would generate and download a CSV file
  }

  return (
    <div className="flex flex-col min-h-screen bg-slate-50">
      <header className="p-4 flex justify-between items-center bg-white sticky top-0 z-10 shadow-sm">
        <div className="flex items-center">
          <Link href="/absensi" className="mr-3">
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <h1 className="text-lg font-semibold">Riwayat Absensi</h1>
        </div>
        <Button variant="outline" size="sm" onClick={exportAttendanceData}>
          <Download className="h-4 w-4 mr-1" /> Export
        </Button>
      </header>

      <main className="flex-1 p-4 pb-20">
        <div className="mb-4 space-y-3">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <Input
              placeholder="Cari tanggal atau catatan..."
              className="pl-10"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>

          <div className="flex flex-wrap gap-2">
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Semua Status</SelectItem>
                <SelectItem value="present">Hadir</SelectItem>
                <SelectItem value="late">Terlambat</SelectItem>
                <SelectItem value="absent">Tidak Hadir</SelectItem>
              </SelectContent>
            </Select>

            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" className="flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  {dateFilter ? formatDate(dateFilter.toISOString().split("T")[0]) : "Pilih Tanggal"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <CalendarComponent mode="single" selected={dateFilter} onSelect={setDateFilter} initialFocus />
              </PopoverContent>
            </Popover>

            {(searchQuery || statusFilter !== "all" || dateFilter) && (
              <Button variant="ghost" onClick={resetFilters}>
                Reset Filter
              </Button>
            )}
          </div>
        </div>

        <div className="space-y-3 mb-6">
          {currentRecords.length > 0 ? (
            currentRecords.map((record) => (
              <Card key={record.id}>
                <CardContent className="p-4">
                  <div className="flex justify-between items-start">
                    <div>
                      <div className="flex items-center gap-2">
                        <h3 className="font-medium">{formatDate(record.date)}</h3>
                        <span className="text-sm text-gray-500">({getDayName(record.date)})</span>
                      </div>
                      {getStatusBadge(record.status)}
                    </div>
                    <Link href={`/absensi/detail/${record.id}`}>
                      <Button variant="ghost" size="sm">
                        Detail
                      </Button>
                    </Link>
                  </div>

                  <div className="grid grid-cols-2 gap-4 mt-3">
                    <div>
                      <div className="flex items-center text-gray-600">
                        <Clock className="h-4 w-4 mr-1" />
                        <p className="text-sm">Check-In</p>
                      </div>
                      <p className="font-medium">{record.checkIn || "-"}</p>
                    </div>
                    <div>
                      <div className="flex items-center text-gray-600">
                        <Clock className="h-4 w-4 mr-1" />
                        <p className="text-sm">Check-Out</p>
                      </div>
                      <p className="font-medium">{record.checkOut || "-"}</p>
                    </div>
                  </div>

                  {record.location && (
                    <div className="mt-2 flex items-center text-gray-600">
                      <MapPin className="h-4 w-4 mr-1" />
                      <p className="text-sm">{record.location}</p>
                    </div>
                  )}

                  {record.notes && (
                    <div className="mt-2 text-sm text-gray-600">
                      <p className="font-medium">Catatan:</p>
                      <p>{record.notes}</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))
          ) : (
            <div className="text-center py-8 text-gray-500">
              <p>Tidak ada data absensi yang ditemukan</p>
              <Button variant="link" onClick={resetFilters}>
                Reset Filter
              </Button>
            </div>
          )}
        </div>

        {/* Pagination */}
        {filteredRecords.length > recordsPerPage && (
          <div className="flex justify-center items-center gap-2">
            <Button
              variant="outline"
              size="icon"
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>

            <span className="text-sm">
              Halaman {currentPage} dari {totalPages}
            </span>

            <Button
              variant="outline"
              size="icon"
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        )}
      </main>
    </div>
  )
}
