import httpStatus from 'http-status';
import { Service, Prisma, User } from '@prisma/client';
import prisma from '../client';
import ApiError from '../utils/ApiError';

type ServiceWithOutlet = Service & {
  outlet: {
    id: number;
    name: string;
  };
};

/**
 * Create a service
 * @param {Object} serviceBody
 * @param {User} user
 * @returns {Promise<Service>}
 */
const createService = async (serviceBody: any, user: User): Promise<Service> => {
  const { outletId, price, estimationHours, categoryId, ...serviceData } = serviceBody;

  // Check if user has permission to create service for this outlet
  if (user.role !== 'ADMIN') {
    const userOutlets = await prisma.outlet.findMany({
      where: { ownerId: user.id },
      select: { id: true }
    });

    const allowedOutletIds = userOutlets.map((outlet) => outlet.id);

    if (!allowedOutletIds.includes(outletId)) {
      throw new ApiError(httpStatus.FORBIDDEN, 'You can only create services for your own outlets');
    }
  }

  // Check if service with same name already exists in this outlet
  const existingService = await prisma.service.findFirst({
    where: {
      outletId,
      name: serviceData.name
    }
  });

  if (existingService) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      'Service with this name already exists in this outlet'
    );
  }

  // Create service
  const service = await prisma.service.create({
    data: {
      ...serviceData,
      outletId,
      price: price || 0,
      estimationHours: estimationHours || 24,
      categoryId: categoryId || null
    }
  });

  return service;
};

/**
 * Query for services
 * @param {Object} filter - Mongo filter
 * @param {Object} options - Query options
 * @param {User} user
 * @returns {Promise<QueryResult>}
 */
const queryServices = async (filter: any, options: any, user: User) => {
  const { outletId, search, isActive, unit } = filter;

  // Check if user has permission to access this outlet
  if (user.role !== 'ADMIN') {
    const userOutlets = await prisma.outlet.findMany({
      where: { ownerId: user.id },
      select: { id: true }
    });

    const allowedOutletIds = userOutlets.map((outlet) => outlet.id);

    if (!allowedOutletIds.includes(outletId)) {
      throw new ApiError(httpStatus.FORBIDDEN, 'You can only access services for your own outlets');
    }
  }

  const where: Prisma.ServiceWhereInput = {
    outletId: outletId
  };

  if (search) {
    where.OR = [
      { name: { contains: search, mode: 'insensitive' } },
      { description: { contains: search, mode: 'insensitive' } }
    ];
  }

  if (isActive !== undefined) {
    where.isActive = isActive;
  }

  if (unit) {
    where.unit = unit;
  }

  const page = options.page ?? 1;
  const limit = options.limit ?? 10;
  const sortBy = options.sortBy ?? 'createdAt:desc';

  const [sortField, sortOrder] = sortBy.split(':');

  const orderBy: Prisma.ServiceOrderByWithRelationInput = {};
  (orderBy as any)[sortField] = sortOrder || 'asc';

  const services = await prisma.service.findMany({
    where,
    include: {
      outlet: {
        select: {
          id: true,
          name: true
        }
      },
      category: {
        select: {
          id: true,
          name: true,
          description: true
        }
      }
    },
    orderBy,
    skip: (page - 1) * limit,
    take: limit
  });

  const totalResults = await prisma.service.count({ where });
  const totalPages = Math.ceil(totalResults / limit);

  return {
    results: services,
    page,
    limit,
    totalPages,
    totalResults
  };
};

/**
 * Get service by id
 * @param {ObjectId} id
 * @param {User} user
 * @param {number} outletId
 * @returns {Promise<ServiceWithOutlet>}
 */
const getServiceById = async (
  id: number,
  user: User,
  outletId: number
): Promise<ServiceWithOutlet | null> => {
  // Check if user has permission to access this outlet
  if (user.role !== 'ADMIN') {
    const userOutlets = await prisma.outlet.findMany({
      where: { ownerId: user.id },
      select: { id: true }
    });

    const allowedOutletIds = userOutlets.map((outlet) => outlet.id);

    if (!allowedOutletIds.includes(outletId)) {
      throw new ApiError(httpStatus.FORBIDDEN, 'You can only access services for your own outlets');
    }
  }

  const service = await prisma.service.findFirst({
    where: {
      id,
      outletId
    },
    include: {
      outlet: {
        select: {
          id: true,
          name: true
        }
      },
      category: {
        select: {
          id: true,
          name: true,
          description: true
        }
      }
    }
  });

  return service;
};

/**
 * Update service by id
 * @param {ObjectId} serviceId
 * @param {Object} updateBody
 * @param {User} user
 * @returns {Promise<Service>}
 */
const updateServiceById = async (
  serviceId: number,
  updateBody: any,
  user: User
): Promise<Service> => {
  const service = await getServiceById(serviceId, user, updateBody.outletId || 0);
  if (!service) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Service not found');
  }

  // Check if user has permission to update service for this outlet
  if (user.role !== 'ADMIN') {
    const userOutlets = await prisma.outlet.findMany({
      where: { ownerId: user.id },
      select: { id: true }
    });

    const allowedOutletIds = userOutlets.map((outlet) => outlet.id);

    if (!allowedOutletIds.includes(service.outletId)) {
      throw new ApiError(httpStatus.FORBIDDEN, 'You can only update services for your own outlets');
    }
  }

  // Check if new name already exists in this outlet (if name is being updated)
  if (updateBody.name && updateBody.name !== service.name) {
    const existingService = await prisma.service.findFirst({
      where: {
        outletId: service.outletId,
        name: updateBody.name,
        id: { not: serviceId }
      }
    });

    if (existingService) {
      throw new ApiError(
        httpStatus.BAD_REQUEST,
        'Service with this name already exists in this outlet'
      );
    }
  }

  const updatedService = await prisma.service.update({
    where: { id: serviceId },
    data: updateBody
  });

  return updatedService;
};

/**
 * Delete service by id
 * @param {ObjectId} serviceId
 * @param {User} user
 * @param {number} outletId
 * @returns {Promise<void>}
 */
const deleteServiceById = async (
  serviceId: number,
  user: User,
  outletId: number
): Promise<void> => {
  const service = await getServiceById(serviceId, user, outletId);
  if (!service) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Service not found');
  }

  // Check if user has permission to delete service for this outlet
  if (user.role !== 'ADMIN') {
    const userOutlets = await prisma.outlet.findMany({
      where: { ownerId: user.id },
      select: { id: true }
    });

    const allowedOutletIds = userOutlets.map((outlet) => outlet.id);

    if (!allowedOutletIds.includes(service.outletId)) {
      throw new ApiError(httpStatus.FORBIDDEN, 'You can only delete services for your own outlets');
    }
  }

  // Check if service is being used in any orders
  const ordersUsingService = await prisma.orderItem.findFirst({
    where: { serviceId }
  });

  if (ordersUsingService) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      'Cannot delete service that is being used in orders'
    );
  }

  await prisma.service.delete({
    where: { id: serviceId }
  });
};

export default {
  createService,
  queryServices,
  getServiceById,
  updateServiceById,
  deleteServiceById
};
