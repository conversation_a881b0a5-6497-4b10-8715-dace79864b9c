'use client';

import { useState, use } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import {
  ArrowLeft,
  Phone,
  MapPin,
  Mail,
  ShoppingBag,
  Calendar,
  Edit,
  Trash2,
  MessageSquare,
  Share2,
  Info,
  User,
  Globe,
  ChevronRight,
  MessageCircle,
  Loader2,
  RefreshCw,
  AlertCircle,
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

import {
  useCustomer,
  useDeleteCustomer,
  formatCurrency,
  getCustomerStatusBadge,
  getCustomerTypeLabel,
} from '@/hooks/useCustomers';

export default function CustomerDetailPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const router = useRouter();
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [activeTab, setActiveTab] = useState('info');
  const customerId = parseInt(use(params).id);

  const { data: customer, isLoading, error, refetch } = useCustomer(customerId);

  const deleteCustomerMutation = useDeleteCustomer();

  const handleDelete = async () => {
    if (customer) {
      try {
        await deleteCustomerMutation.mutateAsync(customer.id);
        setShowDeleteDialog(false);
        router.push('/akun/customers');
      } catch (error) {
        // Error sudah ditangani di mutation
      }
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="flex flex-col min-h-screen bg-slate-50">
        <header className="p-4 flex justify-between items-center bg-white sticky top-0 z-10 shadow-sm">
          <div className="flex items-center">
            <Link href="/akun/customers" className="mr-3">
              <ArrowLeft className="h-5 w-5" />
            </Link>
            <h1 className="text-lg font-semibold">Detail Pelanggan</h1>
          </div>
        </header>
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p>Memuat data pelanggan...</p>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error || !customer) {
    return (
      <div className="flex flex-col min-h-screen bg-slate-50">
        <header className="p-4 flex justify-between items-center bg-white sticky top-0 z-10 shadow-sm">
          <div className="flex items-center">
            <Link href="/akun/customers" className="mr-3">
              <ArrowLeft className="h-5 w-5" />
            </Link>
            <h1 className="text-lg font-semibold">Detail Pelanggan</h1>
          </div>
        </header>
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-lg font-semibold mb-2">
              Pelanggan Tidak Ditemukan
            </h2>
            <p className="text-gray-500 mb-4">
              {error
                ? 'Gagal memuat data pelanggan'
                : 'Pelanggan dengan ID tersebut tidak ditemukan'}
            </p>
            <div className="space-x-2">
              <Button onClick={() => refetch()} variant="outline">
                <RefreshCw className="h-4 w-4 mr-2" />
                Coba Lagi
              </Button>
              <Button onClick={() => router.push('/akun/customers')}>
                Kembali ke Daftar
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const statusBadge = getCustomerStatusBadge(customer.status);
  const orderCount = customer._count?.orders || customer.totalOrders || 0;
  const totalSpent = customer.financialData?.totalSpent || 0;
  const deposit = customer.financialData?.deposit || 0;
  const debt = customer.financialData?.debt || 0;
  const cashback = customer.financialData?.cashback || 0;
  const loyaltyPoints = customer.financialData?.loyaltyPoints || 0;

  // Format tanggal dalam bahasa Indonesia
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  };

  return (
    <div className="flex flex-col min-h-screen bg-slate-50">
      <header className="p-4 flex justify-between items-center bg-white sticky top-0 z-10 shadow-sm">
        <div className="flex items-center">
          <Link href="/akun/customers" className="mr-3">
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <h1 className="text-lg font-semibold">Detail Pelanggan</h1>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="icon" onClick={() => refetch()}>
            <RefreshCw className="h-4 w-4" />
          </Button>
          <Link href={`/akun/customers/edit/${customerId}`}>
            <Button variant="outline" size="icon">
              <Edit className="h-4 w-4" />
            </Button>
          </Link>
          <Button
            variant="outline"
            size="icon"
            className="text-red-500"
            onClick={() => setShowDeleteDialog(true)}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </header>

      <main className="flex-1 p-4 pb-20">
        <Card className="mb-4">
          <div className="p-4">
            <div className="flex justify-between items-start mb-2">
              <h2 className="text-xl font-bold">{customer.name}</h2>
              <div className="flex gap-2">
                {customer.phone && (
                  <a href={`tel:${customer.phone}`}>
                    <Button variant="outline" size="icon" className="h-8 w-8">
                      <Phone className="h-4 w-4" />
                    </Button>
                  </a>
                )}
                {customer.phone && (
                  <a
                    href={`https://wa.me/${customer.phone.replace(/^0/, '62')}`}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <Button variant="outline" size="icon" className="h-8 w-8">
                      <MessageCircle className="h-4 w-4" />
                    </Button>
                  </a>
                )}
              </div>
            </div>

            <div className="flex flex-wrap gap-1 mb-4">
              <Badge variant={statusBadge.variant}>{statusBadge.label}</Badge>
              {customer.labels &&
                customer.labels.length > 0 &&
                customer.labels.map((label, index) => (
                  <Badge key={index} variant="secondary">
                    {label}
                  </Badge>
                ))}
            </div>

            <div className="space-y-2 text-sm">
              {customer.phone && (
                <div className="flex items-center gap-2">
                  <Phone className="h-4 w-4 text-gray-500" />
                  <span>{customer.phone}</span>
                </div>
              )}
              {customer.email && (
                <div className="flex items-center gap-2">
                  <Mail className="h-4 w-4 text-gray-500" />
                  <span>{customer.email}</span>
                </div>
              )}
              {customer.address && (
                <div className="flex items-start gap-2">
                  <MapPin className="h-4 w-4 text-gray-500 mt-0.5" />
                  <span>{customer.address}</span>
                </div>
              )}
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-gray-500" />
                <span>Bergabung sejak {formatDate(customer.createdAt)}</span>
              </div>
              <div className="flex items-center gap-2">
                <User className="h-4 w-4 text-gray-500" />
                <span>
                  Tipe Pelanggan {getCustomerTypeLabel(customer.customerType)}
                </span>
              </div>
              {customer.source && (
                <div className="flex items-center gap-2">
                  <Globe className="h-4 w-4 text-gray-500" />
                  <span>Sumber: {customer.source}</span>
                </div>
              )}
            </div>
          </div>
        </Card>

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="info">Info</TabsTrigger>
            <TabsTrigger value="financial">Keuangan</TabsTrigger>
            <TabsTrigger value="notes">Catatan</TabsTrigger>
          </TabsList>

          <TabsContent value="info" className="space-y-4">
            <Card>
              <div className="p-4">
                <h3 className="text-lg font-semibold mb-4">
                  Statistik Pesanan
                </h3>
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-500">
                      {orderCount}
                    </div>
                    <div className="text-sm text-gray-500">Total Pesanan</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-500">
                      {formatCurrency(Number(totalSpent))}
                    </div>
                    <div className="text-sm text-gray-500">Total Belanja</div>
                  </div>
                </div>
              </div>
            </Card>

            {customer.province && (
              <Card>
                <div className="p-4">
                  <h3 className="text-lg font-semibold mb-4">Lokasi</h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-500">Provinsi:</span>
                      <span>{customer.province.name}</span>
                    </div>
                    {customer.city && (
                      <div className="flex justify-between">
                        <span className="text-gray-500">Kota:</span>
                        <span>{customer.city.name}</span>
                      </div>
                    )}
                    {customer.mapLink && (
                      <div className="pt-2">
                        <a
                          href={customer.mapLink}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-500 hover:underline text-sm"
                        >
                          Lihat di Maps →
                        </a>
                      </div>
                    )}
                  </div>
                </div>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="financial" className="space-y-4">
            <Card>
              <div className="p-4">
                <h3 className="text-lg font-semibold mb-4">Data Keuangan</h3>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-500">Total Belanja</span>
                    <span className="font-semibold text-green-600">
                      {formatCurrency(Number(totalSpent))}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-500">Deposit</span>
                    <span className="font-semibold text-blue-600">
                      {formatCurrency(Number(deposit))}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-500">Cashback</span>
                    <span className="font-semibold text-purple-600">
                      {formatCurrency(Number(cashback))}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-500">Hutang</span>
                    <span className="font-semibold text-red-600">
                      {formatCurrency(Number(debt))}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-500">Poin Loyalty</span>
                    <span className="font-semibold text-yellow-600">
                      {loyaltyPoints.toLocaleString()} poin
                    </span>
                  </div>
                  {customer.financialData?.preferredPaymentMethod && (
                    <div className="flex justify-between items-center">
                      <span className="text-gray-500">
                        Metode Pembayaran Favorit
                      </span>
                      <span className="font-medium">
                        {customer.financialData.preferredPaymentMethod}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </Card>
          </TabsContent>

          <TabsContent value="notes" className="space-y-4">
            {customer.notes ? (
              <Card>
                <div className="p-4">
                  <h3 className="text-lg font-semibold mb-4">
                    Catatan Pelanggan
                  </h3>
                  <div className="bg-gray-50 p-3 rounded text-sm">
                    {customer.notes}
                  </div>
                </div>
              </Card>
            ) : (
              <Card>
                <div className="p-4 text-center text-gray-500">
                  Belum ada catatan untuk pelanggan ini
                </div>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      </main>

      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Hapus Pelanggan</DialogTitle>
            <DialogDescription>
              Apakah Anda yakin ingin menghapus pelanggan {customer.name}?
              Tindakan ini tidak dapat dibatalkan dan akan menghapus semua data
              terkait.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowDeleteDialog(false)}
              disabled={deleteCustomerMutation.isPending}
            >
              Batal
            </Button>
            <Button
              variant="destructive"
              onClick={handleDelete}
              disabled={deleteCustomerMutation.isPending}
            >
              {deleteCustomerMutation.isPending && (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              )}
              Hapus
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
