import axios from 'axios';

const API_BASE_URL =
  process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/v1';

// Helper function to get token from localStorage or cookies
function getAccessToken(): string | null {
  // First try localStorage
  if (typeof window !== 'undefined') {
    const token = localStorage.getItem('accessToken');
    if (token) return token;

    // If not in localStorage, try to get from cookies via auth context
    const tokensStr = localStorage.getItem('tokens');
    if (tokensStr) {
      try {
        const tokens = JSON.parse(tokensStr);
        return tokens?.access?.token || null;
      } catch (error) {
        console.error('Error parsing tokens from localStorage:', error);
      }
    }
  }

  return null;
}

// Helper function to get refresh token
function getRefreshToken(): string | null {
  if (typeof window !== 'undefined') {
    const token = localStorage.getItem('refreshToken');
    if (token) return token;

    const tokensStr = localStorage.getItem('tokens');
    if (tokensStr) {
      try {
        const tokens = JSON.parse(tokensStr);
        return tokens?.refresh?.token || null;
      } catch (error) {
        console.error('Error parsing tokens from localStorage:', error);
      }
    }
  }

  return null;
}

// Create axios instance with base configuration
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = getAccessToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor to handle token refresh
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        const refreshToken = getRefreshToken();
        if (!refreshToken) {
          throw new Error('No refresh token');
        }

        const response = await axios.post(
          `${API_BASE_URL}/auth/refresh-tokens`,
          {
            refreshToken,
          }
        );

        const { access, refresh } = response.data;

        // Update both localStorage formats for compatibility
        localStorage.setItem('accessToken', access.token);
        localStorage.setItem('refreshToken', refresh.token);
        localStorage.setItem('tokens', JSON.stringify(response.data));

        // Update cookies for middleware
        document.cookie = `tokens=${JSON.stringify(
          response.data
        )}; path=/; max-age=${7 * 24 * 60 * 60}`;

        return api(originalRequest);
      } catch (refreshError) {
        console.error('Token refresh failed:', refreshError);
        localStorage.removeItem('accessToken');
        localStorage.removeItem('refreshToken');
        localStorage.removeItem('tokens');
        localStorage.removeItem('user');

        // Clear cookies
        document.cookie =
          'tokens=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
        document.cookie =
          'user=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';

        window.location.href = '/auth/login';
        return Promise.reject(refreshError);
      }
    }

    return Promise.reject(error);
  }
);

// TypeScript interfaces
export interface Cashbox {
  id: number;
  outletId: number;
  name: string;
  type: 'TUNAI' | 'NON_TUNAI';
  isActive: boolean;
  balance: number;
  createdAt: string;
  updatedAt: string;
}

export interface CreateCashboxRequest {
  name: string;
  type: 'TUNAI' | 'NON_TUNAI';
  isActive?: boolean;
  balance?: number;
}

export interface UpdateCashboxRequest {
  name?: string;
  type?: 'TUNAI' | 'NON_TUNAI';
  isActive?: boolean;
}

export interface GetCashboxesParams {
  type?: 'TUNAI' | 'NON_TUNAI';
  isActive?: boolean;
}

export interface CashboxBalance {
  cashboxId: number;
  name: string;
  type: 'TUNAI' | 'NON_TUNAI';
  balance: number;
  lastUpdated: string;
}

export interface AdjustBalanceRequest {
  balance: number;
  reason: string;
}

export interface AdjustBalanceResponse {
  id: number;
  outletId: number;
  name: string;
  type: 'TUNAI' | 'NON_TUNAI';
  isActive: boolean;
  balance: number;
  createdAt: string;
  updatedAt: string;
}

// API methods
export const cashboxAPI = {
  // Get all cashboxes for an outlet
  async getCashboxes(
    outletId: number,
    params?: GetCashboxesParams
  ): Promise<Cashbox[]> {
    const response = await api.get(`/outlets/${outletId}/cashboxes`, {
      params,
    });
    return response.data.results || response.data;
  },

  // Get single cashbox by ID
  async getCashbox(outletId: number, cashboxId: number): Promise<Cashbox> {
    const response = await api.get(
      `/outlets/${outletId}/cashboxes/${cashboxId}`
    );
    return response.data;
  },

  // Create new cashbox
  async createCashbox(
    outletId: number,
    data: CreateCashboxRequest
  ): Promise<Cashbox> {
    const response = await api.post(`/outlets/${outletId}/cashboxes`, data);
    return response.data;
  },

  // Update cashbox
  async updateCashbox(
    outletId: number,
    cashboxId: number,
    data: UpdateCashboxRequest
  ): Promise<Cashbox> {
    const response = await api.patch(
      `/outlets/${outletId}/cashboxes/${cashboxId}`,
      data
    );
    return response.data;
  },

  // Delete cashbox
  async deleteCashbox(outletId: number, cashboxId: number): Promise<void> {
    await api.delete(`/outlets/${outletId}/cashboxes/${cashboxId}`);
  },

  // Get cashbox balance
  async getCashboxBalance(
    outletId: number,
    cashboxId: number
  ): Promise<CashboxBalance> {
    const response = await api.get(
      `/outlets/${outletId}/cashboxes/${cashboxId}/balance`
    );
    // Backend returns { balance }, so we need to construct the full object
    const cashbox = await this.getCashbox(outletId, cashboxId);
    return {
      cashboxId: cashbox.id,
      name: cashbox.name,
      type: cashbox.type,
      balance: response.data.balance,
      lastUpdated: cashbox.updatedAt,
    };
  },

  // Adjust cashbox balance
  async adjustCashboxBalance(
    outletId: number,
    cashboxId: number,
    data: AdjustBalanceRequest
  ): Promise<AdjustBalanceResponse> {
    const response = await api.patch(
      `/outlets/${outletId}/cashboxes/${cashboxId}/balance`,
      data
    );
    return response.data;
  },
};
