'use client';

import type React from 'react';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { ArrowLeft, Save } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { usePerfume, useUpdatePerfume } from '@/hooks/usePerfumes';

import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { toast } from '@/components/ui/use-toast';
import { Toaster } from '@/components/ui/toaster';

const schema = z.object({
  name: z.string().min(1, 'Nama parfum wajib diisi'),
  description: z.string().optional(),
  isPopular: z.boolean().optional(),
  isActive: z.boolean().optional(),
});

type FormValues = z.infer<typeof schema>;

export default function EditPerfumePage({
  params,
}: {
  params: { id: string };
}) {
  const router = useRouter();
  const perfumeId = Number.parseInt(params.id);
  const { data, isLoading } = usePerfume(perfumeId);
  const updatePerfume = useUpdatePerfume();
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    setValue,
    watch,
    reset,
  } = useForm<FormValues>({
    resolver: zodResolver(schema),
    defaultValues: {
      name: '',
      description: '',
      isPopular: false,
      isActive: true,
    },
  });

  useEffect(() => {
    if (data) {
      reset({
        name: data.name,
        description: data.description || '',
        isPopular: data.isPopular,
        isActive: data.isActive,
      });
    }
  }, [data, reset]);

  const onSubmit = async (values: FormValues) => {
    try {
      await updatePerfume.mutateAsync({ id: perfumeId, data: values });
      toast({
        title: 'Berhasil',
        description: `Parfum ${values.name} berhasil diperbarui.`,
      });
      router.push('/akun/perfumes');
    } catch (error) {
      toast({
        title: 'Error',
        description: error?.message || 'Gagal update parfum',
        variant: 'destructive',
      });
    }
  };

  if (isLoading) {
    return (
      <div className="flex flex-col min-h-screen bg-slate-50">
        <header className="p-4 flex justify-between items-center bg-white sticky top-0 z-10 shadow-sm">
          <div className="flex items-center">
            <Link href="/akun/perfumes" className="mr-3">
              <ArrowLeft className="h-5 w-5" />
            </Link>
            <h1 className="text-lg font-semibold">Edit Parfum</h1>
          </div>
        </header>

        <main className="flex-1 p-4 pb-20 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
            <p className="text-gray-500">Memuat data parfum...</p>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen bg-slate-50">
      <Toaster />
      <header className="p-4 flex justify-between items-center bg-white sticky top-0 z-10 shadow-sm">
        <div className="flex items-center">
          <Link href="/akun/perfumes" className="mr-3">
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <h1 className="text-lg font-semibold">Edit Parfum</h1>
        </div>
        <Button
          type="submit"
          form="perfume-form"
          className="bg-blue-500 hover:bg-blue-600"
          size="sm"
          disabled={isSubmitting}
        >
          <Save className="h-4 w-4 mr-1" />
          {isSubmitting ? 'Menyimpan...' : 'Simpan'}
        </Button>
      </header>

      <main className="flex-1 p-4 pb-20">
        <Card>
          <form
            id="perfume-form"
            onSubmit={handleSubmit(onSubmit)}
            className="p-4 space-y-6"
          >
            <div className="space-y-4">
              <div>
                <Label htmlFor="name">Nama Parfum</Label>
                <Input
                  id="name"
                  {...register('name')}
                  placeholder="Masukkan nama parfum"
                  className="mt-1"
                />
                {errors.name && (
                  <p className="text-sm text-red-500 mt-1">
                    {errors.name.message}
                  </p>
                )}
              </div>

              <div>
                <Label htmlFor="description">Deskripsi</Label>
                <Textarea
                  id="description"
                  {...register('description')}
                  placeholder="Masukkan deskripsi parfum"
                  className="mt-1"
                  rows={3}
                />
                {errors.description && (
                  <p className="text-sm text-red-500 mt-1">
                    {errors.description.message}
                  </p>
                )}
              </div>

              <Separator />

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="isPopular">Tandai sebagai Populer</Label>
                  <p className="text-sm text-gray-500">
                    Parfum populer akan direkomendasikan kepada pelanggan
                  </p>
                </div>
                <Switch
                  id="isPopular"
                  checked={watch('isPopular')}
                  onCheckedChange={(checked) => setValue('isPopular', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="isActive">Status Aktif</Label>
                  <p className="text-sm text-gray-500">
                    Parfum tidak aktif tidak akan ditampilkan saat pembuatan
                    order
                  </p>
                </div>
                <Switch
                  id="isActive"
                  checked={watch('isActive')}
                  onCheckedChange={(checked) => setValue('isActive', checked)}
                />
              </div>
            </div>
          </form>
        </Card>
      </main>
    </div>
  );
}
