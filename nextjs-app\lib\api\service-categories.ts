import { api } from '../auth-context';

// Types
export interface ServiceCategory {
  id: number;
  name: string;
  description?: string;
  productionProcess: string[];
  outletId: number;
  createdAt: string;
  updatedAt: string;
  services?: {
    id: number;
    name: string;
    isActive: boolean;
    estimationHours: number;
    price: number;
    unit: string;
  }[];
  outlet?: {
    id: number;
    name: string;
  };
  _count?: {
    services: number;
  };
}

export interface CreateServiceCategoryRequest {
  name: string;
  description?: string;
  productionProcess?: string[];
  outletId?: number;
}

export interface UpdateServiceCategoryRequest {
  name?: string;
  description?: string;
  productionProcess?: string[];
  outletId?: number;
}

export interface ServiceCategoryFilters {
  search?: string;
  page?: number;
  limit?: number;
  outletId?: number;
  estimationHours?: number;
}

export interface ServiceCategoriesResponse {
  success: boolean;
  message: string;
  results: ServiceCategory[];
  pagination?: {
    page: number;
    limit: number;
    totalCount: number;
    totalPages: number;
  };
}

export interface ServiceCategoriesCount {
  estimateHour: number;
  count: number;
}

export interface ServiceCategoriesCountResponse {
  success: boolean;
  message: string;
  data: ServiceCategoriesCount[];
}

export interface ServiceCategoryResponse {
  success: boolean;
  message: string;
  data: ServiceCategory;
}

export interface ServiceCategorySelectItem {
  id: number;
  name: string;
  description?: string;
  _count: {
    services: number;
  };
}

// API functions
export const serviceCategoriesAPI = {
  // Get all service categories
  getServiceCategories: async (
    filters: ServiceCategoryFilters
  ): Promise<ServiceCategoriesResponse> => {
    const response = await api.get('/service-categories', { params: filters });
    return response.data;
  },

  // Get single service category
  getServiceCategory: async (id: number): Promise<ServiceCategoryResponse> => {
    const response = await api.get(`/service-categories/${id}`);
    return response.data;
  },

  // Create service category
  createServiceCategory: async (
    data: CreateServiceCategoryRequest
  ): Promise<ServiceCategoryResponse> => {
    const response = await api.post('/service-categories', data);
    return response.data;
  },

  // Update service category
  updateServiceCategory: async (
    id: number,
    data: UpdateServiceCategoryRequest
  ): Promise<ServiceCategoryResponse> => {
    const response = await api.patch(`/service-categories/${id}`, data);
    return response.data;
  },

  // Delete service category
  deleteServiceCategory: async (id: number): Promise<void> => {
    await api.delete(`/service-categories/${id}`);
  },

  // Get service categories for select dropdown
  getServiceCategoriesForSelect: async (): Promise<{
    success: boolean;
    message: string;
    data: ServiceCategorySelectItem[];
  }> => {
    const response = await api.get('/service-categories/select');
    return response.data;
  },

  // Get service categories count by duration/estimation hours
  getServiceCategoriesCount: async (filters: {
    outletId?: number;
  }): Promise<ServiceCategoriesCount[]> => {
    const response = await api.get('/service-categories/count', {
      params: filters,
    });
    console.log('🚀 ~ response:', response);
    return response.data;
  },
};
