# 📋 **Dokumentasi API Orders Frontend**

## 🎯 **Overview**

Implementasi API Orders frontend telah selesai dibuat dengan pola yang konsisten dengan modul lain (customers, cashbox, dll). Implementasi ini mencakup:

- **Types**: Definisi TypeScript interface untuk semua entitas order
- **API Layer**: Fungsi-fungsi untuk berkomunikasi dengan backend
- **Hooks**: React hooks untuk state management dengan React Query
- **Helper Functions**: Utility functions untuk formatting dan error handling

---

## 📁 **File Structure**

```
nextjs-app/
├── types/
│   └── orders.ts              # Type definitions
├── lib/api/
│   └── orders.ts              # API functions
└── hooks/
    └── useOrders.ts           # React hooks
```

---

## 🔧 **Implementasi**

### **1. Types (`types/orders.ts`)**

Berisi semua interface TypeScript yang diperlukan:

- **Order Types**: `OrderStatus`, `PaymentStatus`, `PaymentMethod`, `OrderItemStatus`
- **Entity Interfaces**: `Order`, `OrderItem`, `Payment`, `Customer`, `Service`, `Perfume`
- **Request/Response Interfaces**: `CreateOrderRequest`, `UpdateOrderRequest`, `GetOrdersResponse`, dll.
- **History Interfaces**: `OrderStatusHistory`, `OrderItemStatusHistory`, `OrderTimeline`

### **2. API Layer (`lib/api/orders.ts`)**

Berisi fungsi-fungsi untuk berkomunikasi dengan backend:

#### **Core Functions:**

- `getOrders(params)` - Get all orders dengan filter & pagination
- `getOrder(id)` - Get single order by ID
- `createOrder(data)` - Create new order
- `updateOrder(id, data)` - Update order
- `deleteOrder(id)` - Delete order

#### **Status Management:**

- `updateOrderStatus(id, data)` - Update order status
- `updateOrderItemStatus(orderId, itemId, data)` - Update item status
- `updateOrderItems(id, data)` - Update/add/delete order items

#### **Payment:**

- `addPayment(id, data)` - Add payment to order

#### **History & Timeline:**

- `getOrderStatusHistory(id)` - Get order status history
- `getOrderItemStatusHistory(orderId, itemId)` - Get item status history
- `getOrderTimeline(id)` - Get complete timeline

#### **Helper Functions:**

- `getOrderStatusLabel(status)` - Convert status ke label Indonesia
- `getPaymentStatusLabel(status)` - Convert payment status ke label
- `getPaymentMethodLabel(method)` - Convert payment method ke label
- `formatCurrency(amount)` - Format mata uang IDR
- `formatDate(dateString)` - Format tanggal Indonesia
- `getOrderErrorMessage(error)` - Extract error message dari API response

### **3. Hooks (`hooks/useOrders.ts`)**

Berisi React hooks untuk state management:

#### **Basic Hooks:**

- `useOrders(params)` - Get orders dengan filter
- `useOrder(id)` - Get single order
- `useCreateOrder()` - Create order
- `useUpdateOrder()` - Update order
- `useDeleteOrder()` - Delete order

#### **Status Management Hooks:**

- `useUpdateOrderStatus()` - Update order status
- `useUpdateOrderItems()` - Update order items
- `useUpdateOrderItemStatus()` - Update item status

#### **Payment Hook:**

- `useAddPayment()` - Add payment

#### **History Hooks:**

- `useOrderStatusHistory(orderId)` - Get order status history
- `useOrderItemStatusHistory(orderId, itemId)` - Get item status history
- `useOrderTimeline(orderId)` - Get complete timeline

#### **Specialized Hooks:**

- `useOrdersByStatus(status, params)` - Filter by order status
- `useOrdersByPaymentStatus(paymentStatus, params)` - Filter by payment status
- `useOrdersByCustomer(customerId, params)` - Filter by customer
- `useSearchOrders(searchQuery, params)` - Search orders
- `useOrdersByDateRange(dateFrom, dateTo, params)` - Filter by date range
- `useOrderCounts()` - Get counts untuk semua status

---

## 🚀 **Cara Penggunaan**

### **1. Basic Usage - Get Orders**

```tsx
import { useOrders } from '@/hooks/useOrders';

function OrdersList() {
  const {
    data: orders,
    isLoading,
    error,
  } = useOrders({
    limit: 10,
    page: 1,
    sortBy: 'createdAt:desc',
  });

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <div>
      {orders?.results.map((order) => (
        <div key={order.id}>
          <h3>{order.orderNumber}</h3>
          <p>Status: {order.status}</p>
          <p>Total: {formatCurrency(order.totalPrice)}</p>
        </div>
      ))}
    </div>
  );
}
```

### **2. Filter Orders by Status**

```tsx
import { useOrdersByStatus } from '@/hooks/useOrders';

function OrdersByStatus({ status }: { status: string }) {
  const { data: orders, isLoading } = useOrdersByStatus(status, {
    limit: 20,
    sortBy: 'createdAt:desc',
  });

  // Render orders...
}
```

### **3. Search Orders**

```tsx
import { useSearchOrders } from '@/hooks/useOrders';

function SearchOrders({ searchQuery }: { searchQuery: string }) {
  const { data: orders, isLoading } = useSearchOrders(searchQuery);

  // Render search results...
}
```

### **4. Get Single Order Detail**

```tsx
import { useOrder } from '@/hooks/useOrders';

function OrderDetail({ orderId }: { orderId: number }) {
  const { data: order, isLoading, error } = useOrder(orderId);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;
  if (!order) return <div>Order not found</div>;

  return (
    <div>
      <h1>{order.orderNumber}</h1>
      <p>Customer: {order.customer?.name}</p>
      <p>Status: {getOrderStatusLabel(order.status)}</p>
      <p>Payment: {getPaymentStatusLabel(order.paymentStatus)}</p>
      <p>Total: {formatCurrency(order.totalPrice)}</p>

      <h3>Items:</h3>
      {order.items.map((item) => (
        <div key={item.id}>
          <p>
            {item.serviceName} - {item.quantity} {item.unit}
          </p>
          <p>Status: {item.status}</p>
          <p>Subtotal: {formatCurrency(item.subtotal)}</p>
        </div>
      ))}
    </div>
  );
}
```

### **5. Update Order Status**

```tsx
import { useUpdateOrderStatus } from '@/hooks/useOrders';

function UpdateOrderStatus({ orderId }: { orderId: number }) {
  const updateStatus = useUpdateOrderStatus();

  const handleStatusUpdate = (newStatus: string) => {
    updateStatus.mutate({
      id: orderId,
      data: {
        status: newStatus as any,
        notes: 'Status updated by user',
      },
    });
  };

  return (
    <div>
      <button onClick={() => handleStatusUpdate('PROCESSING')}>
        Set to Processing
      </button>
      <button onClick={() => handleStatusUpdate('READY')}>Set to Ready</button>
    </div>
  );
}
```

### **6. Add Payment**

```tsx
import { useAddPayment } from '@/hooks/useOrders';

function AddPayment({ orderId }: { orderId: number }) {
  const addPayment = useAddPayment();

  const handleAddPayment = () => {
    addPayment.mutate({
      id: orderId,
      data: {
        amount: 50000,
        method: 'CASH',
        reference: 'CASH-001',
        notes: 'Payment via cash',
      },
    });
  };

  return <button onClick={handleAddPayment}>Add Payment</button>;
}
```

### **7. Get Order Counts untuk Tabs**

```tsx
import { useOrderCounts } from '@/hooks/useOrders';

function OrderTabs() {
  const counts = useOrderCounts();

  return (
    <div>
      <button>All ({counts.all})</button>
      <button>Pending ({counts.pending})</button>
      <button>Processing ({counts.processing})</button>
      <button>Ready ({counts.ready})</button>
      <button>Delivered ({counts.delivered})</button>
      <button>Unpaid ({counts.unpaid})</button>
      <button>Paid ({counts.paid})</button>
    </div>
  );
}
```

---

## 📝 **Status Mapping**

### **Order Status:**

- `PENDING` → "Menunggu"
- `PROCESSING` → "Diproses"
- `WASHING` → "Dicuci"
- `DRYING` → "Dikeringkan"
- `IRONING` → "Disetrika"
- `READY` → "Siap Diambil"
- `DELIVERED` → "Selesai"
- `CANCELLED` → "Dibatalkan"

### **Payment Status:**

- `UNPAID` → "Belum Bayar"
- `PARTIAL` → "Bayar Sebagian"
- `PAID` → "Lunas"
- `REFUNDED` → "Dikembalikan"

### **Payment Method:**

- `CASH` → "Tunai"
- `TRANSFER` → "Transfer"
- `CREDIT_CARD` → "Kartu Kredit"
- `DEBIT_CARD` → "Kartu Debit"
- `E_WALLET` → "E-Wallet"
- `DEPOSIT` → "Deposit"

---

## ⚠️ **Catatan Penting**

### **1. Fitur yang Belum Terintegrasi dengan UI Existing:**

Beberapa fitur API backend yang belum bisa diintegrasikan dengan UI yang ada karena perbedaan struktur data:

#### **Backend API Features:**

- ✅ **Create Order dengan Payment** - Backend support payment saat create order
- ✅ **Order Item Status Management** - Backend support individual item status
- ✅ **Order Timeline** - Backend provide complete timeline
- ✅ **Payment History** - Backend support multiple payments per order
- ✅ **Status History** - Backend track semua perubahan status

#### **Frontend UI Limitations:**

- ❌ **UI belum support payment saat create order** - UI masih hardcoded data
- ❌ **UI belum support individual item status** - UI masih menggunakan overall status
- ❌ **UI belum support real timeline** - UI masih menggunakan mock data
- ❌ **UI belum connect ke API** - UI masih menggunakan static data

### **2. Mapping Status UI ke Backend:**

UI menggunakan status yang berbeda dengan backend:

#### **UI Status (Current):**

- `none`, `cuci`, `kering`, `setrika`, `packing`, `selesai`

#### **Backend Status (API):**

- `PENDING`, `PROCESSING`, `WASHING`, `DRYING`, `IRONING`, `READY`, `DELIVERED`, `CANCELLED`

### **3. Next Steps untuk Full Integration:**

1. **Update UI Components** untuk menggunakan hooks yang sudah dibuat
2. **Mapping Status** antara UI dan Backend
3. **Replace Mock Data** dengan real API calls
4. **Add Error Handling** di UI components
5. **Add Loading States** di UI components
6. **Update Create Order Flow** untuk support payment
7. **Add Individual Item Status Management**

---

## 🎉 **Kesimpulan**

✅ **Implementasi API Orders frontend sudah lengkap** dengan:

- Types yang comprehensive
- API functions yang lengkap sesuai backend
- Hooks yang powerful untuk state management
- Helper functions untuk UI formatting
- Error handling yang proper
- Documentation yang detail

🚧 **Yang masih perlu dilakukan:**

- Integrasi dengan UI components yang ada
- Mapping status antara UI dan backend
- Replace mock data dengan real API calls

Implementasi ini sudah siap digunakan dan tinggal diintegrasikan dengan UI components yang ada di halaman orders! 🎯
