import httpStatus from 'http-status';
import catchAsync from '../utils/catchAsync';
import ApiError from '../utils/ApiError';
import { cashboxService } from '../services';
import { User } from '@prisma/client';

const createCashbox = catchAsync(async (req, res) => {
  if (!req.user) {
    throw new ApiError(httpStatus.UNAUTHORIZED, 'User not authenticated');
  }

  const outletId = parseInt(req.params.outletId);
  const cashbox = await cashboxService.createCashbox(req.body, outletId, req.user as User);
  res.status(httpStatus.CREATED).send(cashbox);
});

const getCashboxes = catchAsync(async (req, res) => {
  if (!req.user) {
    throw new ApiError(httpStatus.UNAUTHORIZED, 'User not authenticated');
  }

  const outletId = parseInt(req.params.outletId);
  const options = {
    type: req.query.type as any,
    isActive: req.query.isActive ? req.query.isActive === 'true' : undefined,
    page: req.query.page ? parseInt(req.query.page as string) : undefined,
    limit: req.query.limit ? parseInt(req.query.limit as string) : undefined
  };

  const result = await cashboxService.getCashboxes(outletId, req.user as User, options);
  res.send(result);
});

const getCashbox = catchAsync(async (req, res) => {
  if (!req.user) {
    throw new ApiError(httpStatus.UNAUTHORIZED, 'User not authenticated');
  }

  const cashboxId = parseInt(req.params.cashboxId);
  const outletId = parseInt(req.params.outletId);
  const cashbox = await cashboxService.getCashboxById(cashboxId, outletId, req.user as User);
  res.send(cashbox);
});

const updateCashbox = catchAsync(async (req, res) => {
  if (!req.user) {
    throw new ApiError(httpStatus.UNAUTHORIZED, 'User not authenticated');
  }

  const cashboxId = parseInt(req.params.cashboxId);
  const outletId = parseInt(req.params.outletId);
  const cashbox = await cashboxService.updateCashbox(
    cashboxId,
    outletId,
    req.body,
    req.user as User
  );
  res.send(cashbox);
});

const deleteCashbox = catchAsync(async (req, res) => {
  if (!req.user) {
    throw new ApiError(httpStatus.UNAUTHORIZED, 'User not authenticated');
  }

  const cashboxId = parseInt(req.params.cashboxId);
  const outletId = parseInt(req.params.outletId);
  await cashboxService.deleteCashbox(cashboxId, outletId, req.user as User);
  res.status(httpStatus.NO_CONTENT).send();
});

const getCashboxBalance = catchAsync(async (req, res) => {
  if (!req.user) {
    throw new ApiError(httpStatus.UNAUTHORIZED, 'User not authenticated');
  }

  const cashboxId = parseInt(req.params.cashboxId);
  const outletId = parseInt(req.params.outletId);
  const balance = await cashboxService.getCashboxBalance(cashboxId, outletId, req.user as User);
  res.send({ balance });
});

const adjustCashboxBalance = catchAsync(async (req, res) => {
  if (!req.user) {
    throw new ApiError(httpStatus.UNAUTHORIZED, 'User not authenticated');
  }

  const cashboxId = parseInt(req.params.cashboxId);
  const outletId = parseInt(req.params.outletId);
  const { balance, reason } = req.body;

  const cashbox = await cashboxService.adjustCashboxBalance(
    cashboxId,
    outletId,
    balance,
    req.user as User,
    reason
  );
  res.send(cashbox);
});

export default {
  createCashbox,
  getCashboxes,
  getCashbox,
  updateCashbox,
  deleteCashbox,
  getCashboxBalance,
  adjustCashboxBalance
};
