import { Skeleton } from "@/components/ui/skeleton"
import { Card } from "@/components/ui/card"

export default function AbsensiLoading() {
  return (
    <div className="flex flex-col min-h-screen bg-slate-50">
      <header className="p-4 flex justify-between items-center bg-white shadow-sm">
        <Skeleton className="h-8 w-32" />
        <Skeleton className="h-9 w-24" />
      </header>

      <main className="flex-1 p-4 pb-20">
        <div className="mb-4">
          <Skeleton className="h-10 w-full mb-4" />
        </div>

        <Card className="p-4 mb-4">
          <div className="text-center mb-4">
            <Skeleton className="h-10 w-32 mx-auto mb-2" />
            <Skeleton className="h-5 w-48 mx-auto" />
          </div>

          <div className="flex items-center justify-center mb-4">
            <Skeleton className="h-12 w-12 rounded-full" />
            <Skeleton className="h-5 w-48 ml-2" />
          </div>

          <Skeleton className="h-10 w-full mb-4" />
          <div className="grid grid-cols-2 gap-3">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
          </div>
        </Card>

        <Card className="p-4 mb-4">
          <Skeleton className="h-6 w-48 mb-4" />
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <div className="flex items-center">
                <Skeleton className="h-10 w-10 rounded-full mr-3" />
                <div>
                  <Skeleton className="h-5 w-20 mb-1" />
                  <Skeleton className="h-4 w-16" />
                </div>
              </div>
              <Skeleton className="h-6 w-24" />
            </div>
            <div className="flex justify-between items-center">
              <div className="flex items-center">
                <Skeleton className="h-10 w-10 rounded-full mr-3" />
                <div>
                  <Skeleton className="h-5 w-20 mb-1" />
                  <Skeleton className="h-4 w-16" />
                </div>
              </div>
              <Skeleton className="h-6 w-24" />
            </div>
          </div>
        </Card>

        <div className="flex justify-between">
          <Skeleton className="h-10 w-[48%]" />
          <Skeleton className="h-10 w-[48%]" />
        </div>
      </main>

      <div className="h-16 bg-white border-t border-gray-200" />
    </div>
  )
}
