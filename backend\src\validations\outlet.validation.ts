import Joi from 'joi';

const createOutlet = {
  body: Joi.object().keys({
    name: Joi.string().required().min(3).max(255),
    address: Joi.string().required().min(10),
    province: Joi.string().required().min(2).max(100),
    city: Joi.string().required().min(2).max(100),
    provinceId: Joi.number().integer().positive().optional(),
    cityId: Joi.number().integer().positive().optional(),
    timezone: Joi.string().optional().default('Asia/Jakarta'),
    phone: Joi.string()
      .required()
      .pattern(/^[0-9+\-\s()]+$/),
    latitude: Joi.number().optional().min(-90).max(90),
    longitude: Joi.number().optional().min(-180).max(180),
    copiedFromId: Joi.number().optional().integer().positive()
  })
};

const getOutlets = {
  query: Joi.object().keys({
    name: Joi.string().optional(),
    city: Joi.string().optional(),
    province: Joi.string().optional(),
    isActive: Joi.boolean().optional(),
    sortBy: Joi.string().optional(),
    sortType: Joi.string().valid('asc', 'desc').optional(),
    limit: Joi.number().integer().min(1).max(100).optional(),
    page: Joi.number().integer().min(1).optional()
  })
};

const getOutlet = {
  params: Joi.object().keys({
    outletId: Joi.number().integer().positive().required()
  })
};

const updateOutlet = {
  params: Joi.object().keys({
    outletId: Joi.number().integer().positive().required()
  }),
  body: Joi.object()
    .keys({
      name: Joi.string().optional().min(3).max(255),
      address: Joi.string().optional().min(10),
      province: Joi.string().optional().min(2).max(100),
      city: Joi.string().optional().min(2).max(100),
      provinceId: Joi.number().integer().positive().optional(),
      cityId: Joi.number().integer().positive().optional(),
      timezone: Joi.string().optional(),
      phone: Joi.string()
        .optional()
        .pattern(/^[0-9+\-\s()]+$/),
      latitude: Joi.number().optional().min(-90).max(90),
      longitude: Joi.number().optional().min(-180).max(180),
      isActive: Joi.boolean().optional()
    })
    .min(1)
};

const deleteOutlet = {
  params: Joi.object().keys({
    outletId: Joi.number().integer().positive().required()
  })
};

const copyServices = {
  params: Joi.object().keys({
    outletId: Joi.number().integer().positive().required()
  }),
  body: Joi.object().keys({
    sourceOutletId: Joi.number().integer().positive().required()
  })
};

export default {
  createOutlet,
  getOutlets,
  getOutlet,
  updateOutlet,
  deleteOutlet,
  copyServices
};
