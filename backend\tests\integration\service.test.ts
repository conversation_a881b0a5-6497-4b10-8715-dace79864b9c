import request from 'supertest';
import httpStatus from 'http-status';
import moment from 'moment';
import app from '../../src/app';
import config from '../../src/config/config';
import { tokenService } from '../../src/services';
import { setupTestDBOptimized } from '../utils/setupTestDb';
import { describe, beforeEach, test, expect, jest } from '@jest/globals';
import {
  userOne,
  admin,
  insertUsers,
  createOutletForOwner,
  createEmployeeWithOutlet
} from '../fixtures/user.fixture';
import {
  serviceOne,
  serviceTwo,
  insertServices,
  insertOutletServices
} from '../fixtures/service.fixture';
import { Role, TokenType } from '@prisma/client';
import prisma from '../../src/client';

// Reduce timeout for faster tests
jest.setTimeout(30000);

setupTestDBOptimized();

describe('Service routes', () => {
  let ownerAccessToken: string;
  let adminAccessToken: string;
  let outletId: number;
  let ownerUser: any;
  let adminUser: any;

  beforeEach(async () => {
    // Insert users with unique emails to avoid conflicts
    const uniqueUserOne = {
      ...userOne,
      email: `owner.${Math.floor(Math.random() * 100000)}@test.com`,
      phone: `081${Math.floor(Math.random() * 100000000)
        .toString()
        .padStart(8, '0')}`
    };

    const uniqueAdmin = {
      ...admin,
      email: `admin.${Math.floor(Math.random() * 100000)}@test.com`,
      phone: `082${Math.floor(Math.random() * 100000000)
        .toString()
        .padStart(8, '0')}`
    };

    await insertUsers([uniqueUserOne, uniqueAdmin]);

    // Get inserted users to get their actual IDs
    const dbOwner = await prisma.user.findUnique({ where: { email: uniqueUserOne.email } });
    const dbAdmin = await prisma.user.findUnique({ where: { email: uniqueAdmin.email } });

    // Create outlets
    const outlet = await createOutletForOwner(dbOwner!.id);
    outletId = outlet.id;

    // Update owner user with outletId
    await prisma.user.update({
      where: { id: dbOwner!.id },
      data: { outletId: outletId }
    });

    // Get updated user data
    ownerUser = await prisma.user.findUnique({ where: { id: dbOwner!.id } });
    adminUser = await prisma.user.findUnique({ where: { id: dbAdmin!.id } });

    // Generate tokens
    ownerAccessToken = tokenService.generateToken(
      ownerUser!.id,
      moment().add(config.jwt.accessExpirationMinutes, 'minutes'),
      TokenType.ACCESS
    );

    adminAccessToken = tokenService.generateToken(
      adminUser!.id,
      moment().add(config.jwt.accessExpirationMinutes, 'minutes'),
      TokenType.ACCESS
    );
  });

  describe('POST /v1/services', () => {
    let newService: any;

    beforeEach(() => {
      newService = {
        name: 'Cuci Express',
        description: 'Cuci kilat dalam 3 jam',
        price: 15000,
        unit: 'kg',
        estimationHours: 3,
        isActive: true,
        outletId: outletId
      };
    });

    test('should return 201 and successfully create new service if data is ok', async () => {
      const res = await request(app)
        .post('/v1/services')
        .set('Authorization', `Bearer ${ownerAccessToken}`)
        .send(newService)
        .expect(httpStatus.CREATED);

      expect(res.body).toEqual({
        id: expect.any(Number),
        name: newService.name,
        description: newService.description,
        price: newService.price,
        unit: newService.unit,
        estimationHours: newService.estimationHours,
        isActive: newService.isActive,
        outletId: newService.outletId,
        categoryId: null,
        createdAt: expect.any(String),
        updatedAt: expect.any(String)
      });
    });

    test('should return 401 error if access token is missing', async () => {
      await request(app).post('/v1/services').send(newService).expect(httpStatus.UNAUTHORIZED);
    });

    test('should return 400 error if name is missing', async () => {
      delete newService.name;

      await request(app)
        .post('/v1/services')
        .set('Authorization', `Bearer ${ownerAccessToken}`)
        .send(newService)
        .expect(httpStatus.BAD_REQUEST);
    });

    test('should return 400 error if outletId is missing', async () => {
      delete newService.outletId;

      await request(app)
        .post('/v1/services')
        .set('Authorization', `Bearer ${ownerAccessToken}`)
        .send(newService)
        .expect(httpStatus.BAD_REQUEST);
    });
  });

  describe('GET /v1/services', () => {
    test('should return 200 and apply the default query options', async () => {
      const services = await insertServices([serviceOne, serviceTwo], outletId);

      const res = await request(app)
        .get('/v1/services')
        .set('Authorization', `Bearer ${ownerAccessToken}`)
        .query({ outletId: outletId })
        .expect(httpStatus.OK);

      expect(res.body).toEqual({
        results: expect.any(Array),
        page: 1,
        limit: 10,
        totalPages: expect.any(Number),
        totalResults: expect.any(Number)
      });

      expect(res.body.results).toHaveLength(2);
      expect(res.body.results[0]).toEqual({
        id: expect.any(Number),
        name: expect.any(String),
        description: expect.any(String),
        price: expect.any(Number),
        unit: expect.any(String),
        estimationHours: expect.any(Number),
        isActive: expect.any(Boolean),
        outletId: expect.any(Number),
        categoryId: null,
        category: null,
        createdAt: expect.any(String),
        updatedAt: expect.any(String),
        outlet: expect.any(Object)
      });
    });

    test('should return 401 if access token is missing', async () => {
      await request(app).get('/v1/services').expect(httpStatus.UNAUTHORIZED);
    });

    test('should return 400 if outletId is missing', async () => {
      await request(app)
        .get('/v1/services')
        .set('Authorization', `Bearer ${ownerAccessToken}`)
        .expect(httpStatus.BAD_REQUEST);
    });
  });

  describe('GET /v1/services/:serviceId', () => {
    test('should return 200 and the service object if data is ok', async () => {
      const [service] = await insertServices([serviceOne], outletId);

      const res = await request(app)
        .get(`/v1/services/${service.id}`)
        .set('Authorization', `Bearer ${ownerAccessToken}`)
        .query({ outletId: outletId })
        .expect(httpStatus.OK);

      expect(res.body).toEqual({
        id: service.id,
        name: service.name,
        description: service.description,
        price: service.price,
        unit: service.unit,
        estimationHours: service.estimationHours,
        isActive: service.isActive,
        outletId: service.outletId,
        categoryId: null,
        category: null,
        createdAt: expect.any(String),
        updatedAt: expect.any(String),
        outlet: expect.any(Object)
      });
    });

    test('should return 401 error if access token is missing', async () => {
      await request(app).get('/v1/services/1').expect(httpStatus.UNAUTHORIZED);
    });

    test('should return 404 error if service is not found', async () => {
      await request(app)
        .get('/v1/services/999')
        .set('Authorization', `Bearer ${ownerAccessToken}`)
        .query({ outletId: outletId })
        .expect(httpStatus.NOT_FOUND);
    });
  });

  describe('PATCH /v1/services/:serviceId', () => {
    test('should return 200 and successfully update service if data is ok', async () => {
      const [service] = await insertServices([serviceOne], outletId);

      const updateBody = {
        name: 'Updated Service Name',
        price: 20000,
        outletId: outletId
      };

      const res = await request(app)
        .patch(`/v1/services/${service.id}`)
        .set('Authorization', `Bearer ${ownerAccessToken}`)
        .send(updateBody)
        .expect(httpStatus.OK);

      expect(res.body).toEqual({
        id: service.id,
        name: updateBody.name,
        description: service.description,
        price: updateBody.price,
        unit: service.unit,
        estimationHours: service.estimationHours,
        isActive: service.isActive,
        outletId: service.outletId,
        categoryId: null,
        createdAt: expect.any(String),
        updatedAt: expect.any(String)
      });
    });

    test('should return 401 error if access token is missing', async () => {
      await request(app)
        .patch('/v1/services/1')
        .send({ name: 'Updated Name' })
        .expect(httpStatus.UNAUTHORIZED);
    });

    test('should return 404 error if service is not found', async () => {
      const updateBody = {
        name: 'Updated Service Name',
        outletId: outletId
      };

      await request(app)
        .patch('/v1/services/999')
        .set('Authorization', `Bearer ${ownerAccessToken}`)
        .send(updateBody)
        .expect(httpStatus.NOT_FOUND);
    });
  });

  describe('DELETE /v1/services/:serviceId', () => {
    test('should return 204 if data is ok', async () => {
      const [service] = await insertServices([serviceOne], outletId);

      await request(app)
        .delete(`/v1/services/${service.id}`)
        .set('Authorization', `Bearer ${ownerAccessToken}`)
        .query({ outletId: outletId })
        .expect(httpStatus.NO_CONTENT);
    });

    test('should return 401 error if access token is missing', async () => {
      await request(app).delete('/v1/services/1').expect(httpStatus.UNAUTHORIZED);
    });

    test('should return 404 error if service already is not found', async () => {
      await request(app)
        .delete('/v1/services/999')
        .set('Authorization', `Bearer ${ownerAccessToken}`)
        .query({ outletId: outletId })
        .expect(httpStatus.NOT_FOUND);
    });
  });
});
