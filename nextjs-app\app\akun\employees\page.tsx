'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import {
  ArrowLeft,
  Plus,
  Search,
  MoreVertical,
  Edit,
  Trash2,
  Eye,
  Lock,
  Loader2,
} from 'lucide-react';
import { useRouter } from 'next/navigation';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

import { useEmployees, useDeleteEmployee } from '@/hooks/useEmployees';

export default function EmployeesPage() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [employeeToDelete, setEmployeeToDelete] = useState<number | null>(null);
  const [showResetPasswordDialog, setShowResetPasswordDialog] = useState(false);
  const [employeeToResetPassword, setEmployeeToResetPassword] = useState<
    number | null
  >(null);

  // Fetch employees data
  const {
    data: employeesData,
    isLoading,
    error,
  } = useEmployees({
    name: searchQuery || undefined,
    limit: 50, // Show more employees per page
  });

  const deleteEmployeeMutation = useDeleteEmployee();

  const handleDeleteEmployee = (id: number) => {
    setEmployeeToDelete(id);
    setShowDeleteDialog(true);
  };

  const confirmDelete = async () => {
    if (employeeToDelete) {
      await deleteEmployeeMutation.mutateAsync(employeeToDelete);
      setShowDeleteDialog(false);
      setEmployeeToDelete(null);
    }
  };

  const handleResetPassword = (id: number) => {
    setEmployeeToResetPassword(id);
    setShowResetPasswordDialog(true);
  };

  const confirmResetPassword = () => {
    // TODO: Implement reset password API
    alert(
      `Password for employee with ID ${employeeToResetPassword} has been reset`
    );
    setShowResetPasswordDialog(false);
    setEmployeeToResetPassword(null);
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="flex flex-col min-h-screen bg-slate-50">
        <header className="p-4 flex justify-between items-center bg-white sticky top-0 z-10 shadow-sm">
          <div className="flex items-center">
            <Link href="/akun" className="mr-3">
              <ArrowLeft className="h-5 w-5" />
            </Link>
            <h1 className="text-lg font-semibold">Kelola Pegawai</h1>
          </div>
          <Link href="/akun/employees/add">
            <Button size="sm" className="bg-blue-500 hover:bg-blue-600">
              <Plus className="h-4 w-4 mr-1" /> Tambah
            </Button>
          </Link>
        </header>
        <main className="flex-1 p-4 flex items-center justify-center">
          <div className="flex items-center gap-2">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span>Memuat data pegawai...</span>
          </div>
        </main>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="flex flex-col min-h-screen bg-slate-50">
        <header className="p-4 flex justify-between items-center bg-white sticky top-0 z-10 shadow-sm">
          <div className="flex items-center">
            <Link href="/akun" className="mr-3">
              <ArrowLeft className="h-5 w-5" />
            </Link>
            <h1 className="text-lg font-semibold">Kelola Pegawai</h1>
          </div>
          <Link href="/akun/employees/add">
            <Button size="sm" className="bg-blue-500 hover:bg-blue-600">
              <Plus className="h-4 w-4 mr-1" /> Tambah
            </Button>
          </Link>
        </header>
        <main className="flex-1 p-4 flex items-center justify-center">
          <div className="text-center">
            <h2 className="text-xl font-semibold mb-2">Terjadi Kesalahan</h2>
            <p className="text-gray-600 mb-4">
              Gagal memuat data pegawai. Silakan coba lagi.
            </p>
            <Button onClick={() => window.location.reload()}>Muat Ulang</Button>
          </div>
        </main>
      </div>
    );
  }

  const employees = employeesData?.results || [];

  return (
    <div className="flex flex-col min-h-screen bg-slate-50">
      <header className="p-4 flex justify-between items-center bg-white sticky top-0 z-10 shadow-sm">
        <div className="flex items-center">
          <Link href="/akun" className="mr-3">
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <h1 className="text-lg font-semibold">Kelola Pegawai</h1>
        </div>
        <Link href="/akun/employees/add">
          <Button size="sm" className="bg-blue-500 hover:bg-blue-600">
            <Plus className="h-4 w-4 mr-1" /> Tambah
          </Button>
        </Link>
      </header>

      <div className="p-4">
        <div className="relative mb-4">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <Input
            placeholder="Cari pegawai..."
            className="pl-10"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        <div className="space-y-4 mb-20">
          {employees.length > 0 ? (
            employees.map((employee) => (
              <Card key={employee.id} className="p-4">
                <div className="flex justify-between items-start">
                  <div className="flex items-start gap-3">
                    <Avatar className="h-12 w-12">
                      <AvatarImage src="/placeholder.svg" alt={employee.name} />
                      <AvatarFallback>
                        {employee.name.substring(0, 2).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <h3 className="font-medium">{employee.name}</h3>
                      <div className="flex items-center gap-2">
                        <Badge variant="secondary">{employee.role}</Badge>
                        <Badge
                          variant="outline"
                          className={
                            employee.isActive
                              ? 'border-green-500 text-green-500'
                              : 'border-red-500 text-red-500'
                          }
                        >
                          {employee.isActive ? 'Aktif' : 'Tidak Aktif'}
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-500 mt-1">
                        {employee.employeeAt?.name || 'Outlet tidak ditemukan'}
                      </p>
                    </div>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem
                        onClick={() =>
                          router.push(`/akun/employees/${employee.id}`)
                        }
                      >
                        <Eye className="h-4 w-4 mr-2" /> Lihat Detail
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() =>
                          router.push(`/akun/employees/edit/${employee.id}`)
                        }
                      >
                        <Edit className="h-4 w-4 mr-2" /> Edit
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => handleResetPassword(employee.id)}
                      >
                        <Lock className="h-4 w-4 mr-2" /> Reset Password
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        className="text-red-500"
                        onClick={() => handleDeleteEmployee(employee.id)}
                      >
                        <Trash2 className="h-4 w-4 mr-2" /> Hapus
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
                <div className="mt-3 grid grid-cols-2 gap-2 text-sm">
                  <div>
                    <p className="text-gray-500">Email</p>
                    <p>{employee.email}</p>
                  </div>
                  <div>
                    <p className="text-gray-500">Telepon</p>
                    <p>{employee.phone}</p>
                  </div>
                  <div>
                    <p className="text-gray-500">Tanggal Bergabung</p>
                    <p>
                      {new Date(employee.createdAt).toLocaleDateString('id-ID')}
                    </p>
                  </div>
                </div>
              </Card>
            ))
          ) : (
            <div className="text-center py-8 text-gray-500">
              {searchQuery
                ? 'Tidak ada pegawai yang sesuai dengan pencarian'
                : 'Belum ada pegawai'}
            </div>
          )}
        </div>
      </div>

      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Hapus Pegawai</DialogTitle>
            <DialogDescription>
              Apakah Anda yakin ingin menghapus pegawai ini? Tindakan ini tidak
              dapat dibatalkan.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowDeleteDialog(false)}
            >
              Batal
            </Button>
            <Button
              variant="destructive"
              onClick={confirmDelete}
              disabled={deleteEmployeeMutation.isPending}
            >
              {deleteEmployeeMutation.isPending ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Menghapus...
                </>
              ) : (
                'Hapus'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Reset Password Dialog */}
      <Dialog
        open={showResetPasswordDialog}
        onOpenChange={setShowResetPasswordDialog}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Reset Password</DialogTitle>
            <DialogDescription>
              Apakah Anda yakin ingin mereset password pegawai ini? Password
              baru akan dikirimkan ke email pegawai.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowResetPasswordDialog(false)}
            >
              Batal
            </Button>
            <Button onClick={confirmResetPassword}>Reset Password</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
