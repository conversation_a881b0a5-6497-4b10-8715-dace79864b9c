"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { ArrowLeft, Edit, Trash2, Plus, AlertCircle, Truck, FileText, Minus } from "lucide-react"
import { useRouter } from "next/navigation"

import { Button } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

// Mock data for inventory items
const mockInventory = [
  {
    id: 1,
    name: "Deterjen Cair",
    category: "supplies",
    stock: 25,
    unit: "Liter",
    minStock: 10,
    price: 25000,
    supplier: "PT Supplier Bersih",
    lastRestock: "15/03/2025",
    notes: "Deterjen khusus untuk mesin cuci front loading",
    history: [
      { date: "15/03/2025", type: "restock", amount: 10, notes: "Pembelian rutin" },
      { date: "01/03/2025", type: "usage", amount: -3, notes: "Penggunaan harian" },
      { date: "15/02/2025", type: "restock", amount: 15, notes: "Pembelian rutin" },
    ],
  },
  {
    id: 2,
    name: "Pelembut Pakaian",
    category: "supplies",
    stock: 15,
    unit: "Liter",
    minStock: 8,
    price: 30000,
    supplier: "PT Supplier Bersih",
    lastRestock: "15/03/2025",
    notes: "",
    history: [
      { date: "15/03/2025", type: "restock", amount: 5, notes: "Pembelian rutin" },
      { date: "01/03/2025", type: "usage", amount: -2, notes: "Penggunaan harian" },
      { date: "15/02/2025", type: "restock", amount: 10, notes: "Pembelian rutin" },
    ],
  },
  {
    id: 3,
    name: "Pemutih",
    category: "supplies",
    stock: 5,
    unit: "Liter",
    minStock: 5,
    price: 20000,
    supplier: "PT Supplier Bersih",
    lastRestock: "10/03/2025",
    notes: "Hanya untuk pakaian putih",
    history: [
      { date: "10/03/2025", type: "restock", amount: 5, notes: "Pembelian rutin" },
      { date: "01/03/2025", type: "usage", amount: -3, notes: "Penggunaan harian" },
      { date: "15/02/2025", type: "restock", amount: 8, notes: "Pembelian rutin" },
    ],
  },
  {
    id: 4,
    name: "Plastik Packaging Kecil",
    category: "packaging",
    stock: 200,
    unit: "Pcs",
    minStock: 100,
    price: 500,
    supplier: "PT Plastik Jaya",
    lastRestock: "20/03/2025",
    notes: "",
    history: [
      { date: "20/03/2025", type: "restock", amount: 100, notes: "Pembelian rutin" },
      { date: "10/03/2025", type: "usage", amount: -50, notes: "Penggunaan harian" },
      { date: "01/03/2025", type: "restock", amount: 150, notes: "Pembelian rutin" },
    ],
  },
  {
    id: 5,
    name: "Plastik Packaging Besar",
    category: "packaging",
    stock: 150,
    unit: "Pcs",
    minStock: 100,
    price: 1000,
    supplier: "PT Plastik Jaya",
    lastRestock: "20/03/2025",
    notes: "",
    history: [
      { date: "20/03/2025", type: "restock", amount: 50, notes: "Pembelian rutin" },
      { date: "10/03/2025", type: "usage", amount: -30, notes: "Penggunaan harian" },
      { date: "01/03/2025", type: "restock", amount: 100, notes: "Pembelian rutin" },
    ],
  },
  {
    id: 6,
    name: "Hanger",
    category: "equipment",
    stock: 80,
    unit: "Pcs",
    minStock: 50,
    price: 2500,
    supplier: "PT Peralatan Laundry",
    lastRestock: "05/03/2025",
    notes: "",
    history: [
      { date: "05/03/2025", type: "restock", amount: 30, notes: "Pembelian rutin" },
      { date: "20/02/2025", type: "usage", amount: -10, notes: "Penggunaan harian" },
      { date: "10/02/2025", type: "restock", amount: 50, notes: "Pembelian rutin" },
    ],
  },
  {
    id: 7,
    name: "Setrika",
    category: "equipment",
    stock: 10,
    unit: "Pcs",
    minStock: 5,
    price: 150000,
    supplier: "PT Peralatan Laundry",
    lastRestock: "01/03/2025",
    notes: "Setrika uap premium",
    history: [
      { date: "01/03/2025", type: "restock", amount: 3, notes: "Pembelian tambahan" },
      { date: "15/01/2025", type: "restock", amount: 7, notes: "Pembelian awal" },
    ],
  },
  {
    id: 8,
    name: "Kertas Nota",
    category: "stationery",
    stock: 3,
    unit: "Pack",
    minStock: 5,
    price: 35000,
    supplier: "PT ATK Sejahtera",
    lastRestock: "25/02/2025",
    notes: "Segera restock",
    history: [
      { date: "25/02/2025", type: "restock", amount: 5, notes: "Pembelian rutin" },
      { date: "20/02/2025", type: "usage", amount: -1, notes: "Penggunaan harian" },
      { date: "10/02/2025", type: "usage", amount: -1, notes: "Penggunaan harian" },
    ],
  },
]

export default function InventoryDetailPage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const id = Number.parseInt(params.id)

  const [item, setItem] = useState<any>(null)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [showRestockDialog, setShowRestockDialog] = useState(false)
  const [restockAmount, setRestockAmount] = useState(1)

  useEffect(() => {
    // Find the inventory item by ID
    const foundItem = mockInventory.find((item) => item.id === id)

    if (foundItem) {
      setItem(foundItem)
    } else {
      // If item not found, redirect to inventory list
      router.push("/akun/inventory")
    }
  }, [id, router])

  const confirmDelete = () => {
    // In a real app, you would delete the item from your backend
    alert(`Item dengan ID ${id} telah dihapus`)
    setShowDeleteDialog(false)
    router.push("/akun/inventory")
  }

  const confirmRestock = () => {
    // In a real app, you would update the item stock in your backend
    alert(`Menambahkan ${restockAmount} ${item.unit} ke stok ${item.name}`)
    setShowRestockDialog(false)
    // Update local state for demo purposes
    setItem({
      ...item,
      stock: item.stock + restockAmount,
      lastRestock: new Date()
        .toLocaleDateString("id-ID", { day: "2-digit", month: "2-digit", year: "numeric" })
        .replace(/\//g, "/"),
      history: [
        {
          date: new Date()
            .toLocaleDateString("id-ID", { day: "2-digit", month: "2-digit", year: "numeric" })
            .replace(/\//g, "/"),
          type: "restock",
          amount: restockAmount,
          notes: "Penambahan stok manual",
        },
        ...item.history,
      ],
    })
  }

  const getStockStatus = (item: any) => {
    const percentage = (item.stock / item.minStock) * 100
    if (item.stock <= item.minStock * 0.5) return "low"
    if (item.stock <= item.minStock) return "warning"
    return "good"
  }

  const getCategoryLabel = (category: string) => {
    switch (category) {
      case "supplies":
        return "Supplies"
      case "packaging":
        return "Packaging"
      case "equipment":
        return "Equipment"
      case "stationery":
        return "Stationery"
      default:
        return "Lainnya"
    }
  }

  if (!item) {
    return <div className="p-4 text-center">Loading...</div>
  }

  return (
    <div className="flex flex-col min-h-screen bg-slate-50">
      <header className="p-4 flex justify-between items-center bg-white sticky top-0 z-10 shadow-sm">
        <div className="flex items-center">
          <Link href="/akun/inventory" className="mr-3">
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <h1 className="text-lg font-semibold">Detail Item Inventaris</h1>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowRestockDialog(true)}
            className="flex items-center gap-1"
          >
            <Plus className="h-4 w-4" /> Tambah Stok
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.push(`/akun/inventory/edit/${id}`)}
            className="flex items-center gap-1"
          >
            <Edit className="h-4 w-4" /> Edit
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="text-red-500 border-red-200 hover:bg-red-50"
            onClick={() => setShowDeleteDialog(true)}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </header>

      <main className="flex-1 p-4 pb-20">
        <Card className="p-4 mb-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <h2 className="text-xl font-semibold">{item.name}</h2>
                <Badge variant="outline">{getCategoryLabel(item.category)}</Badge>
                {item.stock <= item.minStock && (
                  <Badge variant="destructive" className="flex items-center gap-1">
                    <AlertCircle className="h-3 w-3" /> Stok Rendah
                  </Badge>
                )}
              </div>

              <div className="grid grid-cols-2 gap-4 mb-4">
                <div>
                  <p className="text-sm text-gray-500">Stok Saat Ini</p>
                  <p className="font-semibold text-lg">
                    {item.stock} {item.unit}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Stok Minimum</p>
                  <p className="font-semibold text-lg">
                    {item.minStock} {item.unit}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Harga</p>
                  <p className="font-semibold text-lg">Rp {item.price.toLocaleString()}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Terakhir Restock</p>
                  <p className="font-semibold text-lg">{item.lastRestock}</p>
                </div>
              </div>

              <div className="mb-4">
                <div className="flex justify-between text-xs mb-1">
                  <span>Status Stok</span>
                  <span
                    className={
                      getStockStatus(item) === "low"
                        ? "text-red-500"
                        : getStockStatus(item) === "warning"
                          ? "text-amber-500"
                          : "text-green-500"
                    }
                  >
                    {getStockStatus(item) === "low"
                      ? "Sangat Rendah"
                      : getStockStatus(item) === "warning"
                        ? "Perlu Restock"
                        : "Baik"}
                  </span>
                </div>
                <Progress
                  value={(item.stock / (item.minStock * 2)) * 100}
                  className={
                    getStockStatus(item) === "low"
                      ? "bg-red-100"
                      : getStockStatus(item) === "warning"
                        ? "bg-amber-100"
                        : "bg-green-100"
                  }
                  indicatorClassName={
                    getStockStatus(item) === "low"
                      ? "bg-red-500"
                      : getStockStatus(item) === "warning"
                        ? "bg-amber-500"
                        : "bg-green-500"
                  }
                />
              </div>

              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Truck className="h-4 w-4 text-gray-500" />
                  <p className="text-gray-700">
                    Supplier: <span className="font-medium">{item.supplier}</span>
                  </p>
                </div>
                {item.notes && (
                  <div className="flex items-start gap-2">
                    <FileText className="h-4 w-4 text-gray-500 mt-0.5" />
                    <p className="text-gray-700">
                      Catatan: <span className="font-medium">{item.notes}</span>
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </Card>

        <h3 className="text-lg font-medium mb-3">Riwayat Stok</h3>
        <Card className="p-4 mb-4">
          <div className="space-y-4">
            {item.history.map((record: any, index: number) => (
              <div key={index} className="flex items-start gap-3">
                <div
                  className={`p-2 rounded-full ${
                    record.type === "restock" ? "bg-green-100 text-green-600" : "bg-amber-100 text-amber-600"
                  }`}
                >
                  {record.type === "restock" ? <Plus className="h-4 w-4" /> : <Minus className="h-4 w-4" />}
                </div>
                <div className="flex-1">
                  <div className="flex justify-between">
                    <div>
                      <p className="font-medium">{record.type === "restock" ? "Penambahan Stok" : "Penggunaan"}</p>
                      <p className="text-sm text-gray-500">{record.notes}</p>
                    </div>
                    <div className="text-right">
                      <p className={`font-medium ${record.type === "restock" ? "text-green-600" : "text-amber-600"}`}>
                        {record.type === "restock" ? "+" : ""}
                        {record.amount} {item.unit}
                      </p>
                      <p className="text-sm text-gray-500">{record.date}</p>
                    </div>
                  </div>
                </div>
              </div>
            ))}
            {item.history.length === 0 && <p className="text-center text-gray-500 py-4">Belum ada riwayat stok</p>}
          </div>
        </Card>
      </main>

      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Hapus Item</DialogTitle>
            <DialogDescription>
              Apakah Anda yakin ingin menghapus item ini? Tindakan ini tidak dapat dibatalkan.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteDialog(false)}>
              Batal
            </Button>
            <Button variant="destructive" onClick={confirmDelete}>
              Hapus
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Restock Dialog */}
      <Dialog open={showRestockDialog} onOpenChange={setShowRestockDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Tambah Stok</DialogTitle>
            <DialogDescription>Masukkan jumlah yang ingin ditambahkan ke stok saat ini.</DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <Label htmlFor="restock-amount">Jumlah</Label>
            <div className="flex items-center mt-2">
              <Button variant="outline" size="icon" onClick={() => setRestockAmount(Math.max(1, restockAmount - 1))}>
                -
              </Button>
              <Input
                id="restock-amount"
                type="number"
                min="1"
                value={restockAmount}
                onChange={(e) => setRestockAmount(Number.parseInt(e.target.value) || 1)}
                className="mx-2 text-center"
              />
              <Button variant="outline" size="icon" onClick={() => setRestockAmount(restockAmount + 1)}>
                +
              </Button>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowRestockDialog(false)}>
              Batal
            </Button>
            <Button onClick={confirmRestock}>Tambah Stok</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
