"use client"

import { useState } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { ArrowLeft, Calendar, Clock, MapPin, Edit, Save, X } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { toast } from "@/components/ui/use-toast"

// Mock data for attendance detail
const mockAttendanceDetail = {
  id: 1,
  date: "2023-10-25",
  checkIn: "08:05:23",
  checkOut: "17:15:45",
  status: "present",
  location: "Cabang Utama",
  notes: "",
  workHours: "9:10:22",
}

export default function AttendanceDetailPage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const [isEditing, setIsEditing] = useState(false)
  const [notes, setNotes] = useState(mockAttendanceDetail.notes)

  // Format date as DD/MM/YYYY
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("id-ID", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    })
  }

  // Get day name
  const getDayName = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("id-ID", { weekday: "long" })
  }

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "present":
        return <Badge className="bg-green-500">Hadir</Badge>
      case "late":
        return <Badge className="bg-yellow-500">Terlambat</Badge>
      case "absent":
        return <Badge className="bg-red-500">Tidak Hadir</Badge>
      default:
        return <Badge className="bg-gray-500">Tidak Diketahui</Badge>
    }
  }

  // Handle save notes
  const handleSaveNotes = () => {
    // In a real app, you would save the notes to your backend
    toast({
      title: "Catatan disimpan",
      description: "Catatan absensi telah berhasil disimpan.",
    })
    setIsEditing(false)
  }

  // Handle cancel edit
  const handleCancelEdit = () => {
    setNotes(mockAttendanceDetail.notes)
    setIsEditing(false)
  }

  return (
    <div className="flex flex-col min-h-screen bg-slate-50">
      <header className="p-4 flex justify-between items-center bg-white sticky top-0 z-10 shadow-sm">
        <div className="flex items-center">
          <Link href="/absensi/history" className="mr-3">
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <h1 className="text-lg font-semibold">Detail Absensi</h1>
        </div>
        {!isEditing ? (
          <Button variant="ghost" size="sm" onClick={() => setIsEditing(true)}>
            <Edit className="h-4 w-4 mr-1" /> Edit
          </Button>
        ) : (
          <div className="flex gap-2">
            <Button variant="ghost" size="sm" onClick={handleCancelEdit}>
              <X className="h-4 w-4 mr-1" /> Batal
            </Button>
            <Button size="sm" onClick={handleSaveNotes}>
              <Save className="h-4 w-4 mr-1" /> Simpan
            </Button>
          </div>
        )}
      </header>

      <main className="flex-1 p-4 pb-20">
        <Card className="mb-6">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-lg">{formatDate(mockAttendanceDetail.date)}</CardTitle>
                <p className="text-sm text-gray-500">{getDayName(mockAttendanceDetail.date)}</p>
              </div>
              {getStatusBadge(mockAttendanceDetail.status)}
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div className="bg-gray-50 p-3 rounded-lg">
                <div className="flex items-center text-gray-600 mb-1">
                  <Clock className="h-4 w-4 mr-1" />
                  <p className="text-sm">Check-In</p>
                </div>
                <p className="font-semibold">{mockAttendanceDetail.checkIn || "-"}</p>
              </div>
              <div className="bg-gray-50 p-3 rounded-lg">
                <div className="flex items-center text-gray-600 mb-1">
                  <Clock className="h-4 w-4 mr-1" />
                  <p className="text-sm">Check-Out</p>
                </div>
                <p className="font-semibold">{mockAttendanceDetail.checkOut || "-"}</p>
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <div className="flex items-center text-gray-600 mb-1">
                  <MapPin className="h-4 w-4 mr-1" />
                  <p className="text-sm">Lokasi</p>
                </div>
                <p>{mockAttendanceDetail.location || "-"}</p>
              </div>

              <div>
                <div className="flex items-center text-gray-600 mb-1">
                  <Clock className="h-4 w-4 mr-1" />
                  <p className="text-sm">Jam Kerja</p>
                </div>
                <p>{mockAttendanceDetail.workHours || "-"}</p>
              </div>

              <div>
                <div className="flex items-center text-gray-600 mb-1">
                  <Calendar className="h-4 w-4 mr-1" />
                  <p className="text-sm">Catatan</p>
                </div>
                {isEditing ? (
                  <Textarea
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                    placeholder="Tambahkan catatan..."
                    rows={3}
                  />
                ) : (
                  <p>{notes || "Tidak ada catatan"}</p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </main>
    </div>
  )
}
