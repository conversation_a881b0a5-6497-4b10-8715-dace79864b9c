# Payment Method Field Cleanup - Dokumentasi

## Overview

Telah dilakukan cleanup untuk menghilangkan duplikasi dan inkonsistensi antara field `paymentMethod` di level order dengan `payment.method` di object payment.

## Masalah Sebelumnya

### Duplikasi Field

```json
// SEBELUM - Ada duplikasi dan potensi inkonsistensi
{
  "customerId": 1,
  "items": [...],
  "paymentMethod": "CASH",  // ❌ Redundant
  "payment": {
    "amount": 10000,
    "method": "CASH"        // ✅ Source of truth
  }
}
```

### Potensi Inkonsistensi

- `paymentMethod` bisa berbeda dengan `payment.method`
- Tidak jelas mana yang menjadi source of truth
- Validasi ganda yang tidak perlu
- Confusion untuk developer

## Solusi yang Diimplementasikan

### 1. Hapus Field Redundant

- ❌ Removed: `paymentMethod` dari `createOrder` validation
- ❌ Removed: `paymentMethod` dari `updateOrder` validation
- ✅ Keep: `payment.method` sebagai single source of truth

### 2. Update Service Logic

```typescript
// SEBELUM
paymentMethod: payment?.method || orderData.paymentMethod;

// SESUDAH
paymentMethod: payment?.method; // Hanya dari payment object
```

### 3. Konsistensi API Design

```json
// SEKARANG - Konsisten dan jelas
{
  "customerId": 1,
  "items": [...],
  "payment": {              // ✅ Payment info terpusat
    "amount": 10000,
    "method": "CASH",
    "cashboxId": 1,
    "reference": "REF-001"
  }
}
```

## Impact pada Database

### Order Table

- Field `paymentMethod` di database **tetap ada** untuk backward compatibility
- Nilai diisi dari `payment.method` saat create order dengan payment
- Nilai `null` jika order dibuat tanpa payment

### Payment Table

- Field `method` tetap menjadi source of truth untuk payment records
- Setiap payment record memiliki method yang spesifik

## API Changes

### Create Order

```json
// ✅ VALID - Order dengan payment
{
  "customerId": 1,
  "items": [...],
  "payment": {
    "amount": 10000,
    "method": "CASH",
    "cashboxId": 1
  }
}

// ✅ VALID - Order tanpa payment
{
  "customerId": 1,
  "items": [...]
}

// ❌ INVALID - paymentMethod tidak lagi diterima
{
  "customerId": 1,
  "items": [...],
  "paymentMethod": "CASH"  // Field ini sudah dihapus
}
```

### Update Order

```json
// ✅ VALID - Update order info
{
  "notes": "Updated notes",
  "estimatedFinish": "2024-01-01T10:00:00Z"
}

// ❌ INVALID - paymentMethod tidak bisa diupdate langsung
{
  "paymentMethod": "TRANSFER"  // Field ini sudah dihapus
}
```

## Migration Guide

### Untuk Frontend/Client

1. **Remove** `paymentMethod` dari create order request
2. **Use** `payment.method` untuk specify payment method
3. **Update** form validation untuk menggunakan payment object

### Untuk Testing

1. **Update** test cases yang menggunakan `paymentMethod`
2. **Use** payment object dalam test data
3. **Verify** response structure masih konsisten

## Benefits

### 1. Consistency

- Single source of truth untuk payment method
- Tidak ada duplikasi data
- Clear API contract

### 2. Maintainability

- Lebih mudah maintain karena tidak ada duplikasi
- Validasi terpusat di payment object
- Reduced complexity

### 3. Extensibility

- Payment object bisa diperluas dengan field lain
- Flexible untuk future payment features
- Better separation of concerns

## Backward Compatibility

### Database Level

- ✅ Field `paymentMethod` di Order table tetap ada
- ✅ Existing orders tidak terpengaruh
- ✅ Queries existing tetap berfungsi

### API Level

- ❌ Request dengan `paymentMethod` akan ditolak (validation error)
- ✅ Response format tetap sama
- ✅ Existing payment endpoints tidak berubah

## Examples

### Create Order dengan Cash Payment

```json
POST /v1/outlets/1/orders
{
  "customerId": 1,
  "items": [
    {
      "serviceId": 1,
      "quantity": 2,
      "unit": "kg",
      "price": 5000,
      "subtotal": 10000
    }
  ],
  "payment": {
    "amount": 10000,
    "method": "CASH",
    "cashboxId": 1,
    "reference": "CASH-001"
  }
}
```

### Create Order dengan Deposit Payment

```json
POST /v1/outlets/1/orders
{
  "customerId": 1,
  "items": [
    {
      "serviceId": 1,
      "quantity": 1.5,
      "unit": "kg",
      "price": 5000,
      "subtotal": 7500
    }
  ],
  "payment": {
    "amount": 7500,
    "method": "DEPOSIT",
    "reference": "DEPOSIT-PAY-001"
  }
}
```

### Create Order tanpa Payment

```json
POST /v1/outlets/1/orders
{
  "customerId": 1,
  "items": [
    {
      "serviceId": 1,
      "quantity": 3,
      "unit": "kg",
      "price": 5000,
      "subtotal": 15000
    }
  ],
  "notes": "Order tanpa pembayaran langsung"
}
```

## Validation Rules

### Payment Object (Optional)

- `amount`: Required, positive number
- `method`: Required, valid payment method enum
- `cashboxId`: Optional, required untuk non-DEPOSIT methods
- `reference`: Optional, max 100 characters
- `notes`: Optional, max 500 characters

### Business Rules

- Jika ada payment, amount tidak boleh melebihi total order
- Untuk DEPOSIT method, saldo customer harus mencukupi
- Untuk non-DEPOSIT method, cashboxId harus valid dan aktif

## Status

✅ **IMPLEMENTED & TESTED**

Cleanup telah selesai dan sistem sekarang memiliki API yang lebih konsisten dan maintainable untuk payment handling.
