import Joi from 'joi';

const createPromotion = {
  body: Joi.object().keys({
    name: Joi.string().required().max(200),
    code: Joi.string().required().max(50).uppercase(),
    description: Joi.string().optional().max(500),
    discountType: Joi.string().valid('PERCENTAGE', 'FIXED').required(),
    discountValue: Joi.number().positive().required(),
    minOrderValue: Joi.number().min(0).optional().default(0),
    maxDiscountAmount: Joi.number().positive().optional().allow(null),
    isActive: Joi.boolean().optional().default(true),
    validFrom: Joi.date().iso().required(),
    validUntil: Joi.date().iso().greater(Joi.ref('validFrom')).required(),
    usageLimit: Joi.number().integer().positive().optional().allow(null),
    isFirstTimeOnly: Joi.boolean().optional().default(false),
    applicableServices: Joi.array().items(Joi.string()).optional().default([]),
    outletId: Joi.number().integer().positive().required()
  })
};

const getPromotions = {
  query: Joi.object().keys({
    search: Joi.string().optional().allow(''),
    name: Joi.string().optional(),
    code: Joi.string().optional(),
    discountType: Joi.string().valid('PERCENTAGE', 'FIXED').optional(),
    isActive: Joi.boolean().optional(),
    isExpired: Joi.boolean().optional(),
    sortBy: Joi.string().optional(),
    limit: Joi.number().integer().min(1).max(100).optional(),
    page: Joi.number().integer().min(1).optional(),
    outletId: Joi.number().integer().positive().optional()
  })
};

const getPromotion = {
  params: Joi.object().keys({
    promotionId: Joi.number().integer().required()
  })
};

const updatePromotion = {
  params: Joi.object().keys({
    promotionId: Joi.number().integer().required()
  }),
  body: Joi.object()
    .keys({
      name: Joi.string().max(200),
      code: Joi.string().max(50).uppercase(),
      description: Joi.string().max(500).allow(null, ''),
      discountType: Joi.string().valid('PERCENTAGE', 'FIXED'),
      discountValue: Joi.number().positive(),
      minOrderValue: Joi.number().min(0),
      maxDiscountAmount: Joi.number().positive().allow(null),
      isActive: Joi.boolean(),
      validFrom: Joi.date().iso(),
      validUntil: Joi.date().iso(),
      usageLimit: Joi.number().integer().positive().allow(null),
      isFirstTimeOnly: Joi.boolean(),
      applicableServices: Joi.array().items(Joi.string()),
      outletId: Joi.number().integer().positive().required()
    })
    .min(1) // At least one field must be provided
};

const deletePromotion = {
  params: Joi.object().keys({
    promotionId: Joi.number().integer().required()
  }),
  body: Joi.object().keys({
    outletId: Joi.number().integer().positive().required()
  })
};

const validatePromotion = {
  body: Joi.object().keys({
    code: Joi.string().required(),
    orderTotal: Joi.number().positive().required(),
    customerId: Joi.number().integer().positive().required(),
    outletId: Joi.number().integer().positive().required()
  })
};

export default {
  createPromotion,
  getPromotions,
  getPromotion,
  updatePromotion,
  deletePromotion,
  validatePromotion
};
