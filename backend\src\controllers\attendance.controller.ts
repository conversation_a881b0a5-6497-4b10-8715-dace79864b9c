import httpStatus from 'http-status';
import catchAsync from '../utils/catchAsync';
import { attendanceService } from '../services';
import ApiError from '../utils/ApiError';
import pick from '../utils/pick';
import { AttendanceStatus } from '@prisma/client';
import prisma from '../client';

/**
 * Check in user for attendance
 */
const checkIn = catchAsync(async (req: any, res) => {
  const { user } = req;

  if (!user) {
    throw new ApiError(httpStatus.UNAUTHORIZED, 'User not authenticated');
  }

  const { outletId, location, pin, notes } = req.body;
  let photo = undefined;

  // Handle photo upload if provided
  if (req.file) {
    photo = req.file.path || req.file.filename;
  }

  const checkInData = {
    userId: user.id,
    outletId: parseInt(outletId),
    location,
    pin,
    photo,
    notes
  };

  const attendance = await attendanceService.checkIn(checkInData);
  res.status(httpStatus.CREATED).json(attendance);
});

/**
 * Check out user for attendance
 */
const checkOut = catchAsync(async (req: any, res) => {
  const { user } = req;

  if (!user) {
    throw new ApiError(httpStatus.UNAUTHORIZED, 'User not authenticated');
  }

  const { outletId, notes } = req.body;
  let photo = undefined;

  // Handle photo upload if provided
  if (req.file) {
    photo = req.file.path || req.file.filename;
  }

  const checkOutData = {
    userId: user.id,
    outletId: parseInt(outletId),
    photo,
    notes
  };

  const attendance = await attendanceService.checkOut(checkOutData);
  res.status(httpStatus.OK).json(attendance);
});

/**
 * Get today's attendance status for user
 */
const getTodayStatus = catchAsync(async (req: any, res) => {
  const { user } = req;

  if (!user) {
    throw new ApiError(httpStatus.UNAUTHORIZED, 'User not authenticated');
  }

  const { outletId } = req.query;

  if (!outletId) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'outletId is required');
  }

  const attendance = await attendanceService.getTodayStatus(user.id, parseInt(outletId as string));

  if (!attendance) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Belum ada absensi hari ini');
  }

  res.status(httpStatus.OK).json(attendance);
});

/**
 * Get attendance history for user
 */
const getAttendanceHistory = catchAsync(async (req: any, res) => {
  const { user } = req;

  if (!user) {
    throw new ApiError(httpStatus.UNAUTHORIZED, 'User not authenticated');
  }

  const { outletId } = req.query;

  if (!outletId) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'outletId is required');
  }

  const filter = pick(req.query, ['startDate', 'endDate', 'status']);
  const options = pick(req.query, ['sortBy', 'limit', 'page']);

  // Parse dates if provided
  if (filter.startDate) {
    filter.startDate = new Date(filter.startDate as string);
  }
  if (filter.endDate) {
    filter.endDate = new Date(filter.endDate as string);
  }

  // Parse status if provided
  if (filter.status && typeof filter.status === 'string') {
    filter.status = filter.status as AttendanceStatus;
  }

  // Calculate offset and limit
  const page = parseInt(options.page as string) || 1;
  const limit = parseInt(options.limit as string) || 30;
  const offset = (page - 1) * limit;

  const attendances = await attendanceService.getAttendanceHistory(
    user.id,
    parseInt(outletId as string),
    {
      ...filter,
      limit,
      offset
    }
  );

  res.status(httpStatus.OK).json({
    results: attendances,
    page,
    limit,
    totalResults: attendances.length
  });
});

/**
 * Get team attendance for today (owner/manager only)
 */
const getTeamAttendance = catchAsync(async (req: any, res) => {
  const { user } = req;

  if (!user) {
    throw new ApiError(httpStatus.UNAUTHORIZED, 'User not authenticated');
  }

  const { outletId, date } = req.query;

  if (!outletId) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'outletId is required');
  }

  // Check if user has access to this outlet (owner or manager)
  // This would typically be checked in middleware, but for now we'll do basic check
  const outlet = await prisma.outlet.findUnique({
    where: { id: parseInt(outletId as string) },
    include: { owner: true, employees: true }
  });

  if (!outlet) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Outlet not found');
  }

  // Check if user is owner or employee of this outlet
  const hasAccess =
    outlet.ownerId === user.id || outlet.employees.some((emp) => emp.id === user.id);

  if (!hasAccess) {
    throw new ApiError(httpStatus.FORBIDDEN, 'Access denied to this outlet');
  }

  let targetDate = new Date();
  if (date) {
    targetDate = new Date(date as string);
  }

  const attendances = await attendanceService.getTeamAttendance(
    parseInt(outletId as string),
    targetDate
  );

  res.status(httpStatus.OK).json(attendances);
});

/**
 * Create work schedule for user
 */
const createWorkSchedule = catchAsync(async (req: any, res) => {
  const {
    userId,
    outletId,
    name,
    dayOfWeek,
    startTime,
    endTime,
    breakStartTime,
    breakEndTime,
    effectiveFrom,
    effectiveTo
  } = req.body;

  const schedule = await prisma.workSchedule.create({
    data: {
      userId: parseInt(userId),
      outletId: parseInt(outletId),
      name,
      dayOfWeek: parseInt(dayOfWeek),
      startTime,
      endTime,
      breakStartTime,
      breakEndTime,
      effectiveFrom: new Date(effectiveFrom),
      effectiveTo: effectiveTo ? new Date(effectiveTo) : null
    },
    include: {
      user: {
        select: {
          id: true,
          name: true,
          email: true
        }
      }
    }
  });

  res.status(httpStatus.CREATED).json(schedule);
});

/**
 * Get work schedules for outlet
 */
const getWorkSchedules = catchAsync(async (req: any, res) => {
  const { outletId, userId } = req.query;

  if (!outletId) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'outletId is required');
  }

  const where: any = {
    outletId: parseInt(outletId as string),
    isActive: true
  };

  if (userId) {
    where.userId = parseInt(userId as string);
  }

  const schedules = await prisma.workSchedule.findMany({
    where,
    include: {
      user: {
        select: {
          id: true,
          name: true,
          email: true
        }
      }
    },
    orderBy: [{ userId: 'asc' }, { dayOfWeek: 'asc' }, { startTime: 'asc' }]
  });

  res.status(httpStatus.OK).json(schedules);
});

/**
 * Update attendance settings for outlet
 */
const updateAttendanceSettings = catchAsync(async (req: any, res) => {
  const { outletId } = req.params;
  const updateData = pick(req.body, [
    'requirePin',
    'requirePhoto',
    'allowLateCheckIn',
    'defaultLateThreshold',
    'allowEarlyCheckOut',
    'autoCheckOut',
    'autoCheckOutTime',
    'geoFencing',
    'maxDistance',
    'overtimeThreshold'
  ]);

  const settings = await prisma.attendanceSettings.upsert({
    where: { outletId: parseInt(outletId) },
    update: updateData,
    create: {
      outletId: parseInt(outletId),
      ...updateData
    }
  });

  res.status(httpStatus.OK).json(settings);
});

/**
 * Get attendance settings for outlet
 */
const getAttendanceSettings = catchAsync(async (req: any, res) => {
  const { outletId } = req.query;

  if (!outletId) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'outletId is required');
  }

  const settings = await prisma.attendanceSettings.findUnique({
    where: { outletId: parseInt(outletId as string) }
  });

  if (!settings) {
    // Return default settings if none exist
    const defaultSettings = {
      outletId: parseInt(outletId as string),
      requirePin: false,
      requirePhoto: false,
      allowLateCheckIn: true,
      defaultLateThreshold: 15,
      allowEarlyCheckOut: true,
      autoCheckOut: false,
      autoCheckOutTime: '23:59',
      geoFencing: false,
      maxDistance: 100,
      overtimeThreshold: 8.0
    };

    res.status(httpStatus.OK).json(defaultSettings);
    return;
  }

  res.status(httpStatus.OK).json(settings);
});

export default {
  checkIn,
  checkOut,
  getTodayStatus,
  getAttendanceHistory,
  getTeamAttendance,
  createWorkSchedule,
  getWorkSchedules,
  updateAttendanceSettings,
  getAttendanceSettings
};
