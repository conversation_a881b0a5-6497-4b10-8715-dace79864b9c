import request from 'supertest';
import { faker } from '@faker-js/faker';
import httpStatus from 'http-status';
import moment from 'moment';
import app from '../../src/app';
import { setupTestDBOptimized } from '../utils/setupTestDb';
import { describe, beforeEach, test, expect, jest, afterAll } from '@jest/globals';
import {
  userOne,
  userTwo,
  insertUsers,
  createOutletForOwner,
  createEmployeeWithOutlet
} from '../fixtures/user.fixture';
import {
  attendanceOne,
  scheduleOne,
  settingsOne,
  insertAttendances,
  insertWorkSchedules,
  insertAttendanceSettings
} from '../fixtures/attendance.fixture';
import { tokenService } from '../../src/services';
import { TokenType, AttendanceStatus } from '@prisma/client';
import prisma from '../../src/client';

setupTestDBOptimized();

describe('Attendance routes', () => {
  let outletOne: any;
  let dbUserOne: any;
  let dbUserTwo: any;
  let userOneAccessToken: string;
  let userTwoAccessToken: string;

  beforeEach(async () => {
    await insertUsers([userOne, userTwo]);

    // Get users from database with IDs
    dbUserOne = await prisma.user.findUnique({ where: { email: userOne.email } });
    dbUserTwo = await prisma.user.findUnique({ where: { email: userTwo.email } });

    outletOne = await createOutletForOwner(dbUserOne.id);

    // Generate access tokens
    userOneAccessToken = tokenService.generateToken(
      dbUserOne.id,
      moment().add(1, 'hour'),
      TokenType.ACCESS
    );
    userTwoAccessToken = tokenService.generateToken(
      dbUserTwo.id,
      moment().add(1, 'hour'),
      TokenType.ACCESS
    );

    // Insert default attendance settings
    await insertAttendanceSettings([{ ...settingsOne, outletId: outletOne.id }]);
  });

  describe('POST /v1/attendance/check-in', () => {
    test('should return 201 and create attendance record for valid check-in', async () => {
      // Insert schedule for user
      await insertWorkSchedules([{ ...scheduleOne, userId: dbUserOne.id, outletId: outletOne.id }]);

      const checkInData = {
        outletId: outletOne.id,
        location: '-6.2088,106.8456',
        notes: 'Check in dari test'
      };

      const res = await request(app)
        .post('/v1/attendance/check-in')
        .set('Authorization', `Bearer ${userOneAccessToken}`)
        .send(checkInData)
        .expect(httpStatus.CREATED);

      expect(res.body).toMatchObject({
        id: expect.any(Number),
        userId: dbUserOne.id,
        outletId: outletOne.id,
        status: AttendanceStatus.PRESENT,
        checkIn: expect.any(String),
        location: '-6.2088,106.8456',
        notes: 'Check in dari test'
      });

      // Verify in database
      const dbAttendance = await prisma.attendance.findUnique({
        where: { id: res.body.id }
      });
      expect(dbAttendance).toBeDefined();
    });

    test('should return 201 and mark as late when check-in is after schedule', async () => {
      // Insert schedule with start time 08:00 for Monday (dayOfWeek: 1)
      await insertWorkSchedules([
        {
          ...scheduleOne,
          userId: dbUserOne.id,
          outletId: outletOne.id,
          startTime: '08:00',
          dayOfWeek: 1
        }
      ]);

      // Mock current time to be Monday 08:30 (30 minutes late)
      const mondayLateTime = moment().day(1).hour(8).minute(30).second(0);
      jest.spyOn(moment, 'now').mockReturnValue(mondayLateTime.valueOf());
      jest.spyOn(Date, 'now').mockReturnValue(mondayLateTime.toDate().getTime());

      const checkInData = {
        outletId: outletOne.id,
        location: '-6.2088,106.8456'
      };

      const res = await request(app)
        .post('/v1/attendance/check-in')
        .set('Authorization', `Bearer ${userOneAccessToken}`)
        .send(checkInData)
        .expect(httpStatus.CREATED);

      expect(res.body).toMatchObject({
        status: AttendanceStatus.LATE,
        isLate: true,
        lateByMinutes: expect.any(Number)
      });

      // Restore mocks
      jest.restoreAllMocks();
    });

    test('should return 400 when PIN is required but not provided', async () => {
      // Update settings to require PIN
      await prisma.attendanceSettings.update({
        where: { outletId: outletOne.id },
        data: { requirePin: true }
      });

      const checkInData = {
        outletId: outletOne.id,
        location: '-6.2088,106.8456'
      };

      const res = await request(app)
        .post('/v1/attendance/check-in')
        .set('Authorization', `Bearer ${userOneAccessToken}`)
        .send(checkInData)
        .expect(httpStatus.BAD_REQUEST);

      expect(res.body.message).toBe('PIN wajib diisi');
    });

    test('should return 400 when photo is required but not provided', async () => {
      // Update settings to require photo
      await prisma.attendanceSettings.update({
        where: { outletId: outletOne.id },
        data: { requirePhoto: true }
      });

      const checkInData = {
        outletId: outletOne.id,
        location: '-6.2088,106.8456'
      };

      const res = await request(app)
        .post('/v1/attendance/check-in')
        .set('Authorization', `Bearer ${userOneAccessToken}`)
        .send(checkInData)
        .expect(httpStatus.BAD_REQUEST);

      expect(res.body.message).toBe('Foto wajib diupload');
    });

    test('should return 400 when trying to check-in twice in the same day', async () => {
      // Insert existing attendance for today
      const today = moment().startOf('day').toDate();
      await insertAttendances([
        { ...attendanceOne, userId: dbUserOne.id, outletId: outletOne.id, date: today }
      ]);

      const checkInData = {
        outletId: outletOne.id,
        location: '-6.2088,106.8456'
      };

      const res = await request(app)
        .post('/v1/attendance/check-in')
        .set('Authorization', `Bearer ${userOneAccessToken}`)
        .send(checkInData)
        .expect(httpStatus.BAD_REQUEST);

      expect(res.body.message).toBe('Anda sudah melakukan check-in hari ini');
    });

    test('should return 401 when no access token is provided', async () => {
      const checkInData = {
        outletId: outletOne.id,
        location: '-6.2088,106.8456'
      };

      await request(app)
        .post('/v1/attendance/check-in')
        .send(checkInData)
        .expect(httpStatus.UNAUTHORIZED);
    });

    test('should return 400 when outletId is missing', async () => {
      const checkInData = {
        location: '-6.2088,106.8456'
      };

      const res = await request(app)
        .post('/v1/attendance/check-in')
        .set('Authorization', `Bearer ${userOneAccessToken}`)
        .send(checkInData)
        .expect(httpStatus.BAD_REQUEST);

      expect(res.body.message).toContain('outletId');
    });
  });

  describe('POST /v1/attendance/check-out', () => {
    beforeEach(async () => {
      // Create check-in record first
      const today = moment().startOf('day').toDate();
      await insertAttendances([
        {
          ...attendanceOne,
          userId: dbUserOne.id,
          outletId: outletOne.id,
          date: today,
          checkIn: moment().subtract(8, 'hours').toDate(),
          checkOut: null
        }
      ]);
    });

    test('should return 200 and update attendance record with check-out', async () => {
      const checkOutData = {
        outletId: outletOne.id,
        notes: 'Check out dari test'
      };

      const res = await request(app)
        .post('/v1/attendance/check-out')
        .set('Authorization', `Bearer ${userOneAccessToken}`)
        .send(checkOutData)
        .expect(httpStatus.OK);

      expect(res.body).toMatchObject({
        userId: dbUserOne.id,
        outletId: outletOne.id,
        checkOut: expect.any(String),
        workingHours: expect.any(Number),
        notes: 'Check out dari test'
      });

      expect(res.body.workingHours).toBeGreaterThan(0);
    });

    test('should return 200 and calculate overtime when exceeding threshold', async () => {
      // First, delete any existing attendance for this user today to avoid constraint error
      const today = moment().startOf('day').toDate();
      await prisma.attendance.deleteMany({
        where: {
          userId: dbUserOne.id,
          outletId: outletOne.id,
          date: today
        }
      });

      // Then create a check-in record from 11 hours ago to ensure overtime
      const elevenHoursAgo = moment().subtract(11, 'hours').toDate();

      const { id, ...attendanceData } = attendanceOne;
      await insertAttendances([
        {
          ...attendanceData,
          userId: dbUserOne.id,
          outletId: outletOne.id,
          date: today,
          checkIn: elevenHoursAgo,
          checkOut: null
        }
      ]);

      const checkOutData = {
        outletId: outletOne.id
      };

      const res = await request(app)
        .post('/v1/attendance/check-out')
        .set('Authorization', `Bearer ${userOneAccessToken}`)
        .send(checkOutData)
        .expect(httpStatus.OK);

      expect(res.body.overtimeHours).toBeGreaterThan(0);
      expect(res.body.workingHours).toBeGreaterThan(8); // Should be more than 8 hours
    });

    test('should return 400 when trying to check-out without check-in', async () => {
      const checkOutData = {
        outletId: outletOne.id
      };

      const res = await request(app)
        .post('/v1/attendance/check-out')
        .set('Authorization', `Bearer ${userTwoAccessToken}`)
        .send(checkOutData)
        .expect(httpStatus.BAD_REQUEST);

      expect(res.body.message).toBe('Anda belum melakukan check-in hari ini');
    });

    test('should return 400 when trying to check-out twice', async () => {
      // First check-out
      await request(app)
        .post('/v1/attendance/check-out')
        .set('Authorization', `Bearer ${userOneAccessToken}`)
        .send({ outletId: outletOne.id })
        .expect(httpStatus.OK);

      // Second check-out attempt
      const res = await request(app)
        .post('/v1/attendance/check-out')
        .set('Authorization', `Bearer ${userOneAccessToken}`)
        .send({ outletId: outletOne.id })
        .expect(httpStatus.BAD_REQUEST);

      expect(res.body.message).toBe('Anda sudah melakukan check-out hari ini');
    });

    test('should return 401 when no access token is provided', async () => {
      await request(app)
        .post('/v1/attendance/check-out')
        .send({ outletId: outletOne.id })
        .expect(httpStatus.UNAUTHORIZED);
    });
  });

  describe('GET /v1/attendance/status/today', () => {
    test('should return 200 and attendance status for today', async () => {
      const today = moment().startOf('day').toDate();
      await insertAttendances([
        {
          ...attendanceOne,
          userId: dbUserOne.id,
          outletId: outletOne.id,
          date: today
        }
      ]);

      const res = await request(app)
        .get(`/v1/attendance/status/today?outletId=${outletOne.id}`)
        .set('Authorization', `Bearer ${userOneAccessToken}`)
        .expect(httpStatus.OK);

      expect(res.body).toMatchObject({
        userId: dbUserOne.id,
        outletId: outletOne.id,
        status: AttendanceStatus.PRESENT,
        checkIn: expect.any(String)
      });
    });

    test('should return 404 when no attendance record exists for today', async () => {
      const res = await request(app)
        .get(`/v1/attendance/status/today?outletId=${outletOne.id}`)
        .set('Authorization', `Bearer ${userOneAccessToken}`)
        .expect(httpStatus.NOT_FOUND);

      expect(res.body.message).toBe('Belum ada absensi hari ini');
    });

    test('should return 401 when no access token is provided', async () => {
      await request(app)
        .get(`/v1/attendance/status/today?outletId=${outletOne.id}`)
        .expect(httpStatus.UNAUTHORIZED);
    });

    test('should return 400 when outletId is missing', async () => {
      const res = await request(app)
        .get('/v1/attendance/status/today')
        .set('Authorization', `Bearer ${userOneAccessToken}`)
        .expect(httpStatus.BAD_REQUEST);

      expect(res.body.message).toContain('outletId');
    });
  });

  describe('GET /v1/attendance/history', () => {
    beforeEach(async () => {
      // Insert multiple attendance records
      const dates = [
        moment().subtract(1, 'day').startOf('day').toDate(),
        moment().subtract(2, 'days').startOf('day').toDate(),
        moment().subtract(3, 'days').startOf('day').toDate()
      ];

      const attendances = dates.map((date, index) => ({
        ...attendanceOne,
        id: index + 1,
        userId: dbUserOne.id,
        outletId: outletOne.id,
        date,
        status: index % 2 === 0 ? AttendanceStatus.PRESENT : AttendanceStatus.LATE
      }));

      await insertAttendances(attendances);
    });

    test('should return 200 and attendance history', async () => {
      const res = await request(app)
        .get(`/v1/attendance/history?outletId=${outletOne.id}`)
        .set('Authorization', `Bearer ${userOneAccessToken}`)
        .expect(httpStatus.OK);

      expect(res.body.results).toBeInstanceOf(Array);
      expect(res.body.results.length).toBeGreaterThan(0);
      expect(res.body.results[0]).toMatchObject({
        userId: dbUserOne.id,
        outletId: outletOne.id,
        status: expect.any(String),
        date: expect.any(String)
      });
    });

    test('should return 200 and filtered history by date range', async () => {
      const startDate = moment().subtract(2, 'days').format('YYYY-MM-DD');
      const endDate = moment().subtract(1, 'day').format('YYYY-MM-DD');

      const res = await request(app)
        .get(
          `/v1/attendance/history?outletId=${outletOne.id}&startDate=${startDate}&endDate=${endDate}`
        )
        .set('Authorization', `Bearer ${userOneAccessToken}`)
        .expect(httpStatus.OK);

      expect(res.body.results).toBeInstanceOf(Array);
      expect(res.body.results.length).toBeLessThanOrEqual(2);
    });

    test('should return 200 and filtered history by status', async () => {
      const res = await request(app)
        .get(`/v1/attendance/history?outletId=${outletOne.id}&status=PRESENT`)
        .set('Authorization', `Bearer ${userOneAccessToken}`)
        .expect(httpStatus.OK);

      expect(res.body.results).toBeInstanceOf(Array);
      res.body.results.forEach((attendance: any) => {
        expect(attendance.status).toBe(AttendanceStatus.PRESENT);
      });
    });

    test('should return 401 when no access token is provided', async () => {
      await request(app)
        .get(`/v1/attendance/history?outletId=${outletOne.id}`)
        .expect(httpStatus.UNAUTHORIZED);
    });
  });

  describe('GET /v1/attendance/team/today', () => {
    beforeEach(async () => {
      // Create employee for outlet
      const employee = await createEmployeeWithOutlet(outletOne.id);

      // Insert attendance for multiple users
      const today = moment().startOf('day').toDate();
      await insertAttendances([
        {
          ...attendanceOne,
          id: 1,
          userId: dbUserOne.id,
          outletId: outletOne.id,
          date: today,
          status: AttendanceStatus.PRESENT
        },
        {
          ...attendanceOne,
          id: 2,
          userId: employee.id,
          outletId: outletOne.id,
          date: today,
          status: AttendanceStatus.LATE
        }
      ]);
    });

    test('should return 200 and team attendance for today', async () => {
      const res = await request(app)
        .get(`/v1/attendance/team/today?outletId=${outletOne.id}`)
        .set('Authorization', `Bearer ${userOneAccessToken}`)
        .expect(httpStatus.OK);

      expect(res.body).toBeInstanceOf(Array);
      expect(res.body.length).toBeGreaterThan(0);
      expect(res.body[0]).toMatchObject({
        userId: expect.any(Number),
        outletId: outletOne.id,
        status: expect.any(String),
        user: {
          id: expect.any(Number),
          name: expect.any(String)
        }
      });
    });

    test('should return 401 when no access token is provided', async () => {
      await request(app)
        .get(`/v1/attendance/team/today?outletId=${outletOne.id}`)
        .expect(httpStatus.UNAUTHORIZED);
    });

    test('should return 403 when user is not owner/manager of outlet', async () => {
      // Create another user who is not owner
      const otherUserData = {
        ...userTwo,
        email: faker.internet.email(),
        phone: faker.phone.number('08##########')
      };

      await insertUsers([otherUserData]);

      // Get the created user from database
      const otherUser = await prisma.user.findUnique({
        where: { email: otherUserData.email }
      });

      const otherUserToken = tokenService.generateToken(
        otherUser!.id,
        moment().add(1, 'hour'),
        TokenType.ACCESS
      );

      await request(app)
        .get(`/v1/attendance/team/today?outletId=${outletOne.id}`)
        .set('Authorization', `Bearer ${otherUserToken}`)
        .expect(httpStatus.FORBIDDEN);
    });
  });
});
