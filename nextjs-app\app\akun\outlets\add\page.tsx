'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Save, MapPin, Loader2 } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useCreateOutlet, useOutlets } from '@/hooks/useOutlets';
import {
  useProvinces,
  useCitiesByProvince,
  useTimezones,
} from '@/hooks/useLocations';
import type { CreateOutletRequest } from '@/lib/api/outlets';

export default function AddOutletPage() {
  const router = useRouter();
  const createOutletMutation = useCreateOutlet();
  const { data: outletsData } = useOutlets(); // Untuk pilihan copy layanan

  // Fetch data dari API
  const { data: provinces, isLoading: provincesLoading } = useProvinces();
  const { data: timezones, isLoading: timezonesLoading } = useTimezones();

  const [formData, setFormData] = useState<CreateOutletRequest>({
    name: '',
    address: '',
    province: '',
    city: '',
    timezone: 'Asia/Jakarta',
    phone: '',
    latitude: undefined,
    longitude: undefined,
    copiedFromId: undefined,
  });

  const [copyServices, setCopyServices] = useState(false);
  const [selectedCopyOutlet, setSelectedCopyOutlet] = useState<string>('');
  const [selectedProvinceId, setSelectedProvinceId] = useState<number | null>(
    null
  );

  // Fetch cities berdasarkan provinsi yang dipilih
  const { data: cities, isLoading: citiesLoading } =
    useCitiesByProvince(selectedProvinceId);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]:
        name === 'latitude' || name === 'longitude'
          ? value === ''
            ? undefined
            : parseFloat(value)
          : value,
    }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }));

    // Reset city when province changes
    if (name === 'province') {
      setFormData((prev) => ({ ...prev, city: '' }));

      // Find province ID untuk fetch cities
      const selectedProvince = provinces?.find((p) => p.name === value);
      setSelectedProvinceId(selectedProvince?.id || null);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      const submitData = {
        ...formData,
        copiedFromId:
          copyServices && selectedCopyOutlet
            ? parseInt(selectedCopyOutlet)
            : undefined,
      };

      await createOutletMutation.mutateAsync(submitData);
      router.push('/akun/outlets');
    } catch (error) {
      // Error handling sudah ada di hook
    }
  };

  return (
    <div className="flex flex-col min-h-screen bg-slate-50">
      <header className="p-4 flex justify-between items-center bg-white sticky top-0 z-10 shadow-sm">
        <div className="flex items-center">
          <Link href="/akun/outlets" className="mr-3">
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <h1 className="text-lg font-semibold">Tambah Outlet Baru</h1>
        </div>
        <Button
          type="submit"
          form="add-outlet-form"
          className="bg-blue-500 hover:bg-blue-600"
          disabled={createOutletMutation.isPending}
        >
          <Save className="h-4 w-4 mr-2" />
          {createOutletMutation.isPending ? 'Menyimpan...' : 'Simpan'}
        </Button>
      </header>

      <main className="flex-1 p-4 pb-20">
        <form
          id="add-outlet-form"
          onSubmit={handleSubmit}
          className="space-y-6"
        >
          {/* Informasi Dasar */}
          <Card className="p-6">
            <h2 className="text-lg font-semibold mb-4">Informasi Dasar</h2>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">Nama Outlet *</Label>
                <Input
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  placeholder="Contoh: Felis Laundry - Cabang Kemang"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="address">Alamat Lengkap *</Label>
                <Textarea
                  id="address"
                  name="address"
                  value={formData.address}
                  onChange={handleChange}
                  placeholder="Masukkan alamat lengkap outlet"
                  rows={3}
                  required
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="province">Provinsi *</Label>
                  <Select
                    value={formData.province}
                    onValueChange={(value) =>
                      handleSelectChange('province', value)
                    }
                    disabled={provincesLoading}
                  >
                    <SelectTrigger>
                      <SelectValue
                        placeholder={
                          provincesLoading ? 'Memuat...' : 'Pilih provinsi'
                        }
                      />
                    </SelectTrigger>
                    <SelectContent>
                      {provinces?.map((province) => (
                        <SelectItem key={province.id} value={province.name}>
                          {province.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="city">Kota/Kabupaten *</Label>
                  <Select
                    value={formData.city}
                    onValueChange={(value) => handleSelectChange('city', value)}
                    disabled={!formData.province || citiesLoading}
                  >
                    <SelectTrigger>
                      <SelectValue
                        placeholder={
                          !formData.province
                            ? 'Pilih provinsi dulu'
                            : citiesLoading
                            ? 'Memuat...'
                            : 'Pilih kota'
                        }
                      />
                    </SelectTrigger>
                    <SelectContent>
                      {cities?.map((city) => (
                        <SelectItem key={city.id} value={city.name}>
                          {city.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="timezone">Zona Waktu</Label>
                <Select
                  value={formData.timezone}
                  onValueChange={(value) =>
                    handleSelectChange('timezone', value)
                  }
                  disabled={timezonesLoading}
                >
                  <SelectTrigger>
                    <SelectValue
                      placeholder={
                        timezonesLoading ? 'Memuat...' : 'Pilih zona waktu'
                      }
                    />
                  </SelectTrigger>
                  <SelectContent>
                    {timezones?.map((tz) => (
                      <SelectItem key={tz.id} value={tz.id}>
                        <div className="flex flex-col">
                          <span className="font-medium">{tz.name}</span>
                          <span className="text-sm text-gray-500">
                            {tz.description}
                          </span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="phone">Nomor Telepon *</Label>
                <Input
                  id="phone"
                  name="phone"
                  value={formData.phone}
                  onChange={handleChange}
                  placeholder="Contoh: 021-7654321"
                  required
                />
              </div>
            </div>
          </Card>

          {/* Lokasi Maps */}
          <Card className="p-6">
            <h2 className="text-lg font-semibold mb-4 flex items-center gap-2">
              <MapPin className="h-5 w-5" />
              Lokasi Maps
            </h2>
            <div className="space-y-4">
              <div className="h-48 bg-gray-100 rounded-md flex items-center justify-center">
                <p className="text-gray-500">Peta akan ditampilkan di sini</p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="latitude">Latitude</Label>
                  <Input
                    id="latitude"
                    name="latitude"
                    type="number"
                    step="any"
                    value={formData.latitude || ''}
                    onChange={handleChange}
                    placeholder="Contoh: -6.2088"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="longitude">Longitude</Label>
                  <Input
                    id="longitude"
                    name="longitude"
                    type="number"
                    step="any"
                    value={formData.longitude || ''}
                    onChange={handleChange}
                    placeholder="Contoh: 106.8456"
                  />
                </div>
              </div>

              <Button type="button" variant="outline" className="w-full">
                Pilih Lokasi di Peta
              </Button>
            </div>
          </Card>

          {/* Copy Layanan dari Outlet Lain */}
          {outletsData &&
            outletsData.results &&
            outletsData.results.length > 0 && (
              <Card className="p-6">
                <h2 className="text-lg font-semibold mb-4">Copy Layanan</h2>
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="copyServices"
                      checked={copyServices}
                      onChange={(e) => setCopyServices(e.target.checked)}
                      className="rounded border-gray-300"
                    />
                    <Label htmlFor="copyServices">
                      Copy layanan dari outlet yang sudah ada
                    </Label>
                  </div>

                  {copyServices && (
                    <div className="space-y-2">
                      <Label htmlFor="copyFromOutlet">Pilih Outlet</Label>
                      <Select
                        value={selectedCopyOutlet}
                        onValueChange={setSelectedCopyOutlet}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Pilih outlet untuk copy layanan" />
                        </SelectTrigger>
                        <SelectContent>
                          {outletsData.results.map((outlet: any) => (
                            <SelectItem
                              key={outlet.id}
                              value={outlet.id.toString()}
                            >
                              {outlet.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  )}
                </div>
              </Card>
            )}
        </form>
      </main>
    </div>
  );
}
