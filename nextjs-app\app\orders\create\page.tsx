'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import {
  ArrowLeft,
  Plus,
  Search,
  User,
  MapPin,
  Sparkles,
  Calendar,
  Truck,
  StickyNote,
  ChevronRight,
  Trash2,
  Check,
  X,
  Tag,
  CreditCard,
  Map,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetFooter,
} from '@/components/ui/sheet';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { useActivePerfumes } from '@/hooks/usePerfumes';
import {
  useActivePromotions,
  useValidatePromotion,
} from '@/hooks/usePromotions';
import { useCustomers } from '@/hooks/useCustomers';
import { useServiceCategories } from '@/hooks/useServiceCategories';
import { useCreateOrder } from '@/hooks/useOrders';
import { useCashboxes } from '@/hooks/useCashbox';
import { useAuth } from '@/lib/auth-context';

// Types

interface ServiceVariant {
  id: number;
  name: string;
  price: number;
  duration: number;
  unit: string;
}

interface ServiceCategory {
  id: number;
  name: string;
  processes: string[];
  variants: ServiceVariant[];
}

interface OrderItem {
  variantId: number;
  categoryId: number;
  categoryName: string;
  variantName: string;
  price: number;
  quantity: number;
  unit: string;
  duration: number;
}

// Remove unused interface - using Promotion from API instead

interface OrderDetails {
  customerId: number | null;
  items: OrderItem[];
  notes: string;
  perfume: string;
  isPickupDelivery: boolean;
  isPickup: boolean;
  isDelivery: boolean;
  pickupDate: string;
  pickupTime: string;
  deliveryDate: string;
  deliveryTime: string;
  promoId: number | null;
  paymentMethod: 'later' | 'cash' | 'non-cash' | 'deposit';
  deliveryAddress: string;
  deliveryDistance: number;
  deliveryMapsUrl: string;
}

export default function CreateOrderDetailsPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [searchQuery, setSearchQuery] = useState('');
  const { activeOutlet } = useAuth();

  // API hooks
  const { data: perfumes } = useActivePerfumes();
  const { data: promotions } = useActivePromotions();
  const { data: customersData } = useCustomers({ limit: 100 });
  const { data: serviceCategoriesData } = useServiceCategories({
    outletId: activeOutlet?.id,
  });
  const {
    data: cashboxes,
    isLoading: cashboxesLoading,
    error: cashboxesError,
  } = useCashboxes();

  // Debug cashbox data
  console.log('🚀 ~ CreateOrderDetailsPage ~ cashboxes:', cashboxes);
  console.log(
    '🚀 ~ CreateOrderDetailsPage ~ cashboxesLoading:',
    cashboxesLoading
  );
  console.log('🚀 ~ CreateOrderDetailsPage ~ cashboxesError:', cashboxesError);
  console.log('🚀 ~ CreateOrderDetailsPage ~ activeOutlet:', activeOutlet);
  const validatePromotionMutation = useValidatePromotion();
  const createOrderMutation = useCreateOrder();

  // Extract data from API responses
  const customers = customersData?.results || [];
  const serviceCategories = serviceCategoriesData?.results || [];

  // Convert service categories to the format expected by the UI
  const categories = serviceCategories.map((category) => ({
    id: category.id,
    name: category.name,
    processes: category.productionProcess || [],
    variants:
      category.services?.map((service) => ({
        id: service.id,
        name: service.name,
        price: service.price,
        duration: service.estimationHours || 24,
        unit: service.unit,
      })) || [],
  }));
  const [isServiceSheetOpen, setIsServiceSheetOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] =
    useState<ServiceCategory | null>(null);
  const [selectedVariant, setSelectedVariant] = useState<ServiceVariant | null>(
    null
  );
  const [variantQuantity, setVariantQuantity] = useState<string>('1');
  const [selectedDuration, setSelectedDuration] = useState<number | null>(null);

  // Promo state
  const [isPromoSheetOpen, setIsPromoSheetOpen] = useState(false);
  const [validatedPromotion, setValidatedPromotion] = useState<any>(null); // eslint-disable-line @typescript-eslint/no-explicit-any

  // Order state
  const [orderDetails, setOrderDetails] = useState<OrderDetails>({
    customerId: null,
    items: [],
    notes: '',
    perfume: '',
    isPickupDelivery: false,
    isPickup: false,
    isDelivery: false,
    pickupDate: formatDate(new Date()),
    pickupTime: '18:00',
    deliveryDate: formatDate(new Date(Date.now() + 24 * 60 * 60 * 1000)), // Tomorrow
    deliveryTime: '18:00',
    promoId: null,
    paymentMethod: 'later',
    deliveryAddress: '',
    deliveryDistance: 0,
    deliveryMapsUrl: '',
  });

  // Tambahkan state untuk dialog ganti alamat
  const [isAddressSheetOpen, setIsAddressSheetOpen] = useState(false);
  const [newAddress, setNewAddress] = useState('');
  const [newMapsUrl, setNewMapsUrl] = useState('');

  // Fungsi untuk menghitung jarak (dalam contoh ini menggunakan nilai statis)
  const calculateDistance = () => {
    // Dalam implementasi nyata, Anda akan menggunakan API maps untuk menghitung jarak
    // Untuk contoh ini, kita gunakan nilai random antara 1-10 km
    return Math.floor(Math.random() * 10) + 1;
  };

  // Fungsi untuk menangani perubahan alamat
  const handleAddressChange = () => {
    if (!newAddress) return;

    const distance = calculateDistance();
    setOrderDetails({
      ...orderDetails,
      deliveryAddress: newAddress,
      deliveryDistance: distance,
      deliveryMapsUrl: newMapsUrl,
    });
    setIsAddressSheetOpen(false);
  };

  // Replace the useEffect hook that initializes customer data with these two separate hooks:

  // First hook: Handle redirect if no customer is selected
  useEffect(() => {
    const customerId = searchParams.get('customerId');
    const isNewCustomer = searchParams.get('newCustomer') === 'true';

    if (!customerId && !isNewCustomer) {
      router.push('/orders/select-customer');
    }
  }, [searchParams, router]);

  // Second hook: Initialize customer data only when needed
  useEffect(() => {
    const customerId = searchParams.get('customerId');
    const isNewCustomer = searchParams.get('newCustomer') === 'true';

    if (customerId) {
      const id = Number.parseInt(customerId);
      const customer = customers.find((c) => c.id === id);
      if (customer) {
        setOrderDetails((prevState) => ({
          ...prevState,
          customerId: id,
        }));
      }
    } else if (isNewCustomer) {
      // Handle new customer data from URL
      // TODO: Implement customer creation via API
      // For now, we'll use a temporary ID
      const tempCustomerId = 999999; // Temporary ID for new customer

      setOrderDetails((prevState) => ({
        ...prevState,
        customerId: tempCustomerId,
      }));
    }
  }, [searchParams, customers]);

  // Calculate total price
  const subtotal = orderDetails.items.reduce(
    (sum, item) => sum + item.price * item.quantity,
    0
  );

  // Apply promo if selected
  const selectedPromo = validatedPromotion;
  let discount = 0;
  if (selectedPromo && selectedPromo.isValid) {
    discount = selectedPromo.discountAmount || 0;
  }

  const totalPrice = subtotal - discount;

  // Get selected customer
  const selectedCustomer = orderDetails.customerId
    ? customers.find((c) => c.id === orderDetails.customerId)
    : null;

  // Format date for input fields
  function formatDate(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  // Handle updating service quantity
  const handleUpdateQuantity = (
    index: number,
    newQuantity: string | number
  ) => {
    const quantity =
      typeof newQuantity === 'string' ? parseFloat(newQuantity) : newQuantity;
    if (isNaN(quantity) || quantity < 0) return;

    // Batasi 1 digit di belakang koma
    const roundedQuantity = Math.round(quantity * 10) / 10;

    const updatedItems = [...orderDetails.items];
    updatedItems[index].quantity = roundedQuantity;
    setOrderDetails({ ...orderDetails, items: updatedItems });
  };

  // Tambahkan fungsi untuk menangani perubahan quantity
  const handleVariantQuantityChange = (value: string) => {
    // Hapus semua karakter kecuali angka dan titik desimal
    const cleanValue = value.replace(/[^\d.]/g, '');

    // Pastikan hanya ada satu titik desimal
    const parts = cleanValue.split('.');
    if (parts.length > 2) return;

    // Batasi 1 digit di belakang koma
    if (parts.length === 2 && parts[1].length > 1) return;

    const quantity = parseFloat(cleanValue);
    if (!isNaN(quantity) && quantity >= 0) {
      setVariantQuantity(cleanValue);
    }
  };

  // Modifikasi handleAddService untuk menggunakan parseFloat dan pembulatan
  const handleAddService = () => {
    if (!selectedVariant || !selectedCategory) return;

    const quantity = parseFloat(variantQuantity);
    if (isNaN(quantity) || quantity <= 0) return;

    // Batasi 1 digit di belakang koma
    const roundedQuantity = Math.round(quantity * 10) / 10;

    // Check if the item already exists in the order
    const existingItemIndex = orderDetails.items.findIndex(
      (item) => item.variantId === selectedVariant.id
    );

    if (existingItemIndex >= 0) {
      // Update quantity if item already exists
      const updatedItems = [...orderDetails.items];
      updatedItems[existingItemIndex].quantity += roundedQuantity;
      setOrderDetails({ ...orderDetails, items: updatedItems });
    } else {
      // Add new item
      const newItem: OrderItem = {
        variantId: selectedVariant.id,
        categoryId: selectedCategory.id,
        categoryName: selectedCategory.name,
        variantName: selectedVariant.name,
        price: selectedVariant.price,
        quantity: roundedQuantity,
        unit: selectedVariant.unit,
        duration: selectedVariant.duration,
      };

      // Set the selected duration if this is the first item
      if (orderDetails.items.length === 0) {
        setSelectedDuration(selectedVariant.duration);
      }

      setOrderDetails({
        ...orderDetails,
        items: [...orderDetails.items, newItem],
      });
    }

    // Reset selection
    setSelectedVariant(null);
    setVariantQuantity('1');
    setIsServiceSheetOpen(false);
  };

  // Handle removing a service from the order
  const handleRemoveService = (index: number) => {
    const updatedItems = [...orderDetails.items];
    updatedItems.splice(index, 1);

    // Reset selected duration if no items left
    if (updatedItems.length === 0) {
      setSelectedDuration(null);
    }

    setOrderDetails({ ...orderDetails, items: updatedItems });
  };

  // Handle selecting a promo
  const handleSelectPromo = async (promoCode: string) => {
    if (!selectedCustomer || orderDetails.items.length === 0) return;

    try {
      const result = await validatePromotionMutation.mutateAsync({
        code: promoCode,
        orderTotal: subtotal,
        customerId: selectedCustomer.id,
      });

      if (result.isValid) {
        setValidatedPromotion(result);
        setOrderDetails({ ...orderDetails, promoId: result.promotion.id });
      } else {
        // Show error message if validation failed
        console.log('Promo validation failed');
        setValidatedPromotion(null);
      }
    } catch (error) {
      console.error('Error validating promotion:', error);
      setValidatedPromotion(null);
      // TODO: Show error message to user
    }

    setIsPromoSheetOpen(false);
  };

  // Handle creating the order
  const handleCreateOrder = async () => {
    if (!activeOutlet || !orderDetails.customerId) {
      console.error('Missing required data: outlet or customer');
      return;
    }

    try {
      // Calculate estimated finish time based on current time + max service duration
      const maxDuration =
        orderDetails.items.length > 0
          ? Math.max(...orderDetails.items.map((item) => item.duration))
          : 0;

      const now = new Date();
      const estimatedFinish = new Date(
        now.getTime() + maxDuration * 60 * 60 * 1000
      );

      // Convert UI order details to API format
      const createOrderData = {
        customerId: orderDetails.customerId,
        outletId: activeOutlet.id, // Add outletId as required by backend
        items: orderDetails.items.map((item) => ({
          serviceId: item.variantId,
          quantity: item.quantity,
          unit: item.unit,
          price: item.price,
          subtotal: item.price * item.quantity,
          notes: undefined,
          status: 'PENDING' as const,
        })),
        notes: orderDetails.notes || undefined,
        pickupDate: orderDetails.isPickup ? orderDetails.pickupDate : undefined,
        deliveryDate: orderDetails.isDelivery
          ? orderDetails.deliveryDate
          : undefined,
        estimatedFinish: estimatedFinish.toISOString(), // Add estimated finish time
        perfumeId:
          perfumes?.results.find((p) => p.name === orderDetails.perfume)?.id ||
          undefined,
        perfumeName: orderDetails.perfume || undefined,
        promoId: orderDetails.promoId || undefined,
        payment:
          orderDetails.paymentMethod !== 'later'
            ? {
                amount: totalPrice,
                method: (orderDetails.paymentMethod === 'cash'
                  ? 'CASH'
                  : orderDetails.paymentMethod === 'non-cash'
                  ? 'TRANSFER' // Default non-cash ke TRANSFER, bisa disesuaikan berdasarkan cashbox yang dipilih
                  : orderDetails.paymentMethod === 'deposit'
                  ? 'DEPOSIT'
                  : 'CASH') as import('@/types/orders').PaymentMethod, // fallback
                cashboxId:
                  orderDetails.paymentMethod === 'non-cash' && selectedCashboxId
                    ? selectedCashboxId
                    : undefined,
              }
            : undefined,
      };

      console.log('Creating order with data:', createOrderData);

      const result = await createOrderMutation.mutateAsync(createOrderData);
      console.log('Order created successfully:', result);

      // Redirect ke halaman order detail
      router.push(`/orders/${result.id}`);
    } catch (error) {
      console.error('Error creating order:', error);
      // Error handling sudah ada di mutation hook
    }
  };

  // Create tab content for service selection
  const createServiceTabContent = (duration: number) => {
    // If a duration is already selected, only show services with that duration
    if (selectedDuration !== null && selectedDuration !== duration) {
      return (
        <div className="text-center py-8 text-muted-foreground">
          Anda hanya dapat memilih layanan dengan durasi {selectedDuration} jam
        </div>
      );
    }

    // Filter categories based on duration
    const filteredCategories = categories
      .map((category) => {
        // Filter variants based on duration and search query
        const filteredVariants = category.variants.filter((variant) => {
          const matchesSearch =
            variant.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
            category.name.toLowerCase().includes(searchQuery.toLowerCase());

          return variant.duration === duration && matchesSearch;
        });

        return {
          ...category,
          variants: filteredVariants,
        };
      })
      .filter((category) => category.variants.length > 0);

    return (
      <div className="space-y-4">
        {filteredCategories.length > 0 ? (
          filteredCategories.map((category) => (
            <div key={category.id} className="space-y-2">
              <h3 className="font-medium text-sm px-1">{category.name}</h3>
              <Card>
                <CardContent className="p-0">
                  <div className="divide-y">
                    {category.variants.map((variant) => (
                      <div
                        key={variant.id}
                        className="p-3 flex justify-between items-center cursor-pointer hover:bg-muted/50"
                        onClick={() => {
                          setSelectedCategory(category);
                          setSelectedVariant(variant);
                        }}
                      >
                        <div>
                          <div className="font-medium">{variant.name}</div>
                          <div className="text-sm text-muted-foreground">
                            Rp {variant.price.toLocaleString()} / {variant.unit}
                          </div>
                        </div>
                        <div className="flex items-center">
                          <Badge variant="outline" className="mr-2">
                            {variant.duration} jam
                          </Badge>
                          <ChevronRight className="h-4 w-4 text-muted-foreground" />
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          ))
        ) : (
          <div className="text-center py-8 text-muted-foreground">
            Tidak ada layanan {duration} jam yang ditemukan
          </div>
        )}
      </div>
    );
  };

  // Calculate estimated completion date
  const calculateEstimatedCompletion = () => {
    if (orderDetails.items.length === 0) return 'N/A';

    // Find the item with the longest duration
    const maxDuration = Math.max(
      ...orderDetails.items.map((item) => item.duration)
    );

    // Calculate the completion date based on current time + service duration
    const now = new Date();
    const completionDate = new Date(
      now.getTime() + maxDuration * 60 * 60 * 1000
    );

    // Format the date and time
    const formattedDate = completionDate.toLocaleDateString('id-ID', {
      weekday: 'short',
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });

    const formattedTime = completionDate.toLocaleTimeString('id-ID', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false,
    });

    return `${formattedDate}, ${formattedTime}`;
  };

  // Get current time for display
  const getCurrentTime = () => {
    const now = new Date();
    return now.toLocaleTimeString('id-ID', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false,
    });
  };

  // Open maps
  const handleOpenMaps = () => {
    if (selectedCustomer?.address) {
      // In a real app, you would use a maps API to open the address
      window.open(
        `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(
          selectedCustomer.address
        )}`,
        '_blank'
      );
    }
  };

  const [isPaymentSheetOpen, setIsPaymentSheetOpen] = useState(false);
  const [selectedPaymentTab, setSelectedPaymentTab] = useState<
    'cash' | 'non-cash' | 'deposit'
  >('cash');
  const [selectedCashboxId, setSelectedCashboxId] = useState<number | null>(
    null
  );

  return (
    <div className="max-w-md mx-auto bg-background min-h-screen pb-16">
      {/* Header */}
      <div className="sticky top-0 z-10 bg-background border-b">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-3">
            <Button
              variant="ghost"
              size="icon"
              className="h-9 w-9"
              onClick={() => router.push('/orders/select-customer')}
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <h1 className="text-xl font-semibold">Buat Pesanan Baru</h1>
          </div>
        </div>
      </div>

      <div className="p-4 space-y-6">
        {/* Loading State */}
        {(customersData === undefined ||
          serviceCategoriesData === undefined ||
          cashboxesLoading) && (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="text-sm text-muted-foreground mt-2">Memuat data...</p>
          </div>
        )}

        {/* Error State */}
        {(customers.length === 0 || categories.length === 0) &&
          customersData &&
          serviceCategoriesData && (
            <div className="text-center py-8">
              <p className="text-sm text-muted-foreground">
                {customers.length === 0
                  ? 'Tidak ada pelanggan tersedia.'
                  : 'Tidak ada layanan tersedia.'}
              </p>
              <p className="text-xs text-muted-foreground mt-1">
                Silakan tambahkan data terlebih dahulu.
              </p>
            </div>
          )}

        {/* Customer Information */}
        <Card>
          <CardHeader className="pb-2 pt-4 px-4">
            <h2 className="text-base font-semibold">Informasi Pelanggan</h2>
          </CardHeader>
          <CardContent className="px-4 pb-4 pt-0">
            {selectedCustomer ? (
              <div className="flex flex-col">
                <div className="flex items-center gap-3">
                  <Avatar>
                    <AvatarFallback>
                      {selectedCustomer.name.charAt(0)}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="flex items-center gap-2">
                      <h3 className="font-medium">{selectedCustomer.name}</h3>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {selectedCustomer.labels &&
                          selectedCustomer.labels.map((label, index) => (
                            <Badge
                              key={index}
                              variant="secondary"
                              className="text-xs"
                            >
                              {label}
                            </Badge>
                          ))}
                      </div>
                    </div>
                    <p className="text-sm text-gray-500">
                      {selectedCustomer.phone}
                    </p>
                  </div>
                </div>
                <Button
                  variant="outline"
                  className="w-full mt-2"
                  onClick={() => router.push('/orders/select-customer')}
                >
                  Ganti Pelanggan
                </Button>
              </div>
            ) : (
              <Button
                className="w-full"
                onClick={() => router.push('/orders/select-customer')}
              >
                <User className="h-4 w-4 mr-2" />
                Pilih Pelanggan
              </Button>
            )}
          </CardContent>
        </Card>

        {/* Service Items */}
        <Card>
          <CardHeader className="pb-2 pt-4 px-4">
            <h2 className="text-base font-semibold">Item Pesanan</h2>
          </CardHeader>
          <CardContent className="px-4 pb-4 pt-0">
            {orderDetails.items.length > 0 ? (
              <div className="space-y-3">
                <div className="divide-y">
                  {orderDetails.items.map((item, index) => (
                    <div key={index} className="py-3">
                      <div className="flex justify-between">
                        <div>
                          <div className="font-medium">{item.variantName}</div>
                          <div className="text-sm text-muted-foreground">
                            {item.categoryName} • {item.duration} jam
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="font-medium">
                            Rp {(item.price * item.quantity).toLocaleString()}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {item.quantity} {item.unit} x Rp{' '}
                            {item.price.toLocaleString()}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center justify-between mt-2">
                        <div className="flex items-center">
                          <Button
                            variant="outline"
                            size="icon"
                            className="h-7 w-7"
                            onClick={() =>
                              handleUpdateQuantity(index, item.quantity - 1)
                            }
                          >
                            <span className="sr-only">Decrease</span>
                            <span>-</span>
                          </Button>
                          <Input
                            type="number"
                            min="0"
                            step="0.1"
                            value={item.quantity}
                            onChange={(e) =>
                              handleUpdateQuantity(index, e.target.value)
                            }
                            className="w-16 text-center mx-2"
                          />
                          <Button
                            variant="outline"
                            size="icon"
                            className="h-7 w-7"
                            onClick={() =>
                              handleUpdateQuantity(index, item.quantity + 1)
                            }
                          >
                            <span className="sr-only">Increase</span>
                            <span>+</span>
                          </Button>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-7 text-destructive hover:text-destructive"
                          onClick={() => handleRemoveService(index)}
                        >
                          <Trash2 className="h-4 w-4 mr-1" />
                          Hapus
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
                <Button
                  variant="outline"
                  className="w-full"
                  onClick={() => setIsServiceSheetOpen(true)}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Tambah Item
                </Button>
              </div>
            ) : (
              <Button
                variant="outline"
                className="w-full"
                onClick={() => setIsServiceSheetOpen(true)}
              >
                <Plus className="h-4 w-4 mr-2" />
                Tambah Item
              </Button>
            )}
          </CardContent>
        </Card>

        {/* Order Details */}
        <Card>
          <CardHeader className="pb-2 pt-4 px-4">
            <h2 className="text-base font-semibold">Detail Pesanan</h2>
          </CardHeader>
          <CardContent className="px-4 pb-4 pt-0 space-y-4">
            {/* Perfume Selection */}
            <div className="space-y-2">
              <Label htmlFor="perfume" className="flex items-center gap-2">
                <Sparkles className="h-4 w-4 text-pink-500" />
                Parfum <span className="text-red-500">*</span>
              </Label>
              <Select
                value={orderDetails.perfume}
                onValueChange={(value) =>
                  setOrderDetails({ ...orderDetails, perfume: value })
                }
              >
                <SelectTrigger
                  className={!orderDetails.perfume ? 'border-red-300' : ''}
                >
                  <SelectValue placeholder="Pilih parfum (wajib)" />
                </SelectTrigger>
                <SelectContent>
                  {perfumes?.results.map((perfume) => (
                    <SelectItem key={perfume.id} value={perfume.name}>
                      {perfume.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {!orderDetails.perfume && (
                <p className="text-sm text-red-500">Parfum wajib dipilih</p>
              )}
            </div>

            {/* Pickup/Delivery Option */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label
                  htmlFor="pickup-delivery"
                  className="flex items-center gap-2"
                >
                  <Truck className="h-4 w-4 text-green-500" />
                  Antar Jemput
                </Label>
                <Switch
                  id="pickup-delivery"
                  checked={orderDetails.isPickupDelivery}
                  onCheckedChange={(checked) =>
                    setOrderDetails({
                      ...orderDetails,
                      isPickupDelivery: checked,
                    })
                  }
                />
              </div>
            </div>

            {/* Pickup/Delivery Details */}
            {orderDetails.isPickupDelivery && (
              <div className="space-y-4 border rounded-md p-4">
                {/* Pickup Option */}
                <div className="flex items-center justify-between">
                  <Label htmlFor="pickup" className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-blue-500" />
                    Jemput
                  </Label>
                  <Switch
                    id="pickup"
                    checked={orderDetails.isPickup}
                    onCheckedChange={(checked) =>
                      setOrderDetails({ ...orderDetails, isPickup: checked })
                    }
                  />
                </div>

                {/* Pickup Date/Time */}
                {orderDetails.isPickup && (
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="pickup-date">Tanggal Jemput</Label>
                      <Input
                        id="pickup-date"
                        type="date"
                        value={orderDetails.pickupDate}
                        onChange={(e) =>
                          setOrderDetails({
                            ...orderDetails,
                            pickupDate: e.target.value,
                          })
                        }
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="pickup-time">Waktu Jemput</Label>
                      <Input
                        id="pickup-time"
                        type="time"
                        value={orderDetails.pickupTime}
                        onChange={(e) =>
                          setOrderDetails({
                            ...orderDetails,
                            pickupTime: e.target.value,
                          })
                        }
                      />
                    </div>
                  </div>
                )}

                {/* Delivery Option */}
                <div className="flex items-center justify-between">
                  <Label htmlFor="delivery" className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-green-500" />
                    Antar
                  </Label>
                  <Switch
                    id="delivery"
                    checked={orderDetails.isDelivery}
                    onCheckedChange={(checked) =>
                      setOrderDetails({ ...orderDetails, isDelivery: checked })
                    }
                  />
                </div>

                {/* Delivery Details */}
                {orderDetails.isDelivery && (
                  <div className="space-y-4">
                    {/* Delivery Date/Time */}
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="delivery-date">Tanggal Antar</Label>
                        <Input
                          id="delivery-date"
                          type="date"
                          value={orderDetails.deliveryDate}
                          onChange={(e) =>
                            setOrderDetails({
                              ...orderDetails,
                              deliveryDate: e.target.value,
                            })
                          }
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="delivery-time">Waktu Antar</Label>
                        <Input
                          id="delivery-time"
                          type="time"
                          value={orderDetails.deliveryTime}
                          onChange={(e) =>
                            setOrderDetails({
                              ...orderDetails,
                              deliveryTime: e.target.value,
                            })
                          }
                        />
                      </div>
                    </div>
                  </div>
                )}
                {/* Alamat Pengiriman */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label className="flex items-center gap-2">
                      <MapPin className="h-4 w-4 text-blue-500" />
                      Alamat Pengiriman
                    </Label>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        setNewAddress(selectedCustomer?.address || '');
                        setNewMapsUrl(orderDetails.deliveryMapsUrl || '');
                        setIsAddressSheetOpen(true);
                      }}
                    >
                      Ganti Alamat
                    </Button>
                  </div>
                  <div className="p-3 border rounded-md">
                    <div className="text-sm">{selectedCustomer?.address}</div>
                    <div className="text-sm text-muted-foreground mt-1">
                      Jarak: {calculateDistance()} km
                    </div>
                  </div>
                  {/* Maps Buttons */}
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      className="flex-1"
                      onClick={handleOpenMaps}
                    >
                      <Map className="h-4 w-4 mr-2" />
                      Buka Maps
                    </Button>
                  </div>
                </div>
              </div>
            )}

            {/* Promo Selection */}
            <div className="space-y-2">
              <Label className="flex items-center gap-2">
                <Tag className="h-4 w-4 text-amber-500" />
                Promo
              </Label>
              {validatedPromotion ? (
                <div className="flex items-center justify-between border rounded-md p-3">
                  <div>
                    <div className="font-medium">
                      {validatedPromotion.promotion?.code}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {validatedPromotion.promotion?.description}
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsPromoSheetOpen(true)}
                  >
                    Ganti
                  </Button>
                </div>
              ) : (
                <Button
                  variant="outline"
                  className="w-full"
                  onClick={() => setIsPromoSheetOpen(true)}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Pilih Promo
                </Button>
              )}
            </div>

            {/* Payment Method */}
            <div className="space-y-2">
              <Label
                htmlFor="payment-method"
                className="flex items-center gap-2"
              >
                <CreditCard className="h-4 w-4 text-blue-500" />
                Metode Pembayaran
              </Label>
              <Button
                variant="outline"
                className="w-full justify-between"
                onClick={() => setIsPaymentSheetOpen(true)}
              >
                <span>
                  {orderDetails.paymentMethod === 'later'
                    ? 'Bayar Nanti'
                    : orderDetails.paymentMethod === 'cash'
                    ? 'Tunai'
                    : orderDetails.paymentMethod === 'non-cash'
                    ? 'Non Tunai'
                    : 'Deposit'}
                </span>
                <ChevronRight className="h-4 w-4 text-muted-foreground" />
              </Button>
            </div>

            {/* Notes */}
            <div className="space-y-2">
              <Label htmlFor="notes" className="flex items-center gap-2">
                <StickyNote className="h-4 w-4 text-amber-500" />
                Catatan
              </Label>
              <Textarea
                id="notes"
                placeholder="Tambahkan catatan untuk pesanan ini..."
                value={orderDetails.notes}
                onChange={(e) =>
                  setOrderDetails({ ...orderDetails, notes: e.target.value })
                }
                rows={3}
              />
            </div>
          </CardContent>
        </Card>

        {/* Order Summary */}
        <Card>
          <CardContent className="p-4">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Subtotal</span>
                <span>Rp {subtotal.toLocaleString()}</span>
              </div>

              {selectedPromo && selectedPromo.isValid && (
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">
                    Diskon ({selectedPromo.promotion?.code || 'Promo'})
                  </span>
                  <span className="text-green-600">
                    - Rp {discount.toLocaleString()}
                  </span>
                </div>
              )}

              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">
                  Biaya Antar Jemput
                </span>
                <span>Rp 0</span>
              </div>

              <div className="flex justify-between font-medium text-base pt-2 border-t mt-2">
                <span>Total</span>
                <span>Rp {totalPrice.toLocaleString()}</span>
              </div>

              <div className="flex justify-between text-sm pt-2">
                <span className="text-muted-foreground">
                  Estimasi Selesai
                  {orderDetails.items.length > 0 && (
                    <span className="block text-xs">
                      (Mulai: {getCurrentTime()}, Durasi:{' '}
                      {Math.max(
                        ...orderDetails.items.map((item) => item.duration)
                      )}{' '}
                      jam)
                    </span>
                  )}
                </span>
                <span className="text-right">
                  {calculateEstimatedCompletion()}
                </span>
              </div>

              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Pembayaran</span>
                <span>
                  {orderDetails.paymentMethod === 'later'
                    ? 'Bayar Nanti'
                    : orderDetails.paymentMethod === 'cash'
                    ? 'Tunai'
                    : orderDetails.paymentMethod === 'non-cash'
                    ? selectedCashboxId && cashboxes
                      ? cashboxes.find((c) => c.id === selectedCashboxId)
                          ?.name || 'Non Tunai'
                      : 'Non Tunai'
                    : 'Deposit'}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Submit Button */}
        <Button
          className="w-full h-12 text-base font-medium"
          disabled={
            !orderDetails.customerId ||
            orderDetails.items.length === 0 ||
            !orderDetails.perfume ||
            createOrderMutation.isPending
          }
          onClick={handleCreateOrder}
        >
          {createOrderMutation.isPending
            ? 'Membuat Pesanan...'
            : 'Buat Pesanan'}
        </Button>
      </div>

      {/* Service Selection Sheet */}
      <Sheet open={isServiceSheetOpen} onOpenChange={setIsServiceSheetOpen}>
        <SheetContent side="top" className="h-[80vh] max-h-[80vh]">
          <SheetHeader className="mb-4">
            <SheetTitle>Pilih Layanan</SheetTitle>
          </SheetHeader>

          <div className="relative mb-4">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Cari layanan..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>

          {selectedVariant ? (
            <div className="space-y-4">
              <div className="p-3 border rounded-md">
                <div className="font-medium">{selectedVariant.name}</div>
                <div className="text-sm text-muted-foreground">
                  {selectedCategory?.name}
                </div>
                <div className="text-sm font-medium mt-1">
                  Rp {selectedVariant.price.toLocaleString()} /{' '}
                  {selectedVariant.unit}
                </div>
              </div>

              <div className="space-y-2">
                <Label>Jumlah</Label>
                <div className="flex items-center">
                  <Button
                    variant="outline"
                    size="icon"
                    className="h-9 w-9"
                    onClick={() => {
                      const currentValue = parseFloat(variantQuantity);
                      if (!isNaN(currentValue)) {
                        const newValue = Math.max(0, currentValue - 1);
                        handleVariantQuantityChange(newValue.toFixed(1));
                      }
                    }}
                  >
                    <span>-</span>
                  </Button>
                  <Input
                    type="number"
                    min="0"
                    step="0.1"
                    value={variantQuantity}
                    onChange={(e) =>
                      handleVariantQuantityChange(e.target.value)
                    }
                    className="w-16 text-center mx-2"
                  />
                  <Button
                    variant="outline"
                    size="icon"
                    className="h-9 w-9"
                    onClick={() => {
                      const currentValue = parseFloat(variantQuantity);
                      if (!isNaN(currentValue)) {
                        const newValue = currentValue + 1;
                        handleVariantQuantityChange(newValue.toFixed(1));
                      }
                    }}
                  >
                    <span>+</span>
                  </Button>
                </div>
              </div>

              <div className="flex justify-between font-medium">
                <span>Total</span>
                <span>
                  Rp{' '}
                  {(
                    selectedVariant.price * parseFloat(variantQuantity)
                  ).toLocaleString()}
                </span>
              </div>

              <div className="flex gap-2 mt-4">
                <Button
                  variant="outline"
                  className="flex-1"
                  onClick={() => setSelectedVariant(null)}
                >
                  <X className="h-4 w-4 mr-2" />
                  Batal
                </Button>
                <Button className="flex-1" onClick={handleAddService}>
                  <Check className="h-4 w-4 mr-2" />
                  Tambah
                </Button>
              </div>
            </div>
          ) : (
            <div className="overflow-y-auto max-h-[calc(80vh-10rem)]">
              <Tabs defaultValue="24" className="w-full">
                <TabsList className="grid grid-cols-4 mb-4">
                  <TabsTrigger value="6">6 Jam</TabsTrigger>
                  <TabsTrigger value="24">24 Jam</TabsTrigger>
                  <TabsTrigger value="48">48 Jam</TabsTrigger>
                  <TabsTrigger value="72">72 Jam</TabsTrigger>
                </TabsList>
                <TabsContent value="6">
                  {createServiceTabContent(6)}
                </TabsContent>
                <TabsContent value="24">
                  {createServiceTabContent(24)}
                </TabsContent>
                <TabsContent value="48">
                  {createServiceTabContent(48)}
                </TabsContent>
                <TabsContent value="72">
                  {createServiceTabContent(72)}
                </TabsContent>
              </Tabs>
            </div>
          )}
        </SheetContent>
      </Sheet>

      {/* Promo Selection Sheet */}
      <Sheet open={isPromoSheetOpen} onOpenChange={setIsPromoSheetOpen}>
        <SheetContent side="bottom" className="h-[60vh] max-h-[60vh]">
          <SheetHeader className="mb-4">
            <SheetTitle>Pilih Promo</SheetTitle>
          </SheetHeader>

          <div className="space-y-3 overflow-y-auto max-h-[calc(60vh-8rem)]">
            {promotions?.map((promo) => (
              <div
                key={promo.id}
                className="p-3 border rounded-md cursor-pointer hover:bg-muted/50"
                onClick={() => handleSelectPromo(promo.code)}
              >
                <div className="font-medium">{promo.code}</div>
                <div className="text-sm text-muted-foreground">
                  {promo.description}
                </div>
                <div className="text-sm font-medium text-green-600 mt-1">
                  {promo.discountType === 'PERCENTAGE'
                    ? `${promo.discountValue}% off`
                    : `Rp ${promo.discountValue.toLocaleString()} off`}
                </div>
              </div>
            ))}
          </div>

          <SheetFooter className="mt-4">
            <Button
              variant="outline"
              className="w-full"
              onClick={() => {
                setOrderDetails({ ...orderDetails, promoId: null });
                setIsPromoSheetOpen(false);
              }}
            >
              Hapus Promo
            </Button>
          </SheetFooter>
        </SheetContent>
      </Sheet>

      {/* Payment Method Sheet */}
      <Sheet open={isPaymentSheetOpen} onOpenChange={setIsPaymentSheetOpen}>
        <SheetContent side="bottom" className="h-[70vh] max-h-[70vh]">
          <SheetHeader className="mb-4">
            <SheetTitle>Metode Pembayaran</SheetTitle>
          </SheetHeader>

          <div className="flex border-b mb-4">
            <button
              className={`flex-1 py-3 text-center font-medium ${
                selectedPaymentTab === 'cash'
                  ? 'text-blue-500 border-b-2 border-blue-500'
                  : 'text-muted-foreground'
              }`}
              onClick={() => setSelectedPaymentTab('cash')}
            >
              Tunai
            </button>
            <button
              className={`flex-1 py-3 text-center font-medium ${
                selectedPaymentTab === 'non-cash'
                  ? 'text-blue-500 border-b-2 border-blue-500'
                  : 'text-muted-foreground'
              }`}
              onClick={() => setSelectedPaymentTab('non-cash')}
            >
              Non Tunai
            </button>
            <button
              className={`flex-1 py-3 text-center font-medium ${
                selectedPaymentTab === 'deposit'
                  ? 'text-blue-500 border-b-2 border-blue-500'
                  : 'text-muted-foreground'
              }`}
              onClick={() => setSelectedPaymentTab('deposit')}
            >
              Deposit
            </button>
          </div>

          {selectedPaymentTab === 'cash' && (
            <div className="space-y-4">
              <div>
                <p className="text-sm mb-2">Jumlah yang dibayarkan</p>
                <div className="grid grid-cols-2 gap-3">
                  <Button
                    variant="outline"
                    className="h-12 text-blue-500 border-blue-500"
                    onClick={() => {
                      setOrderDetails({
                        ...orderDetails,
                        paymentMethod: 'cash',
                      });
                      setSelectedCashboxId(null);
                      setIsPaymentSheetOpen(false);
                    }}
                  >
                    Uang Pas
                  </Button>
                  <Button
                    variant="outline"
                    className="h-12 text-blue-500 border-blue-500"
                    onClick={() => {
                      setOrderDetails({
                        ...orderDetails,
                        paymentMethod: 'cash',
                      });
                      setSelectedCashboxId(null);
                      setIsPaymentSheetOpen(false);
                    }}
                  >
                    35,000
                  </Button>
                  <Button
                    variant="outline"
                    className="h-12 text-blue-500 border-blue-500"
                    onClick={() => {
                      setOrderDetails({
                        ...orderDetails,
                        paymentMethod: 'cash',
                      });
                      setSelectedCashboxId(null);
                      setIsPaymentSheetOpen(false);
                    }}
                  >
                    40,000
                  </Button>
                  <Button
                    variant="outline"
                    className="h-12 text-blue-500 border-blue-500"
                    onClick={() => {
                      setOrderDetails({
                        ...orderDetails,
                        paymentMethod: 'cash',
                      });
                      setSelectedCashboxId(null);
                      setIsPaymentSheetOpen(false);
                    }}
                  >
                    50,000
                  </Button>
                </div>
              </div>
              <div>
                <p className="text-sm mb-2">Jumlah Lain</p>
                <div className="flex gap-3">
                  <Input className="h-12" placeholder="Masukkan jumlah" />
                  <Button
                    className="h-12 px-6"
                    onClick={() => {
                      setOrderDetails({
                        ...orderDetails,
                        paymentMethod: 'cash',
                      });
                      setSelectedCashboxId(null);
                      setIsPaymentSheetOpen(false);
                    }}
                  >
                    Bayar
                  </Button>
                </div>
              </div>
              <p className="text-xs text-muted-foreground mt-2">
                * Pembayaran yang diterima kurang dari total tagihan akan
                menjadi pembayaran DP
              </p>
            </div>
          )}

          {selectedPaymentTab === 'non-cash' && (
            <div className="space-y-3">
              <p className="text-sm text-muted-foreground mb-1">
                Pilih Pembayaran
              </p>
              <p className="text-xs text-muted-foreground mb-3">
                Kamu bisa menambahkan cashboxmu sendiri di menu kelola keuangan
              </p>
              {cashboxesLoading ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto"></div>
                  <p className="text-sm text-muted-foreground mt-2">
                    Memuat cashbox...
                  </p>
                </div>
              ) : cashboxesError ? (
                <div className="text-center py-8 text-muted-foreground">
                  <p className="text-sm text-red-500">
                    Error: {cashboxesError.message}
                  </p>
                  <p className="text-xs mt-1">Gagal memuat data cashbox</p>
                </div>
              ) : cashboxes && cashboxes.length > 0 ? (
                cashboxes
                  .filter(
                    (cashbox) =>
                      cashbox.type === 'NON_TUNAI' && cashbox.isActive
                  )
                  .map((cashbox) => (
                    <Button
                      key={cashbox.id}
                      variant="outline"
                      className="w-full justify-between h-14 text-left"
                      onClick={() => {
                        setOrderDetails({
                          ...orderDetails,
                          paymentMethod: 'non-cash',
                        });
                        setSelectedCashboxId(cashbox.id);
                        setIsPaymentSheetOpen(false);
                      }}
                    >
                      <div className="flex items-center gap-3">
                        <span className="text-blue-500 font-medium">
                          {cashbox.name.substring(0, 3).toUpperCase()}
                        </span>
                        <span>{cashbox.name}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-muted-foreground">
                          Rp {cashbox.balance.toLocaleString()}
                        </span>
                        <ChevronRight className="h-4 w-4 text-muted-foreground" />
                      </div>
                    </Button>
                  ))
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <p className="text-sm">
                    Tidak ada cashbox non tunai tersedia
                  </p>
                  <p className="text-xs mt-1">
                    Silakan tambahkan cashbox di menu kelola keuangan
                  </p>
                </div>
              )}
            </div>
          )}

          {selectedPaymentTab === 'deposit' && (
            <div className="space-y-4">
              <div>
                <p className="text-sm mb-2">Saldo Deposit</p>
                <p className="text-3xl font-bold">0</p>
              </div>
              <div className="flex gap-3">
                <Button variant="outline" className="flex-1 h-12 text-blue-500">
                  + TopUp
                </Button>
                <Button
                  className="flex-1 h-12"
                  onClick={() => {
                    setOrderDetails({
                      ...orderDetails,
                      paymentMethod: 'deposit',
                    });
                    setSelectedCashboxId(null);
                    setIsPaymentSheetOpen(false);
                  }}
                >
                  Bayar
                </Button>
              </div>
            </div>
          )}

          <div className="mt-6">
            <Button
              variant="outline"
              className="w-full"
              onClick={() => {
                setOrderDetails({ ...orderDetails, paymentMethod: 'later' });
                setSelectedCashboxId(null);
                setIsPaymentSheetOpen(false);
              }}
            >
              Bayar Nanti
            </Button>
          </div>
        </SheetContent>
      </Sheet>

      {/* Address Sheet */}
      <Sheet open={isAddressSheetOpen} onOpenChange={setIsAddressSheetOpen}>
        <SheetContent side="bottom" className="h-[70vh] max-h-[70vh]">
          <SheetHeader className="mb-4">
            <SheetTitle>Ganti Alamat Pengiriman</SheetTitle>
          </SheetHeader>

          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="address">Alamat</Label>
              <Textarea
                id="address"
                value={newAddress}
                onChange={(e) => setNewAddress(e.target.value)}
                placeholder="Masukkan alamat lengkap"
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="maps-url">URL Google Maps</Label>
              <div className="flex gap-2">
                <Input
                  id="maps-url"
                  value={newMapsUrl}
                  onChange={(e) => setNewMapsUrl(e.target.value)}
                  placeholder="Masukkan URL Google Maps"
                />
                <Button
                  variant="outline"
                  className="flex-1"
                  onClick={() => {
                    if (newMapsUrl) {
                      window.open(newMapsUrl, '_blank');
                    }
                  }}
                >
                  <Map className="h-4 w-4 mr-2" />
                  Buka Maps
                </Button>
              </div>
            </div>

            <div className="flex gap-2">
              <Button className="flex-1" onClick={handleAddressChange}>
                Simpan Alamat
              </Button>
            </div>
          </div>
        </SheetContent>
      </Sheet>
    </div>
  );
}
