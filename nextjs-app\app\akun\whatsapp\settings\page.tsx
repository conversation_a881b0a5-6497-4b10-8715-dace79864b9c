"use client"

import { useState } from "react"
import Link from "next/link"
import { ArrowLeft, Save, RefreshCw, Smartphone } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

export default function WhatsAppSettingsPage() {
  const [settings, setSettings] = useState({
    apiKey: "wha_123456789abcdef",
    phoneNumber: "+628123456789",
    businessName: "Laundry App",
    businessDescription: "Layanan laundry terbaik di kota Anda",
    signature: "<PERSON>nd<PERSON> App",
    dailyLimit: "1000",
    notifyAdmin: true,
    adminPhone: "+628987654321",
    failureRetry: true,
    maxRetries: "3",
    retryInterval: "30",
    logMessages: true,
    messageExpiry: "24",
  })

  const handleSwitchChange = (key: string, value: boolean) => {
    setSettings({
      ...settings,
      [key]: value,
    })
  }

  const handleInputChange = (key: string, value: string) => {
    setSettings({
      ...settings,
      [key]: value,
    })
  }

  const handleSave = () => {
    alert("Pengaturan berhasil disimpan!")
  }

  const handleReconnect = () => {
    alert("Menghubungkan kembali ke WhatsApp...")
  }

  return (
    <div className="flex flex-col min-h-screen bg-slate-50">
      <header className="p-4 flex justify-between items-center bg-white sticky top-0 z-10 shadow-sm">
        <div className="flex items-center">
          <Link href="/akun/whatsapp" className="mr-3">
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <h1 className="text-lg font-semibold">Pengaturan WhatsApp</h1>
        </div>
        <Button onClick={handleSave} className="bg-green-500 hover:bg-green-600">
          <Save className="h-4 w-4 mr-1" /> Simpan
        </Button>
      </header>

      <div className="p-4 pb-20">
        <Tabs defaultValue="account">
          <TabsList className="grid grid-cols-3 mb-4">
            <TabsTrigger value="account">Akun</TabsTrigger>
            <TabsTrigger value="messages">Pesan</TabsTrigger>
            <TabsTrigger value="advanced">Lanjutan</TabsTrigger>
          </TabsList>

          <TabsContent value="account" className="space-y-4">
            <Card>
              <CardContent className="p-4 space-y-4">
                <div className="flex justify-between items-center">
                  <div>
                    <h3 className="font-medium">Status Koneksi</h3>
                    <p className="text-sm text-green-500">Terhubung</p>
                  </div>
                  <Button variant="outline" size="sm" onClick={handleReconnect}>
                    <RefreshCw className="h-4 w-4 mr-1" /> Hubungkan Ulang
                  </Button>
                </div>

                <Separator />

                <div className="space-y-2">
                  <Label htmlFor="apiKey">API Key</Label>
                  <Input
                    id="apiKey"
                    value={settings.apiKey}
                    onChange={(e) => handleInputChange("apiKey", e.target.value)}
                    type="password"
                  />
                  <p className="text-xs text-gray-500">API key untuk mengakses layanan WhatsApp Business API</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="phoneNumber">Nomor Telepon</Label>
                  <Input
                    id="phoneNumber"
                    value={settings.phoneNumber}
                    onChange={(e) => handleInputChange("phoneNumber", e.target.value)}
                  />
                  <p className="text-xs text-gray-500">Nomor telepon yang terdaftar di WhatsApp Business</p>
                </div>

                <div className="flex justify-center">
                  <Button variant="outline" className="w-full max-w-xs">
                    <Smartphone className="h-4 w-4 mr-2" /> Scan QR Code
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4 space-y-4">
                <h3 className="font-medium">Profil Bisnis</h3>

                <div className="space-y-2">
                  <Label htmlFor="businessName">Nama Bisnis</Label>
                  <Input
                    id="businessName"
                    value={settings.businessName}
                    onChange={(e) => handleInputChange("businessName", e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="businessDescription">Deskripsi Bisnis</Label>
                  <Input
                    id="businessDescription"
                    value={settings.businessDescription}
                    onChange={(e) => handleInputChange("businessDescription", e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="signature">Tanda Tangan Pesan</Label>
                  <Input
                    id="signature"
                    value={settings.signature}
                    onChange={(e) => handleInputChange("signature", e.target.value)}
                  />
                  <p className="text-xs text-gray-500">Akan ditambahkan di akhir setiap pesan</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="messages" className="space-y-4">
            <Card>
              <CardContent className="p-4 space-y-4">
                <h3 className="font-medium">Batas Pengiriman</h3>

                <div className="space-y-2">
                  <Label htmlFor="dailyLimit">Batas Harian</Label>
                  <Input
                    id="dailyLimit"
                    type="number"
                    value={settings.dailyLimit}
                    onChange={(e) => handleInputChange("dailyLimit", e.target.value)}
                  />
                  <p className="text-xs text-gray-500">Jumlah maksimum pesan yang dapat dikirim per hari</p>
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="notifyAdmin" className="cursor-pointer">
                    <div>Notifikasi Admin</div>
                    <p className="text-sm text-gray-500">Beri tahu admin saat batas hampir tercapai</p>
                  </Label>
                  <Switch
                    id="notifyAdmin"
                    checked={settings.notifyAdmin}
                    onCheckedChange={(checked) => handleSwitchChange("notifyAdmin", checked)}
                  />
                </div>

                {settings.notifyAdmin && (
                  <div className="space-y-2">
                    <Label htmlFor="adminPhone">Nomor Admin</Label>
                    <Input
                      id="adminPhone"
                      value={settings.adminPhone}
                      onChange={(e) => handleInputChange("adminPhone", e.target.value)}
                    />
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4 space-y-4">
                <h3 className="font-medium">Penanganan Kegagalan</h3>

                <div className="flex items-center justify-between">
                  <Label htmlFor="failureRetry" className="cursor-pointer">
                    <div>Coba Ulang Otomatis</div>
                    <p className="text-sm text-gray-500">Coba kirim ulang pesan yang gagal</p>
                  </Label>
                  <Switch
                    id="failureRetry"
                    checked={settings.failureRetry}
                    onCheckedChange={(checked) => handleSwitchChange("failureRetry", checked)}
                  />
                </div>

                {settings.failureRetry && (
                  <>
                    <div className="space-y-2">
                      <Label htmlFor="maxRetries">Jumlah Percobaan Maksimum</Label>
                      <Input
                        id="maxRetries"
                        type="number"
                        value={settings.maxRetries}
                        onChange={(e) => handleInputChange("maxRetries", e.target.value)}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="retryInterval">Interval Percobaan (menit)</Label>
                      <Input
                        id="retryInterval"
                        type="number"
                        value={settings.retryInterval}
                        onChange={(e) => handleInputChange("retryInterval", e.target.value)}
                      />
                    </div>
                  </>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="advanced" className="space-y-4">
            <Card>
              <CardContent className="p-4 space-y-4">
                <h3 className="font-medium">Pencatatan & Penyimpanan</h3>

                <div className="flex items-center justify-between">
                  <Label htmlFor="logMessages" className="cursor-pointer">
                    <div>Catat Semua Pesan</div>
                    <p className="text-sm text-gray-500">Simpan riwayat semua pesan yang dikirim</p>
                  </Label>
                  <Switch
                    id="logMessages"
                    checked={settings.logMessages}
                    onCheckedChange={(checked) => handleSwitchChange("logMessages", checked)}
                  />
                </div>

                {settings.logMessages && (
                  <div className="space-y-2">
                    <Label htmlFor="messageExpiry">Masa Berlaku Log (jam)</Label>
                    <Input
                      id="messageExpiry"
                      type="number"
                      value={settings.messageExpiry}
                      onChange={(e) => handleInputChange("messageExpiry", e.target.value)}
                    />
                    <p className="text-xs text-gray-500">Berapa lama log pesan disimpan sebelum dihapus otomatis</p>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4 space-y-4">
                <h3 className="font-medium">Integrasi</h3>

                <div className="space-y-2">
                  <Label htmlFor="webhookUrl">URL Webhook</Label>
                  <Input id="webhookUrl" placeholder="https://example.com/webhook" />
                  <p className="text-xs text-gray-500">URL untuk menerima notifikasi pesan masuk</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="webhookSecret">Webhook Secret</Label>
                  <Input id="webhookSecret" type="password" placeholder="••••••••••••••••" />
                </div>

                <Button variant="outline" className="w-full">
                  Uji Webhook
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4 space-y-4">
                <h3 className="font-medium">Tindakan</h3>

                <Button variant="outline" className="w-full">
                  Ekspor Data Pesan
                </Button>

                <Button variant="outline" className="w-full text-red-500 hover:text-red-600">
                  Reset Pengaturan
                </Button>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
