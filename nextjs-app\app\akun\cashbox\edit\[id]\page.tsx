'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Arrow<PERSON>eft, Loader2, Save } from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';

import {
  useCashbox,
  useUpdateCashbox,
  getCashboxErrorMessage,
  getCashboxFieldErrors,
} from '@/hooks/useCashbox';
import type { UpdateCashboxRequest } from '@/lib/api/cashbox';
import { ensureAuthSync } from '@/lib/auth-sync';
import { toast } from '@/components/ui/use-toast';

interface FormData {
  name: string;
  type: 'TUNAI' | 'NON_TUNAI';
  isActive: boolean;
}

interface FormErrors {
  name?: string;
  type?: string;
  general?: string;
}

export default function EditCashboxPage() {
  const router = useRouter();
  const params = useParams();
  const cashboxId = parseInt(params.id as string);

  const [authError, setAuthError] = useState(false);
  const [formData, setFormData] = useState<FormData>({
    name: '',
    type: 'TUNAI',
    isActive: true,
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [isFormReady, setIsFormReady] = useState(false);

  const {
    data: cashbox,
    isLoading: cashboxLoading,
    error: cashboxError,
  } = useCashbox(cashboxId);

  const updateCashboxMutation = useUpdateCashbox();

  // Ensure auth sync on component mount
  useEffect(() => {
    ensureAuthSync();

    // Check if user is properly authenticated
    const checkAuth = () => {
      const user = localStorage.getItem('user');
      const tokens = localStorage.getItem('tokens');

      if (!user || !tokens) {
        console.log('No auth data found');
        setAuthError(true);
        return;
      }

      try {
        const parsedTokens = JSON.parse(tokens);
        const accessToken = parsedTokens?.access?.token;

        if (!accessToken) {
          console.log('No access token found');
          setAuthError(true);
          return;
        }

        setAuthError(false);
      } catch (error) {
        console.error('Error parsing auth data:', error);
        setAuthError(true);
      }
    };

    checkAuth();
  }, [router]);

  // Populate form when cashbox data is loaded
  useEffect(() => {
    if (cashbox && !isFormReady) {
      setFormData({
        name: cashbox.name,
        type: cashbox.type,
        isActive: cashbox.isActive,
      });
      setIsFormReady(true);
    }
  }, [cashbox, isFormReady]);

  const handleInputChange = (
    field: keyof FormData,
    value: string | boolean
  ) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field as keyof FormErrors]) {
      setErrors((prev) => ({ ...prev, [field]: undefined }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // Validate name
    if (!formData.name.trim()) {
      newErrors.name = 'Nama cashbox wajib diisi';
    } else if (formData.name.trim().length < 2) {
      newErrors.name = 'Nama cashbox minimal 2 karakter';
    } else if (formData.name.trim().length > 100) {
      newErrors.name = 'Nama cashbox maksimal 100 karakter';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      const requestData: UpdateCashboxRequest = {
        name: formData.name.trim(),
        type: formData.type,
        isActive: formData.isActive,
      };

      await updateCashboxMutation.mutateAsync({
        id: cashboxId,
        data: requestData,
      });
      router.push(`/akun/cashbox/${cashboxId}`);
    } catch (error: unknown) {
      console.error('Error updating cashbox:', error);

      // Extract field errors
      const fieldErrors = getCashboxFieldErrors(error);
      if (Object.keys(fieldErrors).length > 0) {
        setErrors(fieldErrors);
      } else {
        // Show general error
        const errorMessage = getCashboxErrorMessage(error);
        setErrors({ general: errorMessage });
        toast({
          title: 'Error',
          description: errorMessage,
          variant: 'destructive',
        });
      }
    }
  };

  const handleLoginRedirect = () => {
    localStorage.clear();
    router.push('/auth/login');
  };

  if (authError) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card className="max-w-md mx-auto">
          <CardContent className="pt-6">
            <div className="text-center">
              <h2 className="text-lg font-semibold mb-2">Sesi Berakhir</h2>
              <p className="text-gray-600 mb-4">
                Silakan login kembali untuk melanjutkan.
              </p>
              <Button onClick={handleLoginRedirect} className="w-full">
                Login
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (cashboxLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center py-12">
          <Loader2 className="w-8 h-8 animate-spin" />
        </div>
      </div>
    );
  }

  if (cashboxError || !cashbox) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card className="max-w-md mx-auto">
          <CardContent className="pt-6">
            <div className="text-center">
              <h2 className="text-lg font-semibold mb-2">Terjadi Kesalahan</h2>
              <p className="text-gray-600 mb-4">
                Gagal memuat data cashbox. Silakan coba lagi.
              </p>
              <Link href="/akun/cashbox">
                <Button className="w-full">Kembali ke Daftar Cashbox</Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-6">
      {/* Header - Mobile Responsive */}
      <div className="flex items-center gap-3 mb-6">
        <Link href={`/akun/cashbox/${cashboxId}`}>
          <Button variant="ghost" size="sm" className="p-2">
            <ArrowLeft className="w-4 h-4" />
          </Button>
        </Link>
        <div className="min-w-0 flex-1">
          <h1 className="text-xl sm:text-2xl font-bold">Edit Cashbox</h1>
          <p className="text-sm text-muted-foreground truncate">
            Perbarui informasi cashbox {cashbox.name}
          </p>
        </div>
      </div>

      {/* Form */}
      <div className="max-w-2xl">
        <Card>
          <CardHeader>
            <CardTitle>Informasi Cashbox</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* General Error */}
              {errors.general && (
                <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                  <p className="text-red-700 text-sm">{errors.general}</p>
                </div>
              )}

              {/* Current Balance Info */}
              <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
                <p className="text-sm text-blue-700">
                  <strong>Saldo saat ini:</strong>{' '}
                  {new Intl.NumberFormat('id-ID', {
                    style: 'currency',
                    currency: 'IDR',
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 0,
                  }).format(cashbox.balance)}
                </p>
                <p className="text-xs text-blue-600 mt-1">
                  Saldo tidak dapat diubah melalui form edit. Gunakan fitur
                  "Sesuaikan Saldo" untuk mengubah saldo.
                </p>
              </div>

              {/* Name */}
              <div className="space-y-2">
                <Label htmlFor="name">
                  Nama Cashbox <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="name"
                  type="text"
                  placeholder="Contoh: Kas Utama, BCA, Gopay"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  className={errors.name ? 'border-red-500' : ''}
                />
                {errors.name && (
                  <p className="text-red-500 text-sm">{errors.name}</p>
                )}
              </div>

              {/* Type */}
              <div className="space-y-3">
                <Label>
                  Tipe Cashbox <span className="text-red-500">*</span>
                </Label>
                <RadioGroup
                  value={formData.type}
                  onValueChange={(value) =>
                    handleInputChange('type', value as 'TUNAI' | 'NON_TUNAI')
                  }
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="TUNAI" id="type-tunai" />
                    <Label htmlFor="type-tunai" className="font-normal">
                      Tunai
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="NON_TUNAI" id="type-non-tunai" />
                    <Label htmlFor="type-non-tunai" className="font-normal">
                      Non Tunai (Transfer, E-Wallet, dll)
                    </Label>
                  </div>
                </RadioGroup>
                {errors.type && (
                  <p className="text-red-500 text-sm">{errors.type}</p>
                )}
              </div>

              <Separator />

              {/* Active Status */}
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Status Aktif</Label>
                  <p className="text-sm text-gray-500">
                    Cashbox aktif dapat digunakan untuk transaksi
                  </p>
                </div>
                <Switch
                  checked={formData.isActive}
                  onCheckedChange={(checked) =>
                    handleInputChange('isActive', checked)
                  }
                />
              </div>

              <Separator />

              {/* Submit Buttons */}
              <div className="flex gap-3 pt-4">
                <Link href={`/akun/cashbox/${cashboxId}`}>
                  <Button type="button" variant="outline" className="flex-1">
                    Batal
                  </Button>
                </Link>
                <Button
                  type="submit"
                  disabled={updateCashboxMutation.isPending}
                  className="flex-1"
                >
                  {updateCashboxMutation.isPending ? (
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  ) : (
                    <Save className="w-4 h-4 mr-2" />
                  )}
                  Simpan Perubahan
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
