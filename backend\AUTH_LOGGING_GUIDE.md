# Auth Logging System Guide

## Overview

Sistem logging komprehensif telah diimplementasikan untuk semua fitur auth di backend untuk meningkatkan monitoring, debugging, dan keamanan aplikasi.

## Komponen yang Dilengkapi Logging

### 1. Auth Controller (`src/controllers/auth.controller.ts`)

**Semua endpoint auth dilengkapi dengan logging berikut:**

- **Request Tracking**: IP address, User Agent, timestamp
- **User Information**: Email, UserID, Role
- **Security Events**: Unverified email/phone warnings, failed attempts
- **Success/Failure Events**: Detailed error messages untuk debugging

### 2. Auth Service (`src/services/auth.service.ts`)

**Business logic level logging:**

- **Login Process**: User lookup, password verification, account status
- **Token Operations**: Token validation, refresh, cleanup
- **Password Reset**: Token generation, user verification
- **Email Verification**: Token validation, user status updates

### 3. Token Service (`src/services/token.service.ts`)

**Token lifecycle tracking:**

- **Token Generation**: UserID, Type, Expiration
- **Token Verification**: JWT validation, database lookup
- **Token Storage**: Database operations, blacklisting

### 4. Phone Verification Service (`src/services/phoneVerification.service.ts`)

**SMS verification flow:**

- **Code Generation**: Phone masking, expiration tracking
- **Code Verification**: Validation attempts, success/failure
- **Code Cleanup**: Expired code removal

### 5. Email Verification Service (`src/services/emailVerification.service.ts`)

**Email OTP verification:**

- **OTP Generation**: Email tracking, code expiration
- **OTP Verification**: Validation flow, user updates
- **Code Management**: Cleanup operations

## Log Levels Digunakan

### 🔍 DEBUG

Digunakan untuk detailed tracing dan debugging:

```
[AUTH_SERVICE] Starting login process for email: <EMAIL>
[TOKEN_SERVICE] JWT payload verified - UserID: 123, Type: ACCESS
```

### ℹ️ INFO

Untuk successful operations dan important events:

```
[AUTH] Login successful - UserID: 123, Email: <EMAIL>, Role: OWNER
[PHONE_VERIFICATION] Phone verification code sent successfully - Phone: 0812***
```

### ⚠️ WARN

Untuk security concerns dan potential issues:

```
[AUTH] Login failed - Email: <EMAIL>, Error: Incorrect email or password
[AUTH_SERVICE] User logged in with unverified email - UserID: 123
```

### ❌ ERROR

Untuk system errors dan critical failures:

```
[AUTH] Registration failed - Email: <EMAIL>, Error: Email already exists
[TOKEN_SERVICE] Token generation failed - UserID: 123, Type: ACCESS
```

## Format Log

Semua log mengikuti format konsisten:

```
[COMPONENT] Action description - Key: Value, Key2: Value2
```

Contoh:

```
[AUTH] Login successful - UserID: 123, Email: <EMAIL>, Role: OWNER, IP: ***********
```

## Data Privacy

### Sensitive Data Masking

- **Email**: Ditampilkan lengkap (diperlukan untuk debugging)
- **Phone**: Hanya 4 digit pertama ditampilkan (`0812***`)
- **OTP/Code**: Hanya 2 digit pertama ditampilkan (`12***`)
- **Password**: Tidak pernah di-log

### IP Tracking

Semua auth requests mencatat:

- Client IP address
- User Agent
- Timestamp otomatis dari winston

## Monitoring Use Cases

### 1. Security Monitoring

```bash
# Monitor failed login attempts
grep "Login failed" logs/app.log

# Track unverified account logins
grep "unverified email" logs/app.log

# Monitor token-related security events
grep "Token verification failed" logs/app.log
```

### 2. Performance Monitoring

```bash
# Track auth operation completion
grep "successful" logs/app.log

# Monitor token generation performance
grep "Auth tokens generated" logs/app.log
```

### 3. Debugging Issues

```bash
# Debug specific user issues
grep "UserID: 123" logs/app.log

# Debug email verification issues
grep "EMAIL_VERIFICATION" logs/app.log

# Debug phone verification flow
grep "PHONE_VERIFICATION" logs/app.log
```

## Log Rotation & Management

### Development Environment

- Log level: `debug`
- Output: Console dengan warna
- Full OTP codes ditampilkan untuk testing

### Production Environment

- Log level: `info`
- Output: File-based logging
- OTP codes tidak ditampilkan

### Recommended Log Rotation

```javascript
// Tambahan konfigurasi untuk production
new winston.transports.File({
  filename: 'logs/auth.log',
  level: 'info',
  maxsize: 5242880, // 5MB
  maxFiles: 5,
  colorize: false
});
```

## Alert Configuration

### Critical Events untuk Alert

```javascript
// Events yang perlu immediate attention
const criticalEvents = [
  'Token generation failed',
  'Auth tokens generation failed',
  'Login error - UserID:',
  'Password reset failed',
  'Email verification failed'
];
```

### Suspicious Activity Patterns

```javascript
// Pattern untuk detect suspicious activity
const suspiciousPatterns = [
  'Multiple login failed attempts',
  'Invalid token verification attempts',
  'Repeated OTP failures'
];
```

## Integration dengan Monitoring Tools

### ELK Stack Integration

```javascript
// Structured logging untuk Elasticsearch
logger.info('Auth event', {
  component: 'AUTH',
  action: 'LOGIN_SUCCESS',
  userId: 123,
  email: '<EMAIL>',
  ip: '***********',
  userAgent: 'Mozilla/5.0...'
});
```

### Metrics Collection

- Login success/failure rates
- Token refresh frequency
- Verification attempt patterns
- Geographic login distribution

## Best Practices

### 1. Log Standardization

- Gunakan prefix `[COMPONENT]` yang konsisten
- Format key-value yang readable
- Consistent timestamp handling

### 2. Security Considerations

- Mask sensitive data appropriately
- Log security events dengan context
- Track IP addresses untuk forensics

### 3. Performance Impact

- Use appropriate log levels
- Async logging untuk high-traffic endpoints
- Regular log cleanup

### 4. Debugging Support

- Include correlation IDs
- Log request/response flow
- Detailed error context

## Contoh Log Output

### Successful Login Flow

```
[AUTH] Login attempt - Email: <EMAIL>, IP: ***********, UserAgent: Mozilla/5.0...
[AUTH_SERVICE] Starting login process for email: <EMAIL>
[AUTH_SERVICE] User found - UserID: 123, Email: <EMAIL>, Role: OWNER, IsActive: true
[AUTH_SERVICE] Login successful - UserID: 123, Email: <EMAIL>, Role: OWNER
[TOKEN_SERVICE] Starting auth tokens generation - UserID: 123
[TOKEN_SERVICE] Access token generated - UserID: 123, ExpiresAt: 2024-01-15T10:30:00.000Z
[TOKEN_SERVICE] Refresh token generated and saved - UserID: 123, ExpiresAt: 2024-01-22T09:30:00.000Z
[AUTH] Login successful - UserID: 123, Email: <EMAIL>, Role: OWNER, IP: ***********
```

### Failed Authentication

```
[AUTH] Login attempt - Email: <EMAIL>, IP: ***********, UserAgent: Mozilla/5.0...
[AUTH_SERVICE] Starting login process for email: <EMAIL>
[AUTH_SERVICE] Login failed - User not found: <EMAIL>
[AUTH] Login failed - Email: <EMAIL>, Error: Incorrect email or password, IP: ***********
```

## Konfigurasi Logger

Untuk mengoptimalkan logging berdasarkan environment:

```javascript
// config/logger.js
const logger = winston.createLogger({
  level: config.env === 'development' ? 'debug' : 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console({
      format: winston.format.combine(winston.format.colorize(), winston.format.simple())
    })
  ]
});
```

Sistem logging ini memberikan visibilitas lengkap terhadap semua operasi auth, memungkinkan monitoring proaktif dan debugging yang efektif.
