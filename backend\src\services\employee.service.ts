import { User, Role, Prisma } from '@prisma/client';
import httpStatus from 'http-status';
import prisma from '../client';
import ApiError from '../utils/ApiError';
import { encryptPassword } from '../utils/encryption';
import exclude from '../utils/exclude';

/**
 * Create an employee
 * @param {Object} employeeBody
 * @param {User} currentUser
 * @returns {Promise<User>}
 */
const createEmployee = async (
  email: string,
  password: string,
  name: string,
  phone: string,
  outletId: number,
  currentUser: User
): Promise<Omit<User, 'password'>> => {
  // Check if email already exists
  const existingUser = await prisma.user.findUnique({ where: { email } });
  if (existingUser) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Email already taken');
  }

  // Check if phone already exists
  const existingPhone = await prisma.user.findUnique({ where: { phone } });
  if (existingPhone) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Phone number already taken');
  }

  // Check if outlet exists
  const outlet = await prisma.outlet.findUnique({ where: { id: outletId } });
  if (!outlet) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Outlet not found');
  }

  // Check if current user can create employee for this outlet
  if (currentUser.role === Role.OWNER) {
    if (outlet.ownerId !== currentUser.id) {
      throw new ApiError(
        httpStatus.FORBIDDEN,
        'You can only create employees for your own outlets'
      );
    }
  }

  const employee = await prisma.user.create({
    data: {
      email,
      name,
      phone,
      password: await encryptPassword(password),
      role: Role.EMPLOYEE,
      outletId
    }
  });

  return exclude(employee, ['password']);
};

/**
 * Query for employees
 * @param {Object} filter - Prisma filter
 * @param {Object} options - Query options
 * @param {User} currentUser
 * @returns {Promise<QueryResult>}
 */
const queryEmployees = async (filter: any, options: any, currentUser: User): Promise<any> => {
  // Build where clause
  let where: Prisma.UserWhereInput = {
    role: Role.EMPLOYEE,
    isActive: true
  };

  // Apply outlet filtering based on user role
  if (currentUser.role === Role.OWNER) {
    // Owner can only see employees from their outlets
    const ownerOutlets = await prisma.outlet.findMany({
      where: { ownerId: currentUser.id },
      select: { id: true }
    });
    const outletIds = ownerOutlets.map((outlet) => outlet.id);

    // If specific outletId is requested, check if owner owns it
    if (filter.outletId) {
      // Convert filter.outletId to number for comparison
      const requestedOutletId =
        typeof filter.outletId === 'string' ? parseInt(filter.outletId, 10) : filter.outletId;
      if (!outletIds.includes(requestedOutletId)) {
        throw new ApiError(
          httpStatus.FORBIDDEN,
          'You can only access employees from your own outlets'
        );
      }
      where.outletId = requestedOutletId;
    } else {
      // If no specific outlet requested, show all employees from owner's outlets
      where.outletId = { in: outletIds };
    }
  } else if (currentUser.role === Role.ADMIN) {
    // Admin can see all employees, apply outletId filter if provided
    if (filter.outletId) {
      const requestedOutletId =
        typeof filter.outletId === 'string' ? parseInt(filter.outletId, 10) : filter.outletId;
      where.outletId = requestedOutletId;
    }
  }

  // Apply name filter if provided
  if (filter.name) {
    where.name = {
      contains: filter.name,
      mode: 'insensitive'
    };
  }

  const page = options.page ?? 1;
  const limit = options.limit ?? 10;
  const sortBy = options.sortBy;

  let orderBy: Prisma.UserOrderByWithRelationInput = { createdAt: 'desc' };
  if (sortBy) {
    const [field, order] = sortBy.split(':');
    orderBy = { [field]: order || 'asc' };
  }

  const employees = await prisma.user.findMany({
    where,
    orderBy,
    skip: (page - 1) * limit,
    take: limit,
    select: {
      id: true,
      email: true,
      name: true,
      phone: true,
      role: true,
      isActive: true,
      outletId: true,
      createdAt: true,
      updatedAt: true,
      employeeAt: {
        select: {
          id: true,
          name: true
        }
      }
    }
  });

  const totalResults = await prisma.user.count({ where });
  const totalPages = Math.ceil(totalResults / limit);

  return {
    results: employees,
    page,
    limit,
    totalPages,
    totalResults
  };
};

/**
 * Get employee by id
 * @param {number} id
 * @param {User} currentUser
 * @returns {Promise<User>}
 */
const getEmployeeById = async (id: number, currentUser: User): Promise<any> => {
  const employee = await prisma.user.findFirst({
    where: {
      id,
      role: Role.EMPLOYEE,
      isActive: true
    },
    include: {
      employeeAt: {
        select: {
          id: true,
          name: true,
          ownerId: true
        }
      }
    }
  });

  if (!employee) {
    return null;
  }

  // Check access permissions
  if (currentUser.role === Role.OWNER) {
    if (employee.employeeAt?.ownerId !== currentUser.id) {
      throw new ApiError(
        httpStatus.FORBIDDEN,
        'You can only access employees from your own outlets'
      );
    }
  }

  return exclude(employee, ['password']);
};

/**
 * Update employee by id
 * @param {number} employeeId
 * @param {Object} updateBody
 * @param {User} currentUser
 * @returns {Promise<User>}
 */
const updateEmployeeById = async (
  employeeId: number,
  updateBody: any,
  currentUser: User
): Promise<any> => {
  const employee = await getEmployeeById(employeeId, currentUser);
  if (!employee) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Employee not found');
  }

  // Check if email is being updated and already exists
  if (updateBody.email && updateBody.email !== employee.email) {
    const existingUser = await prisma.user.findUnique({ where: { email: updateBody.email } });
    if (existingUser) {
      throw new ApiError(httpStatus.BAD_REQUEST, 'Email already taken');
    }
  }

  // Check if phone is being updated and already exists
  if (updateBody.phone && updateBody.phone !== employee.phone) {
    const existingPhone = await prisma.user.findUnique({ where: { phone: updateBody.phone } });
    if (existingPhone) {
      throw new ApiError(httpStatus.BAD_REQUEST, 'Phone number already taken');
    }
  }

  // Check if outletId is being updated
  if (updateBody.outletId) {
    const outlet = await prisma.outlet.findUnique({ where: { id: updateBody.outletId } });
    if (!outlet) {
      throw new ApiError(httpStatus.BAD_REQUEST, 'Outlet not found');
    }

    // Check if current user can assign employee to this outlet
    if (currentUser.role === Role.OWNER) {
      if (outlet.ownerId !== currentUser.id) {
        throw new ApiError(
          httpStatus.FORBIDDEN,
          'You can only assign employees to your own outlets'
        );
      }
    }
  }

  // Encrypt password if provided
  if (updateBody.password) {
    updateBody.password = await encryptPassword(updateBody.password);
  }

  const updatedEmployee = await prisma.user.update({
    where: { id: employeeId },
    data: updateBody,
    include: {
      employeeAt: {
        select: {
          id: true,
          name: true
        }
      }
    }
  });

  return exclude(updatedEmployee, ['password']);
};

/**
 * Delete employee by id (soft delete)
 * @param {number} employeeId
 * @param {User} currentUser
 * @returns {Promise<User>}
 */
const deleteEmployeeById = async (employeeId: number, currentUser: User): Promise<void> => {
  const employee = await getEmployeeById(employeeId, currentUser);
  if (!employee) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Employee not found');
  }

  await prisma.user.update({
    where: { id: employeeId },
    data: { isActive: false }
  });
};

export default {
  createEmployee,
  queryEmployees,
  getEmployeeById,
  updateEmployeeById,
  deleteEmployeeById
};
