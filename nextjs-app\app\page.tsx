'use client';

import {
  Bell,
  QrCode,
  CheckCircle,
  MapPin,
  ClipboardList,
  RefreshCw,
  Package,
  Truck,
  InboxIcon,
  CalendarCheck,
  CalendarDays,
  CalendarRange,
  AlertCircle,
  UserCheck,
} from 'lucide-react';
import Image from 'next/image';
import { useState } from 'react';
import Link from 'next/link';

import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import BottomNavigation from '@/components/bottom-navigation';

export default function Home() {
  const [activeTab, setActiveTab] = useState('keuangan');

  return (
    <div className="flex flex-col min-h-screen bg-slate-50">
      <h1>Landing Page</h1>
    </div>
  );
}
