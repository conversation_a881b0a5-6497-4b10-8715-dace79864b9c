import Joi from 'joi';

const createServiceCategory = {
  body: Joi.object().keys({
    name: Joi.string().required().trim().min(1).max(100),
    description: Joi.string().optional().max(500).allow(null),
    productionProcess: Joi.array().items(Joi.string()).optional(),
    outletId: Joi.number().integer().positive().required()
  })
};

const getServiceCategories = {
  query: Joi.object().keys({
    search: Joi.string().optional().trim().allow(''),
    limit: Joi.number().optional().integer().min(1).max(100),
    page: Joi.number().optional().integer().min(1),
    outletId: Joi.number().integer().positive().optional(),
    estimationHours: Joi.number().optional().integer().min(1)
  })
};

const getServiceCategory = {
  params: Joi.object().keys({
    id: Joi.number().integer().positive().required()
  }),
  query: Joi.object().keys({
    outletId: Joi.number().integer().positive().optional()
  })
};

const updateServiceCategory = {
  params: Joi.object().keys({
    id: Joi.number().integer().positive().required()
  }),
  body: Joi.object()
    .keys({
      name: Joi.string().optional().trim().min(1).max(100),
      description: Joi.string().optional().max(500).allow(null),
      productionProcess: Joi.array().items(Joi.string()).optional(),
      outletId: Joi.number().integer().positive().required()
    })
    .min(1)
};

const deleteServiceCategory = {
  params: Joi.object().keys({
    id: Joi.number().integer().positive().required()
  }),
  query: Joi.object().keys({
    outletId: Joi.number().integer().positive().optional()
  })
};

const getServiceCategoriesForSelect = {
  query: Joi.object().keys({
    outletId: Joi.number().integer().positive().optional()
  })
};

const queryCount = {
  query: Joi.object().keys({
    outletId: Joi.number().integer().positive().required()
  })
};

export default {
  createServiceCategory,
  getServiceCategories,
  getServiceCategory,
  updateServiceCategory,
  deleteServiceCategory,
  getServiceCategoriesForSelect,
  queryCount
};
