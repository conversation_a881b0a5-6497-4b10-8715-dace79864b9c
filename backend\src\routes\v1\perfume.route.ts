import express from 'express';
import auth from '../../middlewares/auth';
import validate from '../../middlewares/validate';
import { perfumeValidation } from '../../validations';
import { perfumeController } from '../../controllers';

const router = express.Router();

router
  .route('/')
  .post(auth(), validate(perfumeValidation.createPerfume), perfumeController.createPerfume)
  .get(auth(), validate(perfumeValidation.getPerfumes), perfumeController.getPerfumes);

router
  .route('/:perfumeId')
  .get(auth(), validate(perfumeValidation.getPerfume), perfumeController.getPerfume)
  .patch(auth(), validate(perfumeValidation.updatePerfume), perfumeController.updatePerfume)
  .delete(auth(), validate(perfumeValidation.deletePerfume), perfumeController.deletePerfume);

export default router;

/**
 * @swagger
 * tags:
 *   name: Perfumes
 *   description: Perfume management for laundry orders
 */

/**
 * @swagger
 * /perfumes:
 *   post:
 *     summary: Create a new perfume
 *     description: Add a new perfume to the outlet's collection. Perfume names must be unique within each outlet.
 *     tags: [Perfumes]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - outletId
 *             properties:
 *               name:
 *                 type: string
 *                 maxLength: 100
 *                 description: Perfume name (must be unique per outlet)
 *               description:
 *                 type: string
 *                 maxLength: 500
 *                 description: Perfume description
 *               brand:
 *                 type: string
 *                 maxLength: 100
 *                 description: Perfume brand
 *               scent:
 *                 type: string
 *                 maxLength: 100
 *                 description: Scent type (e.g., fresh, floral, citrus, woody)
 *               isActive:
 *                 type: boolean
 *                 default: true
 *                 description: Whether the perfume is active/available
 *               outletId:
 *                 type: integer
 *                 description: Outlet ID
 *               isPopular:
 *                 type: boolean
 *                 default: false
 *                 description: Whether the perfume is popular
 *               isNew:
 *                 type: boolean
 *                 default: false
 *             example:
 *               name: "Lavender Fresh"
 *               description: "Aroma lavender yang menenangkan untuk pakaian Anda"
 *               brand: "Fresh Laundry Co"
 *               scent: "floral"
 *               isActive: true
 *               outletId: 1
 *               isPopular: false
 *               isNew: false
 *     responses:
 *       "201":
 *         description: Created
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Perfume'
 *       "400":
 *         $ref: '#/components/responses/BadRequest'
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "403":
 *         $ref: '#/components/responses/Forbidden'
 *
 *   get:
 *     summary: Get all perfumes
 *     description: Retrieve all perfumes for the current outlet with optional filtering and pagination.
 *     tags: [Perfumes]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: name
 *         schema:
 *           type: string
 *         description: Filter by perfume name (case-insensitive search)
 *       - in: query
 *         name: brand
 *         schema:
 *           type: string
 *         description: Filter by brand (case-insensitive search)
 *       - in: query
 *         name: scent
 *         schema:
 *           type: string
 *         description: Filter by scent type (case-insensitive search)
 *       - in: query
 *         name: isActive
 *         schema:
 *           type: boolean
 *         description: Filter by active status
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *         description: Sort by query in the form of field:desc/asc (ex. name:asc, createdAt:desc)
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         default: 10
 *         description: Maximum number of perfumes per page
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         default: 1
 *         description: Page number
 *       - in: query
 *         name: outletId
 *         schema:
 *           type: integer
 *         description: Outlet ID
 *       - in: query
 *         name: isPopular
 *         schema:
 *           type: boolean
 *         description: Filter by popular status
 *       - in: query
 *         name: isNew
 *         schema:
 *           type: boolean
 *         description: Filter by new status
 *     responses:
 *       "200":
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 results:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Perfume'
 *                 page:
 *                   type: integer
 *                   example: 1
 *                 limit:
 *                   type: integer
 *                   example: 10
 *                 totalPages:
 *                   type: integer
 *                   example: 1
 *                 totalResults:
 *                   type: integer
 *                   example: 5
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "403":
 *         $ref: '#/components/responses/Forbidden'
 */

/**
 * @swagger
 * /perfumes/{perfumeId}:
 *   get:
 *     summary: Get perfume by ID
 *     description: Retrieve a specific perfume by its ID from the current outlet.
 *     tags: [Perfumes]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: perfumeId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Perfume ID
 *     responses:
 *       "200":
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Perfume'
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "403":
 *         $ref: '#/components/responses/Forbidden'
 *       "404":
 *         $ref: '#/components/responses/NotFound'
 *
 *   patch:
 *     summary: Update perfume
 *     description: Update perfume information. At least one field must be provided. Name must remain unique within the outlet.
 *     tags: [Perfumes]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: perfumeId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Perfume ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 maxLength: 100
 *                 description: Perfume name (must be unique per outlet)
 *               description:
 *                 type: string
 *                 maxLength: 500
 *                 description: Perfume description
 *               brand:
 *                 type: string
 *                 maxLength: 100
 *                 description: Perfume brand
 *               scent:
 *                 type: string
 *                 maxLength: 100
 *                 description: Scent type
 *               isActive:
 *                 type: boolean
 *                 description: Whether the perfume is active/available
 *             minProperties: 1
 *             example:
 *               name: "Lavender Fresh - Updated"
 *               description: "Aroma lavender premium yang menenangkan"
 *               brand: "Premium Fresh Co"
 *               scent: "floral premium"
 *               isActive: true
 *               isPopular: false
 *               isNew: false
 *     responses:
 *       "200":
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Perfume'
 *       "400":
 *         $ref: '#/components/responses/BadRequest'
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "403":
 *         $ref: '#/components/responses/Forbidden'
 *       "404":
 *         $ref: '#/components/responses/NotFound'
 *
 *   delete:
 *     summary: Delete perfume
 *     description: Delete a perfume from the outlet. Cannot delete if the perfume is being used in any orders.
 *     tags: [Perfumes]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: perfumeId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Perfume ID
 *     responses:
 *       "204":
 *         description: No content - perfume deleted successfully
 *       "400":
 *         description: Bad request - perfume is being used in orders
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 400
 *                 message:
 *                   type: string
 *                   example: "Cannot delete perfume. It is being used in 5 order(s)."
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "403":
 *         $ref: '#/components/responses/Forbidden'
 *       "404":
 *         $ref: '#/components/responses/NotFound'
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     Perfume:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: Unique perfume identifier
 *         name:
 *           type: string
 *           description: Perfume name
 *         description:
 *           type: string
 *           description: Perfume description
 *         brand:
 *           type: string
 *           description: Perfume brand
 *         scent:
 *           type: string
 *           description: Scent type (fresh, floral, citrus, woody, etc.)
 *         isActive:
 *           type: boolean
 *           description: Whether the perfume is active/available
 *         outletId:
 *           type: integer
 *           description: Outlet ID that owns this perfume
 *         isPopular:
 *           type: boolean
 *           description: Whether the perfume is popular
 *         isNew:
 *           type: boolean
 *           description: Whether the perfume is new
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Creation timestamp
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Last update timestamp
 *       example:
 *         id: 1
 *         name: "Lavender Fresh"
 *         description: "Aroma lavender yang menenangkan untuk pakaian Anda"
 *         brand: "Fresh Laundry Co"
 *         scent: "floral"
 *         isActive: true
 *         outletId: 1
 *         isPopular: false
 *         isNew: false
 *         createdAt: "2024-01-15T08:00:00Z"
 *         updatedAt: "2024-01-15T08:00:00Z"
 */
