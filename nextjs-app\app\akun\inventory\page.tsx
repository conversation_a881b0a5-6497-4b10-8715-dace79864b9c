"use client"

import { Label } from "@/components/ui/label"

import { useState } from "react"
import Link from "next/link"
import { ArrowLeft, Plus, Search, MoreVertical, Edit, Trash2, AlertCircle } from "lucide-react"
import { useRouter } from "next/navigation"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"

// Mock data for inventory items
const mockInventory = [
  {
    id: 1,
    name: "Deterjen Cair",
    category: "supplies",
    stock: 25,
    unit: "Liter",
    minStock: 10,
    price: 25000,
    supplier: "PT Supplier Bersih",
    lastRestock: "15/03/2025",
  },
  {
    id: 2,
    name: "Pelembut Pakaian",
    category: "supplies",
    stock: 15,
    unit: "Liter",
    minStock: 8,
    price: 30000,
    supplier: "PT Supplier Bersih",
    lastRestock: "15/03/2025",
  },
  {
    id: 3,
    name: "Pemutih",
    category: "supplies",
    stock: 5,
    unit: "Liter",
    minStock: 5,
    price: 20000,
    supplier: "PT Supplier Bersih",
    lastRestock: "10/03/2025",
  },
  {
    id: 4,
    name: "Plastik Packaging Kecil",
    category: "packaging",
    stock: 200,
    unit: "Pcs",
    minStock: 100,
    price: 500,
    supplier: "PT Plastik Jaya",
    lastRestock: "20/03/2025",
  },
  {
    id: 5,
    name: "Plastik Packaging Besar",
    category: "packaging",
    stock: 150,
    unit: "Pcs",
    minStock: 100,
    price: 1000,
    supplier: "PT Plastik Jaya",
    lastRestock: "20/03/2025",
  },
  {
    id: 6,
    name: "Hanger",
    category: "equipment",
    stock: 80,
    unit: "Pcs",
    minStock: 50,
    price: 2500,
    supplier: "PT Peralatan Laundry",
    lastRestock: "05/03/2025",
  },
  {
    id: 7,
    name: "Setrika",
    category: "equipment",
    stock: 10,
    unit: "Pcs",
    minStock: 5,
    price: 150000,
    supplier: "PT Peralatan Laundry",
    lastRestock: "01/03/2025",
  },
  {
    id: 8,
    name: "Kertas Nota",
    category: "stationery",
    stock: 3,
    unit: "Pack",
    minStock: 5,
    price: 35000,
    supplier: "PT ATK Sejahtera",
    lastRestock: "25/02/2025",
  },
]

export default function InventoryPage() {
  const router = useRouter()
  const [searchQuery, setSearchQuery] = useState("")
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [itemToDelete, setItemToDelete] = useState<number | null>(null)
  const [showRestockDialog, setShowRestockDialog] = useState(false)
  const [itemToRestock, setItemToRestock] = useState<number | null>(null)
  const [restockAmount, setRestockAmount] = useState(1)

  // Filter inventory items based on search query
  const filteredItems = mockInventory.filter(
    (item) =>
      item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.category.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.supplier.toLowerCase().includes(searchQuery.toLowerCase()),
  )

  const handleDeleteItem = (id: number) => {
    setItemToDelete(id)
    setShowDeleteDialog(true)
  }

  const confirmDelete = () => {
    // In a real app, you would delete the item from your backend
    alert(`Item with ID ${itemToDelete} has been deleted`)
    setShowDeleteDialog(false)
    setItemToDelete(null)
  }

  const handleRestockItem = (id: number) => {
    setItemToRestock(id)
    setRestockAmount(1)
    setShowRestockDialog(true)
  }

  const confirmRestock = () => {
    // In a real app, you would update the item stock in your backend
    alert(`Added ${restockAmount} units to item with ID ${itemToRestock}`)
    setShowRestockDialog(false)
    setItemToRestock(null)
  }

  const getStockStatus = (item: (typeof mockInventory)[0]) => {
    const percentage = (item.stock / item.minStock) * 100
    if (item.stock <= item.minStock * 0.5) return "low"
    if (item.stock <= item.minStock) return "warning"
    return "good"
  }

  return (
    <div className="flex flex-col min-h-screen bg-slate-50">
      <header className="p-4 flex justify-between items-center bg-white sticky top-0 z-10 shadow-sm">
        <div className="flex items-center">
          <Link href="/akun" className="mr-3">
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <h1 className="text-lg font-semibold">Inventaris</h1>
        </div>
        <Link href="/akun/inventory/add">
          <Button size="sm" className="bg-blue-500 hover:bg-blue-600">
            <Plus className="h-4 w-4 mr-1" /> Tambah
          </Button>
        </Link>
      </header>

      <div className="p-4">
        <div className="relative mb-4">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <Input
            placeholder="Cari inventaris..."
            className="pl-10"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        <div className="space-y-4 mb-20">
          {filteredItems.length > 0 ? (
            filteredItems.map((item) => (
              <Card key={item.id} className="p-4">
                <div className="flex justify-between items-start">
                  <div>
                    <div className="flex items-center gap-2">
                      <h3 className="font-medium">{item.name}</h3>
                      <Badge variant="secondary">{item.category}</Badge>
                      {item.stock <= item.minStock && (
                        <Badge variant="destructive" className="flex items-center gap-1">
                          <AlertCircle className="h-3 w-3" /> Stok Rendah
                        </Badge>
                      )}
                    </div>
                    <p className="text-sm text-gray-500 mt-1">
                      Supplier: {item.supplier} | Terakhir restock: {item.lastRestock}
                    </p>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleRestockItem(item.id)}>
                        <Plus className="h-4 w-4 mr-2" /> Tambah Stok
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => router.push(`/akun/inventory/edit/${item.id}`)}>
                        <Edit className="h-4 w-4 mr-2" /> Edit
                      </DropdownMenuItem>
                      <DropdownMenuItem className="text-red-500" onClick={() => handleDeleteItem(item.id)}>
                        <Trash2 className="h-4 w-4 mr-2" /> Hapus
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>

                <div className="mt-3 grid grid-cols-3 gap-4">
                  <div>
                    <p className="text-xs text-gray-500">Stok Saat Ini</p>
                    <p className="font-semibold">
                      {item.stock} {item.unit}
                    </p>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500">Stok Minimum</p>
                    <p className="font-semibold">
                      {item.minStock} {item.unit}
                    </p>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500">Harga</p>
                    <p className="font-semibold">Rp {item.price.toLocaleString()}</p>
                  </div>
                </div>

                <div className="mt-3">
                  <div className="flex justify-between text-xs mb-1">
                    <span>Status Stok</span>
                    <span
                      className={
                        getStockStatus(item) === "low"
                          ? "text-red-500"
                          : getStockStatus(item) === "warning"
                            ? "text-amber-500"
                            : "text-green-500"
                      }
                    >
                      {getStockStatus(item) === "low"
                        ? "Sangat Rendah"
                        : getStockStatus(item) === "warning"
                          ? "Perlu Restock"
                          : "Baik"}
                    </span>
                  </div>
                  <Progress
                    value={(item.stock / (item.minStock * 2)) * 100}
                    className={
                      getStockStatus(item) === "low"
                        ? "bg-red-100"
                        : getStockStatus(item) === "warning"
                          ? "bg-amber-100"
                          : "bg-green-100"
                    }
                    indicatorClassName={
                      getStockStatus(item) === "low"
                        ? "bg-red-500"
                        : getStockStatus(item) === "warning"
                          ? "bg-amber-500"
                          : "bg-green-500"
                    }
                  />
                </div>
              </Card>
            ))
          ) : (
            <div className="text-center py-8 text-gray-500">Tidak ada item yang ditemukan</div>
          )}
        </div>
      </div>

      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Hapus Item</DialogTitle>
            <DialogDescription>
              Apakah Anda yakin ingin menghapus item ini? Tindakan ini tidak dapat dibatalkan.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteDialog(false)}>
              Batal
            </Button>
            <Button variant="destructive" onClick={confirmDelete}>
              Hapus
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Restock Dialog */}
      <Dialog open={showRestockDialog} onOpenChange={setShowRestockDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Tambah Stok</DialogTitle>
            <DialogDescription>Masukkan jumlah yang ingin ditambahkan ke stok saat ini.</DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <Label htmlFor="restock-amount">Jumlah</Label>
            <div className="flex items-center mt-2">
              <Button variant="outline" size="icon" onClick={() => setRestockAmount(Math.max(1, restockAmount - 1))}>
                -
              </Button>
              <Input
                id="restock-amount"
                type="number"
                min="1"
                value={restockAmount}
                onChange={(e) => setRestockAmount(Number.parseInt(e.target.value) || 1)}
                className="mx-2 text-center"
              />
              <Button variant="outline" size="icon" onClick={() => setRestockAmount(restockAmount + 1)}>
                +
              </Button>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowRestockDialog(false)}>
              Batal
            </Button>
            <Button onClick={confirmRestock}>Tambah Stok</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
