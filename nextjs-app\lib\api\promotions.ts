import { api } from '../auth-context';

export interface Promotion {
  id: number;
  name: string;
  code: string;
  description?: string;
  discountType: 'PERCENTAGE' | 'FIXED';
  discountValue: number;
  minOrderValue: number;
  maxDiscountAmount?: number;
  isActive: boolean;
  validFrom: string;
  validUntil: string;
  usageLimit?: number;
  usageCount: number;
  isFirstTimeOnly: boolean;
  applicableServices: string[];
  outletId: number;
  createdAt: string;
  updatedAt: string;
}

export interface CreatePromotionRequest {
  name: string;
  code: string;
  description?: string;
  discountType: 'PERCENTAGE' | 'FIXED';
  discountValue: number;
  minOrderValue?: number;
  maxDiscountAmount?: number;
  isActive?: boolean;
  validFrom: string;
  validUntil: string;
  usageLimit?: number;
  isFirstTimeOnly?: boolean;
  applicableServices?: string[];
  outletId: number;
}

export interface UpdatePromotionRequest {
  name?: string;
  code?: string;
  description?: string;
  discountType?: 'PERCENTAGE' | 'FIXED';
  discountValue?: number;
  minOrderValue?: number;
  maxDiscountAmount?: number;
  isActive?: boolean;
  validFrom?: string;
  validUntil?: string;
  usageLimit?: number;
  isFirstTimeOnly?: boolean;
  applicableServices?: string[];
  outletId: number;
}

export interface GetPromotionsParams {
  search?: string;
  name?: string;
  code?: string;
  discountType?: 'PERCENTAGE' | 'FIXED';
  isActive?: boolean;
  isExpired?: boolean;
  sortBy?: string;
  limit?: number;
  page?: number;
  outletId?: number;
}

export interface PromotionsResponse {
  results: Promotion[];
  page: number;
  limit: number;
  totalPages: number;
  totalResults: number;
}

export interface ValidatePromotionRequest {
  code: string;
  orderTotal: number;
  customerId: number;
  outletId: number;
}

export interface ValidatePromotionResponse {
  promotion: Promotion;
  discountAmount: number;
  isValid: boolean;
}

export const promotionAPI = {
  // Get all promotions
  getPromotions: async (
    params?: GetPromotionsParams
  ): Promise<PromotionsResponse> => {
    const response = await api.get('/promotions', { params });
    return response.data;
  },

  // Get promotion by ID
  getPromotion: async (id: number): Promise<Promotion> => {
    const response = await api.get(`/promotions/${id}`);
    return response.data;
  },

  // Create new promotion
  createPromotion: async (data: CreatePromotionRequest): Promise<Promotion> => {
    const response = await api.post('/promotions', data);
    return response.data;
  },

  // Update promotion
  updatePromotion: async (
    id: number,
    data: UpdatePromotionRequest
  ): Promise<Promotion> => {
    const response = await api.patch(`/promotions/${id}`, data);
    return response.data;
  },

  // Delete promotion
  deletePromotion: async (id: number, outletId: number): Promise<void> => {
    await api.delete(`/promotions/${id}`, {
      data: { outletId },
    });
  },

  // Validate promotion code
  validatePromotion: async (
    data: ValidatePromotionRequest
  ): Promise<ValidatePromotionResponse> => {
    const response = await api.post('/promotions/validate', data);
    return response.data;
  },

  // Get active promotions for dropdown
  getActivePromotions: async (outletId: number): Promise<Promotion[]> => {
    const response = await api.get('/promotions', {
      params: {
        outletId,
        isActive: true,
        isExpired: false,
        limit: 100,
        sortBy: 'name:asc',
      },
    });
    return response.data.results;
  },
};
 