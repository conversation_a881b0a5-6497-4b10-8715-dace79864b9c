/**
 * <PERSON><PERSON><PERSON> penggunaan SendGrid Email Service
 * File ini menunjukkan cara menggunakan fungsi-fungsi email dengan SendGrid
 */

import emailService from '../services/email.service';
import logger from '../config/logger';

// Contoh mengirim email biasa dengan SendGrid
export const sendBasicEmail = async () => {
  try {
    await emailService.sendEmailWithSendGrid(
      '<EMAIL>',
      'Selamat Datang di Laundry App',
      'Terima kasih telah bergabung dengan layanan laundry kami!',
      `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #333;">Selamat Datang! 👕</h2>
        <p>Halo!</p>
        <p>Terima kasih telah bergabung dengan <strong>Laundry App</strong>.</p>
        <p>Kami siap membantu kebutuhan laundry Anda dengan layanan terbaik.</p>
        <div style="background-color: #f8f9fa; padding: 20px; margin: 20px 0; border-radius: 5px;">
          <h3>Fitur yang bisa Anda nikmati:</h3>
          <ul>
            <li>📱 Order melalui aplikasi</li>
            <li>🚚 Pickup & delivery gratis</li>
            <li>💧 Cuci dengan deterjen premium</li>
            <li>⚡ Layanan express tersedia</li>
          </ul>
        </div>
        <hr style="border: none; border-top: 1px solid #eee; margin: 20px 0;">
        <p style="color: #666; font-size: 12px;">Email ini dikirim dari Laundry App</p>
      </div>
      `
    );
    logger.info('Email selamat datang berhasil dikirim');
  } catch (error) {
    logger.error('Gagal mengirim email selamat datang:', error);
  }
};

// Contoh mengirim email reset password dengan SendGrid
export const sendPasswordReset = async () => {
  try {
    const resetToken = 'example-reset-token-123';
    await emailService.sendResetPasswordEmailWithSendGrid('<EMAIL>', resetToken);
    logger.info('Email reset password berhasil dikirim');
  } catch (error) {
    logger.error('Gagal mengirim email reset password:', error);
  }
};

// Contoh mengirim email verifikasi dengan SendGrid
export const sendEmailVerification = async () => {
  try {
    const verificationToken = 'example-verification-token-456';
    await emailService.sendVerificationEmailWithSendGrid('<EMAIL>', verificationToken);
    logger.info('Email verifikasi berhasil dikirim');
  } catch (error) {
    logger.error('Gagal mengirim email verifikasi:', error);
  }
};

// Contoh mengirim email notifikasi order
export const sendOrderNotification = async (orderData: any) => {
  try {
    const { customerEmail, orderId, status, items, totalPrice } = orderData;

    const subject = `Update Order #${orderId} - Status: ${status}`;
    const text = `Order Anda #${orderId} telah diupdate dengan status: ${status}`;

    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #333;">Update Order Anda 📦</h2>
        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
          <h3>Detail Order:</h3>
          <p><strong>Order ID:</strong> #${orderId}</p>
          <p><strong>Status:</strong> <span style="color: #28a745; font-weight: bold;">${status}</span></p>
          <p><strong>Total Harga:</strong> Rp ${totalPrice.toLocaleString('id-ID')}</p>
        </div>
        
        <div style="background-color: #fff; border: 1px solid #dee2e6; border-radius: 5px; padding: 15px; margin: 20px 0;">
          <h4>Item yang dicuci:</h4>
          <ul>
            ${items
              .map((item: any) => `<li>${item.name} (${item.quantity}x) - ${item.service}</li>`)
              .join('')}
          </ul>
        </div>
        
        ${
          status === 'ready'
            ? `
          <div style="text-align: center; margin: 30px 0;">
            <p style="color: #28a745; font-size: 18px; font-weight: bold;">🎉 Laundry Anda sudah siap diambil!</p>
          </div>
        `
            : ''
        }
        
        <hr style="border: none; border-top: 1px solid #eee; margin: 20px 0;">
        <p style="color: #666; font-size: 12px;">Terima kasih telah menggunakan layanan Laundry App</p>
      </div>
    `;

    await emailService.sendEmailWithSendGrid(customerEmail, subject, text, html);
    logger.info(`Email notifikasi order #${orderId} berhasil dikirim`);
  } catch (error) {
    logger.error('Gagal mengirim email notifikasi order:', error);
  }
};

// Fungsi untuk testing semua email
export const testAllEmails = async () => {
  logger.info('Memulai test semua fungsi email SendGrid...');

  await sendBasicEmail();
  await sendPasswordReset();
  await sendEmailVerification();

  // Test order notification
  const mockOrderData = {
    customerEmail: '<EMAIL>',
    orderId: 'ORD-001',
    status: 'ready',
    items: [
      { name: 'Kemeja', quantity: 3, service: 'Cuci Setrika' },
      { name: 'Celana', quantity: 2, service: 'Dry Clean' }
    ],
    totalPrice: 45000
  };

  await sendOrderNotification(mockOrderData);

  logger.info('Test email SendGrid selesai!');
};

export default {
  sendBasicEmail,
  sendPasswordReset,
  sendEmailVerification,
  sendOrderNotification,
  testAllEmails
};
