import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function seedLocation() {
  console.log('🌍 Seeding location data...');

  try {
    // Data provinsi Indonesia terbaru 2024
    const provinces = [
      { name: '<PERSON><PERSON>', code: 'AC' },
      { name: 'Sumatera Utara', code: 'SU' },
      { name: 'Sumatera Barat', code: 'SB' },
      { name: '<PERSON><PERSON><PERSON>', code: 'RI' },
      { name: '<PERSON><PERSON>', code: 'J<PERSON>' },
      { name: '<PERSON><PERSON>ra Selatan', code: 'SS' },
      { name: '<PERSON><PERSON><PERSON><PERSON>', code: 'BE' },
      { name: '<PERSON><PERSON><PERSON>', code: 'LA' },
      { name: '<PERSON><PERSON><PERSON><PERSON>', code: 'BB' },
      { name: '<PERSON><PERSON><PERSON><PERSON>', code: 'KR' },
      { name: 'DKI Jakarta', code: 'JK' },
      { name: 'Jawa Barat', code: 'J<PERSON>' },
      { name: '<PERSON><PERSON> Ten<PERSON>', code: 'JT' },
      { name: 'DI Yogyakarta', code: 'YO' },
      { name: '<PERSON><PERSON> Tim<PERSON>', code: 'J<PERSON>' },
      { name: '<PERSON><PERSON>', code: '<PERSON>' },
      { name: '<PERSON>', code: 'BA' },
      { name: 'Nusa Tenggara Barat', code: 'NB' },
      { name: 'Nusa Tenggara Timur', code: 'NT' },
      { name: 'Kalimantan Barat', code: 'KB' },
      { name: 'Kalimantan Tengah', code: 'KT' },
      { name: 'Kalimantan Selatan', code: 'KS' },
      { name: 'Kalimantan Timur', code: 'KI' },
      { name: 'Kalimantan Utara', code: 'KU' },
      { name: 'Sulawesi Utara', code: 'SA' },
      { name: 'Sulawesi Tengah', code: 'ST' },
      { name: 'Sulawesi Selatan', code: 'SN' },
      { name: 'Sulawesi Tenggara', code: 'SG' },
      { name: 'Gorontalo', code: 'GO' },
      { name: 'Sulawesi Barat', code: 'SR' },
      { name: 'Maluku', code: 'MA' },
      { name: 'Maluku Utara', code: 'MU' },
      { name: 'Papua Barat', code: 'PB' },
      { name: 'Papua', code: 'PA' },
      { name: 'Papua Tengah', code: 'PT' },
      { name: 'Papua Pegunungan', code: 'PP' },
      { name: 'Papua Selatan', code: 'PS' },
      { name: 'Papua Barat Daya', code: 'PD' }
    ];

    // Data kota/kabupaten utama per provinsi (menggunakan nama provinsi sebagai referensi)
    const cities = [
      // Aceh
      { name: 'Banda Aceh', code: 'BANDA_ACEH', provinceName: 'Aceh' },
      { name: 'Langsa', code: 'LANGSA', provinceName: 'Aceh' },
      { name: 'Lhokseumawe', code: 'LHOKSEUMAWE', provinceName: 'Aceh' },
      { name: 'Sabang', code: 'SABANG', provinceName: 'Aceh' },
      { name: 'Subulussalam', code: 'SUBULUSSALAM', provinceName: 'Aceh' },

      // Sumatera Utara
      { name: 'Medan', code: 'MEDAN', provinceName: 'Sumatera Utara' },
      { name: 'Binjai', code: 'BINJAI', provinceName: 'Sumatera Utara' },
      { name: 'Pematangsiantar', code: 'PEMATANGSIANTAR', provinceName: 'Sumatera Utara' },
      { name: 'Sibolga', code: 'SIBOLGA', provinceName: 'Sumatera Utara' },
      { name: 'Tanjungbalai', code: 'TANJUNGBALAI', provinceName: 'Sumatera Utara' },
      { name: 'Tebing Tinggi', code: 'TEBING_TINGGI', provinceName: 'Sumatera Utara' },
      { name: 'Padangsidimpuan', code: 'PADANGSIDIMPUAN', provinceName: 'Sumatera Utara' },
      { name: 'Gunungsitoli', code: 'GUNUNGSITOLI', provinceName: 'Sumatera Utara' },

      // Sumatera Barat
      { name: 'Padang', code: 'PADANG', provinceName: 'Sumatera Barat' },
      { name: 'Bukittinggi', code: 'BUKITTINGGI', provinceName: 'Sumatera Barat' },
      { name: 'Padangpanjang', code: 'PADANGPANJANG', provinceName: 'Sumatera Barat' },
      { name: 'Payakumbuh', code: 'PAYAKUMBUH', provinceName: 'Sumatera Barat' },
      { name: 'Sawahlunto', code: 'SAWAHLUNTO', provinceName: 'Sumatera Barat' },
      { name: 'Solok', code: 'SOLOK', provinceName: 'Sumatera Barat' },
      { name: 'Pariaman', code: 'PARIAMAN', provinceName: 'Sumatera Barat' },

      // Riau
      { name: 'Pekanbaru', code: 'PEKANBARU', provinceName: 'Riau' },
      { name: 'Dumai', code: 'DUMAI', provinceName: 'Riau' },

      // Jambi
      { name: 'Jambi', code: 'JAMBI', provinceName: 'Jambi' },
      { name: 'Sungai Penuh', code: 'SUNGAI_PENUH', provinceName: 'Jambi' },

      // Sumatera Selatan
      { name: 'Palembang', code: 'PALEMBANG', provinceName: 'Sumatera Selatan' },
      { name: 'Prabumulih', code: 'PRABUMULIH', provinceName: 'Sumatera Selatan' },
      { name: 'Pagar Alam', code: 'PAGAR_ALAM', provinceName: 'Sumatera Selatan' },
      { name: 'Lubuklinggau', code: 'LUBUKLINGGAU', provinceName: 'Sumatera Selatan' },

      // Bengkulu
      { name: 'Bengkulu', code: 'BENGKULU', provinceName: 'Bengkulu' },

      // Lampung
      { name: 'Bandar Lampung', code: 'BANDAR_LAMPUNG', provinceName: 'Lampung' },
      { name: 'Metro', code: 'METRO', provinceName: 'Lampung' },

      // Kepulauan Bangka Belitung
      { name: 'Pangkalpinang', code: 'PANGKALPINANG', provinceName: 'Kepulauan Bangka Belitung' },

      // Kepulauan Riau
      { name: 'Tanjungpinang', code: 'TANJUNGPINANG', provinceName: 'Kepulauan Riau' },
      { name: 'Batam', code: 'BATAM', provinceName: 'Kepulauan Riau' },

      // DKI Jakarta
      { name: 'Jakarta Pusat', code: 'JAKARTA_PUSAT', provinceName: 'DKI Jakarta' },
      { name: 'Jakarta Utara', code: 'JAKARTA_UTARA', provinceName: 'DKI Jakarta' },
      { name: 'Jakarta Barat', code: 'JAKARTA_BARAT', provinceName: 'DKI Jakarta' },
      { name: 'Jakarta Selatan', code: 'JAKARTA_SELATAN', provinceName: 'DKI Jakarta' },
      { name: 'Jakarta Timur', code: 'JAKARTA_TIMUR', provinceName: 'DKI Jakarta' },
      { name: 'Kepulauan Seribu', code: 'KEPULAUAN_SERIBU', provinceName: 'DKI Jakarta' },

      // Jawa Barat
      { name: 'Bandung', code: 'BANDUNG', provinceName: 'Jawa Barat' },
      { name: 'Bekasi', code: 'BEKASI', provinceName: 'Jawa Barat' },
      { name: 'Bogor', code: 'BOGOR', provinceName: 'Jawa Barat' },
      { name: 'Cimahi', code: 'CIMAHI', provinceName: 'Jawa Barat' },
      { name: 'Cirebon', code: 'CIREBON', provinceName: 'Jawa Barat' },
      { name: 'Depok', code: 'DEPOK', provinceName: 'Jawa Barat' },
      { name: 'Sukabumi', code: 'SUKABUMI', provinceName: 'Jawa Barat' },
      { name: 'Tasikmalaya', code: 'TASIKMALAYA', provinceName: 'Jawa Barat' },
      { name: 'Banjar', code: 'BANJAR', provinceName: 'Jawa Barat' },

      // Jawa Tengah
      { name: 'Semarang', code: 'SEMARANG', provinceName: 'Jawa Tengah' },
      { name: 'Surakarta', code: 'SURAKARTA', provinceName: 'Jawa Tengah' },
      { name: 'Magelang', code: 'MAGELANG', provinceName: 'Jawa Tengah' },
      { name: 'Pekalongan', code: 'PEKALONGAN', provinceName: 'Jawa Tengah' },
      { name: 'Tegal', code: 'TEGAL', provinceName: 'Jawa Tengah' },
      { name: 'Salatiga', code: 'SALATIGA', provinceName: 'Jawa Tengah' },

      // DI Yogyakarta
      { name: 'Yogyakarta', code: 'YOGYAKARTA', provinceName: 'DI Yogyakarta' },

      // Jawa Timur
      { name: 'Surabaya', code: 'SURABAYA', provinceName: 'Jawa Timur' },
      { name: 'Malang', code: 'MALANG', provinceName: 'Jawa Timur' },
      { name: 'Kediri', code: 'KEDIRI', provinceName: 'Jawa Timur' },
      { name: 'Blitar', code: 'BLITAR', provinceName: 'Jawa Timur' },
      { name: 'Mojokerto', code: 'MOJOKERTO', provinceName: 'Jawa Timur' },
      { name: 'Madiun', code: 'MADIUN', provinceName: 'Jawa Timur' },
      { name: 'Pasuruan', code: 'PASURUAN', provinceName: 'Jawa Timur' },
      { name: 'Probolinggo', code: 'PROBOLINGGO', provinceName: 'Jawa Timur' },
      { name: 'Batu', code: 'BATU', provinceName: 'Jawa Timur' },

      // Banten
      { name: 'Serang', code: 'SERANG', provinceName: 'Banten' },
      { name: 'Tangerang', code: 'TANGERANG', provinceName: 'Banten' },
      { name: 'Tangerang Selatan', code: 'TANGERANG_SELATAN', provinceName: 'Banten' },
      { name: 'Cilegon', code: 'CILEGON', provinceName: 'Banten' },

      // Bali
      { name: 'Denpasar', code: 'DENPASAR', provinceName: 'Bali' },

      // Nusa Tenggara Barat
      { name: 'Mataram', code: 'MATARAM', provinceName: 'Nusa Tenggara Barat' },
      { name: 'Bima', code: 'BIMA', provinceName: 'Nusa Tenggara Barat' },

      // Nusa Tenggara Timur
      { name: 'Kupang', code: 'KUPANG', provinceName: 'Nusa Tenggara Timur' },

      // Kalimantan Barat
      { name: 'Pontianak', code: 'PONTIANAK', provinceName: 'Kalimantan Barat' },
      { name: 'Singkawang', code: 'SINGKAWANG', provinceName: 'Kalimantan Barat' },

      // Kalimantan Tengah
      { name: 'Palangka Raya', code: 'PALANGKA_RAYA', provinceName: 'Kalimantan Tengah' },

      // Kalimantan Selatan
      { name: 'Banjarmasin', code: 'BANJARMASIN', provinceName: 'Kalimantan Selatan' },
      { name: 'Banjarbaru', code: 'BANJARBARU', provinceName: 'Kalimantan Selatan' },

      // Kalimantan Timur
      { name: 'Samarinda', code: 'SAMARINDA', provinceName: 'Kalimantan Timur' },
      { name: 'Balikpapan', code: 'BALIKPAPAN', provinceName: 'Kalimantan Timur' },
      { name: 'Bontang', code: 'BONTANG', provinceName: 'Kalimantan Timur' },

      // Kalimantan Utara
      { name: 'Tarakan', code: 'TARAKAN', provinceName: 'Kalimantan Utara' },

      // Sulawesi Utara
      { name: 'Manado', code: 'MANADO', provinceName: 'Sulawesi Utara' },
      { name: 'Bitung', code: 'BITUNG', provinceName: 'Sulawesi Utara' },
      { name: 'Tomohon', code: 'TOMOHON', provinceName: 'Sulawesi Utara' },
      { name: 'Kotamobagu', code: 'KOTAMOBAGU', provinceName: 'Sulawesi Utara' },

      // Sulawesi Tengah
      { name: 'Palu', code: 'PALU', provinceName: 'Sulawesi Tengah' },

      // Sulawesi Selatan
      { name: 'Makassar', code: 'MAKASSAR', provinceName: 'Sulawesi Selatan' },
      { name: 'Parepare', code: 'PAREPARE', provinceName: 'Sulawesi Selatan' },
      { name: 'Palopo', code: 'PALOPO', provinceName: 'Sulawesi Selatan' },

      // Sulawesi Tenggara
      { name: 'Kendari', code: 'KENDARI', provinceName: 'Sulawesi Tenggara' },
      { name: 'Bau-Bau', code: 'BAU_BAU', provinceName: 'Sulawesi Tenggara' },

      // Gorontalo
      { name: 'Gorontalo', code: 'GORONTALO', provinceName: 'Gorontalo' },

      // Sulawesi Barat
      { name: 'Mamuju', code: 'MAMUJU', provinceName: 'Sulawesi Barat' },

      // Maluku
      { name: 'Ambon', code: 'AMBON', provinceName: 'Maluku' },
      { name: 'Tual', code: 'TUAL', provinceName: 'Maluku' },

      // Maluku Utara
      { name: 'Ternate', code: 'TERNATE', provinceName: 'Maluku Utara' },
      { name: 'Tidore Kepulauan', code: 'TIDORE_KEPULAUAN', provinceName: 'Maluku Utara' },

      // Papua Barat
      { name: 'Manokwari', code: 'MANOKWARI', provinceName: 'Papua Barat' },
      { name: 'Sorong', code: 'SORONG', provinceName: 'Papua Barat' },

      // Papua
      { name: 'Jayapura', code: 'JAYAPURA', provinceName: 'Papua' },

      // Papua Tengah
      { name: 'Nabire', code: 'NABIRE', provinceName: 'Papua Tengah' },

      // Papua Pegunungan
      { name: 'Jayawijaya', code: 'JAYAWIJAYA', provinceName: 'Papua Pegunungan' },

      // Papua Selatan
      { name: 'Merauke', code: 'MERAUKE', provinceName: 'Papua Selatan' },

      // Papua Barat Daya
      { name: 'Fakfak', code: 'FAKFAK', provinceName: 'Papua Barat Daya' }
    ];

    // Clear existing data
    console.log('🗑️  Clearing existing location data...');
    await prisma.city.deleteMany();
    await prisma.province.deleteMany();

    // Seed provinces using upsert
    console.log('🏛️  Seeding provinces...');
    const createdProvinces: { [key: string]: number } = {};

    for (const province of provinces) {
      const createdProvince = await prisma.province.upsert({
        where: { name: province.name },
        update: {
          code: province.code
        },
        create: {
          name: province.name,
          code: province.code
        }
      });
      createdProvinces[province.name] = createdProvince.id;
    }

    // Seed cities
    console.log('🏙️  Seeding cities...');
    for (const city of cities) {
      const provinceId = createdProvinces[city.provinceName];
      if (!provinceId) {
        console.error(`❌ Province not found: ${city.provinceName}`);
        continue;
      }

      await prisma.city.create({
        data: {
          name: city.name,
          code: city.code,
          provinceId: provinceId
        }
      });
    }

    console.log('✅ Location data seeded successfully!');
    console.log(`   📍 ${provinces.length} provinces created/updated`);
    console.log(`   🏙️  ${cities.length} cities created`);
  } catch (error) {
    console.error('❌ Error seeding location data:', error);
    throw error;
  }
}

export default seedLocation;

// Run seeder if called directly
if (require.main === module) {
  seedLocation()
    .catch((e) => {
      console.error(e);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
}
