import prisma from '../../src/client';
import { beforeAll, beforeEach, afterAll } from '@jest/globals';

const clearDatabase = async () => {
  try {
    // Use raw SQL with CASCADE to handle foreign key constraints
    await prisma.$executeRaw`TRUNCATE TABLE "Payment" CASCADE`;
    await prisma.$executeRaw`TRUNCATE TABLE "OrderItem" CASCADE`;
    await prisma.$executeRaw`TRUNCATE TABLE "Order" CASCADE`;
    await prisma.$executeRaw`TRUNCATE TABLE "Service" CASCADE`;
    await prisma.$executeRaw`TRUNCATE TABLE "Attendance" CASCADE`;
    await prisma.$executeRaw`TRUNCATE TABLE "WorkSchedule" CASCADE`;
    await prisma.$executeRaw`TRUNCATE TABLE "ScheduleTemplate" CASCADE`;
    await prisma.$executeRaw`TRUNCATE TABLE "AttendanceSettings" CASCADE`;
    await prisma.$executeRaw`TRUNCATE TABLE "customer_notes" CASCADE`;
    await prisma.$executeRaw`TRUNCATE TABLE "customer_financials" CASCADE`;
    await prisma.$executeRaw`TRUNCATE TABLE "customers" CASCADE`;
    await prisma.$executeRaw`TRUNCATE TABLE "PhoneVerification" CASCADE`;
    await prisma.$executeRaw`TRUNCATE TABLE "EmailVerification" CASCADE`;
    await prisma.$executeRaw`TRUNCATE TABLE "Token" CASCADE`;
    await prisma.$executeRaw`TRUNCATE TABLE "User" CASCADE`;
    await prisma.$executeRaw`TRUNCATE TABLE "Outlet" CASCADE`;
    await prisma.$executeRaw`TRUNCATE TABLE "City" CASCADE`;
    await prisma.$executeRaw`TRUNCATE TABLE "Province" CASCADE`;
  } catch (error) {
    console.error('Error in clearDatabase:', error);
    throw error;
  }
};

const closeDatabase = async () => {
  await clearDatabase();
  await prisma.$disconnect();
};

const setupTestDB = () => {
  beforeAll(async () => {
    await prisma.$connect();
  });

  beforeEach(async () => {
    await prisma.payment.deleteMany();
    await prisma.orderItem.deleteMany();
    await prisma.order.deleteMany();
    await prisma.customerNote.deleteMany();
    await prisma.customerFinancial.deleteMany();
    await prisma.customer.deleteMany();
    await prisma.service.deleteMany();
    // Delete attendance-related tables first to avoid foreign key constraints
    await prisma.attendance.deleteMany();
    await prisma.workSchedule.deleteMany();
    await prisma.scheduleTemplate.deleteMany();
    await prisma.attendanceSettings.deleteMany();
    await prisma.outlet.deleteMany();
    await prisma.token.deleteMany();
    await prisma.phoneVerification.deleteMany();
    await prisma.emailVerification.deleteMany();
    await prisma.user.deleteMany();
    await prisma.city.deleteMany();
    await prisma.province.deleteMany();
  });

  afterAll(async () => {
    await prisma.$disconnect();
  });
};

// Optimized version for read-only tests like location
const setupTestDBOptimized = () => {
  beforeAll(async () => {
    await prisma.$connect();
    // Clean once at the beginning
    await clearDatabase();
  }, 30000);

  beforeEach(async () => {
    // Use raw SQL with CASCADE to handle foreign key constraints
    await prisma.$executeRaw`TRUNCATE TABLE "Payment" CASCADE`;
    await prisma.$executeRaw`TRUNCATE TABLE "OrderItem" CASCADE`;
    await prisma.$executeRaw`TRUNCATE TABLE "Order" CASCADE`;
    await prisma.$executeRaw`TRUNCATE TABLE "Service" CASCADE`;
    await prisma.$executeRaw`TRUNCATE TABLE "Attendance" CASCADE`;
    await prisma.$executeRaw`TRUNCATE TABLE "WorkSchedule" CASCADE`;
    await prisma.$executeRaw`TRUNCATE TABLE "ScheduleTemplate" CASCADE`;
    await prisma.$executeRaw`TRUNCATE TABLE "AttendanceSettings" CASCADE`;
    await prisma.$executeRaw`TRUNCATE TABLE "customer_notes" CASCADE`;
    await prisma.$executeRaw`TRUNCATE TABLE "customer_financials" CASCADE`;
    await prisma.$executeRaw`TRUNCATE TABLE "customers" CASCADE`;
    await prisma.$executeRaw`TRUNCATE TABLE "PhoneVerification" CASCADE`;
    await prisma.$executeRaw`TRUNCATE TABLE "EmailVerification" CASCADE`;
    await prisma.$executeRaw`TRUNCATE TABLE "Token" CASCADE`;
    await prisma.$executeRaw`TRUNCATE TABLE "User" CASCADE`;
    await prisma.$executeRaw`TRUNCATE TABLE "Outlet" CASCADE`;
  }, 10000);

  afterAll(async () => {
    await closeDatabase();
  }, 15000);
};

export default setupTestDB;
export { setupTestDBOptimized, clearDatabase, closeDatabase };
