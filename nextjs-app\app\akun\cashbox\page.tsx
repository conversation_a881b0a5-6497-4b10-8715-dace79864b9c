'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import {
  ArrowLeft,
  Search,
  Plus,
  Filter,
  MoreVertical,
  Edit,
  Trash2,
  Eye,
  Wallet,
  TrendingUp,
  Loader2,
  RefreshCw,
  DollarSign,
} from 'lucide-react';
import { useRouter } from 'next/navigation';

import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Separator } from '@/components/ui/separator';

import {
  useCashboxes,
  useDeleteCashbox,
  formatCurrency,
  getCashboxTypeBadge,
  getCashboxStatusBadge,
} from '@/hooks/useCashbox';
import type { GetCashboxesParams } from '@/lib/api/cashbox';
import { ensureAuthSync } from '@/lib/auth-sync';
import { toast } from '@/components/ui/use-toast';

export default function CashboxPage() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState('semua');
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [cashboxToDelete, setCashboxToDelete] = useState<{
    id: number;
    name: string;
    balance: number;
  } | null>(null);
  const [showFilterDialog, setShowFilterDialog] = useState(false);
  const [authError, setAuthError] = useState(false);
  const [filterOptions, setFilterOptions] = useState({
    type: 'semua',
    isActive: 'semua',
  });

  // Ensure auth sync on component mount
  useEffect(() => {
    ensureAuthSync();

    // Check if user is properly authenticated
    const checkAuth = () => {
      const user = localStorage.getItem('user');
      const tokens = localStorage.getItem('tokens');

      if (!user || !tokens) {
        console.log('No auth data found');
        setAuthError(true);
        return;
      }

      try {
        const parsedTokens = JSON.parse(tokens);
        const accessToken = parsedTokens?.access?.token;

        if (!accessToken) {
          console.log('No access token found');
          setAuthError(true);
          return;
        }

        // Check if token is expired
        const expiresAt = new Date(parsedTokens.access.expires);
        const now = new Date();

        if (expiresAt <= now) {
          console.log('Access token expired, API will handle refresh');
        }

        setAuthError(false);
      } catch (error) {
        console.error('Error parsing auth data:', error);
        setAuthError(true);
      }
    };

    checkAuth();
  }, [router]);

  // Build API params from current state
  const apiParams: GetCashboxesParams = {
    type:
      filterOptions.type !== 'semua'
        ? (filterOptions.type as 'TUNAI' | 'NON_TUNAI')
        : undefined,
    isActive:
      filterOptions.isActive !== 'semua'
        ? filterOptions.isActive === 'aktif'
        : undefined,
  };

  // Apply tab filter to type
  if (activeTab !== 'semua') {
    if (activeTab === 'tunai') {
      apiParams.type = 'TUNAI';
    } else if (activeTab === 'non-tunai') {
      apiParams.type = 'NON_TUNAI';
    } else if (activeTab === 'aktif') {
      apiParams.isActive = true;
    } else if (activeTab === 'nonaktif') {
      apiParams.isActive = false;
    }
  }

  const {
    data: cashboxes = [],
    isLoading,
    error,
    refetch,
    isRefetching,
  } = useCashboxes(apiParams);

  const deleteCustomerMutation = useDeleteCashbox();

  const handleDeleteCashbox = (id: number, name: string, balance: number) => {
    setCashboxToDelete({ id, name, balance });
    setShowDeleteDialog(true);
  };

  const confirmDelete = async () => {
    if (cashboxToDelete) {
      // Check if cashbox has balance
      if (cashboxToDelete.balance !== 0) {
        toast({
          title: 'Error',
          description:
            'Tidak dapat menghapus cashbox yang memiliki saldo. Kosongkan saldo terlebih dahulu.',
          variant: 'destructive',
        });
        setShowDeleteDialog(false);
        setCashboxToDelete(null);
        return;
      }

      try {
        await deleteCustomerMutation.mutateAsync(cashboxToDelete.id);
        setShowDeleteDialog(false);
        setCashboxToDelete(null);
      } catch {
        // Error sudah ditangani di mutation
      }
    }
  };

  const applyFilters = () => {
    setShowFilterDialog(false);
  };

  const handleCashboxClick = (e: React.MouseEvent, cashboxId: number) => {
    // Prevent navigation if clicking on dropdown menu
    if ((e.target as HTMLElement).closest('.dropdown-menu')) {
      return;
    }
    router.push(`/akun/cashbox/${cashboxId}`);
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  // Filter cashboxes based on search query
  const filteredCashboxes = cashboxes.filter((cashbox) =>
    searchQuery
      ? cashbox.name.toLowerCase().includes(searchQuery.toLowerCase())
      : true
  );

  // Calculate tab counts
  const getTabCounts = () => {
    if (!cashboxes)
      return { semua: 0, tunai: 0, nonTunai: 0, aktif: 0, nonaktif: 0 };

    const counts = cashboxes.reduce(
      (acc, cashbox) => {
        acc.semua++;
        if (cashbox.type === 'TUNAI') acc.tunai++;
        if (cashbox.type === 'NON_TUNAI') acc.nonTunai++;
        if (cashbox.isActive) acc.aktif++;
        if (!cashbox.isActive) acc.nonaktif++;
        return acc;
      },
      { semua: 0, tunai: 0, nonTunai: 0, aktif: 0, nonaktif: 0 }
    );

    return counts;
  };

  const tabCounts = getTabCounts();

  // Calculate total balance
  const totalBalance = cashboxes.reduce(
    (sum, cashbox) => sum + cashbox.balance,
    0
  );
  const activeCashboxes = cashboxes.filter((cb) => cb.isActive).length;

  const handleLoginRedirect = () => {
    localStorage.clear();
    router.push('/auth/login');
  };

  if (authError) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card className="max-w-md mx-auto">
          <CardContent className="pt-6">
            <div className="text-center">
              <h2 className="text-lg font-semibold mb-2">Sesi Berakhir</h2>
              <p className="text-gray-600 mb-4">
                Silakan login kembali untuk melanjutkan.
              </p>
              <Button onClick={handleLoginRedirect} className="w-full">
                Login
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card className="max-w-md mx-auto">
          <CardContent className="pt-6">
            <div className="text-center">
              <h2 className="text-lg font-semibold mb-2">Terjadi Kesalahan</h2>
              <p className="text-gray-600 mb-4">
                Gagal memuat data cashbox. Silakan coba lagi.
              </p>
              <Button onClick={() => refetch()} className="w-full">
                <RefreshCw className="w-4 h-4 mr-2" />
                Coba Lagi
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-4">
      {/* Header - Mobile Responsive */}
      <header className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3 mb-4">
        <div className="flex items-center gap-3">
          <Link href="/akun">
            <Button variant="ghost" size="sm" className="p-2">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <div>
            <h1 className="text-lg font-semibold">Cashbox</h1>
            <p className="text-sm text-muted-foreground hidden sm:block">
              Kelola cashbox outlet
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => refetch()}
            disabled={isRefetching}
            className="hidden sm:flex"
          >
            <RefreshCw
              className={`h-4 w-4 mr-2 ${isRefetching ? 'animate-spin' : ''}`}
            />
            Refresh
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => refetch()}
            disabled={isRefetching}
            className="sm:hidden p-2"
          >
            <RefreshCw
              className={`h-4 w-4 ${isRefetching ? 'animate-spin' : ''}`}
            />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowFilterDialog(true)}
            className="hidden sm:flex"
          >
            <Filter className="h-4 w-4 mr-2" />
            Filter
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowFilterDialog(true)}
            className="sm:hidden p-2"
          >
            <Filter className="h-4 w-4" />
          </Button>
          <Button
            size="sm"
            className="bg-blue-500 hover:bg-blue-600"
            onClick={() => router.push('/akun/cashbox/add')}
          >
            <Plus className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">Tambah</span>
          </Button>
        </div>
      </header>

      {/* Stats Cards - Minimalis */}
      <div className="grid grid-cols-3 gap-3 mb-4">
        <Card className="border-0 bg-gradient-to-r from-green-50 to-green-100">
          <CardContent className="p-3">
            <div className="flex items-center gap-2">
              <DollarSign className="w-5 h-5 text-green-600" />
              <div>
                <p className="text-xs text-gray-600">Total Saldo</p>
                <p className="text-sm font-bold text-green-700">
                  {formatCurrency(totalBalance)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card className="border-0 bg-gradient-to-r from-blue-50 to-blue-100">
          <CardContent className="p-3">
            <div className="flex items-center gap-2">
              <Wallet className="w-5 h-5 text-blue-600" />
              <div>
                <p className="text-xs text-gray-600">Total</p>
                <p className="text-sm font-bold text-blue-700">
                  {cashboxes.length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card className="border-0 bg-gradient-to-r from-emerald-50 to-emerald-100">
          <CardContent className="p-3">
            <div className="flex items-center gap-2">
              <TrendingUp className="w-5 h-5 text-emerald-600" />
              <div>
                <p className="text-xs text-gray-600">Aktif</p>
                <p className="text-sm font-bold text-emerald-700">
                  {activeCashboxes}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filter */}
      <div className="flex gap-3 mb-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Cari cashbox..."
            value={searchQuery}
            onChange={handleSearchChange}
            className="pl-10 h-9"
          />
        </div>
      </div>

      {/* Tabs - Compact */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="mb-4">
        <TabsList className="grid w-full grid-cols-5 h-9">
          <TabsTrigger value="semua" className="text-xs">
            Semua ({tabCounts.semua})
          </TabsTrigger>
          <TabsTrigger value="tunai" className="text-xs">
            Tunai ({tabCounts.tunai})
          </TabsTrigger>
          <TabsTrigger value="non-tunai" className="text-xs">
            Non Tunai ({tabCounts.nonTunai})
          </TabsTrigger>
          <TabsTrigger value="aktif" className="text-xs">
            Aktif ({tabCounts.aktif})
          </TabsTrigger>
          <TabsTrigger value="nonaktif" className="text-xs">
            Nonaktif ({tabCounts.nonaktif})
          </TabsTrigger>
        </TabsList>
      </Tabs>

      {/* Cashbox List */}
      {isLoading ? (
        <div className="flex justify-center items-center py-12">
          <Loader2 className="w-8 h-8 animate-spin" />
        </div>
      ) : filteredCashboxes.length === 0 ? (
        <Card>
          <CardContent className="py-12">
            <div className="text-center">
              <Wallet className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Belum ada cashbox</h3>
              <p className="text-gray-600 mb-4">
                {searchQuery
                  ? 'Tidak ada cashbox yang sesuai dengan pencarian.'
                  : 'Mulai dengan menambahkan cashbox pertama Anda.'}
              </p>
              {!searchQuery && (
                <Link href="/akun/cashbox/add">
                  <Button>
                    <Plus className="w-4 h-4 mr-2" />
                    Tambah Cashbox
                  </Button>
                </Link>
              )}
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-2">
          {filteredCashboxes.map((cashbox) => {
            const typeBadge = getCashboxTypeBadge(cashbox.type);
            const statusBadge = getCashboxStatusBadge(cashbox.isActive);

            return (
              <Card
                key={cashbox.id}
                className="cursor-pointer hover:bg-gray-50 transition-colors border-l-4 border-l-blue-500 shadow-sm"
                onClick={(e) => handleCashboxClick(e, cashbox.id)}
              >
                <CardContent className="p-3">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="font-semibold text-base">
                          {cashbox.name}
                        </h3>
                        <div className="flex gap-1">
                          <Badge
                            variant={typeBadge.variant}
                            className={`${typeBadge.color} text-xs px-2 py-0.5`}
                          >
                            {typeBadge.label}
                          </Badge>
                          <Badge
                            variant={statusBadge.variant}
                            className={`${statusBadge.color} text-xs px-2 py-0.5`}
                          >
                            {statusBadge.label}
                          </Badge>
                        </div>
                      </div>
                      <div className="flex items-center justify-between text-sm text-gray-600">
                        <span>
                          Saldo:{' '}
                          <span className="font-semibold text-gray-900">
                            {formatCurrency(cashbox.balance)}
                          </span>
                        </span>
                        <span>
                          {new Date(cashbox.createdAt).toLocaleDateString(
                            'id-ID',
                            {
                              day: '2-digit',
                              month: '2-digit',
                              year: '2-digit',
                            }
                          )}
                        </span>
                      </div>
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="dropdown-menu ml-2"
                        >
                          <MoreVertical className="w-4 h-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem
                          onClick={(e) => {
                            e.stopPropagation();
                            router.push(`/akun/cashbox/${cashbox.id}`);
                          }}
                        >
                          <Eye className="w-4 h-4 mr-2" />
                          Lihat Detail
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={(e) => {
                            e.stopPropagation();
                            router.push(`/akun/cashbox/edit/${cashbox.id}`);
                          }}
                        >
                          <Edit className="w-4 h-4 mr-2" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteCashbox(
                              cashbox.id,
                              cashbox.name,
                              cashbox.balance
                            );
                          }}
                          className="text-red-600"
                        >
                          <Trash2 className="w-4 h-4 mr-2" />
                          Hapus
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Hapus Cashbox</DialogTitle>
            <DialogDescription>
              Apakah Anda yakin ingin menghapus cashbox{' '}
              <strong>{cashboxToDelete?.name}</strong>?
              {cashboxToDelete?.balance !== 0 && (
                <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-red-700">
                  <strong>Peringatan:</strong> Cashbox ini memiliki saldo{' '}
                  {formatCurrency(cashboxToDelete?.balance || 0)}. Kosongkan
                  saldo terlebih dahulu sebelum menghapus.
                </div>
              )}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowDeleteDialog(false)}
            >
              Batal
            </Button>
            <Button
              variant="destructive"
              onClick={confirmDelete}
              disabled={
                deleteCustomerMutation.isPending ||
                cashboxToDelete?.balance !== 0
              }
            >
              {deleteCustomerMutation.isPending ? (
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              ) : null}
              Hapus
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Filter Dialog */}
      <Dialog open={showFilterDialog} onOpenChange={setShowFilterDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Filter Cashbox</DialogTitle>
            <DialogDescription>
              Atur filter untuk menampilkan cashbox sesuai kriteria.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-6">
            <div>
              <Label className="text-base font-medium">Tipe Cashbox</Label>
              <RadioGroup
                value={filterOptions.type}
                onValueChange={(value) =>
                  setFilterOptions({ ...filterOptions, type: value })
                }
                className="mt-2"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="semua" id="type-semua" />
                  <Label htmlFor="type-semua">Semua Tipe</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="TUNAI" id="type-tunai" />
                  <Label htmlFor="type-tunai">Tunai</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="NON_TUNAI" id="type-non-tunai" />
                  <Label htmlFor="type-non-tunai">Non Tunai</Label>
                </div>
              </RadioGroup>
            </div>

            <Separator />

            <div>
              <Label className="text-base font-medium">Status</Label>
              <RadioGroup
                value={filterOptions.isActive}
                onValueChange={(value) =>
                  setFilterOptions({ ...filterOptions, isActive: value })
                }
                className="mt-2"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="semua" id="status-semua" />
                  <Label htmlFor="status-semua">Semua Status</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="aktif" id="status-aktif" />
                  <Label htmlFor="status-aktif">Aktif</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="nonaktif" id="status-nonaktif" />
                  <Label htmlFor="status-nonaktif">Nonaktif</Label>
                </div>
              </RadioGroup>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowFilterDialog(false)}
            >
              Batal
            </Button>
            <Button onClick={applyFilters}>Terapkan Filter</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
