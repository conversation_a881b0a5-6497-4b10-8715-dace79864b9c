import express from 'express';
import authRoute from './auth.route';
import userRoute from './user.route';
import outletRoute from './outlet.route';
import locationRoute from './location.route';
import employeeRoute from './employee.route';
import customerRoute from './customer.route';
import orderRoute from './order.route';
import attendanceRoute from './attendance.route';
import serviceRoute from './service.route';
import serviceCategoryRoute from './service-category.route';
import perfumeRoute from './perfume.route';
import promotionRoute from './promotion.route';
import docsRoute from './docs.route';
import cashboxRoute from './cashbox.route';
import customerDepositRoute from './customerDeposit.route';
import config from '../../config/config';

const router = express.Router();

const defaultRoutes = [
  {
    path: '/auth',
    route: authRoute
  },
  {
    path: '/users/employees',
    route: employeeRoute
  },
  {
    path: '/users',
    route: userRoute
  },
  {
    path: '/outlets',
    route: outletRoute
  },
  {
    path: '/locations',
    route: locationRoute
  },
  {
    path: '/customers',
    route: customerRoute
  },
  {
    path: '/orders',
    route: orderRoute
  },
  {
    path: '/attendance',
    route: attendanceRoute
  },
  {
    path: '/services',
    route: serviceRoute
  },
  {
    path: '/service-categories',
    route: serviceCategoryRoute
  },
  {
    path: '/perfumes',
    route: perfumeRoute
  },
  {
    path: '/promotions',
    route: promotionRoute
  },
  {
    path: '/',
    route: cashboxRoute
  },
  {
    path: '/',
    route: customerDepositRoute
  }
];

const devRoutes = [
  // routes available only in development mode
  {
    path: '/docs',
    route: docsRoute
  }
];

defaultRoutes.forEach((route) => {
  router.use(route.path, route.route);
});

/* istanbul ignore next */
if (config.env === 'development') {
  devRoutes.forEach((route) => {
    router.use(route.path, route.route);
  });
}

export default router;
