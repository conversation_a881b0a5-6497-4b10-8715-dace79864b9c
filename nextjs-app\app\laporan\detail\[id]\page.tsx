"use client"

import { useState } from "react"
import Link from "next/link"
import { ArrowLeft, Download, Share2, Printer, Edit, Calendar, ChevronDown, RefreshCw } from "lucide-react"
import { format, subDays } from "date-fns"
import { id } from "date-fns/locale"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Separator } from "@/components/ui/separator"

// Mock data for the report
const reportData = {
  id: "monthly-sales",
  name: "Laporan Penjualan Bulanan",
  description: "Ringkasan penjualan bulanan termasuk pendapatan, jumlah order, dan layanan populer",
  lastUpdated: "2 hari yang lalu",
  period: "this-month",
  metrics: [
    {
      id: "revenue",
      name: "Pendapatan Bulanan",
      value: "Rp 8.800.000",
      change: "+12%",
      chartType: "line",
    },
    {
      id: "orders",
      name: "Jumlah Order",
      value: "142",
      change: "+8%",
      chartType: "line",
    },
    {
      id: "service_distribution",
      name: "Distribusi Layanan",
      chartType: "pie",
    },
    {
      id: "customer_growth",
      name: "Pertumbuhan Pelanggan",
      chartType: "bar",
    },
  ],
}

export default function ReportDetailPage({ params }: { params: { id: string } }) {
  const [dateRange, setDateRange] = useState("this-month")

  // Format date for display
  const formatDateRange = () => {
    const today = new Date()
    switch (dateRange) {
      case "today":
        return format(today, "dd MMMM yyyy", { locale: id })
      case "yesterday":
        return format(subDays(today, 1), "dd MMMM yyyy", { locale: id })
      case "this-week":
        return `Minggu Ini`
      case "this-month":
        return format(today, "MMMM yyyy", { locale: id })
      case "this-year":
        return format(today, "yyyy", { locale: id })
      case "custom":
        return "Rentang Kustom"
      default:
        return ""
    }
  }

  // Handle date range changes
  const handleDateRangeChange = (value: string) => {
    setDateRange(value)
  }

  // Handle export report
  const handleExportReport = (format: string) => {
    // In a real app, you would generate and download the report
    alert(`Laporan akan diunduh dalam format ${format.toUpperCase()}`)
  }

  return (
    <div className="flex flex-col min-h-screen bg-slate-50">
      <header className="p-4 flex justify-between items-center bg-white sticky top-0 z-10 shadow-sm">
        <div className="flex items-center">
          <Link href="/laporan" className="mr-3">
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <h1 className="text-lg font-semibold">{reportData.name}</h1>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="icon">
            <Printer className="h-4 w-4" />
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="icon">
                <Download className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Ekspor Laporan</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => handleExportReport("pdf")}>PDF</DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleExportReport("csv")}>CSV</DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleExportReport("excel")}>Excel</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          <Button variant="outline" size="icon">
            <Share2 className="h-4 w-4" />
          </Button>
          <Link href={`/laporan/edit/${params.id}`}>
            <Button variant="outline" size="icon">
              <Edit className="h-4 w-4" />
            </Button>
          </Link>
        </div>
      </header>

      <main className="flex-1 p-4 pb-20">
        <div className="flex justify-between items-center mb-4">
          <div>
            <h2 className="text-lg font-semibold">{reportData.name}</h2>
            <p className="text-sm text-gray-500">Periode: {formatDateRange()}</p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={() => handleDateRangeChange("this-month")}>
              <RefreshCw className="h-3 w-3 mr-1" /> Reset
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm">
                  <Calendar className="h-3 w-3 mr-1" /> Periode <ChevronDown className="h-3 w-3 ml-1" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => handleDateRangeChange("today")}>Hari Ini</DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleDateRangeChange("yesterday")}>Kemarin</DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleDateRangeChange("this-week")}>Minggu Ini</DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleDateRangeChange("this-month")}>Bulan Ini</DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleDateRangeChange("this-year")}>Tahun Ini</DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => handleDateRangeChange("custom")}>Kustom...</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        <Card className="mb-4">
          <CardHeader>
            <CardTitle>Ringkasan</CardTitle>
            <CardDescription>{reportData.description}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {reportData.metrics.slice(0, 2).map((metric) => (
                <div key={metric.id} className="space-y-1">
                  <p className="text-sm text-gray-500">{metric.name}</p>
                  <p className="text-2xl font-bold">{metric.value}</p>
                  {metric.change && (
                    <p className={`text-xs ${metric.change.startsWith("+") ? "text-green-500" : "text-red-500"}`}>
                      {metric.change} dari periode sebelumnya
                    </p>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <div className="space-y-4">
          {reportData.metrics.map((metric) => (
            <Card key={metric.id}>
              <CardHeader>
                <CardTitle>{metric.name}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] w-full bg-gray-100 rounded-md flex items-center justify-center">
                  <span className="text-gray-500">Grafik {metric.name}</span>
                </div>
                {metric.id === "revenue" && (
                  <div className="mt-4">
                    <Separator className="my-4" />
                    <h4 className="font-medium mb-2">Data Mentah</h4>
                    <div className="rounded-md border">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Bulan
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Pendapatan
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Perubahan
                            </th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {["Januari", "Februari", "Maret", "April", "Mei"].map((month, index) => (
                            <tr key={month} className="hover:bg-gray-50">
                              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                {month} 2025
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                Rp {(4500000 + index * 700000).toLocaleString()}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-green-500">+{5 + index * 2}%</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      </main>
    </div>
  )
}
