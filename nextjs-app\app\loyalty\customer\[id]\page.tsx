"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import {
  ArrowLeft,
  Star,
  Calendar,
  Phone,
  Mail,
  MapPin,
  Package,
  Gift,
  ChevronRight,
  Plus,
  Download,
  MoreHorizontal,
  Edit,
  Trash,
  AlertCircle,
  CheckCircle,
  Award,
  Crown,
  Zap,
  Send,
} from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs"
import { Separator } from "@/components/ui/separator"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Di<PERSON>,
  DialogContent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"

// Mock customer data
const customer = {
  id: 2,
  name: "Siti Rahayu",
  phone: "081234567891",
  email: "<EMAIL>",
  address: "Jl. Merdeka No. 123, Jakarta Selatan",
  points: 4200,
  tier: "Gold",
  transactions: 28,
  joinDate: "20/03/2023",
  lastTransaction: "18/03/2024",
  birthDate: "15/05/1990",
  totalSpent: 4850000,
  averageOrder: 173214,
  favoriteService: "Cuci Setrika",
  favoriteOutlet: "Felis Laundry - Kemang",
  notes: "Pelanggan VIP, suka parfum lavender",
  pointsHistory: [
    { id: 1, date: "18/03/2024", amount: 250, description: "Order #TRX-145", type: "earned" },
    { id: 2, date: "02/03/2024", amount: 180, description: "Order #TRX-132", type: "earned" },
    { id: 3, date: "15/02/2024", amount: -750, description: "Redeem: Gratis Antar-Jemput", type: "redeemed" },
    { id: 4, date: "10/02/2024", amount: 200, description: "Order #TRX-120", type: "earned" },
    { id: 5, date: "25/01/2024", amount: 150, description: "Order #TRX-115", type: "earned" },
    { id: 6, date: "15/01/2024", amount: 500, description: "Bonus Ulang Tahun", type: "bonus" },
    { id: 7, date: "05/01/2024", amount: 220, description: "Order #TRX-105", type: "earned" },
    { id: 8, date: "20/12/2023", amount: -200, description: "Redeem: Diskon 10%", type: "redeemed" },
    { id: 9, date: "10/12/2023", amount: 180, description: "Order #TRX-098", type: "earned" },
    { id: 10, date: "25/11/2023", amount: 300, description: "Bonus Referral", type: "bonus" },
  ],
  rewardHistory: [
    {
      id: 1,
      date: "15/02/2024",
      reward: "Gratis Antar-Jemput",
      points: 750,
      status: "Digunakan",
      usedDate: "20/02/2024",
      orderNumber: "TRX-132",
    },
    {
      id: 2,
      date: "20/12/2023",
      reward: "Diskon 10%",
      points: 200,
      status: "Digunakan",
      usedDate: "22/12/2023",
      orderNumber: "TRX-098",
    },
    {
      id: 3,
      date: "10/10/2023",
      reward: "Voucher Rp 50.000",
      points: 1500,
      status: "Kedaluwarsa",
      expiryDate: "10/11/2023",
    },
  ],
  orderHistory: [
    {
      id: "TRX-145",
      date: "18/03/2024",
      items: "Cuci Setrika 5kg",
      amount: 125000,
      status: "Selesai",
      pointsEarned: 250,
    },
    {
      id: "TRX-132",
      date: "02/03/2024",
      items: "Cuci Setrika 3.6kg",
      amount: 90000,
      status: "Selesai",
      pointsEarned: 180,
    },
    {
      id: "TRX-120",
      date: "10/02/2024",
      items: "Cuci Setrika 4kg, Bed Cover 1pc",
      amount: 200000,
      status: "Selesai",
      pointsEarned: 200,
    },
    {
      id: "TRX-115",
      date: "25/01/2024",
      items: "Cuci Setrika 3kg",
      amount: 75000,
      status: "Selesai",
      pointsEarned: 150,
    },
    {
      id: "TRX-105",
      date: "05/01/2024",
      items: "Cuci Setrika 4.4kg",
      amount: 110000,
      status: "Selesai",
      pointsEarned: 220,
    },
  ],
  referrals: [
    {
      id: 1,
      name: "Budi Santoso",
      date: "25/11/2023",
      status: "Aktif",
      orders: 7,
      bonusEarned: 300,
    },
    {
      id: 2,
      name: "Dewi Anggraini",
      date: "10/01/2024",
      status: "Aktif",
      orders: 5,
      bonusEarned: 300,
    },
  ],
  availableRewards: [
    {
      id: 1,
      name: "Diskon 10%",
      description: "Diskon 10% untuk order berikutnya",
      pointsRequired: 200,
      category: "diskon",
    },
    {
      id: 2,
      name: "Gratis Cuci 2kg",
      description: "Gratis layanan cuci 2kg untuk order berikutnya",
      pointsRequired: 500,
      category: "layanan",
    },
    {
      id: 3,
      name: "Gratis Antar-Jemput",
      description: "Gratis layanan antar-jemput untuk 3 order berikutnya",
      pointsRequired: 750,
      category: "pengiriman",
    },
    {
      id: 4,
      name: "Diskon 25%",
      description: "Diskon 25% untuk order berikutnya",
      pointsRequired: 1000,
      category: "diskon",
    },
    {
      id: 5,
      name: "Voucher Rp 50.000",
      description: "Voucher senilai Rp 50.000 untuk order berikutnya",
      pointsRequired: 1500,
      category: "voucher",
    },
  ],
}

// Tier data
const tiers = [
  {
    name: "Bronze",
    pointsRequired: 0,
    benefits: ["Poin setiap transaksi", "Diskon ulang tahun 5%"],
    color: "bg-amber-600",
    icon: <Award className="h-5 w-5" />,
  },
  {
    name: "Silver",
    pointsRequired: 1000,
    benefits: ["Poin setiap transaksi", "Diskon ulang tahun 10%", "Gratis antar-jemput 1x/bulan"],
    color: "bg-gray-400",
    icon: <Award className="h-5 w-5" />,
  },
  {
    name: "Gold",
    pointsRequired: 3000,
    benefits: [
      "Poin setiap transaksi",
      "Diskon ulang tahun 15%",
      "Gratis antar-jemput 2x/bulan",
      "Prioritas pengerjaan",
    ],
    color: "bg-yellow-500",
    icon: <Crown className="h-5 w-5" />,
  },
  {
    name: "Platinum",
    pointsRequired: 6000,
    benefits: [
      "Poin setiap transaksi",
      "Diskon ulang tahun 20%",
      "Gratis antar-jemput unlimited",
      "Prioritas pengerjaan",
      "Diskon 5% setiap transaksi",
    ],
    color: "bg-blue-600",
    icon: <Zap className="h-5 w-5" />,
  },
]

export default function CustomerDetailPage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState("overview")
  const [showAddPointsDialog, setShowAddPointsDialog] = useState(false)
  const [showRedeemDialog, setShowRedeemDialog] = useState(false)
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [showReferralDialog, setShowReferralDialog] = useState(false)
  const [selectedReward, setSelectedReward] = useState<any>(null)

  const getTierColor = (tier: string) => {
    const tierObj = tiers.find((t) => t.name === tier)
    return tierObj ? tierObj.color : "bg-gray-500"
  }

  const getTierIcon = (tier: string) => {
    const tierObj = tiers.find((t) => t.name === tier)
    return tierObj ? tierObj.icon : <Award className="h-5 w-5" />
  }

  const getNextTier = (currentTier: string) => {
    const currentIndex = tiers.findIndex((t) => t.name === currentTier)
    if (currentIndex < tiers.length - 1) {
      return tiers[currentIndex + 1]
    }
    return null
  }

  const nextTier = getNextTier(customer.tier)

  const handleRedeemReward = (reward: any) => {
    setSelectedReward(reward)
    setShowRedeemDialog(true)
  }

  return (
    <div className="flex flex-col min-h-screen bg-slate-50">
      <header className="p-4 flex justify-between items-center bg-white sticky top-0 z-10 shadow-sm">
        <div className="flex items-center">
          <Link href="/loyalty" className="mr-3">
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <h1 className="text-lg font-semibold">Detail Pelanggan</h1>
        </div>
        <div className="flex items-center gap-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon">
                <MoreHorizontal className="h-5 w-5" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Aksi</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => setShowEditDialog(true)}>
                <Edit className="h-4 w-4 mr-2" />
                Edit Pelanggan
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setShowAddPointsDialog(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Tambah Poin
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setShowReferralDialog(true)}>
                <Send className="h-4 w-4 mr-2" />
                Kirim Kode Referral
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => router.push(`/loyalty/customer/${params.id}/export`)}>
                <Download className="h-4 w-4 mr-2" />
                Export Data
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => setShowDeleteDialog(true)} className="text-red-500">
                <Trash className="h-4 w-4 mr-2" />
                Hapus Pelanggan
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </header>

      <div className="p-4">
        <Card className="mb-4">
          <CardContent className="p-4">
            <div className="flex flex-col md:flex-row md:items-center gap-4">
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-1">
                  <h2 className="text-xl font-semibold">{customer.name}</h2>
                  <Badge className={`${getTierColor(customer.tier)} text-white flex items-center gap-1`}>
                    {getTierIcon(customer.tier)}
                    {customer.tier}
                  </Badge>
                </div>
                <div className="flex flex-col gap-1 text-sm text-gray-500">
                  <div className="flex items-center gap-1">
                    <Phone className="h-3.5 w-3.5" />
                    <span>{customer.phone}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Mail className="h-3.5 w-3.5" />
                    <span>{customer.email}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <MapPin className="h-3.5 w-3.5" />
                    <span>{customer.address}</span>
                  </div>
                </div>
              </div>
              <div className="flex flex-col items-center md:items-end gap-1">
                <div className="flex items-center gap-1">
                  <Star className="h-5 w-5 text-yellow-500" />
                  <span className="text-xl font-bold">{customer.points}</span>
                  <span className="text-sm text-gray-500">poin</span>
                </div>
                <div className="flex items-center gap-1 text-sm text-gray-500">
                  <Package className="h-3.5 w-3.5" />
                  <span>{customer.transactions} transaksi</span>
                </div>
                <div className="flex items-center gap-1 text-sm text-gray-500">
                  <Calendar className="h-3.5 w-3.5" />
                  <span>Bergabung {customer.joinDate}</span>
                </div>
              </div>
            </div>

            {nextTier && (
              <div className="mt-4">
                <div className="flex justify-between text-xs text-gray-500 mb-1">
                  <span>{customer.tier}</span>
                  <span>{nextTier.name}</span>
                </div>
                <Progress
                  value={
                    ((customer.points - tiers.find((t) => t.name === customer.tier)!.pointsRequired) /
                      (nextTier.pointsRequired - tiers.find((t) => t.name === customer.tier)!.pointsRequired)) *
                    100
                  }
                  className="h-2"
                />
                <p className="text-xs text-gray-500 mt-1 text-right">
                  {nextTier.pointsRequired - customer.points} poin lagi untuk {nextTier.name}
                </p>
              </div>
            )}

            <div className="flex flex-wrap gap-2 mt-4">
              <Button
                variant="outline"
                size="sm"
                className="text-blue-500 border-blue-500"
                onClick={() => setShowAddPointsDialog(true)}
              >
                <Plus className="h-4 w-4 mr-1" />
                Tambah Poin
              </Button>
              <Button variant="outline" size="sm" onClick={() => router.push(`/orders/create?customer=${customer.id}`)}>
                <Package className="h-4 w-4 mr-1" />
                Buat Pesanan
              </Button>
              <Button variant="outline" size="sm" onClick={() => router.push(`/loyalty/customer/${params.id}/message`)}>
                <Send className="h-4 w-4 mr-1" />
                Kirim Pesan
              </Button>
            </div>
          </CardContent>
        </Card>

        <Tabs defaultValue="overview" className="mb-4" onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-4 w-full">
            <TabsTrigger value="overview">Ringkasan</TabsTrigger>
            <TabsTrigger value="points">Poin</TabsTrigger>
            <TabsTrigger value="rewards">Rewards</TabsTrigger>
            <TabsTrigger value="referrals">Referral</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="mt-4 space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-base">Statistik Pelanggan</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-500">Total Belanja</span>
                      <span className="font-medium">Rp {customer.totalSpent.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-500">Rata-rata Order</span>
                      <span className="font-medium">Rp {customer.averageOrder.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-500">Layanan Favorit</span>
                      <span className="font-medium">{customer.favoriteService}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-500">Outlet Favorit</span>
                      <span className="font-medium">{customer.favoriteOutlet}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-500">Tanggal Lahir</span>
                      <span className="font-medium">{customer.birthDate}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-base">Catatan Pelanggan</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600">{customer.notes || "Tidak ada catatan"}</p>
                  <Button variant="ghost" size="sm" className="mt-2" onClick={() => setShowEditDialog(true)}>
                    <Edit className="h-3.5 w-3.5 mr-1" />
                    Edit Catatan
                  </Button>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">Transaksi Terakhir</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {customer.orderHistory.slice(0, 3).map((order) => (
                    <div key={order.id} className="flex justify-between items-center">
                      <div>
                        <div className="flex items-center gap-1">
                          <span className="font-medium">{order.id}</span>
                          <Badge variant="outline" className="text-green-500 border-green-500">
                            {order.status}
                          </Badge>
                        </div>
                        <div className="text-sm text-gray-500">
                          <span>{order.date}</span> • <span>{order.items}</span>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-medium">Rp {order.amount.toLocaleString()}</div>
                        <div className="text-sm text-yellow-500 flex items-center justify-end">
                          <Star className="h-3.5 w-3.5 mr-1" />
                          {order.pointsEarned} poin
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
                {customer.orderHistory.length > 3 && (
                  <Button variant="ghost" size="sm" className="mt-3 w-full" onClick={() => setActiveTab("points")}>
                    Lihat Semua Transaksi
                    <ChevronRight className="h-4 w-4 ml-1" />
                  </Button>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">Rewards Tersedia</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {customer.availableRewards.slice(0, 3).map((reward) => (
                    <div key={reward.id} className="flex justify-between items-center">
                      <div>
                        <div className="font-medium">{reward.name}</div>
                        <div className="text-sm text-gray-500">{reward.description}</div>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        disabled={customer.points < reward.pointsRequired}
                        onClick={() => handleRedeemReward(reward)}
                      >
                        <Star className="h-3.5 w-3.5 mr-1 text-yellow-500" />
                        {reward.pointsRequired} poin
                      </Button>
                    </div>
                  ))}
                </div>
                {customer.availableRewards.length > 3 && (
                  <Button variant="ghost" size="sm" className="mt-3 w-full" onClick={() => setActiveTab("rewards")}>
                    Lihat Semua Rewards
                    <ChevronRight className="h-4 w-4 ml-1" />
                  </Button>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Points Tab */}
          <TabsContent value="points" className="mt-4 space-y-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">Riwayat Poin</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {customer.pointsHistory.map((history) => (
                    <div key={history.id} className="flex justify-between items-center">
                      <div>
                        <div className="font-medium">{history.description}</div>
                        <div className="text-sm text-gray-500">{history.date}</div>
                      </div>
                      <Badge
                        variant="outline"
                        className={
                          history.type === "earned"
                            ? "text-green-500 border-green-500"
                            : history.type === "redeemed"
                              ? "text-red-500 border-red-500"
                              : "text-blue-500 border-blue-500"
                        }
                      >
                        {history.type === "earned" ? "+" : history.type === "redeemed" ? "-" : "+"}
                        {history.amount} poin
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">Semua Transaksi</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {customer.orderHistory.map((order) => (
                    <div key={order.id} className="flex justify-between items-center">
                      <div>
                        <div className="flex items-center gap-1">
                          <span className="font-medium">{order.id}</span>
                          <Badge variant="outline" className="text-green-500 border-green-500">
                            {order.status}
                          </Badge>
                        </div>
                        <div className="text-sm text-gray-500">
                          <span>{order.date}</span> • <span>{order.items}</span>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-medium">Rp {order.amount.toLocaleString()}</div>
                        <div className="text-sm text-yellow-500 flex items-center justify-end">
                          <Star className="h-3.5 w-3.5 mr-1" />
                          {order.pointsEarned} poin
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Rewards Tab */}
          <TabsContent value="rewards" className="mt-4 space-y-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">Riwayat Penukaran Reward</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {customer.rewardHistory.map((reward) => (
                    <div key={reward.id} className="flex justify-between items-center">
                      <div>
                        <div className="font-medium">{reward.reward}</div>
                        <div className="text-sm text-gray-500">
                          Ditukar pada {reward.date} • {reward.points} poin
                        </div>
                        {reward.status === "Digunakan" && (
                          <div className="text-sm text-green-500 flex items-center">
                            <CheckCircle className="h-3.5 w-3.5 mr-1" />
                            Digunakan pada {reward.usedDate} ({reward.orderNumber})
                          </div>
                        )}
                        {reward.status === "Kedaluwarsa" && (
                          <div className="text-sm text-red-500 flex items-center">
                            <AlertCircle className="h-3.5 w-3.5 mr-1" />
                            Kedaluwarsa pada {reward.expiryDate}
                          </div>
                        )}
                      </div>
                      <Badge
                        variant={
                          reward.status === "Digunakan"
                            ? "outline"
                            : reward.status === "Kedaluwarsa"
                              ? "secondary"
                              : "default"
                        }
                        className={
                          reward.status === "Digunakan"
                            ? "text-green-500 border-green-500"
                            : reward.status === "Kedaluwarsa"
                              ? "text-red-500 border-red-500"
                              : ""
                        }
                      >
                        {reward.status}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">Rewards Tersedia</CardTitle>
                <CardDescription>Tukarkan poin dengan rewards menarik</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {customer.availableRewards.map((reward) => (
                    <div key={reward.id} className="flex justify-between items-center">
                      <div>
                        <div className="font-medium">{reward.name}</div>
                        <div className="text-sm text-gray-500">{reward.description}</div>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        disabled={customer.points < reward.pointsRequired}
                        onClick={() => handleRedeemReward(reward)}
                      >
                        <Star className="h-3.5 w-3.5 mr-1 text-yellow-500" />
                        {reward.pointsRequired} poin
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Referrals Tab */}
          <TabsContent value="referrals" className="mt-4 space-y-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">Program Referral</CardTitle>
                <CardDescription>
                  Dapatkan 300 poin untuk setiap referral yang melakukan transaksi pertama
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="bg-blue-50 p-3 rounded-lg mb-4">
                  <div className="text-center">
                    <h3 className="font-medium text-blue-700 mb-1">Kode Referral</h3>
                    <div className="bg-white p-2 rounded border border-blue-200 font-mono text-lg font-bold mb-2">
                      SITI25
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      className="text-blue-600 border-blue-200"
                      onClick={() => setShowReferralDialog(true)}
                    >
                      <Send className="h-3.5 w-3.5 mr-1" />
                      Bagikan Kode
                    </Button>
                  </div>
                </div>

                <div className="space-y-3">
                  <h3 className="font-medium">Referral Aktif ({customer.referrals.length})</h3>
                  {customer.referrals.length > 0 ? (
                    customer.referrals.map((referral) => (
                      <div key={referral.id} className="flex justify-between items-center">
                        <div>
                          <div className="font-medium">{referral.name}</div>
                          <div className="text-sm text-gray-500">
                            Bergabung {referral.date} • {referral.orders} transaksi
                          </div>
                        </div>
                        <Badge variant="outline" className="text-green-500 border-green-500">
                          +{referral.bonusEarned} poin
                        </Badge>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-6 text-gray-500">
                      <Gift className="h-10 w-10 mx-auto mb-2 text-gray-300" />
                      <p>Belum ada referral aktif</p>
                    </div>
                  )}
                </div>
              </CardContent>
              <CardFooter className="pt-0">
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full"
                  onClick={() => router.push(`/loyalty/customer/${params.id}/referrals`)}
                >
                  Lihat Detail Referral
                  <ChevronRight className="h-4 w-4 ml-1" />
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      {/* Add Points Dialog */}
      <Dialog open={showAddPointsDialog} onOpenChange={setShowAddPointsDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Tambah Poin</DialogTitle>
            <DialogDescription>Tambahkan poin loyalitas untuk {customer.name}</DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-2">
            <div className="space-y-2">
              <Label htmlFor="points-amount">Jumlah Poin</Label>
              <Input id="points-amount" type="number" placeholder="Masukkan jumlah poin" />
            </div>

            <div className="space-y-2">
              <Label htmlFor="points-reason">Alasan</Label>
              <Select defaultValue="bonus">
                <SelectTrigger id="points-reason">
                  <SelectValue placeholder="Pilih alasan" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="bonus">Bonus</SelectItem>
                  <SelectItem value="correction">Koreksi</SelectItem>
                  <SelectItem value="compensation">Kompensasi</SelectItem>
                  <SelectItem value="birthday">Bonus Ulang Tahun</SelectItem>
                  <SelectItem value="referral">Bonus Referral</SelectItem>
                  <SelectItem value="other">Lainnya</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="points-note">Catatan (opsional)</Label>
              <Textarea id="points-note" placeholder="Tambahkan catatan" />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAddPointsDialog(false)}>
              Batal
            </Button>
            <Button onClick={() => setShowAddPointsDialog(false)}>Tambah Poin</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Redeem Dialog */}
      <Dialog open={showRedeemDialog} onOpenChange={setShowRedeemDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Tukar Reward</DialogTitle>
            <DialogDescription>
              Tukarkan {selectedReward?.pointsRequired} poin untuk {selectedReward?.name}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-2">
            <div className="bg-yellow-50 p-3 rounded-lg">
              <div className="flex items-center gap-2">
                <Star className="h-5 w-5 text-yellow-500" />
                <div>
                  <h3 className="font-medium">{selectedReward?.name}</h3>
                  <p className="text-sm text-gray-600">{selectedReward?.description}</p>
                </div>
              </div>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-500">Poin Saat Ini</span>
              <span className="font-medium">{customer.points} poin</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-500">Poin yang Ditukarkan</span>
              <span className="font-medium text-red-500">-{selectedReward?.pointsRequired} poin</span>
            </div>
            <Separator />
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-500">Sisa Poin</span>
              <span className="font-medium">{customer.points - (selectedReward?.pointsRequired || 0)} poin</span>
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="apply-now">Gunakan sekarang</Label>
                <Switch id="apply-now" />
              </div>
              <p className="text-xs text-gray-500">
                Jika diaktifkan, reward akan langsung digunakan untuk pesanan berikutnya
              </p>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowRedeemDialog(false)}>
              Batal
            </Button>
            <Button onClick={() => setShowRedeemDialog(false)}>Tukar Reward</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Dialog */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Edit Pelanggan</DialogTitle>
            <DialogDescription>Perbarui informasi pelanggan</DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-2">
            <div className="space-y-2">
              <Label htmlFor="customer-name">Nama</Label>
              <Input id="customer-name" defaultValue={customer.name} />
            </div>

            <div className="space-y-2">
              <Label htmlFor="customer-phone">Nomor Telepon</Label>
              <Input id="customer-phone" defaultValue={customer.phone} />
            </div>

            <div className="space-y-2">
              <Label htmlFor="customer-email">Email</Label>
              <Input id="customer-email" type="email" defaultValue={customer.email} />
            </div>

            <div className="space-y-2">
              <Label htmlFor="customer-address">Alamat</Label>
              <Textarea id="customer-address" defaultValue={customer.address} />
            </div>

            <div className="space-y-2">
              <Label htmlFor="customer-birthdate">Tanggal Lahir</Label>
              <Input id="customer-birthdate" defaultValue={customer.birthDate} />
            </div>

            <div className="space-y-2">
              <Label htmlFor="customer-notes">Catatan</Label>
              <Textarea id="customer-notes" defaultValue={customer.notes} />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowEditDialog(false)}>
              Batal
            </Button>
            <Button onClick={() => setShowEditDialog(false)}>Simpan</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Hapus Pelanggan</DialogTitle>
            <DialogDescription>
              Apakah Anda yakin ingin menghapus pelanggan ini? Tindakan ini tidak dapat dibatalkan.
            </DialogDescription>
          </DialogHeader>

          <div className="py-2">
            <div className="bg-red-50 p-3 rounded-lg">
              <div className="flex items-start gap-2">
                <AlertCircle className="h-5 w-5 text-red-500 mt-0.5" />
                <div>
                  <h3 className="font-medium text-red-700">Peringatan</h3>
                  <p className="text-sm text-red-600">
                    Menghapus pelanggan akan menghapus semua data terkait, termasuk riwayat transaksi, poin, dan
                    rewards.
                  </p>
                </div>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteDialog(false)}>
              Batal
            </Button>
            <Button variant="destructive" onClick={() => setShowDeleteDialog(false)}>
              Hapus Pelanggan
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Referral Dialog */}
      <Dialog open={showReferralDialog} onOpenChange={setShowReferralDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Bagikan Kode Referral</DialogTitle>
            <DialogDescription>Bagikan kode referral kepada teman dan keluarga</DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-2">
            <div className="bg-blue-50 p-4 rounded-lg text-center">
              <h3 className="font-medium text-blue-700 mb-1">Kode Referral</h3>
              <div className="bg-white p-3 rounded border border-blue-200 font-mono text-xl font-bold">SITI25</div>
            </div>

            <div className="space-y-2">
              <Label>Bagikan melalui</Label>
              <div className="grid grid-cols-3 gap-2">
                <Button variant="outline" className="flex flex-col items-center py-3">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="text-green-600 mb-1"
                  >
                    <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z" />
                  </svg>
                  <span className="text-xs">WhatsApp</span>
                </Button>
                <Button variant="outline" className="flex flex-col items-center py-3">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="text-blue-600 mb-1"
                  >
                    <rect width="20" height="16" x="2" y="4" rx="2" />
                    <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7" />
                  </svg>
                  <span className="text-xs">Email</span>
                </Button>
                <Button variant="outline" className="flex flex-col items-center py-3">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="text-gray-600 mb-1"
                  >
                    <rect width="14" height="20" x="5" y="2" rx="2" ry="2" />
                    <path d="M12 18h.01" />
                  </svg>
                  <span className="text-xs">SMS</span>
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="referral-message">Pesan</Label>
              <Textarea
                id="referral-message"
                defaultValue={`Halo! Gunakan kode SITI25 untuk mendapatkan diskon 10% di Felis Laundry. Kunjungi felislaundry.com atau download aplikasinya sekarang!`}
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowReferralDialog(false)}>
              Batal
            </Button>
            <Button onClick={() => setShowReferralDialog(false)}>Salin & Bagikan</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
