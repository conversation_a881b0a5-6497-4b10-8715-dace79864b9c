# Swagger Documentation Update

## Overview

Dokumentasi ini menjelaskan update swagger docs yang telah dilakukan untuk sistem laundry app, khususnya untuk route Order, Cashbox, dan Customer Deposit.

## 1. Order Route Update

### <PERSON>bahan <PERSON>

- **Menghapus field `paymentMethod`** dari request body
- **Menambahkan object `payment`** yang lebih komprehensif
- **Mendukung payment method DEPOSIT** untuk pembayaran dengan saldo customer

### Struktur Payment Object Baru

```json
{
  "payment": {
    "amount": 25000,
    "method": "CASH|TRANSFER|CREDIT_CARD|DEBIT_CARD|E_WALLET|DEPOSIT",
    "cashboxId": 1, // Required untuk non-DEPOSIT methods
    "reference": "CASH-001",
    "notes": "Pembayaran tunai"
  }
}
```

### Endpoint yang Diupdate

- `POST /orders` - Create order dengan optional payment

### Fitur Payment

- **Optional Payment**: Jika tidak ada payment object, order akan berstatus UNPAID
- **Automatic Payment Status**: Sistem otomatis menghitung status (UNPAID/PARTIAL/PAID)
- **Cashbox Integration**: Payment non-DEPOSIT otomatis update balance cashbox
- **Deposit Payment**: Payment dengan method DEPOSIT hanya mempengaruhi saldo customer

## 2. Cashbox Route Documentation

### Endpoints Tersedia

1. **POST** `/outlets/{outletId}/cashboxes` - Create cashbox
2. **GET** `/outlets/{outletId}/cashboxes` - Get all cashboxes
3. **GET** `/outlets/{outletId}/cashboxes/{cashboxId}` - Get cashbox by ID
4. **PATCH** `/outlets/{outletId}/cashboxes/{cashboxId}` - Update cashbox
5. **DELETE** `/outlets/{outletId}/cashboxes/{cashboxId}` - Delete cashbox
6. **GET** `/outlets/{outletId}/cashboxes/{cashboxId}/balance` - Get balance
7. **PATCH** `/outlets/{outletId}/cashboxes/{cashboxId}/balance` - Adjust balance

### Fitur Utama

- **Multiple Cashbox Types**: TUNAI dan NON_TUNAI
- **Balance Management**: Real-time balance tracking
- **Active/Inactive Status**: Enable/disable cashbox
- **Balance Adjustment**: Manual balance correction dengan audit trail
- **Delete Protection**: Hanya cashbox dengan balance 0 yang bisa dihapus

### Request Examples

#### Create Cashbox

```json
{
  "name": "Kas Utama",
  "type": "TUNAI",
  "isActive": true,
  "balance": 0
}
```

#### Adjust Balance

```json
{
  "amount": 50000,
  "reason": "Initial cash deposit",
  "reference": "ADJ-001"
}
```

## 3. Customer Deposit Route Documentation

### Endpoints Tersedia

1. **POST** `/deposit/in` - Deposit money to customer
2. **POST** `/deposit/out` - Withdraw money from customer
3. **POST** `/deposit/pay` - Pay order with deposit
4. **GET** `/outlets/{outletId}/customers/{customerId}/deposit/history` - Transaction history
5. **GET** `/outlets/{outletId}/customers/{customerId}/deposit/balance` - Current balance

### Transaction Types

- **DEPOSIT**: Setor saldo (customer balance +, cashbox balance +)
- **WITHDRAW**: Tarik saldo (customer balance -, cashbox balance -)
- **PAYMENT**: Bayar order (customer balance -, cashbox balance unchanged)
- **ADJUSTMENT**: Koreksi saldo

### Request Examples

#### Deposit In

```json
{
  "customerId": 1,
  "outletId": 1,
  "amount": 100000,
  "cashboxId": 1,
  "reference": "DEPOSIT-IN-001"
}
```

#### Deposit Out

```json
{
  "customerId": 1,
  "outletId": 1,
  "amount": 50000,
  "cashboxId": 1,
  "reference": "DEPOSIT-OUT-001"
}
```

#### Pay with Deposit

```json
{
  "customerId": 1,
  "outletId": 1,
  "orderId": 123,
  "amount": 25000,
  "reference": "DEPOSIT-PAY-001"
}
```

## 4. Response Structures

### Success Response Format

```json
{
  "success": true,
  "message": "Operation successful",
  "data": {
    // Response data
  }
}
```

### Error Response Format

```json
{
  "error": "Error message",
  "details": "Additional error details"
}
```

## 5. Authentication & Authorization

Semua endpoint memerlukan:

- **Bearer Token Authentication**
- **Email/Phone Verification** (requireVerification middleware)
- **Role-based Permissions**:
  - `manageCashbox` - untuk operasi cashbox
  - `getCashbox` - untuk read cashbox
  - `manageCustomerDeposit` - untuk operasi deposit
  - `getCustomerDeposit` - untuk read deposit
  - `manageOrders` - untuk operasi order

## 6. Validation Rules

### Cashbox Validation

- Name: required, max 100 characters
- Type: required, enum [TUNAI, NON_TUNAI]
- Balance: number, default 0

### Customer Deposit Validation

- Amount: required, minimum 0.01
- Customer ID: required, integer
- Outlet ID: required, integer
- Cashbox ID: required untuk deposit in/out

### Order Payment Validation

- Amount: minimum 0.01
- Method: enum [CASH, TRANSFER, CREDIT_CARD, DEBIT_CARD, E_WALLET, DEPOSIT]
- Cashbox ID: required untuk non-DEPOSIT methods

## 7. Business Logic

### Payment Flow

1. **Cashbox Payment**: Payment → Cashbox balance update → Payment record
2. **Deposit Payment**: Payment → Customer balance update → Deposit transaction

### Balance Tracking

- **Cashbox Balance**: Updated by payments dan adjustments
- **Customer Deposit**: Updated by deposit in/out dan payments
- **Atomic Transactions**: Semua operasi menggunakan database transactions

## 8. Error Handling

### Common Errors

- **400 Bad Request**: Invalid input, insufficient balance
- **401 Unauthorized**: Missing or invalid token
- **403 Forbidden**: Insufficient permissions
- **404 Not Found**: Resource not found

### Specific Error Messages

- "Saldo deposit tidak mencukupi" - Insufficient deposit balance
- "Cannot delete cashbox with non-zero balance" - Delete protection
- "Order tidak ditemukan atau sudah lunas" - Invalid order for payment

## 9. Testing

### Swagger UI Access

- URL: `http://localhost:3000/v1/docs/`
- Environment: Development only
- Authentication: Bearer token required

### Test Scenarios

1. Create cashbox dengan berbagai types
2. Deposit in/out dengan balance validation
3. Order creation dengan payment options
4. Balance adjustment dengan audit trail
5. Error handling untuk insufficient balance

## 10. Integration Notes

### Frontend Integration

- Gunakan response `success` field untuk status checking
- Handle error messages untuk user feedback
- Implement loading states untuk async operations

### Database Consistency

- Semua balance operations menggunakan transactions
- Audit trail untuk semua financial operations
- Soft delete untuk data integrity

## Kesimpulan

Update swagger documentation ini memberikan:

- **Comprehensive API Documentation** untuk semua financial operations
- **Clear Request/Response Examples** untuk easy integration
- **Proper Error Handling** documentation
- **Business Logic Explanation** untuk better understanding
- **Security & Validation** guidelines

Dokumentasi ini memungkinkan developer untuk dengan mudah mengintegrasikan sistem cashbox dan customer deposit dengan confidence dan clarity.
