'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import {
  <PERSON>Left,
  Edit,
  Trash2,
  TrendingUp,
  Loader2,
  RefreshCw,
  DollarSign,
  Settings,
} from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Separator } from '@/components/ui/separator';

import {
  useCashbox,
  useCashboxBalance,
  useDeleteCashbox,
  useAdjustCashboxBalance,
  formatCurrency,
  getCashboxTypeBadge,
  getCashboxStatusBadge,
  getCashboxErrorMessage,
  getCashboxFieldErrors,
} from '@/hooks/useCashbox';
import type { AdjustBalanceRequest } from '@/lib/api/cashbox';
import { ensureAuthSync } from '@/lib/auth-sync';
import { toast } from '@/components/ui/use-toast';

interface AdjustBalanceFormData {
  balance: string;
  reason: string;
}

interface AdjustBalanceFormErrors {
  balance?: string;
  reason?: string;
  general?: string;
}

export default function CashboxDetailPage() {
  const router = useRouter();
  const params = useParams();
  const cashboxId = parseInt(params.id as string);

  const [authError, setAuthError] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showAdjustDialog, setShowAdjustDialog] = useState(false);
  const [adjustFormData, setAdjustFormData] = useState<AdjustBalanceFormData>({
    balance: '',
    reason: '',
  });
  const [adjustErrors, setAdjustErrors] = useState<AdjustBalanceFormErrors>({});

  // Ensure auth sync on component mount
  useEffect(() => {
    ensureAuthSync();

    // Check if user is properly authenticated
    const checkAuth = () => {
      const user = localStorage.getItem('user');
      const tokens = localStorage.getItem('tokens');

      if (!user || !tokens) {
        console.log('No auth data found');
        setAuthError(true);
        return;
      }

      try {
        const parsedTokens = JSON.parse(tokens);
        const accessToken = parsedTokens?.access?.token;

        if (!accessToken) {
          console.log('No access token found');
          setAuthError(true);
          return;
        }

        setAuthError(false);
      } catch (error) {
        console.error('Error parsing auth data:', error);
        setAuthError(true);
      }
    };

    checkAuth();
  }, [router]);

  const {
    data: cashbox,
    isLoading: cashboxLoading,
    error: cashboxError,
    refetch: refetchCashbox,
  } = useCashbox(cashboxId);

  const {
    data: balance,
    isLoading: balanceLoading,
    error: balanceError,
    refetch: refetchBalance,
  } = useCashboxBalance(cashboxId);

  const deleteCashboxMutation = useDeleteCashbox();
  const adjustBalanceMutation = useAdjustCashboxBalance();

  const handleDeleteCashbox = () => {
    setShowDeleteDialog(true);
  };

  const confirmDelete = async () => {
    if (cashbox) {
      // Check if cashbox has balance
      if (cashbox.balance !== 0) {
        toast({
          title: 'Error',
          description:
            'Tidak dapat menghapus cashbox yang memiliki saldo. Kosongkan saldo terlebih dahulu.',
          variant: 'destructive',
        });
        setShowDeleteDialog(false);
        return;
      }

      try {
        await deleteCashboxMutation.mutateAsync(cashbox.id);
        setShowDeleteDialog(false);
        router.push('/akun/cashbox');
      } catch (error) {
        // Error sudah ditangani di mutation
      }
    }
  };

  const handleAdjustBalance = () => {
    setAdjustFormData({
      balance: cashbox?.balance.toString() || '',
      reason: '',
    });
    setAdjustErrors({});
    setShowAdjustDialog(true);
  };

  const handleAdjustInputChange = (
    field: keyof AdjustBalanceFormData,
    value: string
  ) => {
    setAdjustFormData((prev) => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (adjustErrors[field as keyof AdjustBalanceFormErrors]) {
      setAdjustErrors((prev) => ({ ...prev, [field]: undefined }));
    }
  };

  const validateAdjustForm = (): boolean => {
    const newErrors: AdjustBalanceFormErrors = {};

    // Validate balance
    const balance = parseFloat(adjustFormData.balance);
    if (!adjustFormData.balance.trim()) {
      newErrors.balance = 'Saldo baru wajib diisi';
    } else if (isNaN(balance)) {
      newErrors.balance = 'Saldo harus berupa angka';
    } else if (balance < 0) {
      newErrors.balance = 'Saldo tidak boleh negatif';
    }

    // Validate reason
    if (!adjustFormData.reason.trim()) {
      newErrors.reason = 'Alasan penyesuaian wajib diisi';
    } else if (adjustFormData.reason.trim().length < 5) {
      newErrors.reason = 'Alasan minimal 5 karakter';
    } else if (adjustFormData.reason.trim().length > 500) {
      newErrors.reason = 'Alasan maksimal 500 karakter';
    }

    setAdjustErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleAdjustSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateAdjustForm()) {
      return;
    }

    try {
      const requestData: AdjustBalanceRequest = {
        balance: parseFloat(adjustFormData.balance),
        reason: adjustFormData.reason.trim(),
      };

      await adjustBalanceMutation.mutateAsync({
        id: cashboxId,
        data: requestData,
      });

      setShowAdjustDialog(false);
      setAdjustFormData({
        balance: '',
        reason: '',
      });
      setAdjustErrors({});
    } catch (error: unknown) {
      console.error('Error adjusting balance:', error);

      // Extract field errors
      const fieldErrors = getCashboxFieldErrors(error);
      if (Object.keys(fieldErrors).length > 0) {
        setAdjustErrors(fieldErrors);
      } else {
        // Show general error
        const errorMessage = getCashboxErrorMessage(error);
        setAdjustErrors({ general: errorMessage });
        toast({
          title: 'Error',
          description: errorMessage,
          variant: 'destructive',
        });
      }
    }
  };

  const handleLoginRedirect = () => {
    localStorage.clear();
    router.push('/auth/login');
  };

  if (authError) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card className="max-w-md mx-auto">
          <CardContent className="pt-6">
            <div className="text-center">
              <h2 className="text-lg font-semibold mb-2">Sesi Berakhir</h2>
              <p className="text-gray-600 mb-4">
                Silakan login kembali untuk melanjutkan.
              </p>
              <Button onClick={handleLoginRedirect} className="w-full">
                Login
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (cashboxLoading || balanceLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center py-12">
          <Loader2 className="w-8 h-8 animate-spin" />
        </div>
      </div>
    );
  }

  if (cashboxError || balanceError || !cashbox) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card className="max-w-md mx-auto">
          <CardContent className="pt-6">
            <div className="text-center">
              <h2 className="text-lg font-semibold mb-2">Terjadi Kesalahan</h2>
              <p className="text-gray-600 mb-4">
                Gagal memuat data cashbox. Silakan coba lagi.
              </p>
              <Button onClick={() => refetchCashbox()} className="w-full">
                <RefreshCw className="w-4 h-4 mr-2" />
                Coba Lagi
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  const typeBadge = getCashboxTypeBadge(cashbox.type);
  const statusBadge = getCashboxStatusBadge(cashbox.isActive);

  return (
    <div className="container mx-auto px-4 py-6">
      {/* Header - Mobile Responsive */}
      <div className="mb-6">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4">
          <div className="flex items-center gap-3">
            <Link href="/akun/cashbox">
              <Button variant="ghost" size="sm" className="p-2">
                <ArrowLeft className="w-4 h-4" />
              </Button>
            </Link>
            <div className="min-w-0 flex-1">
              <h1 className="text-xl sm:text-2xl font-bold truncate">
                {cashbox.name}
              </h1>
              <p className="text-sm text-muted-foreground">
                Detail informasi cashbox
              </p>
            </div>
          </div>
          <div className="flex flex-wrap gap-2">
            <Button
              variant="outline"
              onClick={() => refetchBalance()}
              size="sm"
              className="flex-1 sm:flex-none"
            >
              <RefreshCw className="w-4 h-4 sm:mr-2" />
              <span className="hidden sm:inline">Refresh</span>
            </Button>
            <Link
              href={`/akun/cashbox/edit/${cashbox.id}`}
              className="flex-1 sm:flex-none"
            >
              <Button variant="outline" size="sm" className="w-full">
                <Edit className="w-4 h-4 sm:mr-2" />
                <span className="hidden sm:inline">Edit</span>
              </Button>
            </Link>
            <Button
              variant="destructive"
              size="sm"
              onClick={handleDeleteCashbox}
              className="flex-1 sm:flex-none"
            >
              <Trash2 className="w-4 h-4 sm:mr-2" />
              <span className="hidden sm:inline">Hapus</span>
            </Button>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Info */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="w-5 h-5" />
                Informasi Cashbox
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-gray-500">
                    Nama Cashbox
                  </Label>
                  <p className="text-lg font-semibold">{cashbox.name}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">
                    Tipe
                  </Label>
                  <div className="mt-1">
                    <Badge
                      variant={typeBadge.variant}
                      className={typeBadge.color}
                    >
                      {typeBadge.label}
                    </Badge>
                  </div>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">
                    Status
                  </Label>
                  <div className="mt-1">
                    <Badge
                      variant={statusBadge.variant}
                      className={statusBadge.color}
                    >
                      {statusBadge.label}
                    </Badge>
                  </div>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">
                    Dibuat
                  </Label>
                  <p className="text-sm">
                    {new Date(cashbox.createdAt).toLocaleDateString('id-ID', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit',
                    })}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Balance Info */}
        <div className="space-y-6">
          {/* Current Balance */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="w-5 h-5" />
                Saldo Saat Ini
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center">
                <p className="text-3xl font-bold text-green-600">
                  {formatCurrency(balance?.balance || cashbox.balance)}
                </p>
                <p className="text-sm text-gray-500 mt-1">
                  Terakhir diperbarui:{' '}
                  {balance?.lastUpdated
                    ? new Date(balance.lastUpdated).toLocaleString('id-ID')
                    : new Date(cashbox.updatedAt).toLocaleString('id-ID')}
                </p>
              </div>
              <Separator className="my-4" />
              <Button
                onClick={handleAdjustBalance}
                className="w-full"
                disabled={!cashbox.isActive}
              >
                <TrendingUp className="w-4 h-4 mr-2" />
                Sesuaikan Saldo
              </Button>
              {!cashbox.isActive && (
                <p className="text-xs text-gray-500 text-center mt-2">
                  Cashbox nonaktif tidak dapat disesuaikan
                </p>
              )}
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Aksi Cepat</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Link href={`/akun/cashbox/edit/${cashbox.id}`}>
                <Button variant="outline" className="w-full justify-start">
                  <Edit className="w-4 h-4 mr-2" />
                  Edit Cashbox
                </Button>
              </Link>
              <Button
                variant="outline"
                className="w-full justify-start"
                onClick={() => refetchBalance()}
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                Refresh Saldo
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Hapus Cashbox</DialogTitle>
            <DialogDescription>
              Apakah Anda yakin ingin menghapus cashbox{' '}
              <strong>{cashbox.name}</strong>?
              {cashbox.balance !== 0 && (
                <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-red-700">
                  <strong>Peringatan:</strong> Cashbox ini memiliki saldo{' '}
                  {formatCurrency(cashbox.balance)}. Kosongkan saldo terlebih
                  dahulu sebelum menghapus.
                </div>
              )}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowDeleteDialog(false)}
            >
              Batal
            </Button>
            <Button
              variant="destructive"
              onClick={confirmDelete}
              disabled={
                deleteCashboxMutation.isPending || cashbox.balance !== 0
              }
            >
              {deleteCashboxMutation.isPending ? (
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              ) : null}
              Hapus
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Adjust Balance Dialog */}
      <Dialog open={showAdjustDialog} onOpenChange={setShowAdjustDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Sesuaikan Saldo Cashbox</DialogTitle>
            <DialogDescription>
              Lakukan penyesuaian saldo untuk cashbox {cashbox.name}
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleAdjustSubmit} className="space-y-4">
            {/* General Error */}
            {adjustErrors.general && (
              <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                <p className="text-red-700 text-sm">{adjustErrors.general}</p>
              </div>
            )}

            {/* Current Balance Info */}
            <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
              <p className="text-sm text-blue-700">
                <strong>Saldo saat ini:</strong>{' '}
                {formatCurrency(cashbox.balance)}
              </p>
            </div>

            {/* Balance */}
            <div className="space-y-2">
              <Label htmlFor="adjust-balance">
                Saldo Baru <span className="text-red-500">*</span>
              </Label>
              <Input
                id="adjust-balance"
                type="number"
                step="0.01"
                placeholder="Masukkan saldo baru"
                value={adjustFormData.balance}
                onChange={(e) =>
                  handleAdjustInputChange('balance', e.target.value)
                }
                className={adjustErrors.balance ? 'border-red-500' : ''}
              />
              {adjustErrors.balance && (
                <p className="text-red-500 text-sm">{adjustErrors.balance}</p>
              )}
              <p className="text-xs text-gray-500">
                Masukkan saldo baru yang diinginkan
              </p>
            </div>

            {/* Reason */}
            <div className="space-y-2">
              <Label htmlFor="adjust-reason">
                Alasan Penyesuaian <span className="text-red-500">*</span>
              </Label>
              <Textarea
                id="adjust-reason"
                placeholder="Jelaskan alasan penyesuaian saldo"
                value={adjustFormData.reason}
                onChange={(e) =>
                  handleAdjustInputChange('reason', e.target.value)
                }
                className={adjustErrors.reason ? 'border-red-500' : ''}
                rows={3}
              />
              {adjustErrors.reason && (
                <p className="text-red-500 text-sm">{adjustErrors.reason}</p>
              )}
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setShowAdjustDialog(false)}
              >
                Batal
              </Button>
              <Button type="submit" disabled={adjustBalanceMutation.isPending}>
                {adjustBalanceMutation.isPending ? (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                ) : null}
                Sesuaikan Saldo
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
}
