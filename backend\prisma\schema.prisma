// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id              Int      @id @default(autoincrement())
  email           String   @unique
  name            String?
  phone           String?  @unique
  password        String
  role            Role     @default(EMPLOYEE)
  isEmailVerified Boolean  @default(false)
  isPhoneVerified Boolean  @default(false)
  isActive        Boolean  @default(true)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  Token           Token[]
  
  // Relasi dengan outlet
  outlets         Outlet[] @relation("OutletOwner")
  employeeAt      Outlet?  @relation("OutletEmployee", fields: [outletId], references: [id])
  outletId        Int?
  
  // Relasi dengan attendance system
  attendances     Attendance[]
  workSchedules   WorkSchedule[]
  
  // Relasi dengan order status history
  orderStatusHistories     OrderStatusHistory[]
  orderItemStatusHistories OrderItemStatusHistory[]

  // <PERSON><PERSON>i ke CustomerDepositTransaction
  depositTransactions   CustomerDepositTransaction[] @relation("UserDepositTransaction")
}

model Token {
  id          Int       @id @default(autoincrement())
  token       String
  type        TokenType
  expires     DateTime
  blacklisted Boolean
  createdAt   DateTime  @default(now())
  user        User      @relation(fields: [userId], references: [id])
  userId      Int
}

model PhoneVerification {
  id        Int      @id @default(autoincrement())
  phone     String
  code      String
  expiresAt DateTime
  isUsed    Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  @@index([phone, isUsed])
}

model EmailVerification {
  id        Int      @id @default(autoincrement())
  email     String
  code      String
  expiresAt DateTime
  isUsed    Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  @@index([email, isUsed])
}

model Province {
  id        Int      @id @default(autoincrement())
  name      String   @unique
  code      String   @unique
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Relasi
  cities    City[]
  outlets   Outlet[]
  customers Customer[]
}

model City {
  id         Int      @id @default(autoincrement())
  name       String
  code       String   @unique
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  
  // Relasi
  province   Province @relation(fields: [provinceId], references: [id])
  provinceId Int
  outlets    Outlet[]
  customers  Customer[]
  
  @@unique([name, provinceId])
}

model Outlet {
  id                    Int                @id @default(autoincrement())
  name                  String
  address               String
  province              String             // Tetap simpan sebagai string untuk backward compatibility
  city                  String             // Tetap simpan sebagai string untuk backward compatibility
  provinceId            Int?               // Relasi ke Province (optional untuk migration)
  cityId                Int?               // Relasi ke City (optional untuk migration)
  timezone              String             @default("Asia/Jakarta")
  phone                 String
  latitude              Float?
  longitude             Float?
  isActive              Boolean            @default(true)
  isDeleted             Boolean            @default(false) // Soft delete
  createdAt             DateTime           @default(now())
  updatedAt             DateTime           @updatedAt
  deletedAt             DateTime?
  
  // Relasi
  owner                 User               @relation("OutletOwner", fields: [ownerId], references: [id])
  ownerId               Int
  employees             User[]             @relation("OutletEmployee")
  services              Service[]
  categories            ServiceCategory[]
  orders                Order[]
  customers             Customer[]
  perfumes              Perfume[]
  promotions            Promotion[]        // Relasi ke Promotion
  
  // Relasi ke Province dan City
  provinceRef           Province?          @relation(fields: [provinceId], references: [id])
  cityRef               City?              @relation(fields: [cityId], references: [id])
  
  // Outlet yang disalin layanannya
  copiedFrom            Outlet?            @relation("OutletServiceCopy", fields: [copiedFromId], references: [id])
  copiedFromId          Int?
  copiedTo              Outlet[]           @relation("OutletServiceCopy")
  
  // Relasi dengan attendance system
  attendances           Attendance[]
  workSchedules         WorkSchedule[]
  scheduleTemplates     ScheduleTemplate[]
  attendanceSettings    AttendanceSettings?

  // Relasi ke Cashbox
  cashboxes             Cashbox[]
  depositTransactions   CustomerDepositTransaction[]
}

model Service {
  id                    Int                @id @default(autoincrement())
  name                  String
  description           String?
  price                 Float              @default(0)
  unit                  String             @default("kg") // kg, pcs, etc
  estimationHours       Int?               // Estimasi waktu pengerjaan dalam jam
  isActive              Boolean            @default(true)
  createdAt             DateTime           @default(now())
  updatedAt             DateTime           @updatedAt
  icon                  String?
  
  // Relasi
  outlet                Outlet             @relation(fields: [outletId], references: [id], onDelete: Cascade)
  outletId              Int
  orderItems            OrderItem[]
  category              ServiceCategory?   @relation(fields: [categoryId], references: [id])
  categoryId            Int?
  @@unique([outletId, name])
}

model ServiceCategory {
  id                    Int                @id @default(autoincrement())
  name                  String
  description           String?
  services              Service[]
  productionProcess     String[]

  createdAt             DateTime           @default(now())
  updatedAt             DateTime           @updatedAt
  outlet                Outlet             @relation(fields: [outletId], references: [id], onDelete: Cascade)
  outletId              Int
}

model Perfume {
  id                    Int                @id @default(autoincrement())
  name                  String
  description           String?
  brand                 String?
  scent                 String?            // Jenis aroma (fresh, floral, citrus, dll)
  isActive              Boolean            @default(true)
  isPopular             Boolean            @default(false)
  isNew                 Boolean            @default(false)
  createdAt             DateTime           @default(now())
  updatedAt             DateTime           @updatedAt
  
  // Relasi
  outlet                Outlet             @relation(fields: [outletId], references: [id], onDelete: Cascade)
  outletId              Int
  orders                Order[]
  
  @@unique([outletId, name])
}

model Promotion {
  id                    Int                @id @default(autoincrement())
  name                  String             // Nama promosi
  code                  String             // Kode promosi yang digunakan customer
  description           String?            // Deskripsi promosi
  discountType          DiscountType       // PERCENTAGE atau FIXED
  discountValue         Float              // Nilai diskon (persentase atau nominal)
  minOrderValue         Float              @default(0) // Minimum nilai order untuk bisa menggunakan promo
  maxDiscountAmount     Float?             // Maksimum nominal diskon (untuk percentage)
  isActive              Boolean            @default(true)
  validFrom             DateTime           // Tanggal mulai berlaku
  validUntil            DateTime           // Tanggal berakhir
  usageLimit            Int?               // Batas penggunaan (null = unlimited)
  usageCount            Int                @default(0) // Jumlah penggunaan saat ini
  isFirstTimeOnly       Boolean            @default(false) // Hanya untuk pelanggan baru
  applicableServices    String[]           @default([]) // Array ID service yang berlaku (kosong = semua)
  createdAt             DateTime           @default(now())
  updatedAt             DateTime           @updatedAt
  
  // Relasi
  outlet                Outlet             @relation(fields: [outletId], references: [id], onDelete: Cascade)
  outletId              Int
  orders                Order[]            // Orders yang menggunakan promo ini
  
  @@unique([outletId, code])
}

model Customer {
  id              Int      @id @default(autoincrement())
  name            String
  phone           String?  // OPTIONAL - unique per outlet only
  email           String?  // OPTIONAL - unique per outlet only
  address         String?              // OPTIONAL
  mapLink         String?
  latitude        Decimal?
  longitude       Decimal?
  provinceId      Int?
  cityId          Int?
  status          CustomerStatus @default(ACTIVE)
  customerType    CustomerType   @default(INDIVIDUAL)
  source          String?
  labels          String[]       // Array of labels
  photos          String[]       // Array of photo URLs
  notes           String?
  
  // Basic stats (denormalized for performance)
  totalOrders     Int      @default(0)
  lastOrderDate   DateTime?
  
  // Timestamps
  joinDate        DateTime @default(now())
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  
  // Relasi
  outlet          Outlet             @relation(fields: [outletId], references: [id])
  outletId        Int
  province        Province? @relation(fields: [provinceId], references: [id])
  city            City?     @relation(fields: [cityId], references: [id])
  orders          Order[]
  customerNotes   CustomerNote[]
  financialData   CustomerFinancial?  // One-to-one relation
  
  // Relasi ke CustomerDepositTransaction
  depositTransactions CustomerDepositTransaction[]
  
  @@unique([phone, outletId], name: "unique_phone_per_outlet")
  @@unique([email, outletId], name: "unique_email_per_outlet")
  @@map("customers")
}

// Separate table for financial data
model CustomerFinancial {
  id              Int      @id @default(autoincrement())
  customerId      Int      @unique
  totalSpent      Decimal  @default(0)
  loyaltyPoints   Int      @default(0)
  deposit         Decimal  @default(0)
  debt            Decimal  @default(0)
  cashback        Decimal  @default(0)
  
  // Payment preferences
  preferredPaymentMethod String?
  creditLimit     Decimal  @default(0)
  
  // Timestamps
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  
  // Relations
  customer        Customer @relation(fields: [customerId], references: [id], onDelete: Cascade)
  
  @@map("customer_financials")
}

model CustomerNote {
  id         Int      @id @default(autoincrement())
  customerId Int
  text       String
  author     String
  createdAt  DateTime @default(now())
  
  customer   Customer @relation(fields: [customerId], references: [id], onDelete: Cascade)
  
  @@map("customer_notes")
}

model Order {
  id                    Int                @id @default(autoincrement())
  orderNumber           String             @unique
  status                OrderStatus        @default(KONFIRMASI)
  totalWeight           Float?
  totalPrice            Float              @default(0)
  paidAmount            Float              @default(0)
  paymentStatus         PaymentStatus      @default(UNPAID)
  paymentMethod         PaymentMethod?
  notes                 String?
  pickupDate            DateTime?
  deliveryDate          DateTime?
  estimatedFinish       DateTime?
  actualFinish          DateTime?
  
  // Parfum selection
  perfumeId             Int?               // ID parfum yang dipilih
  perfumeName           String?            // Snapshot nama parfum saat order dibuat
  perfumeDescription    String?            // Snapshot deskripsi parfum
  
  // Promotion fields
  promotionId           Int?               // ID promosi yang digunakan
  promotionCode         String?            // Snapshot kode promosi saat order dibuat
  promotionDiscount     Float?             @default(0) // Nominal diskon yang didapat
  
  createdAt             DateTime           @default(now())
  updatedAt             DateTime           @updatedAt
  
  // Relasi
  outlet                Outlet             @relation(fields: [outletId], references: [id])
  outletId              Int
  customer              Customer           @relation(fields: [customerId], references: [id])
  customerId            Int
  items                 OrderItem[]
  payments              Payment[]
  statusHistories       OrderStatusHistory[]
  itemStatusHistories   OrderItemStatusHistory[]
  perfume               Perfume?           @relation(fields: [perfumeId], references: [id])
  promotion             Promotion?         @relation(fields: [promotionId], references: [id])

  // Relasi ke CustomerDepositTransaction
  depositTransactions   CustomerDepositTransaction[]
}

model OrderItem {
  id                    Int                @id @default(autoincrement())
  quantity              Float              @default(1)
  unit                  String             // Unit untuk item ini (kg, pcs, etc)
  price                 Float              // Snapshot harga saat order dibuat
  subtotal              Float
  notes                 String?
  status                OrderItemStatus    @default(PENDING)
  icon                  String?
  
  // Denormalized service data (snapshot saat order dibuat)
  serviceName           String             // Snapshot nama service
  serviceDescription    String?            // Snapshot deskripsi service
  serviceUnit           String             // Snapshot unit service (kg, pcs, etc)
  serviceEstimationHours Int?              // Snapshot estimasi jam pengerjaan
  
  createdAt             DateTime           @default(now())
  updatedAt             DateTime           @updatedAt
  
  // Relasi - tetap simpan referensi ke service untuk keperluan analisis
  order                 Order              @relation(fields: [orderId], references: [id], onDelete: Cascade)
  orderId               Int
  service               Service?           @relation(fields: [serviceId], references: [id])
  serviceId             Int?               // Optional untuk backward compatibility
  statusHistories       OrderItemStatusHistory[]
}

model Payment {
  id                    Int                @id @default(autoincrement())
  amount                Float
  method                PaymentMethod
  reference             String?            // Nomor referensi pembayaran
  notes                 String?
  createdAt             DateTime           @default(now())
  updatedAt             DateTime           @updatedAt
  
  // Relasi
  order                 Order              @relation(fields: [orderId], references: [id])
  orderId               Int
  cashboxId             Int?
  cashbox               Cashbox?           @relation(fields: [cashboxId], references: [id])
}

model OrderStatusHistory {
  id                    Int                @id @default(autoincrement())
  orderId               Int
  previousStatus        OrderStatus?       // Status sebelumnya (null untuk status awal)
  newStatus             OrderStatus        // Status baru
  changedBy             Int                // User ID yang mengubah status
  notes                 String?            // Catatan perubahan
  createdAt             DateTime           @default(now())
  
  // Relasi
  order                 Order              @relation(fields: [orderId], references: [id], onDelete: Cascade)
  user                  User               @relation(fields: [changedBy], references: [id])
  
  @@index([orderId, createdAt])
}

model OrderItemStatusHistory {
  id                    Int                @id @default(autoincrement())
  orderItemId           Int
  orderId               Int                // Untuk filtering dan performa
  previousStatus        OrderItemStatus?   // Status sebelumnya (null untuk status awal)
  newStatus             OrderItemStatus    // Status baru
  changedBy             Int                // User ID yang mengubah status
  notes                 String?            // Catatan perubahan
  createdAt             DateTime           @default(now())
  
  // Relasi
  orderItem             OrderItem          @relation(fields: [orderItemId], references: [id], onDelete: Cascade)
  order                 Order              @relation(fields: [orderId], references: [id], onDelete: Cascade)
  user                  User               @relation(fields: [changedBy], references: [id])
  
  @@index([orderItemId, createdAt])
  @@index([orderId, createdAt])
}

enum Role {
  ADMIN
  OWNER
  EMPLOYEE
}

enum TokenType {
  ACCESS
  REFRESH
  RESET_PASSWORD
  VERIFY_EMAIL
}

enum OrderStatus {
  KONFIRMASI
  PICKUP
  PENDING
  PROCESSING
  READY
  READY_FOR_PICKUP
  COMPLETED
  CANCELLED
}

enum PaymentStatus {
  UNPAID
  PARTIAL
  PAID
  REFUNDED
}

enum PaymentMethod {
  CASH
  TRANSFER
  CREDIT_CARD
  DEBIT_CARD
  E_WALLET
  DEPOSIT
}

enum CustomerStatus {
  ACTIVE
  INACTIVE
  NEW
}

enum CustomerType {
  INDIVIDUAL
  CORPORATE
}

enum OrderItemStatus {
  PENDING
  WASHING
  DRYING
  IRONING
  PACKING
  COMPLETED
  CANCELLED
}

enum AttendanceStatus {
  PRESENT
  LATE
  ABSENT
  SICK
  LEAVE
  EARLY_OUT
  OVERTIME
}

enum CashboxType {
  TUNAI
  NON_TUNAI
}

enum DiscountType {
  PERCENTAGE
  FIXED
}

model Cashbox {
  id         Int          @id @default(autoincrement())
  outletId   Int
  name       String
  type       CashboxType
  isActive   Boolean      @default(true)
  balance    Float        @default(0)
  createdAt  DateTime     @default(now())
  updatedAt  DateTime     @updatedAt

  // Relasi
  outlet     Outlet       @relation(fields: [outletId], references: [id])
  payments   Payment[]
  depositTransactions   CustomerDepositTransaction[]
}

// =================================
// ATTENDANCE SYSTEM MODELS
// =================================

model Attendance {
  id            Int      @id @default(autoincrement())
  userId        Int
  outletId      Int
  scheduleId    Int?     // Relasi ke jadwal yang digunakan
  checkIn       DateTime?
  checkOut      DateTime?
  checkInPhoto  String?  // URL/path foto check-in
  checkOutPhoto String?  // URL/path foto check-out
  location      String?  // Format: "latitude,longitude"
  notes         String?
  status        AttendanceStatus @default(PRESENT)
  isLate        Boolean  @default(false)
  workingHours  Float?   // Jam kerja aktual
  overtimeHours Float?   @default(0)
  lateByMinutes Int?     @default(0)
  date          DateTime // Tanggal absensi (format: YYYY-MM-DD)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  
  // Relasi
  user          User          @relation(fields: [userId], references: [id])
  outlet        Outlet        @relation(fields: [outletId], references: [id])
  schedule      WorkSchedule? @relation(fields: [scheduleId], references: [id])
  
  @@unique([userId, outletId, date])
  @@index([outletId, date])
  @@index([userId, date])
}

// Model untuk jadwal kerja individual karyawan
model WorkSchedule {
  id              Int       @id @default(autoincrement())
  userId          Int
  outletId        Int
  name            String    // "Shift Pagi", "Shift Sore", "Paruh Waktu", dll
  dayOfWeek       Int       // 0=Minggu, 1=Senin, ..., 6=Sabtu
  startTime       String    // Format: "08:00"
  endTime         String    // Format: "17:00"
  breakStartTime  String?   // Format: "12:00" (optional)
  breakEndTime    String?   // Format: "13:00" (optional)
  isActive        Boolean   @default(true)
  effectiveFrom   DateTime  // Tanggal mulai berlaku
  effectiveTo     DateTime? // Tanggal berakhir (null = permanent)
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
  
  // Relasi
  user            User        @relation(fields: [userId], references: [id])
  outlet          Outlet      @relation(fields: [outletId], references: [id])
  attendances     Attendance[]
  
  @@index([userId, outletId, dayOfWeek, isActive])
  @@index([effectiveFrom, effectiveTo])
  @@index([outletId, isActive])
}

// Model untuk template jadwal yang bisa digunakan berulang
model ScheduleTemplate {
  id            Int     @id @default(autoincrement())
  outletId      Int
  name          String  // "Shift Pagi", "Shift Sore", "Full Time", dll
  description   String?
  startTime     String  // Format: "08:00"
  endTime       String  // Format: "17:00"
  breakStartTime String? // Format: "12:00"
  breakEndTime   String? // Format: "13:00"
  workingDays   Int[]   // Array hari kerja [1,2,3,4,5] = Senin-Jumat
  isActive      Boolean @default(true)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  
  // Relasi
  outlet        Outlet  @relation(fields: [outletId], references: [id])
  
  @@index([outletId, isActive])
}

// Model untuk pengaturan absensi per outlet
model AttendanceSettings {
  id                    Int     @id @default(autoincrement())
  outletId              Int     @unique
  requirePin            Boolean @default(false)
  requirePhoto          Boolean @default(false)
  allowLateCheckIn      Boolean @default(true)
  defaultLateThreshold  Int     @default(15)    // Menit
  allowEarlyCheckOut    Boolean @default(true)
  autoCheckOut          Boolean @default(false)
  autoCheckOutTime      String? @default("23:59") // Format: "17:00"
  geoFencing            Boolean @default(false)
  maxDistance           Float?  @default(100)    // Meter
  overtimeThreshold     Float   @default(8.0)    // Jam untuk mulai overtime
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt
  
  // Relasi
  outlet                Outlet  @relation(fields: [outletId], references: [id])
}

enum DepositTransactionType {
  DEPOSIT    // Setor/tambah saldo
  WITHDRAW   // Tarik saldo
  PAYMENT    // Pembayaran order
  ADJUSTMENT // Koreksi saldo
}

model CustomerDepositTransaction {
  id            Int       @id @default(autoincrement())
  customerId    Int
  outletId      Int
  type          DepositTransactionType
  amount        Decimal
  reference     String?
  orderId       Int?
  createdBy     Int?
  createdAt     DateTime  @default(now())
  cashboxId     Int?

  // Relasi
  customer      Customer  @relation(fields: [customerId], references: [id])
  outlet        Outlet    @relation(fields: [outletId], references: [id])
  order         Order?    @relation(fields: [orderId], references: [id])
  user          User?     @relation("UserDepositTransaction", fields: [createdBy], references: [id])
  cashbox       Cashbox?  @relation(fields: [cashboxId], references: [id])
}
