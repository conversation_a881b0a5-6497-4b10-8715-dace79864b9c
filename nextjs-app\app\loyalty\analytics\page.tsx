"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { ArrowLeft, Download, BarChart2, TrendingUp, <PERSON>, Star, Gift, RefreshCw } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"

// Mock analytics data
const analyticsData = {
  overview: {
    totalPoints: 125000,
    pointsRedeemed: 45000,
    activeCustomers: 95,
    redemptionRate: 72,
    monthlyGrowth: 15,
    averagePointsPerCustomer: 1315,
    totalRedemptions: 320,
    newCustomersThisMonth: 12,
  },
  tierDistribution: [
    { tier: "Bronze", percentage: 47, count: 45, color: "bg-amber-600" },
    { tier: "Silver", percentage: 30, count: 28, color: "bg-gray-400" },
    { tier: "Gold", percentage: 16, count: 15, color: "bg-yellow-500" },
    { tier: "Platinum", percentage: 7, count: 7, color: "bg-blue-600" },
  ],
  popularRewards: [
    { name: "Kopi Gratis", redemptions: 67, category: "lainnya" },
    { name: "Diskon 10%", redemptions: 45, category: "diskon" },
    { name: "Gratis Cuci 2kg", redemptions: 32, category: "layanan" },
    { name: "Gratis Antar-Jemput", redemptions: 28, category: "pengiriman" },
    { name: "Diskon 25%", redemptions: 15, category: "diskon" },
  ],
  monthlyData: {
    points: [
      { month: "Jan", earned: 12500, redeemed: 4200 },
      { month: "Feb", earned: 15000, redeemed: 5500 },
      { month: "Mar", earned: 18500, redeemed: 6800 },
      { month: "Apr", earned: 16000, redeemed: 5900 },
      { month: "Mei", earned: 17500, redeemed: 6200 },
      { month: "Jun", earned: 19000, redeemed: 7100 },
    ],
    customers: [
      { month: "Jan", active: 75, new: 8 },
      { month: "Feb", active: 82, new: 10 },
      { month: "Mar", active: 88, new: 9 },
      { month: "Apr", active: 90, new: 7 },
      { month: "Mei", active: 92, new: 5 },
      { month: "Jun", active: 95, new: 12 },
    ],
    redemptions: [
      { month: "Jan", count: 42 },
      { month: "Feb", count: 48 },
      { month: "Mar", count: 55 },
      { month: "Apr", count: 50 },
      { month: "Mei", count: 52 },
      { month: "Jun", count: 60 },
    ],
  },
  topCustomers: [
    { name: "Dewi Anggraini", points: 7500, tier: "Platinum", transactions: 42 },
    { name: "Siti Rahayu", points: 4200, tier: "Gold", transactions: 28 },
    { name: "Rina Wijaya", points: 3800, tier: "Gold", transactions: 25 },
    { name: "Rudi Hermawan", points: 1200, tier: "Silver", transactions: 12 },
    { name: "Ahmad Rizki", points: 2500, tier: "Silver", transactions: 15 },
  ],
  campaigns: [
    {
      name: "Double Points Weekend",
      startDate: "01/04/2024",
      endDate: "30/04/2024",
      pointsEarned: 5200,
      participants: 28,
    },
    {
      name: "Birthday Month Bonus",
      startDate: "01/04/2024",
      endDate: "30/04/2024",
      pointsEarned: 3500,
      participants: 7,
    },
    {
      name: "Gold & Platinum Special",
      startDate: "01/04/2024",
      endDate: "30/04/2024",
      pointsEarned: 4000,
      participants: 12,
    },
  ],
}

export default function LoyaltyAnalyticsPage() {
  const router = useRouter()
  const [dateRange, setDateRange] = useState("last6months")

  return (
    <div className="flex flex-col min-h-screen bg-slate-50">
      <header className="p-4 flex justify-between items-center bg-white sticky top-0 z-10 shadow-sm">
        <div className="flex items-center">
          <Link href="/loyalty" className="mr-3">
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <h1 className="text-lg font-semibold">Analitik Program Loyalitas</h1>
        </div>
        <div className="flex items-center gap-2">
          <Select value={dateRange} onValueChange={setDateRange}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Pilih rentang waktu" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="last30days">30 Hari Terakhir</SelectItem>
              <SelectItem value="last3months">3 Bulan Terakhir</SelectItem>
              <SelectItem value="last6months">6 Bulan Terakhir</SelectItem>
              <SelectItem value="lastyear">1 Tahun Terakhir</SelectItem>
              <SelectItem value="custom">Kustom</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" size="icon">
            <Download className="h-4 w-4" />
          </Button>
          <Button variant="outline" size="icon">
            <RefreshCw className="h-4 w-4" />
          </Button>
        </div>
      </header>

      <div className="p-4">
        <Tabs defaultValue="overview" className="mb-4">
          <TabsList className="grid grid-cols-4 w-full">
            <TabsTrigger value="overview">Ringkasan</TabsTrigger>
            <TabsTrigger value="points">Poin</TabsTrigger>
            <TabsTrigger value="customers">Pelanggan</TabsTrigger>
            <TabsTrigger value="rewards">Rewards</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="mt-4 space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-gray-500">Total Poin</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center">
                    <Star className="h-5 w-5 text-yellow-500 mr-2" />
                    <div className="text-2xl font-bold">{analyticsData.overview.totalPoints.toLocaleString()}</div>
                  </div>
                  <p className="text-xs text-green-500 mt-1">
                    <TrendingUp className="h-3 w-3 inline mr-1" />+{analyticsData.overview.monthlyGrowth}% dari bulan
                    lalu
                  </p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-gray-500">Poin Ditukarkan</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center">
                    <Gift className="h-5 w-5 text-purple-500 mr-2" />
                    <div className="text-2xl font-bold">{analyticsData.overview.pointsRedeemed.toLocaleString()}</div>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    {Math.round((analyticsData.overview.pointsRedeemed / analyticsData.overview.totalPoints) * 100)}%
                    dari total poin
                  </p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-gray-500">Pelanggan Aktif</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center">
                    <Users className="h-5 w-5 text-blue-500 mr-2" />
                    <div className="text-2xl font-bold">{analyticsData.overview.activeCustomers}</div>
                  </div>
                  <p className="text-xs text-green-500 mt-1">
                    <TrendingUp className="h-3 w-3 inline mr-1" />+{analyticsData.overview.newCustomersThisMonth}{" "}
                    pelanggan baru bulan ini
                  </p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-gray-500">Tingkat Penukaran</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center">
                    <BarChart2 className="h-5 w-5 text-green-500 mr-2" />
                    <div className="text-2xl font-bold">{analyticsData.overview.redemptionRate}%</div>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    {analyticsData.overview.totalRedemptions} penukaran total
                  </p>
                </CardContent>
              </Card>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Distribusi Level</CardTitle>
                  <CardDescription>Persentase pelanggan di setiap level</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {analyticsData.tierDistribution.map((tier) => (
                      <div key={tier.tier} className="space-y-1">
                        <div className="flex justify-between text-sm">
                          <span className="flex items-center">
                            <span className={`w-3 h-3 rounded-full ${tier.color} mr-2`}></span>
                            {tier.tier}
                          </span>
                          <span>
                            {tier.percentage}% ({tier.count})
                          </span>
                        </div>
                        <Progress value={tier.percentage} className="h-2" />
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Reward Terpopuler</CardTitle>
                  <CardDescription>Reward dengan penukaran terbanyak</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {analyticsData.popularRewards.map((reward, index) => (
                      <div key={reward.name} className="flex items-center">
                        <div className="w-6 text-center font-medium">{index + 1}</div>
                        <div className="flex-1 ml-2">{reward.name}</div>
                        <Badge variant="outline" className="ml-2">
                          {reward.category}
                        </Badge>
                        <div className="text-sm text-gray-500 ml-2">{reward.redemptions} penukaran</div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Pelanggan Teratas</CardTitle>
                  <CardDescription>Pelanggan dengan poin tertinggi</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {analyticsData.topCustomers.map((customer, index) => (
                      <div key={customer.name} className="flex items-center">
                        <div className="w-6 text-center font-medium">{index + 1}</div>
                        <div className="flex-1 ml-2">
                          <div className="font-medium">{customer.name}</div>
                          <div className="text-xs text-gray-500">{customer.transactions} transaksi</div>
                        </div>
                        <Badge
                          className={
                            customer.tier === "Platinum"
                              ? "bg-blue-600"
                              : customer.tier === "Gold"
                                ? "bg-yellow-500"
                                : customer.tier === "Silver"
                                  ? "bg-gray-400"
                                  : "bg-amber-600"
                          }
                        >
                          {customer.tier}
                        </Badge>
                        <div className="text-sm font-medium ml-2">{customer.points} poin</div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Kampanye Aktif</CardTitle>
                  <CardDescription>Performa kampanye loyalitas</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {analyticsData.campaigns.map((campaign) => (
                      <div key={campaign.name} className="space-y-2">
                        <div className="flex justify-between">
                          <div className="font-medium">{campaign.name}</div>
                          <Badge variant="outline" className="text-green-500 border-green-500">
                            Aktif
                          </Badge>
                        </div>
                        <div className="text-xs text-gray-500">
                          {campaign.startDate} - {campaign.endDate}
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>Poin diberikan</span>
                          <span className="font-medium">{campaign.pointsEarned}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>Peserta</span>
                          <span className="font-medium">{campaign.participants}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Points Tab */}
          <TabsContent value="points" className="mt-4 space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-base">Tren Poin</CardTitle>
                <CardDescription>Poin yang diperoleh dan ditukarkan per bulan</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80 flex items-center justify-center">
                  <div className="text-center text-gray-500">
                    <BarChart2 className="h-16 w-16 mx-auto mb-4 text-gray-300" />
                    <p>Grafik tren poin akan ditampilkan di sini</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Poin per Bulan</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {analyticsData.monthlyData.points.map((month) => (
                      <div key={month.month} className="space-y-2">
                        <div className="font-medium">{month.month}</div>
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <div className="text-xs text-gray-500">Poin Diperoleh</div>
                            <div className="font-medium text-green-600">{month.earned.toLocaleString()}</div>
                          </div>
                          <div>
                            <div className="text-xs text-gray-500">Poin Ditukarkan</div>
                            <div className="font-medium text-red-600">{month.redeemed.toLocaleString()}</div>
                          </div>
                        </div>
                        <Separator />
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Statistik Poin</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-500">Total Poin Diberikan</span>
                      <span className="font-medium">{analyticsData.overview.totalPoints.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-500">Total Poin Ditukarkan</span>
                      <span className="font-medium">{analyticsData.overview.pointsRedeemed.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-500">Poin yang Belum Ditukarkan</span>
                      <span className="font-medium">
                        {(analyticsData.overview.totalPoints - analyticsData.overview.pointsRedeemed).toLocaleString()}
                      </span>
                    </div>
                    <Separator />
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-500">Rata-rata Poin per Pelanggan</span>
                      <span className="font-medium">
                        {analyticsData.overview.averagePointsPerCustomer.toLocaleString()}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-500">Tingkat Penukaran</span>
                      <span className="font-medium">{analyticsData.overview.redemptionRate}%</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Customers Tab */}
          <TabsContent value="customers" className="mt-4 space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-base">Tren Pelanggan</CardTitle>
                <CardDescription>Pelanggan aktif dan baru per bulan</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80 flex items-center justify-center">
                  <div className="text-center text-gray-500">
                    <BarChart2 className="h-16 w-16 mx-auto mb-4 text-gray-300" />
                    <p>Grafik tren pelanggan akan ditampilkan di sini</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Pelanggan per Bulan</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {analyticsData.monthlyData.customers.map((month) => (
                      <div key={month.month} className="space-y-2">
                        <div className="font-medium">{month.month}</div>
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <div className="text-xs text-gray-500">Pelanggan Aktif</div>
                            <div className="font-medium">{month.active}</div>
                          </div>
                          <div>
                            <div className="text-xs text-gray-500">Pelanggan Baru</div>
                            <div className="font-medium text-green-600">+{month.new}</div>
                          </div>
                        </div>
                        <Separator />
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Distribusi Level</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {analyticsData.tierDistribution.map((tier) => (
                      <div key={tier.tier} className="space-y-2">
                        <div className="flex items-center">
                          <span className={`w-4 h-4 rounded-full ${tier.color} mr-2`}></span>
                          <span className="font-medium">{tier.tier}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-500">Jumlah Pelanggan</span>
                          <span>{tier.count}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-500">Persentase</span>
                          <span>{tier.percentage}%</span>
                        </div>
                        <Progress value={tier.percentage} className="h-2" />
                        <Separator />
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Rewards Tab */}
          <TabsContent value="rewards" className="mt-4 space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-base">Tren Penukaran Reward</CardTitle>
                <CardDescription>Jumlah penukaran reward per bulan</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80 flex items-center justify-center">
                  <div className="text-center text-gray-500">
                    <BarChart2 className="h-16 w-16 mx-auto mb-4 text-gray-300" />
                    <p>Grafik tren penukaran reward akan ditampilkan di sini</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Penukaran per Bulan</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {analyticsData.monthlyData.redemptions.map((month) => (
                      <div key={month.month} className="space-y-2">
                        <div className="font-medium">{month.month}</div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-gray-500">Jumlah Penukaran</span>
                          <span className="font-medium">{month.count}</span>
                        </div>
                        <Progress
                          value={
                            (month.count / Math.max(...analyticsData.monthlyData.redemptions.map((m) => m.count))) * 100
                          }
                          className="h-2"
                        />
                        <Separator />
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Reward Terpopuler</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {analyticsData.popularRewards.map((reward, index) => (
                      <div key={reward.name} className="space-y-2">
                        <div className="flex items-center">
                          <div className="w-6 h-6 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center mr-2 text-sm font-medium">
                            {index + 1}
                          </div>
                          <span className="font-medium">{reward.name}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-500">Kategori</span>
                          <Badge variant="outline">{reward.category}</Badge>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-500">Jumlah Penukaran</span>
                          <span>{reward.redemptions}</span>
                        </div>
                        <Progress
                          value={
                            (reward.redemptions / Math.max(...analyticsData.popularRewards.map((r) => r.redemptions))) *
                            100
                          }
                          className="h-2"
                        />
                        <Separator />
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
