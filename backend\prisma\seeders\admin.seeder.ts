import { PrismaClient, Role } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

export async function seedAdmin() {
  console.log('🔧 Seeding admin user...');

  try {
    // Check if admin already exists
    const existingAdmin = await prisma.user.findFirst({
      where: { role: Role.ADMIN }
    });

    if (existingAdmin) {
      console.log('✅ Admin user already exists, skipping...');
      return;
    }

    // Hash password
    const hashedPassword = await bcrypt.hash('admin123456', 8);

    // Create admin user
    const admin = await prisma.user.create({
      data: {
        name: 'Super Admin',
        email: '<EMAIL>',
        password: hashedPassword,
        phone: '+6281234567890',
        role: Role.ADMIN,
        isEmailVerified: true,
        isPhoneVerified: true,
        isActive: true
      }
    });

    console.log('✅ Admin user created successfully:');
    console.log(`   📧 Email: ${admin.email}`);
    console.log(`   🔑 Password: admin123456`);
    console.log(`   👤 Name: ${admin.name}`);
    console.log(`   📱 Phone: ${admin.phone}`);
    console.log(`   🎭 Role: ${admin.role}`);
  } catch (error) {
    console.error('❌ Error seeding admin user:', error);
    throw error;
  }
}

export default seedAdmin;

// Run seeder if called directly
if (require.main === module) {
  seedAdmin()
    .catch((e) => {
      console.error(e);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
}
