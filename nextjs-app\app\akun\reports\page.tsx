"use client"

import { useState } from "react"
import Link from "next/link"
import { ArrowLeft, Download, Calendar, BarChart2, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Filter } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

export default function ReportsPage() {
  const [activeTab, setActiveTab] = useState("sales")
  const [period, setPeriod] = useState("this-month")

  return (
    <div className="flex flex-col min-h-screen bg-slate-50">
      <header className="p-4 flex justify-between items-center bg-white sticky top-0 z-10 shadow-sm">
        <div className="flex items-center">
          <Link href="/akun" className="mr-3">
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <h1 className="text-lg font-semibold"><PERSON><PERSON><PERSON></h1>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="icon">
            <Filter className="h-4 w-4" />
          </Button>
          <Button variant="outline" size="icon">
            <Download className="h-4 w-4" />
          </Button>
        </div>
      </header>

      <main className="flex-1 p-4 pb-20">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-semibold">Ringkasan Laporan</h2>
          <Select value={period} onValueChange={setPeriod}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Pilih Periode" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="today">Hari Ini</SelectItem>
              <SelectItem value="this-week">Minggu Ini</SelectItem>
              <SelectItem value="this-month">Bulan Ini</SelectItem>
              <SelectItem value="last-month">Bulan Lalu</SelectItem>
              <SelectItem value="this-year">Tahun Ini</SelectItem>
              <SelectItem value="custom">Kustom</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="grid grid-cols-2 gap-4 mb-6">
          <Card className="p-4">
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-sm font-medium text-gray-500">Total Penjualan</h3>
              <TrendingUp className="h-4 w-4 text-green-500" />
            </div>
            <p className="text-2xl font-bold">Rp 5.250.000</p>
            <p className="text-xs text-green-500">+15% dari bulan lalu</p>
          </Card>
          <Card className="p-4">
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-sm font-medium text-gray-500">Total Order</h3>
              <BarChart2 className="h-4 w-4 text-blue-500" />
            </div>
            <p className="text-2xl font-bold">42</p>
            <p className="text-xs text-green-500">+8% dari bulan lalu</p>
          </Card>
          <Card className="p-4">
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-sm font-medium text-gray-500">Rata-rata Order</h3>
              <PieChart className="h-4 w-4 text-purple-500" />
            </div>
            <p className="text-2xl font-bold">Rp 125.000</p>
            <p className="text-xs text-green-500">+5% dari bulan lalu</p>
          </Card>
          <Card className="p-4">
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-sm font-medium text-gray-500">Pelanggan Baru</h3>
              <Calendar className="h-4 w-4 text-orange-500" />
            </div>
            <p className="text-2xl font-bold">8</p>
            <p className="text-xs text-red-500">-2% dari bulan lalu</p>
          </Card>
        </div>

        <Tabs defaultValue="sales" className="mb-4" onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-4 w-full">
            <TabsTrigger value="sales">Penjualan</TabsTrigger>
            <TabsTrigger value="customers">Pelanggan</TabsTrigger>
            <TabsTrigger value="services">Layanan</TabsTrigger>
            <TabsTrigger value="outlets">Outlet</TabsTrigger>
          </TabsList>

          <TabsContent value="sales" className="mt-4">
            <Card className="p-4">
              <h3 className="font-semibold mb-4">Grafik Penjualan</h3>
              <div className="h-64 bg-gray-100 rounded-md flex items-center justify-center">
                <p className="text-gray-500">Grafik penjualan akan ditampilkan di sini</p>
              </div>
              <div className="mt-4 space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-500">Total Penjualan</span>
                  <span className="font-medium">Rp 5.250.000</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Total Order</span>
                  <span className="font-medium">42</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Rata-rata per Order</span>
                  <span className="font-medium">Rp 125.000</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Order Tertinggi</span>
                  <span className="font-medium">Rp 350.000</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Order Terendah</span>
                  <span className="font-medium">Rp 35.000</span>
                </div>
              </div>
            </Card>
          </TabsContent>

          <TabsContent value="customers" className="mt-4">
            <Card className="p-4">
              <h3 className="font-semibold mb-4">Analisis Pelanggan</h3>
              <div className="h-64 bg-gray-100 rounded-md flex items-center justify-center">
                <p className="text-gray-500">Grafik pelanggan akan ditampilkan di sini</p>
              </div>
              <div className="mt-4 space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-500">Total Pelanggan</span>
                  <span className="font-medium">120</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Pelanggan Aktif</span>
                  <span className="font-medium">98</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Pelanggan Baru</span>
                  <span className="font-medium">8</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Pelanggan Teratas</span>
                  <span className="font-medium">Budi Santoso (12 order)</span>
                </div>
              </div>
            </Card>
          </TabsContent>

          <TabsContent value="services" className="mt-4">
            <Card className="p-4">
              <h3 className="font-semibold mb-4">Analisis Layanan</h3>
              <div className="h-64 bg-gray-100 rounded-md flex items-center justify-center">
                <p className="text-gray-500">Grafik layanan akan ditampilkan di sini</p>
              </div>
              <div className="mt-4 space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-500">Layanan Terpopuler</span>
                  <span className="font-medium">Cuci + Setrika (65%)</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Layanan Kedua</span>
                  <span className="font-medium">Express (20%)</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Layanan Ketiga</span>
                  <span className="font-medium">Satuan (15%)</span>
                </div>
              </div>
            </Card>
          </TabsContent>

          <TabsContent value="outlets" className="mt-4">
            <Card className="p-4">
              <h3 className="font-semibold mb-4">Performa Outlet</h3>
              <div className="h-64 bg-gray-100 rounded-md flex items-center justify-center">
                <p className="text-gray-500">Grafik outlet akan ditampilkan di sini</p>
              </div>
              <div className="mt-4 space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-500">Outlet Terbaik</span>
                  <span className="font-medium">Cabang Utama (Rp 3.500.000)</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Outlet Kedua</span>
                  <span className="font-medium">Cabang Kemang (Rp 1.750.000)</span>
                </div>
              </div>
            </Card>
          </TabsContent>
        </Tabs>
      </main>
    </div>
  )
}
