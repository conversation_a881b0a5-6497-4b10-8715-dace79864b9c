import httpStatus from 'http-status';
import tokenService from './token.service';
import userService from './user.service';
import emailVerificationService from './emailVerification.service';
import ApiError from '../utils/ApiError';
import { TokenType, User } from '@prisma/client';
import prisma from '../client';
import { encryptPassword, isPasswordMatch } from '../utils/encryption';
import { AuthTokensResponse } from '../types/response';
import exclude from '../utils/exclude';
import config from '../config/config';
import logger from '../config/logger';

/**
 * Login with username and password
 * @param {string} email
 * @param {string} password
 * @returns {Promise<Omit<User, 'password'>>}
 */
const loginUserWithEmailAndPassword = async (
  email: string,
  password: string
): Promise<Omit<User, 'password'>> => {
  logger.debug(`[AUTH_SERVICE] Starting login process for email: ${email}`);

  try {
    const user = await userService.getUserByEmail(email, [
      'id',
      'email',
      'name',
      'password',
      'role',
      'isEmailVerified',
      'isPhoneVerified',
      'isActive',
      'createdAt',
      'updatedAt',
      'phone',
      'outletId'
    ]);

    if (!user) {
      logger.warn(`[AUTH_SERVICE] Login failed - User not found: ${email}`);
      throw new ApiError(httpStatus.UNAUTHORIZED, 'Incorrect email or password');
    }

    logger.debug(
      `[AUTH_SERVICE] User found - UserID: ${user.id}, Email: ${email}, Role: ${user.role}, IsActive: ${user.isActive}`
    );

    if (!(await isPasswordMatch(password, user.password as string))) {
      logger.warn(
        `[AUTH_SERVICE] Login failed - Incorrect password for email: ${email}, UserID: ${user.id}`
      );
      throw new ApiError(httpStatus.UNAUTHORIZED, 'Incorrect email or password');
    }

    // Check if user is active (for employees)
    if (!user.isActive) {
      logger.warn(`[AUTH_SERVICE] Login failed - Inactive account: ${email}, UserID: ${user.id}`);
      throw new ApiError(httpStatus.UNAUTHORIZED, 'Account is inactive');
    }

    // Log verification status
    if (!user.isEmailVerified) {
      logger.warn(
        `[AUTH_SERVICE] User logging in with unverified email - UserID: ${user.id}, Email: ${email}`
      );
    }
    if (!user.isPhoneVerified) {
      logger.warn(
        `[AUTH_SERVICE] User logging in with unverified phone - UserID: ${user.id}, Phone: ${user.phone}`
      );
    }

    logger.info(
      `[AUTH_SERVICE] Login successful - UserID: ${user.id}, Email: ${email}, Role: ${user.role}`
    );
    return exclude(user, ['password']);
  } catch (error: any) {
    if (error instanceof ApiError) {
      throw error;
    }
    logger.error(`[AUTH_SERVICE] Login error - Email: ${email}, Error: ${error.message}`);
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Login failed due to internal error');
  }
};

/**
 * Logout
 * @param {string} refreshToken
 * @returns {Promise<void>}
 */
const logout = async (refreshToken: string): Promise<void> => {
  logger.debug(`[AUTH_SERVICE] Starting logout process`);

  try {
    const refreshTokenData = await prisma.token.findFirst({
      where: {
        token: refreshToken,
        type: TokenType.REFRESH,
        blacklisted: false
      }
    });

    if (!refreshTokenData) {
      logger.warn(`[AUTH_SERVICE] Logout failed - Refresh token not found or already blacklisted`);
      throw new ApiError(httpStatus.NOT_FOUND, 'Not found');
    }

    logger.debug(
      `[AUTH_SERVICE] Found valid refresh token - TokenID: ${refreshTokenData.id}, UserID: ${refreshTokenData.userId}`
    );

    await prisma.token.delete({ where: { id: refreshTokenData.id } });

    logger.info(
      `[AUTH_SERVICE] Logout successful - TokenID: ${refreshTokenData.id}, UserID: ${refreshTokenData.userId}`
    );
  } catch (error: any) {
    if (error instanceof ApiError) {
      throw error;
    }
    logger.error(`[AUTH_SERVICE] Logout error - Error: ${error.message}`);
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Logout failed due to internal error');
  }
};

/**
 * Refresh auth tokens
 * @param {string} refreshToken
 * @returns {Promise<AuthTokensResponse>}
 */
const refreshAuth = async (refreshToken: string): Promise<AuthTokensResponse> => {
  logger.debug(`[AUTH_SERVICE] Starting token refresh process`);

  try {
    const refreshTokenData = await tokenService.verifyToken(refreshToken, TokenType.REFRESH);
    const { userId } = refreshTokenData;

    logger.debug(
      `[AUTH_SERVICE] Valid refresh token verified - UserID: ${userId}, TokenID: ${refreshTokenData.id}`
    );

    await prisma.token.delete({ where: { id: refreshTokenData.id } });
    logger.debug(`[AUTH_SERVICE] Old refresh token deleted - TokenID: ${refreshTokenData.id}`);

    const newTokens = await tokenService.generateAuthTokens({ id: userId });

    logger.info(
      `[AUTH_SERVICE] Token refresh successful - UserID: ${userId}, New AccessToken expires: ${
        newTokens.access.expires
      }, New RefreshToken expires: ${newTokens.refresh?.expires || 'N/A'}`
    );

    return newTokens;
  } catch (error: any) {
    logger.warn(`[AUTH_SERVICE] Token refresh failed - Error: ${error.message}`);
    throw new ApiError(httpStatus.UNAUTHORIZED, 'Please authenticate');
  }
};

/**
 * Reset password
 * @param {string} resetPasswordToken
 * @param {string} newPassword
 * @returns {Promise<void>}
 */
const resetPassword = async (resetPasswordToken: string, newPassword: string): Promise<void> => {
  logger.debug(`[AUTH_SERVICE] Starting password reset process`);

  try {
    const resetPasswordTokenData = await tokenService.verifyToken(
      resetPasswordToken,
      TokenType.RESET_PASSWORD
    );

    logger.debug(
      `[AUTH_SERVICE] Valid reset token verified - UserID: ${resetPasswordTokenData.userId}, TokenID: ${resetPasswordTokenData.id}`
    );

    const user = await userService.getUserById(resetPasswordTokenData.userId);
    if (!user) {
      logger.error(
        `[AUTH_SERVICE] Password reset failed - User not found: UserID ${resetPasswordTokenData.userId}`
      );
      throw new Error('User not found');
    }

    logger.debug(
      `[AUTH_SERVICE] User found for password reset - UserID: ${user.id}, Email: ${user.email}`
    );

    const encryptedPassword = await encryptPassword(newPassword);
    await userService.updateUserById(user.id, { password: encryptedPassword });

    logger.debug(`[AUTH_SERVICE] Password updated for user - UserID: ${user.id}`);

    await prisma.token.deleteMany({ where: { userId: user.id, type: TokenType.RESET_PASSWORD } });

    logger.info(
      `[AUTH_SERVICE] Password reset successful - UserID: ${user.id}, Email: ${user.email}`
    );
  } catch (error: any) {
    logger.error(`[AUTH_SERVICE] Password reset failed - Error: ${error.message}`);
    throw new ApiError(httpStatus.UNAUTHORIZED, 'Password reset failed');
  }
};

/**
 * Verify email
 * @param {string} verifyEmailToken
 * @returns {Promise<void>}
 */
const verifyEmail = async (verifyEmailToken: string): Promise<void> => {
  logger.debug(`[AUTH_SERVICE] Starting email verification process`);

  try {
    const verifyEmailTokenData = await tokenService.verifyToken(
      verifyEmailToken,
      TokenType.VERIFY_EMAIL
    );

    logger.debug(
      `[AUTH_SERVICE] Valid verification token verified - UserID: ${verifyEmailTokenData.userId}, TokenID: ${verifyEmailTokenData.id}`
    );

    await prisma.token.deleteMany({
      where: { userId: verifyEmailTokenData.userId, type: TokenType.VERIFY_EMAIL }
    });

    logger.debug(
      `[AUTH_SERVICE] Verification tokens deleted for user - UserID: ${verifyEmailTokenData.userId}`
    );

    await userService.updateUserById(verifyEmailTokenData.userId, { isEmailVerified: true });

    logger.info(
      `[AUTH_SERVICE] Email verification successful - UserID: ${verifyEmailTokenData.userId}`
    );
  } catch (error: any) {
    logger.warn(`[AUTH_SERVICE] Email verification failed - Error: ${error.message}`);
    throw new ApiError(httpStatus.UNAUTHORIZED, 'Email verification failed');
  }
};

/**
 * Verify email with OTP
 * @param {string} code
 * @returns {Promise<void>}
 */
const verifyEmailWithOTP = async (code: string): Promise<void> => {
  logger.debug(
    `[AUTH_SERVICE] Starting email OTP verification process - Code: ${code.substring(0, 2)}***`
  );

  try {
    await emailVerificationService.verifyEmailOTPByCode(code);
    logger.info(
      `[AUTH_SERVICE] Email OTP verification successful - Code: ${code.substring(0, 2)}***`
    );
  } catch (error: any) {
    logger.warn(
      `[AUTH_SERVICE] Email OTP verification failed - Code: ${code.substring(0, 2)}***, Error: ${
        error.message
      }`
    );
    throw new ApiError(httpStatus.BAD_REQUEST, 'Invalid or expired verification code');
  }
};

export default {
  loginUserWithEmailAndPassword,
  isPasswordMatch,
  encryptPassword,
  logout,
  refreshAuth,
  resetPassword,
  verifyEmail,
  verifyEmailWithOTP
};
