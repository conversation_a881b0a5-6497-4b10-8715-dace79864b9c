import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from '@/components/ui/use-toast';
import { useAuth } from '@/lib/auth-context';
import { orderAPI, getOrderErrorMessage } from '@/lib/api/orders';
import type {
  Order,
  CreateOrderRequest,
  UpdateOrderRequest,
  UpdateOrderStatusRequest,
  UpdateOrderItemStatusRequest,
  UpdateOrderItemsRequest,
  AddPaymentRequest,
  GetOrdersParams,
  OrderStatusHistory,
  OrderItemStatusHistory,
  OrderTimeline,
} from '@/types/orders';

// Hook untuk mengambil semua orders dengan pagination dan filter
export function useOrders(params?: GetOrdersParams) {
  return useQuery({
    queryKey: ['orders', params],
    queryFn: () => orderAPI.getOrders(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

// Hook untuk mengambil order berdasarkan ID
export function useOrder(id: number | null) {
  return useQuery({
    queryKey: ['orders', id],
    queryFn: () => (id ? orderAPI.getOrder(id) : Promise.reject('No ID')),
    enabled: !!id,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

// Hook untuk membuat order baru
export function useCreateOrder() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateOrderRequest) => orderAPI.createOrder(data),
    onSuccess: (newOrder) => {
      // Invalidate dan refetch orders list
      queryClient.invalidateQueries({ queryKey: ['orders'] });

      toast({
        title: 'Berhasil',
        description: `Order ${newOrder.orderNumber} berhasil dibuat.`,
      });
    },
    // Tidak langsung show error toast, biarkan component handle error
  });
}

// Hook untuk update order
export function useUpdateOrder() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateOrderRequest }) =>
      orderAPI.updateOrder(id, data),
    onSuccess: (updatedOrder) => {
      // Invalidate orders list dan detail
      queryClient.invalidateQueries({ queryKey: ['orders'] });
      queryClient.setQueryData(['orders', updatedOrder.id], updatedOrder);

      toast({
        title: 'Berhasil',
        description: `Order ${updatedOrder.orderNumber} berhasil diperbarui.`,
      });
    },
    // Tidak langsung show error toast, biarkan component handle error
  });
}

// Hook untuk delete order
export function useDeleteOrder() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => orderAPI.deleteOrder(id),
    onSuccess: () => {
      // Invalidate orders list
      queryClient.invalidateQueries({ queryKey: ['orders'] });

      toast({
        title: 'Berhasil',
        description: 'Order berhasil dihapus.',
      });
    },
    onError: (error: any) => {
      const message = getOrderErrorMessage(error);
      toast({
        title: 'Error',
        description: message,
        variant: 'destructive',
      });
    },
  });
}

// Hook untuk update order status
export function useUpdateOrderStatus() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      id,
      data,
    }: {
      id: number;
      data: UpdateOrderStatusRequest;
    }) => orderAPI.updateOrderStatus(id, data),
    onSuccess: (updatedOrder) => {
      // Invalidate orders list dan detail
      queryClient.invalidateQueries({ queryKey: ['orders'] });
      queryClient.setQueryData(['orders', updatedOrder.id], updatedOrder);

      toast({
        title: 'Berhasil',
        description: `Status order ${updatedOrder.orderNumber} berhasil diperbarui.`,
      });
    },
    onError: (error: any) => {
      const message = getOrderErrorMessage(error);
      toast({
        title: 'Error',
        description: message,
        variant: 'destructive',
      });
    },
  });
}

// Hook untuk update order items
export function useUpdateOrderItems() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateOrderItemsRequest }) =>
      orderAPI.updateOrderItems(id, data),
    onSuccess: (updatedOrder) => {
      // Invalidate orders list dan detail
      queryClient.invalidateQueries({ queryKey: ['orders'] });
      queryClient.setQueryData(['orders', updatedOrder.id], updatedOrder);

      toast({
        title: 'Berhasil',
        description: `Item order ${updatedOrder.orderNumber} berhasil diperbarui.`,
      });
    },
    onError: (error: any) => {
      const message = getOrderErrorMessage(error);
      toast({
        title: 'Error',
        description: message,
        variant: 'destructive',
      });
    },
  });
}

// Hook untuk update order item status
export function useUpdateOrderItemStatus() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      orderId,
      itemId,
      data,
    }: {
      orderId: number;
      itemId: number;
      data: UpdateOrderItemStatusRequest;
    }) => orderAPI.updateOrderItemStatus(orderId, itemId, data),
    onSuccess: (_, { orderId }) => {
      // Invalidate orders list dan detail
      queryClient.invalidateQueries({ queryKey: ['orders'] });
      queryClient.invalidateQueries({ queryKey: ['orders', orderId] });

      toast({
        title: 'Berhasil',
        description: 'Status item berhasil diperbarui.',
      });
    },
    onError: (error: any) => {
      const message = getOrderErrorMessage(error);
      toast({
        title: 'Error',
        description: message,
        variant: 'destructive',
      });
    },
  });
}

// Hook untuk menambah payment ke order
export function useAddPayment() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: AddPaymentRequest }) =>
      orderAPI.addPayment(id, data),
    onSuccess: (_, { id }) => {
      // Invalidate orders list dan detail
      queryClient.invalidateQueries({ queryKey: ['orders'] });
      queryClient.invalidateQueries({ queryKey: ['orders', id] });

      toast({
        title: 'Berhasil',
        description: 'Pembayaran berhasil ditambahkan.',
      });
    },
    onError: (error: any) => {
      const message = getOrderErrorMessage(error);
      toast({
        title: 'Error',
        description: message,
        variant: 'destructive',
      });
    },
  });
}

// Hook untuk mengambil order status history
export function useOrderStatusHistory(orderId: number | null) {
  return useQuery({
    queryKey: ['orders', orderId, 'status-history'],
    queryFn: () =>
      orderId
        ? orderAPI.getOrderStatusHistory(orderId)
        : Promise.reject('No order ID'),
    enabled: !!orderId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook untuk mengambil order item status history
export function useOrderItemStatusHistory(
  orderId: number | null,
  itemId: number | null
) {
  return useQuery({
    queryKey: ['orders', orderId, 'items', itemId, 'status-history'],
    queryFn: () =>
      orderId && itemId
        ? orderAPI.getOrderItemStatusHistory(orderId, itemId)
        : Promise.reject('No order ID or item ID'),
    enabled: !!orderId && !!itemId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook untuk mengambil complete order timeline
export function useOrderTimeline(orderId: number | null) {
  return useQuery({
    queryKey: ['orders', orderId, 'timeline'],
    queryFn: () =>
      orderId
        ? orderAPI.getOrderTimeline(orderId)
        : Promise.reject('No order ID'),
    enabled: !!orderId,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

// Hook untuk orders dengan filter berdasarkan status
export function useOrdersByStatus(
  status?: string,
  additionalParams?: GetOrdersParams
) {
  const params: GetOrdersParams = {
    ...additionalParams,
    ...(status && status !== 'all' ? { status: status as any } : {}),
  };

  return useOrders(params);
}

// Hook untuk orders dengan filter berdasarkan payment status
export function useOrdersByPaymentStatus(
  paymentStatus?: string,
  additionalParams?: GetOrdersParams
) {
  const params: GetOrdersParams = {
    ...additionalParams,
    ...(paymentStatus && paymentStatus !== 'all'
      ? { paymentStatus: paymentStatus as any }
      : {}),
  };

  return useOrders(params);
}

// Hook untuk orders dengan filter berdasarkan customer
export function useOrdersByCustomer(
  customerId?: number,
  additionalParams?: GetOrdersParams
) {
  const params: GetOrdersParams = {
    ...additionalParams,
    ...(customerId ? { customerId } : {}),
  };

  return useOrders(params);
}

// Hook untuk search orders
export function useSearchOrders(
  searchQuery?: string,
  additionalParams?: GetOrdersParams
) {
  const params: GetOrdersParams = {
    ...additionalParams,
    ...(searchQuery ? { search: searchQuery } : {}),
  };

  return useOrders(params);
}

// Hook untuk orders dengan date range
export function useOrdersByDateRange(
  dateFrom?: string,
  dateTo?: string,
  additionalParams?: GetOrdersParams
) {
  const params: GetOrdersParams = {
    ...additionalParams,
    ...(dateFrom ? { dateFrom } : {}),
    ...(dateTo ? { dateTo } : {}),
  };

  return useOrders(params);
}

// Helper function untuk mendapatkan count orders berdasarkan status
export function useOrderCounts() {
  const { data: allOrders } = useOrders({ limit: 1000 }); // Get all orders for counting

  const counts = {
    all: allOrders?.totalResults || 0,
    konfirmasi: 0,
    pickup: 0,
    pending: 0,
    processing: 0,
    ready: 0,
    ready_for_pickup: 0,
    completed: 0,
    cancelled: 0,
    unpaid: 0,
    partial: 0,
    paid: 0,
    refunded: 0,
  };

  if (allOrders?.results) {
    allOrders.results.forEach((order) => {
      // Count by order status - sesuai dengan backend schema
      switch (order.status) {
        case 'KONFIRMASI':
          counts.konfirmasi++;
          break;
        case 'PICKUP':
          counts.pickup++;
          break;
        case 'PENDING':
          counts.pending++;
          break;
        case 'PROCESSING':
          counts.processing++;
          break;
        case 'READY':
          counts.ready++;
          break;
        case 'READY_FOR_PICKUP':
          counts.ready_for_pickup++;
          break;
        case 'COMPLETED':
          counts.completed++;
          break;
        case 'CANCELLED':
          counts.cancelled++;
          break;
      }

      // Count by payment status
      switch (order.paymentStatus) {
        case 'UNPAID':
          counts.unpaid++;
          break;
        case 'PARTIAL':
          counts.partial++;
          break;
        case 'PAID':
          counts.paid++;
          break;
        case 'REFUNDED':
          counts.refunded++;
          break;
      }
    });
  }

  return counts;
}

// Hook untuk mendapatkan ringkasan pesanan di dashboard
export function useOrderSummary() {
  return useQuery({
    queryKey: ['order-summary'],
    queryFn: () => orderAPI.getOrderSummary(),
    staleTime: 60 * 1000, // 1 minute
  });
}
