'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Save, Upload, AlertCircle } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Alert, AlertDescription } from '@/components/ui/alert';

import {
  useCreateEmployee,
  getErrorMessage,
  getFieldErrors,
} from '@/hooks/useEmployees';
import { useAuth } from '@/lib/auth-context';
import type { CreateEmployeeRequest } from '@/lib/api/employees';

export default function AddEmployeePage() {
  const router = useRouter();
  const { activeOutlet } = useAuth();
  const [formData, setFormData] = useState<
    Omit<CreateEmployeeRequest, 'outletId'> & { password2: string }
  >({
    name: '',
    email: '',
    phone: '',
    password: '',
    password2: '',
  });
  const [clientErrors, setClientErrors] = useState<Record<string, string>>({});

  const createEmployeeMutation = useCreateEmployee();

  // Extract error details when mutation fails
  const apiError = createEmployeeMutation.error;
  const apiErrorMessage = apiError ? getErrorMessage(apiError) : '';
  const fieldErrors = apiError ? getFieldErrors(apiError) : {};

  // Reset API errors when form data changes
  useEffect(() => {
    if (createEmployeeMutation.error) {
      createEmployeeMutation.reset();
    }
  }, [formData]);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));

    // Clear client error for this field
    if (clientErrors[name]) {
      setClientErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!formData.name.trim()) {
      errors.name = 'Nama lengkap wajib diisi';
    }

    if (!formData.email.trim()) {
      errors.email = 'Email wajib diisi';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = 'Format email tidak valid';
    }

    if (!formData.phone.trim()) {
      errors.phone = 'Nomor telepon wajib diisi';
    } else if (!/^[0-9+]{10,15}$/.test(formData.phone)) {
      errors.phone = 'Nomor telepon harus 10-15 digit';
    }

    if (!formData.password) {
      errors.password = 'Password wajib diisi';
    } else if (formData.password.length < 8) {
      errors.password = 'Password minimal 8 karakter';
    }

    if (!formData.password2) {
      errors.password2 = 'Konfirmasi password wajib diisi';
    } else if (formData.password !== formData.password2) {
      errors.password2 = 'Password dan konfirmasi password tidak sama';
    }

    if (!activeOutlet?.id) {
      errors.outlet = 'Outlet aktif tidak ditemukan';
    }

    setClientErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    if (!activeOutlet?.id) {
      console.error('Active outlet not found');
      return;
    }

    try {
      const { password2, ...createData } = formData;
      await createEmployeeMutation.mutateAsync({
        ...createData,
        outletId: activeOutlet.id,
      });
      router.push('/akun/employees');
    } catch {
      // Error akan ditampilkan melalui fieldErrors dan apiErrorMessage
    }
  };

  const getFieldError = (fieldName: string): string => {
    return clientErrors[fieldName] || fieldErrors[fieldName] || '';
  };

  return (
    <div className="flex flex-col min-h-screen bg-slate-50">
      <header className="p-4 flex justify-between items-center bg-white sticky top-0 z-10 shadow-sm">
        <div className="flex items-center">
          <Link href="/akun/employees" className="mr-3">
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <h1 className="text-lg font-semibold">Tambah Pegawai Baru</h1>
        </div>
        <Button
          type="submit"
          form="add-employee-form"
          className="bg-blue-500 hover:bg-blue-600"
          disabled={createEmployeeMutation.isPending}
        >
          <Save className="h-4 w-4 mr-2" />
          {createEmployeeMutation.isPending ? 'Menyimpan...' : 'Simpan'}
        </Button>
      </header>

      <main className="flex-1 p-4 pb-20">
        <form id="add-employee-form" onSubmit={handleSubmit}>
          {/* General Error Alert */}
          {apiErrorMessage && !Object.keys(fieldErrors).length && (
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{apiErrorMessage}</AlertDescription>
            </Alert>
          )}

          {/* Outlet Error Alert */}
          {clientErrors.outlet && (
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{clientErrors.outlet}</AlertDescription>
            </Alert>
          )}

          <Card className="p-4 space-y-4 mb-4">
            <div className="flex flex-col items-center justify-center">
              <Avatar className="h-24 w-24 mb-2">
                <AvatarImage src="/placeholder.svg" alt="Avatar" />
                <AvatarFallback>AV</AvatarFallback>
              </Avatar>
              <Button
                type="button"
                variant="outline"
                size="sm"
                className="mt-2"
                disabled
              >
                <Upload className="h-4 w-4 mr-2" /> Unggah Foto (Coming Soon)
              </Button>
            </div>
          </Card>

          <Card className="p-4 space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">Nama Lengkap *</Label>
              <Input
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                placeholder="Masukkan nama lengkap"
                className={getFieldError('name') ? 'border-red-500' : ''}
                required
              />
              {getFieldError('name') && (
                <p className="text-sm text-red-500">{getFieldError('name')}</p>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="email">Email *</Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={handleChange}
                  placeholder="<EMAIL>"
                  className={getFieldError('email') ? 'border-red-500' : ''}
                  required
                />
                {getFieldError('email') && (
                  <p className="text-sm text-red-500">
                    {getFieldError('email')}
                  </p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="phone">Nomor Telepon *</Label>
                <Input
                  id="phone"
                  name="phone"
                  value={formData.phone}
                  onChange={handleChange}
                  placeholder="08xxxxxxxxxx"
                  className={getFieldError('phone') ? 'border-red-500' : ''}
                  required
                />
                {getFieldError('phone') && (
                  <p className="text-sm text-red-500">
                    {getFieldError('phone')}
                  </p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="password">Password *</Label>
                <Input
                  id="password"
                  name="password"
                  type="password"
                  value={formData.password}
                  onChange={handleChange}
                  placeholder="Minimal 8 karakter"
                  className={getFieldError('password') ? 'border-red-500' : ''}
                  required
                />
                {getFieldError('password') && (
                  <p className="text-sm text-red-500">
                    {getFieldError('password')}
                  </p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="password2">Konfirmasi Password *</Label>
                <Input
                  id="password2"
                  name="password2"
                  type="password"
                  value={formData.password2}
                  onChange={handleChange}
                  placeholder="Konfirmasi password"
                  className={getFieldError('password2') ? 'border-red-500' : ''}
                  required
                />
                {getFieldError('password2') && (
                  <p className="text-sm text-red-500">
                    {getFieldError('password2')}
                  </p>
                )}
              </div>
            </div>
          </Card>
        </form>
      </main>
    </div>
  );
}
