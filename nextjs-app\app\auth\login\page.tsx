'use client';

import Image from 'next/image';
import Link from 'next/link';
import { EyeIcon, EyeOffIcon } from 'lucide-react';
import { useEffect, useState, useMemo } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { toast } from 'sonner';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { LoginFormValues, loginSchema } from '@/lib/form-schema';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@/components/ui/form';
import { useAuth } from '@/lib/auth-context';

export default function LoginPage() {
  const [showPassword, setShowPassword] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [hasRedirected, setHasRedirected] = useState(false);
  const router = useRouter();
  const searchParams = useSearchParams();
  const callbackUrl = useMemo(
    () => searchParams.get('callbackUrl') || '/',
    [searchParams]
  );
  const {
    login,
    isAuthenticated,
    activeOutlet,
    isLoading: authLoading,
  } = useAuth();

  const form = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  const isLoading = isSubmitting;

  useEffect(() => {
    if (isAuthenticated && !authLoading && !hasRedirected) {
      console.log('🚀 Login redirect logic:', {
        isAuthenticated,
        authLoading,
        activeOutlet,
        callbackUrl,
        hasRedirected,
      });

      setHasRedirected(true);

      // Jika user sudah punya active outlet dan ada callbackUrl khusus, langsung ke callbackUrl
      if (
        activeOutlet &&
        callbackUrl !== '/' &&
        callbackUrl !== '/auth/select-outlet'
      ) {
        console.log('🔄 Redirecting to callbackUrl:', callbackUrl);
        router.push(callbackUrl);
      }
      // Jika user sudah punya active outlet tapi callbackUrl default, ke dashboard
      else if (activeOutlet) {
        console.log('🔄 Redirecting to dashboard');
        router.push('/dashboard');
      }
      // Jika belum ada active outlet, ke select outlet
      else {
        console.log('🔄 Redirecting to select outlet');
        router.push('/auth/select-outlet');
      }
    }
  }, [
    isAuthenticated,
    authLoading,
    activeOutlet,
    router,
    callbackUrl,
    hasRedirected,
  ]);

  if (authLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <p>Loading...</p>
      </div>
    );
  }

  // if (isAuthenticated) {
  //   return (
  //     <div className="flex items-center justify-center min-h-screen">
  //       <p>Redirecting...</p>
  //     </div>
  //   );
  // }

  const onSubmit = async (data: LoginFormValues) => {
    try {
      setIsSubmitting(true);
      await login(data.email, data.password);
      router.push('/auth/select-outlet');
      toast.success('Login berhasil!');
    } catch (error: any) {
      toast.error(error.message || 'Login gagal');
    } finally {
      setIsSubmitting(false);
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <div className="min-h-screen flex">
      {/* Left side - Image */}
      <div className="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-blue-600 to-purple-700 items-center justify-center">
        <div className="text-center text-white">
          <Image
            src="/images/laundry-hero.svg"
            alt="Laundry illustration"
            width={400}
            height={300}
            className="mx-auto mb-8"
          />
          <h2 className="text-3xl font-bold mb-4">
            Selamat Datang di Super Laundry
          </h2>
          <p className="text-xl opacity-90">
            Kelola usaha laundry Anda dengan mudah dan efisien
          </p>
        </div>
      </div>

      {/* Right side - Login Form */}
      <div className="w-full lg:w-1/2 flex items-center justify-center p-8">
        <div className="max-w-md w-full space-y-8">
          <div className="text-center">
            <Image
              src="/images/logo.png"
              alt="Super Laundry Logo"
              width={80}
              height={80}
              className="mx-auto mb-4"
            />
            <h2 className="text-3xl font-bold text-gray-900">Masuk</h2>
            <p className="mt-2 text-gray-600">
              Masuk ke akun Anda untuk melanjutkan
            </p>
          </div>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Input
                        {...field}
                        type="email"
                        placeholder="Email"
                        className="h-12"
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <div className="relative">
                        <Input
                          {...field}
                          type={showPassword ? 'text' : 'password'}
                          placeholder="Password"
                          className="h-12 pr-10"
                          disabled={isLoading}
                        />
                        <button
                          type="button"
                          onClick={togglePasswordVisibility}
                          className="absolute inset-y-0 right-0 pr-3 flex items-center"
                          disabled={isLoading}
                        >
                          {showPassword ? (
                            <EyeOffIcon className="h-5 w-5 text-gray-400" />
                          ) : (
                            <EyeIcon className="h-5 w-5 text-gray-400" />
                          )}
                        </button>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="flex items-center justify-between">
                <Link
                  href="/auth/forgot-password"
                  className="text-sm text-blue-600 hover:text-blue-500"
                >
                  Lupa password?
                </Link>
              </div>

              <Button
                type="submit"
                className="w-full h-12 bg-blue-600 hover:bg-blue-700"
                disabled={isLoading}
              >
                {isLoading ? 'Memproses...' : 'Masuk'}
              </Button>
            </form>
          </Form>

          <div className="text-center">
            <p className="text-gray-600">
              Belum punya akun?{' '}
              <Link
                href="/auth/register"
                className="text-blue-600 hover:text-blue-500 font-medium"
              >
                Daftar sekarang
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
