"use client"

import { useState } from "react"
import Link from "next/link"
import { ArrowLeft, Clock, CheckCircle, XCircle, AlertTriangle } from "lucide-react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"

// Mock data for attendance statistics
const mockAttendanceStats = {
  thisMonth: {
    present: 18,
    late: 3,
    absent: 2,
    total: 23,
    averageCheckIn: "08:05",
    averageCheckOut: "17:15",
    averageWorkHours: "9:10",
    monthName: "Oktober 2023",
  },
  lastMonth: {
    present: 20,
    late: 2,
    absent: 1,
    total: 23,
    averageCheckIn: "08:03",
    averageCheckOut: "17:10",
    averageWorkHours: "9:07",
    monthName: "September 2023",
  },
  twoMonthsAgo: {
    present: 19,
    late: 4,
    absent: 0,
    total: 23,
    averageCheckIn: "08:07",
    averageCheckOut: "17:20",
    averageWorkHours: "9:13",
    monthName: "Agustus 2023",
  },
}

// Mock data for monthly attendance
const mockMonthlyAttendance = [
  { month: "Januari", present: 20, late: 2, absent: 1 },
  { month: "Februari", present: 18, late: 3, absent: 1 },
  { month: "Maret", present: 21, late: 1, absent: 1 },
  { month: "April", present: 19, late: 2, absent: 2 },
  { month: "Mei", present: 20, late: 3, absent: 0 },
  { month: "Juni", present: 18, late: 4, absent: 0 },
  { month: "Juli", present: 21, late: 2, absent: 0 },
  { month: "Agustus", present: 19, late: 4, absent: 0 },
  { month: "September", present: 20, late: 2, absent: 1 },
  { month: "Oktober", present: 18, late: 3, absent: 2 },
]

export default function AttendanceStatisticsPage() {
  const [selectedPeriod, setSelectedPeriod] = useState("thisMonth")

  // Get stats based on selected period
  const stats =
    selectedPeriod === "thisMonth"
      ? mockAttendanceStats.thisMonth
      : selectedPeriod === "lastMonth"
        ? mockAttendanceStats.lastMonth
        : mockAttendanceStats.twoMonthsAgo

  // Calculate percentages
  const presentPercentage = Math.round((stats.present / stats.total) * 100)
  const latePercentage = Math.round((stats.late / stats.total) * 100)
  const absentPercentage = Math.round((stats.absent / stats.total) * 100)

  return (
    <div className="flex flex-col min-h-screen bg-slate-50">
      <header className="p-4 flex justify-between items-center bg-white sticky top-0 z-10 shadow-sm">
        <div className="flex items-center">
          <Link href="/absensi" className="mr-3">
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <h1 className="text-lg font-semibold">Statistik Kehadiran</h1>
        </div>
        <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Pilih Periode" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="thisMonth">{mockAttendanceStats.thisMonth.monthName}</SelectItem>
            <SelectItem value="lastMonth">{mockAttendanceStats.lastMonth.monthName}</SelectItem>
            <SelectItem value="twoMonthsAgo">{mockAttendanceStats.twoMonthsAgo.monthName}</SelectItem>
          </SelectContent>
        </Select>
      </header>

      <main className="flex-1 p-4 pb-20">
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="text-lg">Ringkasan Kehadiran</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-3 gap-4 mb-6">
              <div className="text-center">
                <div className="bg-green-100 rounded-full p-3 mx-auto w-12 h-12 flex items-center justify-center mb-2">
                  <CheckCircle className="h-6 w-6 text-green-600" />
                </div>
                <p className="text-2xl font-bold">{stats.present}</p>
                <p className="text-sm text-gray-500">Hadir</p>
              </div>
              <div className="text-center">
                <div className="bg-yellow-100 rounded-full p-3 mx-auto w-12 h-12 flex items-center justify-center mb-2">
                  <AlertTriangle className="h-6 w-6 text-yellow-600" />
                </div>
                <p className="text-2xl font-bold">{stats.late}</p>
                <p className="text-sm text-gray-500">Terlambat</p>
              </div>
              <div className="text-center">
                <div className="bg-red-100 rounded-full p-3 mx-auto w-12 h-12 flex items-center justify-center mb-2">
                  <XCircle className="h-6 w-6 text-red-600" />
                </div>
                <p className="text-2xl font-bold">{stats.absent}</p>
                <p className="text-sm text-gray-500">Tidak Hadir</p>
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <div className="flex justify-between mb-1">
                  <p className="text-sm font-medium">Hadir</p>
                  <p className="text-sm font-medium">{presentPercentage}%</p>
                </div>
                <Progress value={presentPercentage} className="h-2 bg-gray-200" indicatorClassName="bg-green-500" />
              </div>
              <div>
                <div className="flex justify-between mb-1">
                  <p className="text-sm font-medium">Terlambat</p>
                  <p className="text-sm font-medium">{latePercentage}%</p>
                </div>
                <Progress value={latePercentage} className="h-2 bg-gray-200" indicatorClassName="bg-yellow-500" />
              </div>
              <div>
                <div className="flex justify-between mb-1">
                  <p className="text-sm font-medium">Tidak Hadir</p>
                  <p className="text-sm font-medium">{absentPercentage}%</p>
                </div>
                <Progress value={absentPercentage} className="h-2 bg-gray-200" indicatorClassName="bg-red-500" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="text-lg">Rata-rata Waktu</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-3 gap-4">
              <div className="text-center">
                <div className="bg-blue-100 rounded-full p-3 mx-auto w-12 h-12 flex items-center justify-center mb-2">
                  <Clock className="h-6 w-6 text-blue-600" />
                </div>
                <p className="text-xl font-bold">{stats.averageCheckIn}</p>
                <p className="text-sm text-gray-500">Check-In</p>
              </div>
              <div className="text-center">
                <div className="bg-purple-100 rounded-full p-3 mx-auto w-12 h-12 flex items-center justify-center mb-2">
                  <Clock className="h-6 w-6 text-purple-600" />
                </div>
                <p className="text-xl font-bold">{stats.averageCheckOut}</p>
                <p className="text-sm text-gray-500">Check-Out</p>
              </div>
              <div className="text-center">
                <div className="bg-teal-100 rounded-full p-3 mx-auto w-12 h-12 flex items-center justify-center mb-2">
                  <Clock className="h-6 w-6 text-teal-600" />
                </div>
                <p className="text-xl font-bold">{stats.averageWorkHours}</p>
                <p className="text-sm text-gray-500">Jam Kerja</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Kehadiran Tahunan</CardTitle>
          </CardHeader>
          <CardContent>
            <Accordion type="single" collapsible className="w-full">
              {mockMonthlyAttendance.map((month, index) => (
                <AccordionItem key={index} value={`item-${index}`}>
                  <AccordionTrigger className="hover:no-underline">
                    <div className="flex justify-between items-center w-full pr-4">
                      <span>{month.month}</span>
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-green-600">{month.present} Hadir</span>
                        <span className="text-sm text-yellow-600">{month.late} Terlambat</span>
                        <span className="text-sm text-red-600">{month.absent} Absen</span>
                      </div>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent>
                    <div className="space-y-4 pt-2">
                      <div>
                        <div className="flex justify-between mb-1">
                          <p className="text-sm font-medium">Hadir</p>
                          <p className="text-sm font-medium">
                            {Math.round((month.present / (month.present + month.late + month.absent)) * 100)}%
                          </p>
                        </div>
                        <Progress
                          value={Math.round((month.present / (month.present + month.late + month.absent)) * 100)}
                          className="h-2 bg-gray-200"
                          indicatorClassName="bg-green-500"
                        />
                      </div>
                      <div>
                        <div className="flex justify-between mb-1">
                          <p className="text-sm font-medium">Terlambat</p>
                          <p className="text-sm font-medium">
                            {Math.round((month.late / (month.present + month.late + month.absent)) * 100)}%
                          </p>
                        </div>
                        <Progress
                          value={Math.round((month.late / (month.present + month.late + month.absent)) * 100)}
                          className="h-2 bg-gray-200"
                          indicatorClassName="bg-yellow-500"
                        />
                      </div>
                      <div>
                        <div className="flex justify-between mb-1">
                          <p className="text-sm font-medium">Tidak Hadir</p>
                          <p className="text-sm font-medium">
                            {Math.round((month.absent / (month.present + month.late + month.absent)) * 100)}%
                          </p>
                        </div>
                        <Progress
                          value={Math.round((month.absent / (month.present + month.late + month.absent)) * 100)}
                          className="h-2 bg-gray-200"
                          indicatorClassName="bg-red-500"
                        />
                      </div>
                    </div>
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </CardContent>
        </Card>
      </main>
    </div>
  )
}
