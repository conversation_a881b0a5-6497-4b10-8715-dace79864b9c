import { AttendanceStatus } from '@prisma/client';
import { faker } from '@faker-js/faker';
import moment from 'moment';
import prisma from '../../src/client';

// Sample attendance data
export const attendanceOne = {
  id: 1,
  userId: 1,
  outletId: 1,
  scheduleId: null,
  checkIn: new Date('2024-01-15T08:00:00Z'),
  checkOut: null,
  checkInPhoto: null,
  checkOutPhoto: null,
  location: '-6.2088,106.8456', // Jakarta coordinates
  notes: null,
  status: AttendanceStatus.PRESENT,
  isLate: false,
  workingHours: null,
  overtimeHours: 0,
  lateByMinutes: 0,
  date: new Date('2024-01-15')
};

export const attendanceTwo = {
  id: 2,
  userId: 2,
  outletId: 1,
  scheduleId: null,
  checkIn: new Date('2024-01-15T08:30:00Z'),
  checkOut: null,
  checkInPhoto: 'uploads/checkin_user2_20240115.jpg',
  checkOutPhoto: null,
  location: '-6.2088,106.8456',
  notes: 'Terlambat karena macet',
  status: AttendanceStatus.LATE,
  isLate: true,
  workingHours: null,
  overtimeHours: 0,
  lateByMinutes: 30,
  date: new Date('2024-01-15')
};

export const attendanceThree = {
  id: 3,
  userId: 3,
  outletId: 1,
  scheduleId: null,
  checkIn: null,
  checkOut: null,
  checkInPhoto: null,
  checkOutPhoto: null,
  location: null,
  notes: 'Sakit',
  status: AttendanceStatus.SICK,
  isLate: false,
  workingHours: null,
  overtimeHours: 0,
  lateByMinutes: 0,
  date: new Date('2024-01-15')
};

// Sample work schedule data
export const scheduleOne = {
  id: 1,
  userId: 1,
  outletId: 1,
  name: 'Shift Pagi',
  dayOfWeek: 1, // Senin
  startTime: '08:00',
  endTime: '16:00',
  breakStartTime: '12:00',
  breakEndTime: '13:00',
  isActive: true,
  effectiveFrom: new Date('2024-01-01'),
  effectiveTo: null
};

export const scheduleTwo = {
  id: 2,
  userId: 2,
  outletId: 1,
  name: 'Shift Sore',
  dayOfWeek: 1, // Senin
  startTime: '14:00',
  endTime: '22:00',
  breakStartTime: '18:00',
  breakEndTime: '19:00',
  isActive: true,
  effectiveFrom: new Date('2024-01-01'),
  effectiveTo: null
};

export const scheduleThree = {
  id: 3,
  userId: 1,
  outletId: 1,
  name: 'Shift Pagi',
  dayOfWeek: 2, // Selasa
  startTime: '08:00',
  endTime: '16:00',
  breakStartTime: null,
  breakEndTime: null,
  isActive: true,
  effectiveFrom: new Date('2024-01-01'),
  effectiveTo: null
};

// Sample schedule template data
export const templateOne = {
  id: 1,
  outletId: 1,
  name: 'Shift Pagi Standard',
  description: 'Shift pagi untuk karyawan full-time',
  startTime: '08:00',
  endTime: '16:00',
  breakStartTime: '12:00',
  breakEndTime: '13:00',
  workingDays: [1, 2, 3, 4, 5], // Senin - Jumat
  isActive: true
};

export const templateTwo = {
  id: 2,
  outletId: 1,
  name: 'Shift Sore Standard',
  description: 'Shift sore untuk karyawan full-time',
  startTime: '14:00',
  endTime: '22:00',
  breakStartTime: '18:00',
  breakEndTime: '19:00',
  workingDays: [1, 2, 3, 4, 5], // Senin - Jumat
  isActive: true
};

// Sample attendance settings data
export const settingsOne = {
  id: 1,
  outletId: 1,
  requirePin: false,
  requirePhoto: false,
  allowLateCheckIn: true,
  defaultLateThreshold: 15,
  allowEarlyCheckOut: true,
  autoCheckOut: false,
  autoCheckOutTime: '23:59',
  geoFencing: false,
  maxDistance: 100,
  overtimeThreshold: 8.0
};

export const settingsTwo = {
  id: 2,
  outletId: 2,
  requirePin: true,
  requirePhoto: true,
  allowLateCheckIn: false,
  defaultLateThreshold: 10,
  allowEarlyCheckOut: false,
  autoCheckOut: true,
  autoCheckOutTime: '17:00',
  geoFencing: true,
  maxDistance: 50,
  overtimeThreshold: 8.5
};

// Helper functions to insert test data
export const insertAttendances = async (attendances: any[]) => {
  return Promise.all(
    attendances.map((attendance) =>
      prisma.attendance.create({
        data: attendance
      })
    )
  );
};

export const insertWorkSchedules = async (schedules: any[]) => {
  return Promise.all(
    schedules.map((schedule) =>
      prisma.workSchedule.create({
        data: schedule
      })
    )
  );
};

export const insertScheduleTemplates = async (templates: any[]) => {
  return Promise.all(
    templates.map((template) =>
      prisma.scheduleTemplate.create({
        data: template
      })
    )
  );
};

export const insertAttendanceSettings = async (settings: any[]) => {
  return Promise.all(
    settings.map((setting) =>
      prisma.attendanceSettings.create({
        data: setting
      })
    )
  );
};

// Generate random attendance data for testing
export const generateAttendanceData = (overrides: any = {}) => {
  return {
    userId: faker.datatype.number({ min: 1, max: 10 }),
    outletId: faker.datatype.number({ min: 1, max: 3 }),
    checkIn: faker.date.recent(7),
    status: faker.helpers.arrayElement([
      AttendanceStatus.PRESENT,
      AttendanceStatus.LATE,
      AttendanceStatus.ABSENT
    ]),
    date: faker.date.recent(30),
    ...overrides
  };
};

// Generate random schedule data for testing
export const generateScheduleData = (overrides: any = {}) => {
  const startHour = faker.datatype.number({ min: 6, max: 10 });
  const endHour = startHour + faker.datatype.number({ min: 6, max: 10 });

  return {
    userId: faker.datatype.number({ min: 1, max: 10 }),
    outletId: faker.datatype.number({ min: 1, max: 3 }),
    name: faker.helpers.arrayElement(['Shift Pagi', 'Shift Sore', 'Shift Malam']),
    dayOfWeek: faker.datatype.number({ min: 0, max: 6 }),
    startTime: `${startHour.toString().padStart(2, '0')}:00`,
    endTime: `${endHour.toString().padStart(2, '0')}:00`,
    isActive: true,
    effectiveFrom: faker.date.past(1),
    ...overrides
  };
};

// Test data for check-in/out requests
export const checkInRequest = {
  pin: '1234',
  location: '-6.2088,106.8456',
  notes: 'Check in dari mobile app'
};

export const checkInRequestWithPhoto = {
  pin: '1234',
  location: '-6.2088,106.8456',
  notes: 'Check in dengan foto'
  // photo akan di-mock sebagai file upload
};

export const checkOutRequest = {
  notes: 'Selesai kerja'
};

export const checkOutRequestWithPhoto = {
  notes: 'Check out dengan foto'
  // photo akan di-mock sebagai file upload
};
