import express from 'express';
import auth from '../../middlewares/auth';
import validate from '../../middlewares/validate';
import customerValidation from '../../validations/customer.validation';
import customerController from '../../controllers/customer.controller';

const router = express.Router();

router
  .route('/')
  .post(
    auth('manageCustomers'),
    validate(customerValidation.createCustomer),
    customerController.createCustomer
  )
  .get(
    auth('getCustomers'),
    validate(customerValidation.getCustomers),
    customerController.getCustomers
  );

router
  .route('/:customerId')
  .get(
    auth('getCustomers'),
    validate(customerValidation.getCustomer),
    customerController.getCustomer
  )
  .patch(
    auth('manageCustomers'),
    validate(customerValidation.updateCustomer),
    customerController.updateCustomer
  )
  .delete(
    auth('manageCustomers'),
    validate(customerValidation.deleteCustomer),
    customerController.deleteCustomer
  );

router
  .route('/:customerId/notes')
  .post(
    auth('manageCustomers'),
    validate(customerValidation.createCustomerNote),
    customerController.createCustomerNote
  )
  .get(
    auth('getCustomers'),
    validate(customerValidation.getCustomerNotes),
    customerController.getCustomerNotes
  );

export default router;

/**
 * @swagger
 * tags:
 *   name: Customers
 *   description: Customer management and retrieval
 */

/**
 * @swagger
 * /customers:
 *   post:
 *     summary: Create a customer
 *     description: Create a new customer. At least phone or email is required.
 *     tags: [Customers]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               name:
 *                 type: string
 *                 minLength: 1
 *                 maxLength: 100
 *                 description: Customer name
 *               phone:
 *                 type: string
 *                 pattern: '^08[0-9]{8,13}$'
 *                 description: Indonesian phone number format (08xxxxxxxxx)
 *               email:
 *                 type: string
 *                 format: email
 *                 description: Customer email address
 *               address:
 *                 type: string
 *                 maxLength: 500
 *                 description: Customer address
 *               mapLink:
 *                 type: string
 *                 format: uri
 *                 description: Google Maps or other map link
 *               latitude:
 *                 type: number
 *                 minimum: -90
 *                 maximum: 90
 *                 description: Latitude coordinate
 *               longitude:
 *                 type: number
 *                 minimum: -180
 *                 maximum: 180
 *                 description: Longitude coordinate
 *               provinceId:
 *                 type: integer
 *                 minimum: 1
 *                 description: Province ID
 *               cityId:
 *                 type: integer
 *                 minimum: 1
 *                 description: City ID
 *               status:
 *                 type: string
 *                 enum: [ACTIVE, INACTIVE, NEW]
 *                 description: Customer status
 *               customerType:
 *                 type: string
 *                 enum: [INDIVIDUAL, CORPORATE]
 *                 description: Customer type
 *               source:
 *                 type: string
 *                 maxLength: 100
 *                 description: How customer found the service
 *               labels:
 *                 type: array
 *                 items:
 *                   type: string
 *                   maxLength: 50
 *                 description: Customer labels/tags
 *               photos:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: uri
 *                 description: Customer photo URLs
 *               notes:
 *                 type: string
 *                 maxLength: 1000
 *                 description: Additional notes about customer
 *               financialData:
 *                 type: object
 *                 properties:
 *                   totalSpent:
 *                     type: number
 *                     minimum: 0
 *                   loyaltyPoints:
 *                     type: integer
 *                     minimum: 0
 *                   deposit:
 *                     type: number
 *                     minimum: 0
 *                   debt:
 *                     type: number
 *                     minimum: 0
 *                   cashback:
 *                     type: number
 *                     minimum: 0
 *                   preferredPaymentMethod:
 *                     type: string
 *                     maxLength: 50
 *                   creditLimit:
 *                     type: number
 *                     minimum: 0
 *             example:
 *               name: John Doe
 *               phone: "081234567890"
 *               email: <EMAIL>
 *               address: "Jl. Sudirman No. 123, Jakarta"
 *               status: ACTIVE
 *               customerType: INDIVIDUAL
 *               source: "Social Media"
 *               labels: ["VIP", "Regular"]
 *     responses:
 *       "201":
 *         description: Created
 *         content:
 *           application/json:
 *             schema:
 *                $ref: '#/components/schemas/Customer'
 *       "400":
 *         $ref: '#/components/responses/BadRequest'
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "403":
 *         $ref: '#/components/responses/Forbidden'
 *
 *   get:
 *     summary: Get all customers
 *     description: Retrieve all customers with optional filtering and pagination.
 *     tags: [Customers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search by customer name, phone, or email
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [ACTIVE, INACTIVE, NEW]
 *         description: Filter by customer status
 *       - in: query
 *         name: customerType
 *         schema:
 *           type: string
 *           enum: [INDIVIDUAL, CORPORATE]
 *         description: Filter by customer type
 *       - in: query
 *         name: source
 *         schema:
 *           type: string
 *         description: Filter by customer source
 *       - in: query
 *         name: labels
 *         schema:
 *           type: string
 *         description: Filter by labels (comma-separated)
 *       - in: query
 *         name: provinceId
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Filter by province ID
 *       - in: query
 *         name: cityId
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Filter by city ID
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *         description: sort by query in the form of field:desc/asc (ex. name:asc)
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         default: 10
 *         description: Maximum number of customers
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number
 *     responses:
 *       "200":
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 results:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Customer'
 *                 page:
 *                   type: integer
 *                   example: 1
 *                 limit:
 *                   type: integer
 *                   example: 10
 *                 totalPages:
 *                   type: integer
 *                   example: 1
 *                 totalResults:
 *                   type: integer
 *                   example: 1
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "403":
 *         $ref: '#/components/responses/Forbidden'
 */

/**
 * @swagger
 * /customers/{customerId}:
 *   get:
 *     summary: Get a customer
 *     description: Retrieve a specific customer by ID.
 *     tags: [Customers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: customerId
 *         required: true
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Customer ID
 *     responses:
 *       "200":
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *                $ref: '#/components/schemas/Customer'
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "403":
 *         $ref: '#/components/responses/Forbidden'
 *       "404":
 *         $ref: '#/components/responses/NotFound'
 *
 *   patch:
 *     summary: Update a customer
 *     description: Update customer information. At least one field is required.
 *     tags: [Customers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: customerId
 *         required: true
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Customer ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             minProperties: 1
 *             properties:
 *               name:
 *                 type: string
 *                 minLength: 1
 *                 maxLength: 100
 *                 description: Customer name
 *               phone:
 *                 type: string
 *                 pattern: '^08[0-9]{8,13}$'
 *                 description: Indonesian phone number format (08xxxxxxxxx)
 *               email:
 *                 type: string
 *                 format: email
 *                 description: Customer email address
 *               address:
 *                 type: string
 *                 maxLength: 500
 *                 description: Customer address
 *               mapLink:
 *                 type: string
 *                 format: uri
 *                 description: Google Maps or other map link
 *               latitude:
 *                 type: number
 *                 minimum: -90
 *                 maximum: 90
 *                 description: Latitude coordinate
 *               longitude:
 *                 type: number
 *                 minimum: -180
 *                 maximum: 180
 *                 description: Longitude coordinate
 *               provinceId:
 *                 type: integer
 *                 minimum: 1
 *                 description: Province ID
 *               cityId:
 *                 type: integer
 *                 minimum: 1
 *                 description: City ID
 *               status:
 *                 type: string
 *                 enum: [ACTIVE, INACTIVE, NEW]
 *                 description: Customer status
 *               customerType:
 *                 type: string
 *                 enum: [INDIVIDUAL, CORPORATE]
 *                 description: Customer type
 *               source:
 *                 type: string
 *                 maxLength: 100
 *                 description: How customer found the service
 *               labels:
 *                 type: array
 *                 items:
 *                   type: string
 *                   maxLength: 50
 *                 description: Customer labels/tags
 *               photos:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: uri
 *                 description: Customer photo URLs
 *               notes:
 *                 type: string
 *                 maxLength: 1000
 *                 description: Additional notes about customer
 *               financialData:
 *                 type: object
 *                 properties:
 *                   totalSpent:
 *                     type: number
 *                     minimum: 0
 *                   loyaltyPoints:
 *                     type: integer
 *                     minimum: 0
 *                   deposit:
 *                     type: number
 *                     minimum: 0
 *                   debt:
 *                     type: number
 *                     minimum: 0
 *                   cashback:
 *                     type: number
 *                     minimum: 0
 *                   preferredPaymentMethod:
 *                     type: string
 *                     maxLength: 50
 *                   creditLimit:
 *                     type: number
 *                     minimum: 0
 *             example:
 *               name: John Doe Updated
 *               phone: "081234567891"
 *               status: ACTIVE
 *     responses:
 *       "200":
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *                $ref: '#/components/schemas/Customer'
 *       "400":
 *         $ref: '#/components/responses/BadRequest'
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "403":
 *         $ref: '#/components/responses/Forbidden'
 *       "404":
 *         $ref: '#/components/responses/NotFound'
 *
 *   delete:
 *     summary: Delete a customer
 *     description: Delete a customer and all related data.
 *     tags: [Customers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: customerId
 *         required: true
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Customer ID
 *     responses:
 *       "200":
 *         description: No content
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "403":
 *         $ref: '#/components/responses/Forbidden'
 *       "404":
 *         $ref: '#/components/responses/NotFound'
 */

/**
 * @swagger
 * /customers/{customerId}/notes:
 *   post:
 *     summary: Create a customer note
 *     description: Add a new note to a customer.
 *     tags: [Customers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: customerId
 *         required: true
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Customer ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - text
 *               - author
 *             properties:
 *               text:
 *                 type: string
 *                 minLength: 1
 *                 maxLength: 1000
 *                 description: Note content
 *               author:
 *                 type: string
 *                 minLength: 1
 *                 maxLength: 100
 *                 description: Note author name
 *             example:
 *               text: "Customer prefers morning pickup"
 *               author: "John Admin"
 *     responses:
 *       "201":
 *         description: Created
 *         content:
 *           application/json:
 *             schema:
 *                $ref: '#/components/schemas/CustomerNote'
 *       "400":
 *         $ref: '#/components/responses/BadRequest'
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "403":
 *         $ref: '#/components/responses/Forbidden'
 *       "404":
 *         $ref: '#/components/responses/NotFound'
 *
 *   get:
 *     summary: Get customer notes
 *     description: Retrieve all notes for a specific customer.
 *     tags: [Customers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: customerId
 *         required: true
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Customer ID
 *     responses:
 *       "200":
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/CustomerNote'
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "403":
 *         $ref: '#/components/responses/Forbidden'
 *       "404":
 *         $ref: '#/components/responses/NotFound'
 */
