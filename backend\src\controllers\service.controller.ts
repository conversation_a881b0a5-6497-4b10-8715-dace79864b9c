import httpStatus from 'http-status';
import pick from '../utils/pick';
import ApiError from '../utils/ApiError';
import catchAsync from '../utils/catchAsync';
import serviceService from '../services/service.service';
import { User } from '@prisma/client';

const createService = catchAsync(async (req, res) => {
  if (!req.user) {
    throw new ApiError(httpStatus.UNAUTHORIZED, 'User not authenticated');
  }

  const service = await serviceService.createService(req.body, req.user as User);
  res.status(httpStatus.CREATED).send(service);
});

const getServices = catchAsync(async (req, res) => {
  if (!req.user) {
    throw new ApiError(httpStatus.UNAUTHORIZED, 'User not authenticated');
  }

  const filter = pick(req.query, ['outletId', 'search', 'isActive', 'unit']);
  const options = pick(req.query, ['sortBy', 'limit', 'page']);

  const result = await serviceService.queryServices(filter, options, req.user as User);
  res.send(result);
});

const getService = catchAsync(async (req, res) => {
  if (!req.user) {
    throw new ApiError(httpStatus.UNAUTHORIZED, 'User not authenticated');
  }

  const serviceId = parseInt(req.params.serviceId);
  const outletId = parseInt(req.query.outletId as string);

  const service = await serviceService.getServiceById(serviceId, req.user as User, outletId);
  if (!service) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Service not found');
  }
  res.send(service);
});

const updateService = catchAsync(async (req, res) => {
  if (!req.user) {
    throw new ApiError(httpStatus.UNAUTHORIZED, 'User not authenticated');
  }

  const serviceId = parseInt(req.params.serviceId);
  const service = await serviceService.updateServiceById(serviceId, req.body, req.user as User);
  res.send(service);
});

const deleteService = catchAsync(async (req, res) => {
  if (!req.user) {
    throw new ApiError(httpStatus.UNAUTHORIZED, 'User not authenticated');
  }

  const serviceId = parseInt(req.params.serviceId);
  const outletId = parseInt(req.query.outletId as string);

  await serviceService.deleteServiceById(serviceId, req.user as User, outletId);
  res.status(httpStatus.NO_CONTENT).send();
});

export default {
  createService,
  getServices,
  getService,
  updateService,
  deleteService
};
