import express from 'express';
import auth from '../../middlewares/auth';
import validate from '../../middlewares/validate';
import requireVerification from '../../middlewares/verification';
import { outletValidation } from '../../validations';
import { outletController } from '../../controllers';

const router = express.Router();

router
  .route('/')
  .post(
    auth('manageOutlets'),
    requireVerification(),
    validate(outletValidation.createOutlet),
    outletController.createOutlet
  )
  .get(
    auth('getOutlets'),
    requireVerification(),
    validate(outletValidation.getOutlets),
    outletController.getOutlets
  );

router
  .route('/:outletId')
  .get(
    auth('getOutlets'),
    requireVerification(),
    validate(outletValidation.getOutlet),
    outletController.getOutlet
  )
  .patch(
    auth('manageOutlets'),
    requireVerification(),
    validate(outletValidation.updateOutlet),
    outletController.updateOutlet
  )
  .delete(
    auth('manageOutlets'),
    requireVerification(),
    validate(outletValidation.deleteOutlet),
    outletController.deleteOutlet
  );

router
  .route('/:outletId/services')
  .get(
    auth('getOutlets'),
    validate(outletValidation.getOutlet),
    outletController.getOutletServices
  );

router
  .route('/:outletId/copy-services')
  .post(
    auth('manageOutlets'),
    validate(outletValidation.copyServices),
    outletController.copyServicesFromOutlet
  );

router
  .route('/:outletId/stats')
  .get(auth('getOutlets'), validate(outletValidation.getOutlet), outletController.getOutletStats);

export default router;

/**
 * @swagger
 * tags:
 *   name: Outlets
 *   description: Outlet management and retrieval with location integration
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     Outlet:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: Outlet ID
 *         name:
 *           type: string
 *           description: Outlet name
 *         address:
 *           type: string
 *           description: Outlet address
 *         province:
 *           type: string
 *           description: Province name (string for backward compatibility)
 *         city:
 *           type: string
 *           description: City name (string for backward compatibility)
 *         provinceId:
 *           type: integer
 *           nullable: true
 *           description: Province ID (foreign key)
 *         cityId:
 *           type: integer
 *           nullable: true
 *           description: City ID (foreign key)
 *         timezone:
 *           type: string
 *           default: "Asia/Jakarta"
 *           description: Outlet timezone
 *         phone:
 *           type: string
 *           description: Outlet phone number
 *         latitude:
 *           type: number
 *           format: float
 *           nullable: true
 *           description: Outlet latitude
 *         longitude:
 *           type: number
 *           format: float
 *           nullable: true
 *           description: Outlet longitude

 *         isActive:
 *           type: boolean
 *           default: true
 *           description: Outlet status
 *         isDeleted:
 *           type: boolean
 *           default: false
 *           description: Soft delete status
 *         ownerId:
 *           type: integer
 *           description: Owner user ID
 *         copiedFromId:
 *           type: integer
 *           nullable: true
 *           description: Source outlet ID if services were copied
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 *         deletedAt:
 *           type: string
 *           format: date-time
 *           nullable: true
 *         owner:
 *           type: object
 *           properties:
 *             id:
 *               type: integer
 *             name:
 *               type: string
 *             email:
 *               type: string
 *         provinceRef:
 *           $ref: '#/components/schemas/Province'
 *           nullable: true
 *         cityRef:
 *           $ref: '#/components/schemas/City'
 *           nullable: true
 *       example:
 *         id: 1
 *         name: "Main Outlet"
 *         address: "Jl. Sudirman No. 123"
 *         province: "DKI Jakarta"
 *         city: "Jakarta Pusat"
 *         provinceId: 1
 *         cityId: 1
 *         timezone: "Asia/Jakarta"
 *         phone: "081234567890"
 *         latitude: -6.2088
 *         longitude: 106.8456
 *         isActive: true
 *         ownerId: 1
 *         provinceRef:
 *           id: 1
 *           name: "DKI Jakarta"
 *           code: "31"
 *         cityRef:
 *           id: 1
 *           name: "Jakarta Pusat"
 *           code: "3171"
 *           provinceId: 1
 */

/**
 * @swagger
 * /outlets:
 *   post:
 *     summary: Create a new outlet
 *     description: Create a new outlet with location integration. Supports both string province/city (backward compatibility) and provinceId/cityId (new feature).
 *     tags: [Outlets]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - address
 *               - province
 *               - city
 *               - phone
 *             properties:
 *               name:
 *                 type: string
 *                 minLength: 3
 *                 maxLength: 255
 *                 description: Outlet name
 *               address:
 *                 type: string
 *                 minLength: 10
 *                 description: Outlet address
 *               province:
 *                 type: string
 *                 minLength: 2
 *                 maxLength: 100
 *                 description: Province name
 *               city:
 *                 type: string
 *                 minLength: 2
 *                 maxLength: 100
 *                 description: City name
 *               provinceId:
 *                 type: integer
 *                 minimum: 1
 *                 description: Province ID (optional, for location integration)
 *               cityId:
 *                 type: integer
 *                 minimum: 1
 *                 description: City ID (optional, for location integration)
 *               timezone:
 *                 type: string
 *                 default: "Asia/Jakarta"
 *                 description: Outlet timezone
 *               phone:
 *                 type: string
 *                 pattern: "^[0-9+\\-\\s()]+$"
 *                 description: Outlet phone number
 *               latitude:
 *                 type: number
 *                 format: float
 *                 minimum: -90
 *                 maximum: 90
 *                 description: Outlet latitude
 *               longitude:
 *                 type: number
 *                 format: float
 *                 minimum: -180
 *                 maximum: 180
 *                 description: Outlet longitude

 *               copiedFromId:
 *                 type: integer
 *                 minimum: 1
 *                 description: Source outlet ID to copy services from
 *             example:
 *               name: "Main Outlet"
 *               address: "Jl. Sudirman No. 123"
 *               province: "DKI Jakarta"
 *               city: "Jakarta Pusat"
 *               provinceId: 1
 *               cityId: 1
 *               phone: "081234567890"
 *               latitude: -6.2088
 *               longitude: 106.8456
 *     responses:
 *       "201":
 *         description: Outlet created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Outlet'
 *       "400":
 *         description: Bad request - validation error or location not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             examples:
 *               validation_error:
 *                 value:
 *                   code: 400
 *                   message: "\"name\" is required"
 *               province_not_found:
 *                 value:
 *                   code: 400
 *                   message: "Province not found"
 *               city_not_found:
 *                 value:
 *                   code: 400
 *                   message: "City not found"
 *               city_province_mismatch:
 *                 value:
 *                   code: 400
 *                   message: "City does not belong to the specified province"
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "403":
 *         $ref: '#/components/responses/Forbidden'
 *
 *   get:
 *     summary: Get all outlets
 *     description: Retrieve outlets with filtering and pagination. Owners can only see their outlets, admins can see all.
 *     tags: [Outlets]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: name
 *         schema:
 *           type: string
 *         description: Filter by outlet name
 *       - in: query
 *         name: city
 *         schema:
 *           type: string
 *         description: Filter by city name
 *       - in: query
 *         name: province
 *         schema:
 *           type: string
 *         description: Filter by province name
 *       - in: query
 *         name: isActive
 *         schema:
 *           type: boolean
 *         description: Filter by active status
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           default: createdAt
 *         description: Sort by field
 *       - in: query
 *         name: sortType
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *           default: desc
 *         description: Sort order
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 10
 *         description: Maximum number of outlets
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number
 *     responses:
 *       "200":
 *         description: Outlets retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 results:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Outlet'
 *                 page:
 *                   type: integer
 *                   example: 1
 *                 limit:
 *                   type: integer
 *                   example: 10
 *                 totalPages:
 *                   type: integer
 *                   example: 1
 *                 totalResults:
 *                   type: integer
 *                   example: 1
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 */

/**
 * @swagger
 * /outlets/{outletId}:
 *   get:
 *     summary: Get outlet by ID
 *     description: Retrieve a specific outlet with location details. Owners can only access their outlets, admins can access all.
 *     tags: [Outlets]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: outletId
 *         required: true
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Outlet ID
 *     responses:
 *       "200":
 *         description: Outlet retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Outlet'
 *       "400":
 *         description: Invalid outlet ID
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "403":
 *         $ref: '#/components/responses/Forbidden'
 *       "404":
 *         $ref: '#/components/responses/NotFound'
 *
 *   patch:
 *     summary: Update outlet
 *     description: Update outlet information with location integration support. Owners can only update their outlets, admins can update all.
 *     tags: [Outlets]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: outletId
 *         required: true
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Outlet ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             minProperties: 1
 *             properties:
 *               name:
 *                 type: string
 *                 minLength: 3
 *                 maxLength: 255
 *               address:
 *                 type: string
 *                 minLength: 10
 *               province:
 *                 type: string
 *                 minLength: 2
 *                 maxLength: 100
 *               city:
 *                 type: string
 *                 minLength: 2
 *                 maxLength: 100
 *               provinceId:
 *                 type: integer
 *                 minimum: 1
 *               cityId:
 *                 type: integer
 *                 minimum: 1
 *               timezone:
 *                 type: string
 *               phone:
 *                 type: string
 *                 pattern: "^[0-9+\\-\\s()]+$"
 *               latitude:
 *                 type: number
 *                 format: float
 *                 minimum: -90
 *                 maximum: 90
 *               longitude:
 *                 type: number
 *                 format: float
 *                 minimum: -180
 *                 maximum: 180

 *               isActive:
 *                 type: boolean
 *             example:
 *               name: "Updated Outlet Name"
 *               provinceId: 2
 *               cityId: 5
 *               isActive: false
 *     responses:
 *       "200":
 *         description: Outlet updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Outlet'
 *       "400":
 *         description: Bad request - validation error or location not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "403":
 *         $ref: '#/components/responses/Forbidden'
 *       "404":
 *         $ref: '#/components/responses/NotFound'
 *
 *   delete:
 *     summary: Delete outlet (soft delete)
 *     description: Soft delete an outlet. Owners can only delete their outlets, admins can delete all.
 *     tags: [Outlets]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: outletId
 *         required: true
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Outlet ID
 *     responses:
 *       "204":
 *         description: Outlet deleted successfully
 *       "400":
 *         description: Invalid outlet ID
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "403":
 *         $ref: '#/components/responses/Forbidden'
 *       "404":
 *         $ref: '#/components/responses/NotFound'
 */

/**
 * @swagger
 * /outlets/{outletId}/services:
 *   get:
 *     summary: Get outlet services
 *     description: Retrieve all services available at a specific outlet
 *     tags: [Outlets]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: outletId
 *         required: true
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Outlet ID
 *     responses:
 *       "200":
 *         description: Outlet services retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: integer
 *                   price:
 *                     type: number
 *                     format: float
 *                   isAvailable:
 *                     type: boolean
 *                   estimationHours:
 *                     type: integer
 *                     nullable: true
 *                   service:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                       name:
 *                         type: string
 *                       description:
 *                         type: string
 *                       basePrice:
 *                         type: number
 *                       unit:
 *                         type: string
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "404":
 *         $ref: '#/components/responses/NotFound'
 */

/**
 * @swagger
 * /outlets/{outletId}/copy-services:
 *   post:
 *     summary: Copy services from another outlet
 *     description: Copy all services from a source outlet to the target outlet
 *     tags: [Outlets]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: outletId
 *         required: true
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Target outlet ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - sourceOutletId
 *             properties:
 *               sourceOutletId:
 *                 type: integer
 *                 minimum: 1
 *                 description: Source outlet ID to copy services from
 *             example:
 *               sourceOutletId: 1
 *     responses:
 *       "200":
 *         description: Services copied successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 services:
 *                   type: array
 *                   items:
 *                     type: object
 *       "400":
 *         description: Bad request - source outlet has no services or invalid IDs
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "403":
 *         $ref: '#/components/responses/Forbidden'
 *       "404":
 *         $ref: '#/components/responses/NotFound'
 */

/**
 * @swagger
 * /outlets/{outletId}/stats:
 *   get:
 *     summary: Get outlet statistics
 *     description: Retrieve statistical information about an outlet
 *     tags: [Outlets]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: outletId
 *         required: true
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Outlet ID
 *     responses:
 *       "200":
 *         description: Outlet statistics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 totalOrders:
 *                   type: integer
 *                   description: Total number of orders
 *                 totalCustomers:
 *                   type: integer
 *                   description: Total number of customers
 *                 totalRevenue:
 *                   type: number
 *                   format: float
 *                   description: Total revenue from paid orders
 *                 pendingOrders:
 *                   type: integer
 *                   description: Number of pending orders
 *                 completedOrders:
 *                   type: integer
 *                   description: Number of completed orders
 *                 employeeCount:
 *                   type: integer
 *                   description: Number of employees
 *               example:
 *                 totalOrders: 150
 *                 totalCustomers: 75
 *                 totalRevenue: 15000000
 *                 pendingOrders: 10
 *                 completedOrders: 140
 *                 employeeCount: 5
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "404":
 *         $ref: '#/components/responses/NotFound'
 */
