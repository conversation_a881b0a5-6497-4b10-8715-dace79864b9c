"use client"

import { useState } from "react"
import Link from "next/link"
import {
  ArrowLeft,
  Save,
  Bell,
  Moon,
  Globe,
  Lock,
  CreditCard,
  Printer,
  Smartphone,
  HelpCircle,
  Settings,
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

export default function SettingsPage() {
  const [settings, setSettings] = useState({
    darkMode: false,
    notifications: {
      newOrder: true,
      orderStatus: true,
      paymentReceived: true,
      lowInventory: true,
      promotions: false,
    },
    language: "id",
    printReceipt: true,
    autoPrint: false,
    autoBackup: true,
    backupFrequency: "daily",
  })

  const handleSwitchChange = (key: string, value: boolean) => {
    if (key.includes(".")) {
      const [parent, child] = key.split(".")
      setSettings({
        ...settings,
        [parent]: {
          ...settings[parent as keyof typeof settings],
          [child]: value,
        },
      })
    } else {
      setSettings({
        ...settings,
        [key]: value,
      })
    }
  }

  const handleSelectChange = (key: string, value: string) => {
    setSettings({
      ...settings,
      [key]: value,
    })
  }

  const handleSave = () => {
    // In a real app, you would save the settings to your backend
    alert("Pengaturan berhasil disimpan!")
  }

  return (
    <div className="flex flex-col min-h-screen bg-slate-50">
      <header className="p-4 flex justify-between items-center bg-white sticky top-0 z-10 shadow-sm">
        <div className="flex items-center">
          <Link href="/akun" className="mr-3">
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <h1 className="text-lg font-semibold">Pengaturan</h1>
        </div>
        <Button onClick={handleSave} className="bg-blue-500 hover:bg-blue-600">
          <Save className="h-4 w-4 mr-2" /> Simpan
        </Button>
      </header>

      <main className="flex-1 p-4 pb-20">
        <Card className="p-4 mb-4">
          <h2 className="text-lg font-semibold flex items-center mb-4">
            <Bell className="h-5 w-5 mr-2 text-blue-500" /> Notifikasi
          </h2>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label htmlFor="newOrder" className="cursor-pointer">
                <div>Pesanan Baru</div>
                <p className="text-sm text-gray-500">Dapatkan notifikasi saat ada pesanan baru</p>
              </Label>
              <Switch
                id="newOrder"
                checked={settings.notifications.newOrder}
                onCheckedChange={(checked) => handleSwitchChange("notifications.newOrder", checked)}
              />
            </div>
            <div className="flex items-center justify-between">
              <Label htmlFor="orderStatus" className="cursor-pointer">
                <div>Perubahan Status Pesanan</div>
                <p className="text-sm text-gray-500">Dapatkan notifikasi saat status pesanan berubah</p>
              </Label>
              <Switch
                id="orderStatus"
                checked={settings.notifications.orderStatus}
                onCheckedChange={(checked) => handleSwitchChange("notifications.orderStatus", checked)}
              />
            </div>
            <div className="flex items-center justify-between">
              <Label htmlFor="paymentReceived" className="cursor-pointer">
                <div>Pembayaran Diterima</div>
                <p className="text-sm text-gray-500">Dapatkan notifikasi saat pembayaran diterima</p>
              </Label>
              <Switch
                id="paymentReceived"
                checked={settings.notifications.paymentReceived}
                onCheckedChange={(checked) => handleSwitchChange("notifications.paymentReceived", checked)}
              />
            </div>
            <div className="flex items-center justify-between">
              <Label htmlFor="lowInventory" className="cursor-pointer">
                <div>Stok Rendah</div>
                <p className="text-sm text-gray-500">Dapatkan notifikasi saat stok inventaris rendah</p>
              </Label>
              <Switch
                id="lowInventory"
                checked={settings.notifications.lowInventory}
                onCheckedChange={(checked) => handleSwitchChange("notifications.lowInventory", checked)}
              />
            </div>
            <div className="flex items-center justify-between">
              <Label htmlFor="promotions" className="cursor-pointer">
                <div>Promosi & Penawaran</div>
                <p className="text-sm text-gray-500">Dapatkan notifikasi tentang promosi dan penawaran</p>
              </Label>
              <Switch
                id="promotions"
                checked={settings.notifications.promotions}
                onCheckedChange={(checked) => handleSwitchChange("notifications.promotions", checked)}
              />
            </div>
          </div>
        </Card>

        <Card className="p-4 mb-4">
          <h2 className="text-lg font-semibold flex items-center mb-4">
            <Globe className="h-5 w-5 mr-2 text-green-500" /> Umum
          </h2>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label htmlFor="darkMode" className="cursor-pointer">
                <div className="flex items-center">
                  <Moon className="h-4 w-4 mr-2" /> Mode Gelap
                </div>
                <p className="text-sm text-gray-500">Aktifkan tampilan gelap</p>
              </Label>
              <Switch
                id="darkMode"
                checked={settings.darkMode}
                onCheckedChange={(checked) => handleSwitchChange("darkMode", checked)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="language">Bahasa</Label>
              <Select value={settings.language} onValueChange={(value) => handleSelectChange("language", value)}>
                <SelectTrigger id="language">
                  <SelectValue placeholder="Pilih bahasa" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="id">Bahasa Indonesia</SelectItem>
                  <SelectItem value="en">English</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </Card>

        <Card className="p-4 mb-4">
          <h2 className="text-lg font-semibold flex items-center mb-4">
            <Printer className="h-5 w-5 mr-2 text-purple-500" /> Cetak & Dokumen
          </h2>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label htmlFor="printReceipt" className="cursor-pointer">
                <div>Cetak Struk</div>
                <p className="text-sm text-gray-500">Cetak struk untuk setiap transaksi</p>
              </Label>
              <Switch
                id="printReceipt"
                checked={settings.printReceipt}
                onCheckedChange={(checked) => handleSwitchChange("printReceipt", checked)}
              />
            </div>
            <div className="flex items-center justify-between">
              <Label htmlFor="autoPrint" className="cursor-pointer">
                <div>Cetak Otomatis</div>
                <p className="text-sm text-gray-500">Cetak struk secara otomatis setelah pembayaran</p>
              </Label>
              <Switch
                id="autoPrint"
                checked={settings.autoPrint}
                onCheckedChange={(checked) => handleSwitchChange("autoPrint", checked)}
              />
            </div>
            <Link href="/akun/settings/printer">
              <Button variant="outline" className="w-full mt-2">
                <Settings className="h-4 w-4 mr-2" /> Pengaturan Printer & Nota Lanjutan
              </Button>
            </Link>
          </div>
        </Card>

        <Card className="p-4 mb-4">
          <h2 className="text-lg font-semibold flex items-center mb-4">
            <Lock className="h-5 w-5 mr-2 text-red-500" /> Keamanan & Backup
          </h2>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label htmlFor="autoBackup" className="cursor-pointer">
                <div>Backup Otomatis</div>
                <p className="text-sm text-gray-500">Backup data secara otomatis</p>
              </Label>
              <Switch
                id="autoBackup"
                checked={settings.autoBackup}
                onCheckedChange={(checked) => handleSwitchChange("autoBackup", checked)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="backupFrequency">Frekuensi Backup</Label>
              <Select
                value={settings.backupFrequency}
                onValueChange={(value) => handleSelectChange("backupFrequency", value)}
                disabled={!settings.autoBackup}
              >
                <SelectTrigger id="backupFrequency">
                  <SelectValue placeholder="Pilih frekuensi" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="daily">Harian</SelectItem>
                  <SelectItem value="weekly">Mingguan</SelectItem>
                  <SelectItem value="monthly">Bulanan</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Button variant="outline" className="w-full">
              Backup Sekarang
            </Button>

            <Separator />

            <Button variant="outline" className="w-full text-red-500 hover:text-red-600">
              Ubah Password
            </Button>
          </div>
        </Card>

        <Card className="p-4 mb-4">
          <h2 className="text-lg font-semibold flex items-center mb-4">
            <CreditCard className="h-5 w-5 mr-2 text-amber-500" /> Pembayaran
          </h2>
          <div className="space-y-4">
            <Button variant="outline" className="w-full">
              Kelola Metode Pembayaran
            </Button>
            <Button variant="outline" className="w-full">
              Riwayat Pembayaran
            </Button>
          </div>
        </Card>

        <Card className="p-4 mb-4">
          <h2 className="text-lg font-semibold flex items-center mb-4">
            <Smartphone className="h-5 w-5 mr-2 text-teal-500" /> Aplikasi
          </h2>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span>Versi Aplikasi</span>
              <span className="text-gray-500">1.0.0</span>
            </div>
            <div className="flex justify-between">
              <span>Terakhir Diperbarui</span>
              <span className="text-gray-500">01/03/2025</span>
            </div>
            <Button variant="outline" className="w-full mt-2">
              Periksa Pembaruan
            </Button>
          </div>
        </Card>

        <Card className="p-4">
          <h2 className="text-lg font-semibold flex items-center mb-4">
            <HelpCircle className="h-5 w-5 mr-2 text-blue-500" /> Bantuan & Dukungan
          </h2>
          <div className="space-y-2">
            <Button variant="outline" className="w-full">
              Pusat Bantuan
            </Button>
            <Button variant="outline" className="w-full">
              Hubungi Dukungan
            </Button>
            <Button variant="outline" className="w-full">
              Syarat & Ketentuan
            </Button>
            <Button variant="outline" className="w-full">
              Kebijakan Privasi
            </Button>
          </div>
        </Card>
      </main>
    </div>
  )
}
