// Order types based on backend API
export type OrderStatus =
  | 'KONFIRMASI' // Status awal order
  | 'PICKUP' // Barang sudah dipickup
  | 'PENDING' // Menunggu diproses
  | 'PROCESSING' // Sedang diproses
  | 'READY' // Siap diambil
  | 'READY_FOR_PICKUP' // Siap untuk pickup
  | 'COMPLETED' // Order selesai
  | 'CANCELLED'; // Order dibatalkan

export type PaymentStatus = 'UNPAID' | 'PARTIAL' | 'PAID' | 'REFUNDED';

export type PaymentMethod =
  | 'CASH'
  | 'TRANSFER'
  | 'CREDIT_CARD'
  | 'DEBIT_CARD'
  | 'E_WALLET'
  | 'DEPOSIT';

export type OrderItemStatus =
  | 'PENDING' // Belum diproses
  | 'WASHING' // Sedang dicuci
  | 'DRYING' // Sedang dikeringkan
  | 'IRONING' // Sedang disetrika
  | 'PACKING' // Sedang dikemas
  | 'COMPLETED' // Item selesai
  | 'CANCELLED'; // Item dibatalkan

export interface Service {
  id: number;
  name: string;
  unit: string;
  description?: string;
  price: number;
  estimationHours?: number;
  icon?: string;
}

export interface Customer {
  id: number;
  name: string;
  phone?: string;
  email?: string;
  address?: string;
}

export interface Perfume {
  id: number;
  name: string;
  description?: string;
  brand?: string;
  scent?: string;
}

export interface OrderItem {
  id: number;
  serviceId: number;
  quantity: number;
  unit: string;
  price: number;
  subtotal: number;
  notes?: string;
  status: OrderItemStatus;
  serviceName?: string;
  serviceDescription?: string;
  serviceUnit?: string;
  serviceEstimationHours?: number;
  icon?: string;
  service?: Service;
}

export interface Payment {
  id: number;
  amount: number;
  method: PaymentMethod;
  reference?: string;
  notes?: string;
  createdAt: string;
}

export interface Order {
  id: number;
  orderNumber: string;
  status: OrderStatus;
  totalPrice: number;
  totalWeight?: number;
  paidAmount: number;
  paymentStatus: PaymentStatus;
  paymentMethod?: PaymentMethod;
  notes?: string;
  pickupDate?: string;
  deliveryDate?: string;
  estimatedFinish?: string;
  actualFinish?: string;
  outletId: number;
  customerId: number;
  perfumeId?: number;
  perfumeName?: string;
  perfumeDescription?: string;
  promotionId?: number;
  promotionCode?: string;
  promotionDiscount?: number;
  createdAt: string;
  updatedAt: string;
  customer?: Customer;
  perfume?: Perfume;
  items: OrderItem[];
  payments?: Payment[];
}

export interface CreateOrderItemRequest {
  serviceId: number;
  quantity: number;
  unit: string;
  price: number;
  subtotal: number;
  notes?: string;
  status?: OrderItemStatus;
}

export interface CreatePaymentRequest {
  amount: number;
  method: PaymentMethod;
  cashboxId?: number;
  reference?: string;
  notes?: string;
}

export interface CreateOrderRequest {
  customerId: number;
  outletId: number;
  items: CreateOrderItemRequest[];
  notes?: string;
  pickupDate?: string;
  deliveryDate?: string;
  estimatedFinish?: string;
  perfumeId?: number;
  perfumeName?: string;
  perfumeDescription?: string;
  promoId?: number;
  payment?: CreatePaymentRequest;
}

export interface UpdateOrderRequest {
  notes?: string;
  pickupDate?: string;
  deliveryDate?: string;
  estimatedFinish?: string;
  actualFinish?: string;
  paymentMethod?: PaymentMethod;
}

export interface UpdateOrderStatusRequest {
  status: OrderStatus;
  notes?: string;
}

export interface UpdateOrderItemStatusRequest {
  status: OrderItemStatus;
  notes?: string;
}

export interface UpdateOrderItemsRequest {
  toUpdate?: Array<{
    id: number;
    quantity?: number;
    unit?: string;
    notes?: string;
  }>;
  toDelete?: number[];
  toAdd?: CreateOrderItemRequest[];
}

export interface AddPaymentRequest {
  amount: number;
  method: PaymentMethod;
  reference?: string;
  notes?: string;
}

export interface GetOrdersParams {
  search?: string;
  paymentStatus?: PaymentStatus;
  status?: OrderStatus;
  customerId?: number;
  dateFrom?: string;
  dateTo?: string;
  sortBy?: string;
  limit?: number;
  page?: number;
}

export interface GetOrdersResponse {
  results: Order[];
  page: number;
  limit: number;
  totalPages: number;
  totalResults: number;
}

export interface OrderStatusHistory {
  id: number;
  orderId: number;
  previousStatus: OrderStatus;
  newStatus: OrderStatus;
  changedBy: number;
  notes?: string;
  createdAt: string;
  user?: {
    id: number;
    name: string;
    email: string;
  };
}

export interface OrderItemStatusHistory {
  id: number;
  orderItemId: number;
  orderId: number;
  previousStatus: OrderItemStatus;
  newStatus: OrderItemStatus;
  changedBy: number;
  notes?: string;
  createdAt: string;
  user?: {
    id: number;
    name: string;
    email: string;
  };
}

export interface OrderTimeline {
  orderId: number;
  orderNumber: string;
  timeline: Array<{
    id: number;
    type: 'ORDER_STATUS' | 'ORDER_ITEM_STATUS';
    entityId: number;
    entityName: string;
    previousStatus: string;
    newStatus: string;
    changedBy: number;
    notes?: string;
    createdAt: string;
    user?: {
      id: number;
      name: string;
      email: string;
    };
  }>;
}
