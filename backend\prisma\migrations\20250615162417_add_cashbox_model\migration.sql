-- CreateEnum
CREATE TYPE "CashboxType" AS ENUM ('TUNAI', 'NON_TUNAI');

-- AlterTable
ALTER TABLE "Payment" ADD COLUMN     "cashboxId" INTEGER;

-- CreateTable
CREATE TABLE "Cashbox" (
    "id" SERIAL NOT NULL,
    "outletId" INTEGER NOT NULL,
    "name" TEXT NOT NULL,
    "type" "CashboxType" NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "balance" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Cashbox_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "Payment" ADD CONSTRAINT "Payment_cashboxId_fkey" FOREIGN KEY ("cashboxId") REFERENCES "Cashbox"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Cashbox" ADD CONSTRAINT "Cashbox_outletId_fkey" FOREIGN KEY ("outletId") REFERENCES "Outlet"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
