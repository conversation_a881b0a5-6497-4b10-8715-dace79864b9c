"use client"

import type React from "react"

import { useState } from "react"
import Link from "next/link"
import Image from "next/image"
import { useRouter } from "next/navigation"
import { ArrowLeft, Save, Clock, Trash2 } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Dialog<PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog"

// Mock data for service
const mockService = {
  id: 1,
  name: "Cuci + Setrika",
  price: 8000,
  unit: "Kg",
  image: "/icons/tshirt.png",
  category: "reguler",
  duration: 24,
  durationType: "jam",
  description: "Layanan cuci dan setrika pakaian reguler dengan kualitas terbaik dan wangi tahan lama.",
  isActive: true,
  processSteps: {
    cuci: true,
    kering: true,
    setrika: true,
    lipat: true,
    packaging: true,
  },
  labels: ["Pakaian", "Reguler"],
  group: "pakaian",
}

export default function EditServicePage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const serviceId = params.id

  // Predefined labels for services
  const predefinedLabels = [
    "Pakaian",
    "Sepatu",
    "Tas",
    "Karpet",
    "Selimut",
    "Bed Cover",
    "Sprei",
    "Gorden",
    "Premium",
    "Hemat",
    "Cepat",
    "Reguler",
  ]

  // In a real app, fetch the service by ID
  const [formData, setFormData] = useState(mockService)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [customLabel, setCustomLabel] = useState("")

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSwitchChange = (checked: boolean) => {
    setFormData((prev) => ({ ...prev, isActive: checked }))
  }

  const handleProcessStepChange = (step: string, checked: boolean) => {
    setFormData((prev) => ({
      ...prev,
      processSteps: {
        ...prev.processSteps,
        [step]: checked,
      },
    }))
  }

  const handleLabelToggle = (label: string) => {
    setFormData((prev) => {
      const currentLabels = [...prev.labels]
      if (currentLabels.includes(label)) {
        return { ...prev, labels: currentLabels.filter((l) => l !== label) }
      } else {
        return { ...prev, labels: [...currentLabels, label] }
      }
    })
  }

  const addCustomLabel = () => {
    if (customLabel.trim() && !formData.labels.includes(customLabel.trim())) {
      setFormData((prev) => ({
        ...prev,
        labels: [...prev.labels, customLabel.trim()],
      }))
      setCustomLabel("")
    }
  }

  const removeLabel = (label: string) => {
    setFormData((prev) => ({
      ...prev,
      labels: prev.labels.filter((l) => l !== label),
    }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // In a real app, you would update the service in your backend
    alert("Layanan berhasil diperbarui!")
    router.push(`/akun/services/${serviceId}`)
  }

  const confirmDelete = () => {
    // In a real app, you would delete the service from your backend
    alert(`Layanan dengan ID ${serviceId} telah dihapus`)
    setShowDeleteDialog(false)
    router.push("/akun/services")
  }

  return (
    <div className="flex flex-col min-h-screen bg-slate-50">
      <header className="p-4 flex justify-between items-center bg-white sticky top-0 z-10 shadow-sm">
        <div className="flex items-center">
          <Link href={`/akun/services/${serviceId}`} className="mr-3">
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <h1 className="text-lg font-semibold">Edit Layanan</h1>
        </div>
        <div className="flex gap-2">
          <Button
            type="button"
            variant="outline"
            className="text-red-500 border-red-500"
            onClick={() => setShowDeleteDialog(true)}
          >
            <Trash2 className="h-4 w-4 mr-2" /> Hapus
          </Button>
          <Button type="submit" form="edit-service-form" className="bg-blue-500 hover:bg-blue-600">
            <Save className="h-4 w-4 mr-2" /> Simpan
          </Button>
        </div>
      </header>

      <main className="flex-1 p-4 pb-20">
        <form id="edit-service-form" onSubmit={handleSubmit}>
          <Tabs defaultValue="basic" className="w-full">
            <TabsList className="grid w-full grid-cols-3 mb-4">
              <TabsTrigger value="basic">Informasi Dasar</TabsTrigger>
              <TabsTrigger value="process">Proses Produksi</TabsTrigger>
              <TabsTrigger value="grouping">Pengelompokan</TabsTrigger>
            </TabsList>

            <TabsContent value="basic">
              <Card className="p-4 space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Nama Layanan</Label>
                  <Input
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    placeholder="Contoh: Cuci + Setrika"
                    required
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="price">Harga</Label>
                    <div className="relative">
                      <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500">Rp</span>
                      <Input
                        id="price"
                        name="price"
                        type="number"
                        value={formData.price}
                        onChange={handleChange}
                        className="pl-10"
                        placeholder="0"
                        required
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="unit">Satuan</Label>
                    <Select
                      name="unit"
                      value={formData.unit}
                      onValueChange={(value) => handleSelectChange("unit", value)}
                    >
                      <SelectTrigger id="unit">
                        <SelectValue placeholder="Pilih satuan" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Kg">Kilogram (Kg)</SelectItem>
                        <SelectItem value="Pcs">Per Item (Pcs)</SelectItem>
                        <SelectItem value="Pasang">Pasang</SelectItem>
                        <SelectItem value="Meter">Meter</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Kategori Layanan</Label>
                  <RadioGroup
                    value={formData.category}
                    onValueChange={(value) => handleSelectChange("category", value)}
                    className="flex flex-wrap gap-4"
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="reguler" id="reguler" />
                      <Label htmlFor="reguler" className="cursor-pointer">
                        Reguler
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="express" id="express" />
                      <Label htmlFor="express" className="cursor-pointer">
                        Express
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="satuan" id="satuan" />
                      <Label htmlFor="satuan" className="cursor-pointer">
                        Satuan
                      </Label>
                    </div>
                  </RadioGroup>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="duration">Durasi Pengerjaan</Label>
                  <div className="flex gap-2">
                    <div className="relative flex-1">
                      <Clock className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-500" />
                      <Input
                        id="duration"
                        name="duration"
                        type="number"
                        value={formData.duration}
                        onChange={handleChange}
                        className="pl-10"
                        placeholder="Durasi"
                        required
                      />
                    </div>
                    <Select
                      name="durationType"
                      value={formData.durationType}
                      onValueChange={(value) => handleSelectChange("durationType", value)}
                      className="w-1/3"
                    >
                      <SelectTrigger id="durationType">
                        <SelectValue placeholder="Satuan" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="jam">Jam</SelectItem>
                        <SelectItem value="hari">Hari</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Deskripsi Layanan</Label>
                  <Textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleChange}
                    placeholder="Deskripsi singkat tentang layanan"
                    rows={3}
                  />
                </div>

                <div className="space-y-2">
                  <Label>Ikon Layanan</Label>
                  <div className="flex items-center gap-4">
                    <div className="w-16 h-16 bg-gray-100 rounded-md flex items-center justify-center">
                      <Image
                        src={formData.image || "/placeholder.svg?height=64&width=64"}
                        alt={formData.name}
                        width={40}
                        height={40}
                      />
                    </div>
                    <Button type="button" variant="outline" size="sm">
                      Ganti Ikon
                    </Button>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="isActive" className="cursor-pointer">
                    Status Layanan Aktif
                  </Label>
                  <Switch id="isActive" checked={formData.isActive} onCheckedChange={handleSwitchChange} />
                </div>
              </Card>
            </TabsContent>

            <TabsContent value="process">
              <Card className="p-4 space-y-4">
                <div className="space-y-2">
                  <Label className="text-base font-medium">Tahapan Proses Produksi</Label>
                  <p className="text-sm text-gray-500">Pilih tahapan proses yang diperlukan untuk layanan ini</p>

                  <div className="grid gap-3 pt-2">
                    <div className="flex items-center space-x-3">
                      <Checkbox
                        id="cuci"
                        checked={formData.processSteps.cuci}
                        onCheckedChange={(checked) => handleProcessStepChange("cuci", checked as boolean)}
                      />
                      <Label htmlFor="cuci" className="font-normal">
                        Cuci (Wash)
                      </Label>
                    </div>

                    <div className="flex items-center space-x-3">
                      <Checkbox
                        id="kering"
                        checked={formData.processSteps.kering}
                        onCheckedChange={(checked) => handleProcessStepChange("kering", checked as boolean)}
                      />
                      <Label htmlFor="kering" className="font-normal">
                        Kering (Dry)
                      </Label>
                    </div>

                    <div className="flex items-center space-x-3">
                      <Checkbox
                        id="setrika"
                        checked={formData.processSteps.setrika}
                        onCheckedChange={(checked) => handleProcessStepChange("setrika", checked as boolean)}
                      />
                      <Label htmlFor="setrika" className="font-normal">
                        Setrika (Iron)
                      </Label>
                    </div>

                    <div className="flex items-center space-x-3">
                      <Checkbox
                        id="lipat"
                        checked={formData.processSteps.lipat}
                        onCheckedChange={(checked) => handleProcessStepChange("lipat", checked as boolean)}
                      />
                      <Label htmlFor="lipat" className="font-normal">
                        Lipat (Fold)
                      </Label>
                    </div>

                    <div className="flex items-center space-x-3">
                      <Checkbox
                        id="packaging"
                        checked={formData.processSteps.packaging}
                        onCheckedChange={(checked) => handleProcessStepChange("packaging", checked as boolean)}
                      />
                      <Label htmlFor="packaging" className="font-normal">
                        Packaging
                      </Label>
                    </div>
                  </div>
                </div>

                <div className="pt-4">
                  <p className="text-sm text-gray-500 italic">
                    Tahapan proses yang dipilih akan digunakan untuk melacak progres pesanan dan membantu staf dalam
                    mengelola alur kerja.
                  </p>
                </div>
              </Card>
            </TabsContent>

            <TabsContent value="grouping">
              <Card className="p-4 space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="group">Grup Layanan</Label>
                  <Select
                    name="group"
                    value={formData.group}
                    onValueChange={(value) => handleSelectChange("group", value)}
                  >
                    <SelectTrigger id="group">
                      <SelectValue placeholder="Pilih grup layanan" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="pakaian">Pakaian</SelectItem>
                      <SelectItem value="rumah_tangga">Rumah Tangga</SelectItem>
                      <SelectItem value="sepatu_tas">Sepatu & Tas</SelectItem>
                      <SelectItem value="karpet">Karpet & Permadani</SelectItem>
                      <SelectItem value="lainnya">Lainnya</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Label Layanan</Label>
                  <p className="text-sm text-gray-500">
                    Pilih atau tambahkan label untuk memudahkan pencarian dan pengelompokan
                  </p>

                  <div className="flex flex-wrap gap-2 mt-2">
                    {predefinedLabels.map((label) => (
                      <Badge
                        key={label}
                        variant={formData.labels.includes(label) ? "default" : "outline"}
                        className="cursor-pointer"
                        onClick={() => handleLabelToggle(label)}
                      >
                        {label}
                      </Badge>
                    ))}
                  </div>

                  <div className="flex items-center gap-2 mt-3">
                    <Input
                      placeholder="Tambah label kustom"
                      value={customLabel}
                      onChange={(e) => setCustomLabel(e.target.value)}
                      onKeyDown={(e) => {
                        if (e.key === "Enter") {
                          e.preventDefault()
                          addCustomLabel()
                        }
                      }}
                    />
                    <Button type="button" variant="outline" onClick={addCustomLabel} disabled={!customLabel.trim()}>
                      Tambah
                    </Button>
                  </div>

                  {formData.labels.length > 0 && (
                    <div className="mt-4">
                      <Label>Label yang dipilih:</Label>
                      <div className="flex flex-wrap gap-2 mt-2">
                        {formData.labels.map((label) => (
                          <Badge key={label} variant="secondary" className="flex items-center gap-1">
                            {label}
                            <button
                              type="button"
                              className="ml-1 rounded-full h-4 w-4 inline-flex items-center justify-center text-xs hover:bg-gray-200"
                              onClick={() => removeLabel(label)}
                            >
                              ×
                            </button>
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </Card>
            </TabsContent>
          </Tabs>
        </form>
      </main>

      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Hapus Layanan</DialogTitle>
            <DialogDescription>
              Apakah Anda yakin ingin menghapus layanan ini? Tindakan ini tidak dapat dibatalkan.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteDialog(false)}>
              Batal
            </Button>
            <Button variant="destructive" onClick={confirmDelete}>
              Hapus
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
