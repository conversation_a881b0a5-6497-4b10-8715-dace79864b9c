-- Migration script untuk menambahkan cashbox default ke outlet yang sudah ada
-- Jalankan script ini jika ada outlet existing yang belum memiliki cashbox

-- Tambahkan cashbox TUNAI untuk outlet yang belum memiliki cashbox
INSERT INTO "Cashbox" ("outletId", "name", "type", "isActive", "balance", "createdAt", "updatedAt")
SELECT 
  o.id,
  '<PERSON>s Utama',
  'TUNAI',
  true,
  0,
  NOW(),
  NOW()
FROM "Outlet" o
WHERE o."isDeleted" = false 
  AND o.id NOT IN (
    SELECT DISTINCT "outletId" 
    FROM "Cashbox" 
    WHERE "type" = 'TUNAI'
  );

-- Tambahkan cashbox NON_TUNAI untuk outlet yang belum memiliki cashbox non-tunai
INSERT INTO "Cashbox" ("outletId", "name", "type", "isActive", "balance", "createdAt", "updatedAt")
SELECT 
  o.id,
  'Transfer Bank',
  'NON_TUNAI',
  true,
  0,
  NOW(),
  NOW()
FROM "Outlet" o
WHERE o."isDeleted" = false 
  AND o.id NOT IN (
    SELECT DISTINCT "outletId" 
    FROM "Cashbox" 
    WHERE "type" = 'NON_TUNAI'
  );

-- Verifikasi hasil migration
SELECT 
  o.id as outlet_id,
  o.name as outlet_name,
  COUNT(c.id) as cashbox_count,
  STRING_AGG(CONCAT(c.name, ' (', c.type, ')'), ', ') as cashboxes
FROM "Outlet" o
LEFT JOIN "Cashbox" c ON o.id = c."outletId"
WHERE o."isDeleted" = false
GROUP BY o.id, o.name
ORDER BY o.id; 