export { default as authController } from './auth.controller';
export { default as userController } from './user.controller';
export { default as outletController } from './outlet.controller';
export { default as locationController } from './location.controller';
export { default as employeeController } from './employee.controller';
export { default as attendanceController } from './attendance.controller';
export { default as serviceController } from './service.controller';
export { default as serviceCategoryController } from './service-category.controller';
export { default as customerController } from './customer.controller';
export { default as orderController } from './order.controller';
export { default as perfumeController } from './perfume.controller';
export { default as cashboxController } from './cashbox.controller';
export { default as customerDepositController } from './customerDeposit.controller';
export { default as promotionController } from './promotion.controller';
