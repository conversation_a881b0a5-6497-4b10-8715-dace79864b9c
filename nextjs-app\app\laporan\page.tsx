"use client"

import { useState, useEffect } from "react"
import {
  Calendar,
  Download,
  Filter,
  <PERSON><PERSON>hart2,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart,
  Users,
  Package,
  DollarSign,
  UserCheck,
  Clock,
  Share2,
  Bell,
  RefreshCw,
  ChevronDown,
  Printer,
  FileText,
  Sliders,
  TrendingUp,
  AlertCircle,
} from "lucide-react"
import { format, subDays, startOfMonth, endOfMonth, startOfWeek, endOfWeek, startOfYear, endOfYear } from "date-fns"
import { id } from "date-fns/locale"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import BottomNavigation from "@/components/bottom-navigation"

// Mock data for charts
const salesData = [
  { month: "Jan", revenue: 4500000 },
  { month: "Feb", revenue: 5200000 },
  { month: "Mar", revenue: 4800000 },
  { month: "Apr", revenue: 5500000 },
  { month: "May", revenue: 6200000 },
  { month: "Jun", revenue: 5800000 },
  { month: "Jul", revenue: 6500000 },
  { month: "Aug", revenue: 7200000 },
  { month: "Sep", revenue: 6800000 },
  { month: "Oct", revenue: 7500000 },
  { month: "Nov", revenue: 8200000 },
  { month: "Dec", revenue: 8800000 },
]

const serviceData = [
  { name: "Cuci + Setrika", value: 65 },
  { name: "Cuci Saja", value: 15 },
  { name: "Setrika Saja", value: 10 },
  { name: "Express", value: 8 },
  { name: "Lainnya", value: 2 },
]

const customerData = [
  { month: "Jan", new: 12, returning: 45 },
  { month: "Feb", new: 18, returning: 48 },
  { month: "Mar", new: 15, returning: 52 },
  { month: "Apr", new: 21, returning: 55 },
  { month: "May", new: 25, returning: 58 },
  { month: "Jun", new: 18, returning: 62 },
  { month: "Jul", new: 22, returning: 65 },
  { month: "Aug", new: 28, returning: 68 },
  { month: "Sep", new: 20, returning: 72 },
  { month: "Oct", new: 24, returning: 75 },
  { month: "Nov", new: 30, returning: 78 },
  { month: "Dec", new: 35, returning: 82 },
]

const inventoryData = [
  { name: "Deterjen", current: 75, minimum: 20 },
  { name: "Pelembut", current: 60, minimum: 15 },
  { name: "Pemutih", current: 30, minimum: 10 },
  { name: "Plastik", current: 85, minimum: 30 },
  { name: "Hanger", current: 120, minimum: 50 },
]

const employeePerformance = [
  { name: "Ahmad Rizki", orders: 120, rating: 4.8 },
  { name: "Siti Rahayu", orders: 95, rating: 4.7 },
  { name: "Budi Santoso", orders: 105, rating: 4.5 },
  { name: "Dewi Anggraini", orders: 85, rating: 4.6 },
  { name: "Joko Widodo", orders: 110, rating: 4.9 },
]

export default function ReportingPage() {
  const [activeTab, setActiveTab] = useState("overview")
  const [dateRange, setDateRange] = useState("this-month")
  const [customStartDate, setCustomStartDate] = useState("")
  const [customEndDate, setCustomEndDate] = useState("")
  const [showDateFilter, setShowDateFilter] = useState(false)
  const [reportFormat, setReportFormat] = useState("chart")
  const [isScheduleDialogOpen, setIsScheduleDialogOpen] = useState(false)
  const [scheduleSettings, setScheduleSettings] = useState({
    frequency: "weekly",
    recipients: "",
    format: "pdf",
    includeCharts: true,
    includeRawData: true,
  })

  // Set default date range on component mount
  useEffect(() => {
    const today = new Date()
    const startOfMonthDate = startOfMonth(today)
    const endOfMonthDate = endOfMonth(today)

    setCustomStartDate(format(startOfMonthDate, "yyyy-MM-dd"))
    setCustomEndDate(format(endOfMonthDate, "yyyy-MM-dd"))
  }, [])

  // Handle date range changes
  const handleDateRangeChange = (value: string) => {
    setDateRange(value)
    const today = new Date()

    switch (value) {
      case "today":
        setCustomStartDate(format(today, "yyyy-MM-dd"))
        setCustomEndDate(format(today, "yyyy-MM-dd"))
        break
      case "yesterday":
        const yesterday = subDays(today, 1)
        setCustomStartDate(format(yesterday, "yyyy-MM-dd"))
        setCustomEndDate(format(yesterday, "yyyy-MM-dd"))
        break
      case "this-week":
        setCustomStartDate(format(startOfWeek(today, { weekStartsOn: 1 }), "yyyy-MM-dd"))
        setCustomEndDate(format(endOfWeek(today, { weekStartsOn: 1 }), "yyyy-MM-dd"))
        break
      case "this-month":
        setCustomStartDate(format(startOfMonth(today), "yyyy-MM-dd"))
        setCustomEndDate(format(endOfMonth(today), "yyyy-MM-dd"))
        break
      case "this-year":
        setCustomStartDate(format(startOfYear(today), "yyyy-MM-dd"))
        setCustomEndDate(format(endOfYear(today), "yyyy-MM-dd"))
        break
      case "custom":
        setShowDateFilter(true)
        break
      default:
        break
    }
  }

  // Format date for display
  const formatDateRange = () => {
    if (dateRange === "custom") {
      return `${format(new Date(customStartDate), "dd MMM yyyy", { locale: id })} - ${format(
        new Date(customEndDate),
        "dd MMM yyyy",
        { locale: id },
      )}`
    }

    const today = new Date()
    switch (dateRange) {
      case "today":
        return format(today, "dd MMMM yyyy", { locale: id })
      case "yesterday":
        return format(subDays(today, 1), "dd MMMM yyyy", { locale: id })
      case "this-week":
        return `${format(startOfWeek(today, { weekStartsOn: 1 }), "dd MMM", { locale: id })} - ${format(
          endOfWeek(today, { weekStartsOn: 1 }),
          "dd MMM yyyy",
          { locale: id },
        )}`
      case "this-month":
        return format(today, "MMMM yyyy", { locale: id })
      case "this-year":
        return format(today, "yyyy", { locale: id })
      default:
        return ""
    }
  }

  // Handle schedule settings change
  const handleScheduleSettingChange = (key: string, value: any) => {
    setScheduleSettings({
      ...scheduleSettings,
      [key]: value,
    })
  }

  // Handle schedule report submission
  const handleScheduleReport = () => {
    // In a real app, you would save the schedule settings to your backend
    alert(`Laporan terjadwal akan dikirim ${scheduleSettings.frequency} ke ${scheduleSettings.recipients}`)
    setIsScheduleDialogOpen(false)
  }

  // Handle export report
  const handleExportReport = (format: string) => {
    // In a real app, you would generate and download the report
    alert(`Laporan akan diunduh dalam format ${format.toUpperCase()}`)
  }

  return (
    <div className="flex flex-col min-h-screen bg-slate-50">
      <header className="p-4 flex justify-between items-center bg-white sticky top-0 z-10 shadow-sm">
        <h1 className="text-xl font-semibold">Laporan</h1>
        <div className="flex items-center gap-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="icon">
                <Download className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Ekspor Laporan</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => handleExportReport("pdf")}>
                <FileText className="h-4 w-4 mr-2" /> PDF
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleExportReport("csv")}>
                <FileText className="h-4 w-4 mr-2" /> CSV
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleExportReport("excel")}>
                <FileText className="h-4 w-4 mr-2" /> Excel
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <Dialog open={isScheduleDialogOpen} onOpenChange={setIsScheduleDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="outline" size="icon">
                <Bell className="h-4 w-4" />
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>Jadwalkan Laporan</DialogTitle>
                <DialogDescription>Atur jadwal pengiriman laporan otomatis melalui email.</DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="frequency" className="text-right">
                    Frekuensi
                  </Label>
                  <Select
                    value={scheduleSettings.frequency}
                    onValueChange={(value) => handleScheduleSettingChange("frequency", value)}
                  >
                    <SelectTrigger className="col-span-3">
                      <SelectValue placeholder="Pilih frekuensi" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="daily">Harian</SelectItem>
                      <SelectItem value="weekly">Mingguan</SelectItem>
                      <SelectItem value="monthly">Bulanan</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="recipients" className="text-right">
                    Penerima
                  </Label>
                  <Input
                    id="recipients"
                    placeholder="<EMAIL>, <EMAIL>"
                    className="col-span-3"
                    value={scheduleSettings.recipients}
                    onChange={(e) => handleScheduleSettingChange("recipients", e.target.value)}
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="format" className="text-right">
                    Format
                  </Label>
                  <Select
                    value={scheduleSettings.format}
                    onValueChange={(value) => handleScheduleSettingChange("format", value)}
                  >
                    <SelectTrigger className="col-span-3">
                      <SelectValue placeholder="Pilih format" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="pdf">PDF</SelectItem>
                      <SelectItem value="csv">CSV</SelectItem>
                      <SelectItem value="excel">Excel</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <Separator />
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="include-charts" className="text-right">
                    Sertakan Grafik
                  </Label>
                  <div className="col-span-3">
                    <Switch
                      id="include-charts"
                      checked={scheduleSettings.includeCharts}
                      onCheckedChange={(checked) => handleScheduleSettingChange("includeCharts", checked)}
                    />
                  </div>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="include-raw-data" className="text-right">
                    Sertakan Data Mentah
                  </Label>
                  <div className="col-span-3">
                    <Switch
                      id="include-raw-data"
                      checked={scheduleSettings.includeRawData}
                      onCheckedChange={(checked) => handleScheduleSettingChange("includeRawData", checked)}
                    />
                  </div>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsScheduleDialogOpen(false)}>
                  Batal
                </Button>
                <Button onClick={handleScheduleReport}>Jadwalkan</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          <Popover open={showDateFilter} onOpenChange={setShowDateFilter}>
            <PopoverTrigger asChild>
              <Button variant="outline" size="icon">
                <Filter className="h-4 w-4" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80">
              <div className="grid gap-4">
                <div className="space-y-2">
                  <h4 className="font-medium leading-none">Rentang Waktu</h4>
                  <Select value={dateRange} onValueChange={handleDateRangeChange}>
                    <SelectTrigger>
                      <SelectValue placeholder="Pilih rentang waktu" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectGroup>
                        <SelectLabel>Rentang Waktu</SelectLabel>
                        <SelectItem value="today">Hari Ini</SelectItem>
                        <SelectItem value="yesterday">Kemarin</SelectItem>
                        <SelectItem value="this-week">Minggu Ini</SelectItem>
                        <SelectItem value="this-month">Bulan Ini</SelectItem>
                        <SelectItem value="this-year">Tahun Ini</SelectItem>
                        <SelectItem value="custom">Kustom</SelectItem>
                      </SelectGroup>
                    </SelectContent>
                  </Select>
                </div>
                {dateRange === "custom" && (
                  <div className="grid gap-2">
                    <div className="grid grid-cols-3 items-center gap-4">
                      <Label htmlFor="from">Dari</Label>
                      <Input
                        id="from"
                        type="date"
                        value={customStartDate}
                        onChange={(e) => setCustomStartDate(e.target.value)}
                        className="col-span-2"
                      />
                    </div>
                    <div className="grid grid-cols-3 items-center gap-4">
                      <Label htmlFor="to">Sampai</Label>
                      <Input
                        id="to"
                        type="date"
                        value={customEndDate}
                        onChange={(e) => setCustomEndDate(e.target.value)}
                        className="col-span-2"
                      />
                    </div>
                  </div>
                )}
                <div className="space-y-2">
                  <h4 className="font-medium leading-none">Format Tampilan</h4>
                  <Select value={reportFormat} onValueChange={setReportFormat}>
                    <SelectTrigger>
                      <SelectValue placeholder="Pilih format tampilan" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="chart">Grafik</SelectItem>
                      <SelectItem value="table">Tabel</SelectItem>
                      <SelectItem value="both">Keduanya</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <Button onClick={() => setShowDateFilter(false)}>Terapkan Filter</Button>
              </div>
            </PopoverContent>
          </Popover>
        </div>
      </header>

      <main className="flex-1 p-4 pb-20">
        <div className="flex justify-between items-center mb-4">
          <div>
            <h2 className="text-lg font-semibold">Laporan Bisnis</h2>
            <p className="text-sm text-gray-500">Periode: {formatDateRange()}</p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={() => handleDateRangeChange("this-month")}>
              <RefreshCw className="h-3 w-3 mr-1" /> Reset
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm">
                  <Calendar className="h-3 w-3 mr-1" /> {dateRange === "custom" ? "Kustom" : "Periode"}{" "}
                  <ChevronDown className="h-3 w-3 ml-1" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => handleDateRangeChange("today")}>Hari Ini</DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleDateRangeChange("yesterday")}>Kemarin</DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleDateRangeChange("this-week")}>Minggu Ini</DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleDateRangeChange("this-month")}>Bulan Ini</DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleDateRangeChange("this-year")}>Tahun Ini</DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => handleDateRangeChange("custom")}>Kustom...</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        <Tabs defaultValue="overview" className="mb-4" onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-5 w-full">
            <TabsTrigger value="overview">Ringkasan</TabsTrigger>
            <TabsTrigger value="sales">Penjualan</TabsTrigger>
            <TabsTrigger value="customers">Pelanggan</TabsTrigger>
            <TabsTrigger value="inventory">Inventaris</TabsTrigger>
            <TabsTrigger value="employees">Pegawai</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Pendapatan</CardTitle>
                  <DollarSign className="h-4 w-4 text-green-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">Rp 8.800.000</div>
                  <p className="text-xs text-green-500">+12% dari periode sebelumnya</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Order</CardTitle>
                  <Package className="h-4 w-4 text-blue-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">142</div>
                  <p className="text-xs text-green-500">+8% dari periode sebelumnya</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Pelanggan Baru</CardTitle>
                  <Users className="h-4 w-4 text-purple-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">35</div>
                  <p className="text-xs text-green-500">+15% dari periode sebelumnya</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Rata-rata Order</CardTitle>
                  <BarChart2 className="h-4 w-4 text-orange-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">Rp 62.000</div>
                  <p className="text-xs text-green-500">+3% dari periode sebelumnya</p>
                </CardContent>
              </Card>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle>Tren Pendapatan</CardTitle>
                  <CardDescription>Pendapatan bulanan selama tahun ini</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-[300px] w-full bg-gray-100 rounded-md flex items-center justify-center">
                    <LineChart className="h-16 w-16 text-gray-400" />
                    <span className="ml-2 text-gray-500">Grafik Pendapatan</span>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader>
                  <CardTitle>Distribusi Layanan</CardTitle>
                  <CardDescription>Persentase penggunaan layanan</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-[300px] w-full bg-gray-100 rounded-md flex items-center justify-center">
                    <PieChart className="h-16 w-16 text-gray-400" />
                    <span className="ml-2 text-gray-500">Grafik Distribusi Layanan</span>
                  </div>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Performa Outlet</CardTitle>
                <CardDescription>Perbandingan pendapatan antar outlet</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] w-full bg-gray-100 rounded-md flex items-center justify-center">
                  <BarChart2 className="h-16 w-16 text-gray-400" />
                  <span className="ml-2 text-gray-500">Grafik Performa Outlet</span>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Sales Tab */}
          <TabsContent value="sales" className="space-y-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Pendapatan</CardTitle>
                  <DollarSign className="h-4 w-4 text-green-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">Rp 8.800.000</div>
                  <p className="text-xs text-green-500">+12% dari periode sebelumnya</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Order</CardTitle>
                  <Package className="h-4 w-4 text-blue-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">142</div>
                  <p className="text-xs text-green-500">+8% dari periode sebelumnya</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Rata-rata Order</CardTitle>
                  <BarChart2 className="h-4 w-4 text-orange-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">Rp 62.000</div>
                  <p className="text-xs text-green-500">+3% dari periode sebelumnya</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Order Tertinggi</CardTitle>
                  <TrendingUp className="h-4 w-4 text-purple-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">Rp 350.000</div>
                  <p className="text-xs text-gray-500">TRX/250324/014</p>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle>Tren Penjualan</CardTitle>
                  <CardDescription>Pendapatan bulanan selama tahun ini</CardDescription>
                </div>
                <Select defaultValue="revenue">
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Pilih metrik" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="revenue">Pendapatan</SelectItem>
                    <SelectItem value="orders">Jumlah Order</SelectItem>
                    <SelectItem value="average">Rata-rata Order</SelectItem>
                  </SelectContent>
                </Select>
              </CardHeader>
              <CardContent>
                <div className="h-[400px] w-full bg-gray-100 rounded-md flex items-center justify-center">
                  <LineChart className="h-16 w-16 text-gray-400" />
                  <span className="ml-2 text-gray-500">Grafik Tren Penjualan</span>
                </div>
              </CardContent>
            </Card>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle>Distribusi Layanan</CardTitle>
                  <CardDescription>Persentase penggunaan layanan</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-[300px] w-full bg-gray-100 rounded-md flex items-center justify-center">
                    <PieChart className="h-16 w-16 text-gray-400" />
                    <span className="ml-2 text-gray-500">Grafik Distribusi Layanan</span>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader>
                  <CardTitle>Metode Pembayaran</CardTitle>
                  <CardDescription>Distribusi metode pembayaran yang digunakan</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-[300px] w-full bg-gray-100 rounded-md flex items-center justify-center">
                    <PieChart className="h-16 w-16 text-gray-400" />
                    <span className="ml-2 text-gray-500">Grafik Metode Pembayaran</span>
                  </div>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Detail Penjualan</CardTitle>
                <CardDescription>Daftar transaksi dalam periode yang dipilih</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          ID Transaksi
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Tanggal
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Pelanggan
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Layanan
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Total
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Status
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {[1, 2, 3, 4, 5].map((item) => (
                        <tr key={item} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600">
                            TRX/25032{item}/01{item}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {format(subDays(new Date(), item), "dd/MM/yyyy")}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {["Budi Santoso", "Siti Nurbaya", "Ahmad Dahlan", "Dewi Sartika", "Joko Widodo"][item - 1]}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {["Cuci + Setrika", "Express", "Cuci Saja", "Setrika Saja", "Bed Cover"][item - 1]}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            Rp {(Math.floor(Math.random() * 10) + 5) * 10000}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <Badge
                              variant="outline"
                              className={
                                item % 3 === 0
                                  ? "border-amber-500 text-amber-500"
                                  : item % 2 === 0
                                    ? "border-green-500 text-green-500"
                                    : "border-blue-500 text-blue-500"
                              }
                            >
                              {item % 3 === 0 ? "Proses" : item % 2 === 0 ? "Selesai" : "Baru"}
                            </Badge>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
                <div className="flex justify-center mt-4">
                  <Button variant="outline" size="sm">
                    Lihat Semua Transaksi
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Customers Tab */}
          <TabsContent value="customers" className="space-y-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Pelanggan</CardTitle>
                  <Users className="h-4 w-4 text-blue-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">120</div>
                  <p className="text-xs text-green-500">+12% dari periode sebelumnya</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Pelanggan Baru</CardTitle>
                  <UserCheck className="h-4 w-4 text-green-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">35</div>
                  <p className="text-xs text-green-500">+15% dari periode sebelumnya</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Pelanggan Aktif</CardTitle>
                  <Users className="h-4 w-4 text-purple-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">98</div>
                  <p className="text-xs text-green-500">+5% dari periode sebelumnya</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Rata-rata Belanja</CardTitle>
                  <DollarSign className="h-4 w-4 text-orange-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">Rp 125.000</div>
                  <p className="text-xs text-green-500">+8% dari periode sebelumnya</p>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle>Pertumbuhan Pelanggan</CardTitle>
                  <CardDescription>Pelanggan baru vs pelanggan kembali</CardDescription>
                </div>
                <Select defaultValue="monthly">
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Pilih periode" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="weekly">Mingguan</SelectItem>
                    <SelectItem value="monthly">Bulanan</SelectItem>
                    <SelectItem value="quarterly">Kuartalan</SelectItem>
                    <SelectItem value="yearly">Tahunan</SelectItem>
                  </SelectContent>
                </Select>
              </CardHeader>
              <CardContent>
                <div className="h-[400px] w-full bg-gray-100 rounded-md flex items-center justify-center">
                  <BarChart2 className="h-16 w-16 text-gray-400" />
                  <span className="ml-2 text-gray-500">Grafik Pertumbuhan Pelanggan</span>
                </div>
              </CardContent>
            </Card>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle>Segmentasi Pelanggan</CardTitle>
                  <CardDescription>Berdasarkan frekuensi order</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-[300px] w-full bg-gray-100 rounded-md flex items-center justify-center">
                    <PieChart className="h-16 w-16 text-gray-400" />
                    <span className="ml-2 text-gray-500">Grafik Segmentasi Pelanggan</span>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader>
                  <CardTitle>Pelanggan Teratas</CardTitle>
                  <CardDescription>Berdasarkan total belanja</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {["Budi Santoso", "Siti Nurbaya", "Ahmad Dahlan", "Dewi Sartika", "Joko Widodo"].map(
                      (name, index) => (
                        <div key={index} className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 font-medium">
                              {index + 1}
                            </div>
                            <div>
                              <p className="font-medium">{name}</p>
                              <p className="text-xs text-gray-500">{12 - index} order</p>
                            </div>
                          </div>
                          <p className="font-medium">Rp {(Math.floor(Math.random() * 10) + 5) * 100000}</p>
                        </div>
                      ),
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Inventory Tab */}
          <TabsContent value="inventory" className="space-y-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Item</CardTitle>
                  <Package className="h-4 w-4 text-blue-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">24</div>
                  <p className="text-xs text-gray-500">8 kategori</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Stok Rendah</CardTitle>
                  <AlertCircle className="h-4 w-4 text-red-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">3</div>
                  <p className="text-xs text-red-500">Perlu restock segera</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Nilai Inventaris</CardTitle>
                  <DollarSign className="h-4 w-4 text-green-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">Rp 12.500.000</div>
                  <p className="text-xs text-gray-500">Total nilai stok</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Restock Terakhir</CardTitle>
                  <Clock className="h-4 w-4 text-orange-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">5 hari</div>
                  <p className="text-xs text-gray-500">yang lalu</p>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Status Inventaris</CardTitle>
                <CardDescription>Level stok saat ini vs minimum</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {inventoryData.map((item, index) => (
                    <div key={index}>
                      <div className="flex justify-between mb-1">
                        <span className="text-sm font-medium">{item.name}</span>
                        <span className="text-sm text-gray-500">
                          {item.current} / {item.minimum} {item.current < item.minimum && "(Stok Rendah)"}
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2.5">
                        <div
                          className={`h-2.5 rounded-full ${
                            item.current < item.minimum
                              ? "bg-red-500"
                              : item.current < item.minimum * 2
                                ? "bg-yellow-500"
                                : "bg-green-500"
                          }`}
                          style={{ width: `${Math.min((item.current / (item.minimum * 3)) * 100, 100)}%` }}
                        ></div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle>Penggunaan Inventaris</CardTitle>
                  <CardDescription>Tren penggunaan bulanan</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-[300px] w-full bg-gray-100 rounded-md flex items-center justify-center">
                    <LineChart className="h-16 w-16 text-gray-400" />
                    <span className="ml-2 text-gray-500">Grafik Penggunaan Inventaris</span>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader>
                  <CardTitle>Distribusi Kategori</CardTitle>
                  <CardDescription>Berdasarkan nilai inventaris</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-[300px] w-full bg-gray-100 rounded-md flex items-center justify-center">
                    <PieChart className="h-16 w-16 text-gray-400" />
                    <span className="ml-2 text-gray-500">Grafik Distribusi Kategori</span>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Employees Tab */}
          <TabsContent value="employees" className="space-y-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Pegawai</CardTitle>
                  <Users className="h-4 w-4 text-blue-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">15</div>
                  <p className="text-xs text-gray-500">5 posisi berbeda</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Produktivitas</CardTitle>
                  <BarChart2 className="h-4 w-4 text-green-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">92%</div>
                  <p className="text-xs text-green-500">+5% dari periode sebelumnya</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Rata-rata Order</CardTitle>
                  <Package className="h-4 w-4 text-purple-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">9.5</div>
                  <p className="text-xs text-gray-500">order per pegawai per hari</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Rating Pegawai</CardTitle>
                  <UserCheck className="h-4 w-4 text-orange-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">4.7/5</div>
                  <p className="text-xs text-green-500">+0.2 dari periode sebelumnya</p>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Performa Pegawai</CardTitle>
                <CardDescription>Berdasarkan jumlah order yang diproses</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {employeePerformance.map((employee, index) => (
                    <div key={index}>
                      <div className="flex justify-between mb-1">
                        <span className="text-sm font-medium">{employee.name}</span>
                        <span className="text-sm text-gray-500">
                          {employee.orders} order | Rating: {employee.rating}/5
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2.5">
                        <div
                          className="h-2.5 rounded-full bg-blue-500"
                          style={{ width: `${(employee.orders / 120) * 100}%` }}
                        ></div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle>Distribusi Beban Kerja</CardTitle>
                  <CardDescription>Berdasarkan posisi</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-[300px] w-full bg-gray-100 rounded-md flex items-center justify-center">
                    <PieChart className="h-16 w-16 text-gray-400" />
                    <span className="ml-2 text-gray-500">Grafik Distribusi Beban Kerja</span>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader>
                  <CardTitle>Tren Produktivitas</CardTitle>
                  <CardDescription>Selama 12 bulan terakhir</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-[300px] w-full bg-gray-100 rounded-md flex items-center justify-center">
                    <LineChart className="h-16 w-16 text-gray-400" />
                    <span className="ml-2 text-gray-500">Grafik Tren Produktivitas</span>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>

        <div className="flex justify-between items-center mt-8 mb-4">
          <h2 className="text-lg font-semibold">Laporan Tersimpan</h2>
          <Button variant="outline" size="sm">
            <Sliders className="h-4 w-4 mr-2" /> Kelola Laporan
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <Card className="hover:shadow-md transition-shadow">
            <CardHeader className="pb-2">
              <CardTitle className="text-base">Laporan Penjualan Bulanan</CardTitle>
              <CardDescription>Diperbarui 2 hari yang lalu</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex justify-between">
                <Badge variant="outline" className="bg-blue-50 text-blue-700 hover:bg-blue-100">
                  Penjualan
                </Badge>
                <div className="flex gap-2">
                  <Button variant="ghost" size="icon" className="h-8 w-8">
                    <Printer className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="icon" className="h-8 w-8">
                    <Download className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="icon" className="h-8 w-8">
                    <Share2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card className="hover:shadow-md transition-shadow">
            <CardHeader className="pb-2">
              <CardTitle className="text-base">Laporan Pelanggan Teratas</CardTitle>
              <CardDescription>Diperbarui 5 hari yang lalu</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex justify-between">
                <Badge variant="outline" className="bg-purple-50 text-purple-700 hover:bg-purple-100">
                  Pelanggan
                </Badge>
                <div className="flex gap-2">
                  <Button variant="ghost" size="icon" className="h-8 w-8">
                    <Printer className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="icon" className="h-8 w-8">
                    <Download className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="icon" className="h-8 w-8">
                    <Share2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card className="hover:shadow-md transition-shadow">
            <CardHeader className="pb-2">
              <CardTitle className="text-base">Laporan Inventaris</CardTitle>
              <CardDescription>Diperbarui 1 hari yang lalu</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex justify-between">
                <Badge variant="outline" className="bg-green-50 text-green-700 hover:bg-green-100">
                  Inventaris
                </Badge>
                <div className="flex gap-2">
                  <Button variant="ghost" size="icon" className="h-8 w-8">
                    <Printer className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="icon" className="h-8 w-8">
                    <Download className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="icon" className="h-8 w-8">
                    <Share2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>

      <BottomNavigation activePage="laporan" />
    </div>
  )
}
