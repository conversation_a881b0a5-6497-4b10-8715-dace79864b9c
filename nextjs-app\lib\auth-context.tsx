'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import axios from 'axios';
import { setCookie, getCookie, deleteCookie } from './cookie-utils';
import { Outlet } from './api/outlets';

interface User {
  id: number;
  email: string;
  name: string | null;
  phone: string | null;
  role: string;
  isEmailVerified: boolean;
}

 

interface AuthTokens {
  access: {
    token: string;
    expires: Date;
  };
  refresh: {
    token: string;
    expires: Date;
  };
}

interface AuthContextType {
  user: User | null;
  tokens: AuthTokens | null;
  activeOutlet: Outlet | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<void>;
  register: (
    name: string,
    email: string,
    phone: string,
    password: string
  ) => Promise<any>;
  logout: () => Promise<void>;
  refreshToken: () => Promise<void>;
  forgotPassword: (email: string) => Promise<void>;
  resetPassword: (token: string, password: string) => Promise<void>;
  sendVerificationEmail: (
    method?: 'token' | 'otp',
    accessToken?: string
  ) => Promise<any>;
  setActiveOutlet: (outlet: Outlet) => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Axios instance dengan interceptor untuk token
const api = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/v1',
});

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [tokens, setTokens] = useState<AuthTokens | null>(null);
  const [activeOutlet, setActiveOutletState] = useState<Outlet | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const isAuthenticated = !!user && !!tokens;

  // Helper functions to manage auth data in both localStorage and cookies
  const saveAuthData = (
    userData: User,
    userTokens: AuthTokens,
    outlet?: Outlet
  ) => {
    // Save to localStorage
    localStorage.setItem('user', JSON.stringify(userData));
    localStorage.setItem('tokens', JSON.stringify(userTokens));
    if (outlet) {
      localStorage.setItem('activeOutlet', JSON.stringify(outlet));
    }

    // Save to cookies for middleware access
    setCookie('user', JSON.stringify(userData), 7); // 7 days
    setCookie('tokens', JSON.stringify(userTokens), 7);
    if (outlet) {
      setCookie('activeOutlet', JSON.stringify(outlet), 7);
    }
  };

  const clearAuthData = () => {
    // Clear localStorage
    localStorage.removeItem('user');
    localStorage.removeItem('tokens');
    localStorage.removeItem('activeOutlet');

    // Clear cookies
    deleteCookie('user');
    deleteCookie('tokens');
    deleteCookie('activeOutlet');
  };

  const setActiveOutlet = (outlet: Outlet) => {
    console.log('🏢 Setting active outlet:', outlet);
    setActiveOutletState(outlet);
    localStorage.setItem('activeOutlet', JSON.stringify(outlet));
    setCookie('activeOutlet', JSON.stringify(outlet), 7);
    console.log('✅ Active outlet saved to localStorage and cookies');
  };

  // Load auth data from localStorage on mount
  useEffect(() => {
    const loadAuthData = () => {
      try {
        const savedUser = localStorage.getItem('user');
        const savedTokens = localStorage.getItem('tokens');
        const savedActiveOutlet = localStorage.getItem('activeOutlet');

        if (savedUser && savedTokens) {
          const userData = JSON.parse(savedUser);
          const userTokens = JSON.parse(savedTokens);
          const outletData = savedActiveOutlet
            ? JSON.parse(savedActiveOutlet)
            : null;

          setUser(userData);
          setTokens(userTokens);
          setActiveOutletState(outletData);

          // Sync with cookies if not already there
          if (!getCookie('user') || !getCookie('tokens')) {
            saveAuthData(userData, userTokens, outletData || undefined);
          }
        }
      } catch (error) {
        console.error('Error loading auth data:', error);
        // Clear corrupt data
        clearAuthData();
      }
      setIsLoading(false);
    };

    loadAuthData();
  }, []);

  // Setup axios interceptors
  useEffect(() => {
    // Request interceptor to add auth token
    const requestInterceptor = api.interceptors.request.use(
      (config) => {
        if (tokens?.access?.token) {
          config.headers.Authorization = `Bearer ${tokens.access.token}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor to handle token refresh
    const responseInterceptor = api.interceptors.response.use(
      (response) => response,
      async (error) => {
        const originalRequest = error.config;

        // ✅ Exclude auth endpoints dari auto refresh
        const isAuthEndpoint =
          originalRequest.url?.includes('/auth/login') ||
          originalRequest.url?.includes('/auth/register') ||
          originalRequest.url?.includes('/auth/refresh-tokens');

        if (
          error.response?.status === 401 &&
          !originalRequest._retry &&
          !isAuthEndpoint &&
          tokens?.refresh?.token
        ) {
          originalRequest._retry = true;

          try {
            await refreshToken();
            // Retry original request with new token
            return api(originalRequest);
          } catch (refreshError) {
            // Refresh failed, logout user
            await logout();
            return Promise.reject(refreshError);
          }
        }

        return Promise.reject(error);
      }
    );

    return () => {
      api.interceptors.request.eject(requestInterceptor);
      api.interceptors.response.eject(responseInterceptor);
    };
  }, [tokens]);

  const login = async (email: string, password: string) => {
    try {
      // Don't set isLoading here - let components handle their own loading states
      const response = await api.post('/auth/login', { email, password });
      console.log('🚀 ~ login ~ response:', response.data);
      const { user: userData, tokens: userTokens } = response.data;

      setUser(userData);
      setTokens(userTokens);
      // Clear active outlet on new login - user needs to select again
      setActiveOutletState(null);

      // Save to both localStorage and cookies
      saveAuthData(userData, userTokens);
    } catch (error: any) {
      console.log('🚀 ~ login ~ error:', error);
      console.log('🚀 ~ login ~ error.response:', error.response);

      // ✅ Sekarang bisa akses message dari backend
      const message =
        error.response?.data?.message ||
        error.response?.data?.error ||
        error.message ||
        'Login gagal';
      throw new Error(message);
    }
  };

  const register = async (
    name: string,
    email: string,
    phone: string,
    password: string
  ) => {
    try {
      const response = await api.post('/auth/register', {
        name,
        email,
        phone,
        password,
      });
      console.log('🚀 ~ register ~ response:', response.data);
      const { user: userData, tokens: userTokens } = response.data;

      setUser(userData);
      setTokens(userTokens);
      saveAuthData(userData, userTokens);
      return response.data;
    } catch (error: any) {
      console.log('🚀 ~ register ~ error:', error);
      console.log('🚀 ~ register ~ error.response:', error.response);

      const message =
        error.response?.data?.message ||
        error.response?.data?.error ||
        error.message ||
        'Registrasi gagal';
      throw new Error(message);
    }
  };

  const logout = async () => {
    try {
      if (tokens?.refresh?.token) {
        await api.post('/auth/logout', { refreshToken: tokens.refresh.token });
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Clear state and storage
      setUser(null);
      setTokens(null);
      setActiveOutletState(null);
      clearAuthData();
    }
  };

  const refreshToken = async () => {
    if (!tokens?.refresh?.token) {
      throw new Error('No refresh token available');
    }

    try {
      const response = await api.post('/auth/refresh-tokens', {
        refreshToken: tokens.refresh.token,
      });
      const newTokens = response.data;

      setTokens(newTokens);

      // Update both localStorage and cookies
      localStorage.setItem('tokens', JSON.stringify(newTokens));
      setCookie('tokens', JSON.stringify(newTokens), 7);
    } catch (error) {
      console.error('Token refresh failed:', error);
      throw error;
    }
  };

  const forgotPassword = async (email: string) => {
    try {
      await api.post('/auth/forgot-password', { email });
    } catch (error: any) {
      console.log('🚀 ~ forgotPassword ~ error:', error);
      console.log('🚀 ~ forgotPassword ~ error.response:', error.response);

      const message =
        error.response?.data?.message ||
        error.response?.data?.error ||
        error.message ||
        'Gagal mengirim email reset password';
      throw new Error(message);
    }
  };

  const resetPassword = async (token: string, password: string) => {
    try {
      await api.post(`/auth/reset-password?token=${token}`, { password });
    } catch (error: any) {
      console.log('🚀 ~ resetPassword ~ error:', error);
      console.log('🚀 ~ resetPassword ~ error.response:', error.response);

      const message =
        error.response?.data?.message ||
        error.response?.data?.error ||
        error.message ||
        'Gagal mereset password';
      throw new Error(message);
    }
  };

  const sendVerificationEmail = async (
    method: 'token' | 'otp' = 'otp',
    accessToken?: string
  ) => {
    try {
      const response = await axios.post(
        `/auth/send-verification-email?method=${method}`,
        {
          headers: {
            Authorization: `Bearer ${accessToken || tokens?.access?.token}`,
          },
        }
      );
      return response.data;
    } catch (error: any) {
      console.log('🚀 ~ sendVerificationEmail ~ error:', error);
      console.log(
        '🚀 ~ sendVerificationEmail ~ error.response:',
        error.response
      );

      const message =
        error.response?.data?.message ||
        error.response?.data?.error ||
        error.message ||
        'Gagal mengirim email verifikasi';
      throw new Error(message);
    }
  };

  const value: AuthContextType = {
    user,
    tokens,
    activeOutlet,
    isLoading,
    isAuthenticated,
    login,
    register,
    logout,
    refreshToken,
    forgotPassword,
    resetPassword,
    sendVerificationEmail,
    setActiveOutlet,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// Export configured axios instance for API calls
export { api };
