"use client"

import { useState } from "react"
import Link from "next/link"
import { ArrowLeft, Search, Calendar, CheckCircle, XCircle, AlertTriangle, Clock, MapPin } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar as CalendarComponent } from "@/components/ui/calendar"

// Mock data for team attendance
const mockTeamAttendance = [
  {
    id: 1,
    name: "<PERSON>",
    position: "Manager",
    avatar: "/placeholder.svg",
    status: "present",
    checkIn: "08:05:23",
    checkOut: null,
    location: "Cabang Utama",
  },
  {
    id: 2,
    name: "<PERSON><PERSON>",
    position: "Kasir",
    avatar: "/placeholder.svg",
    status: "present",
    checkIn: "08:00:10",
    checkOut: null,
    location: "Cabang Utama",
  },
  {
    id: 3,
    name: "Budi Santoso",
    position: "Operator",
    avatar: "/placeholder.svg",
    status: "late",
    checkIn: "08:30:45",
    checkOut: null,
    location: "Cabang Utama",
  },
  {
    id: 4,
    name: "Dewi Anggraini",
    position: "Kurir",
    avatar: "/placeholder.svg",
    status: "absent",
    checkIn: null,
    checkOut: null,
    location: null,
  },
  {
    id: 5,
    name: "Joko Widodo",
    position: "Admin",
    avatar: "/placeholder.svg",
    status: "present",
    checkIn: "07:55:33",
    checkOut: null,
    location: "Cabang Utama",
  },
]

// Mock data for attendance history
const mockAttendanceHistory = [
  {
    date: "2023-10-25",
    employees: [
      { id: 1, name: "Ahmad Rizki", status: "present", checkIn: "08:05:23", checkOut: "17:15:45" },
      { id: 2, name: "Siti Rahayu", status: "present", checkIn: "08:00:10", checkOut: "17:10:22" },
      { id: 3, name: "Budi Santoso", status: "late", checkIn: "08:30:45", checkOut: "17:20:15" },
      { id: 4, name: "Dewi Anggraini", status: "absent", checkIn: null, checkOut: null },
      { id: 5, name: "Joko Widodo", status: "present", checkIn: "07:55:33", checkOut: "17:05:12" },
    ],
  },
  {
    date: "2023-10-24",
    employees: [
      { id: 1, name: "Ahmad Rizki", status: "present", checkIn: "08:02:15", checkOut: "17:10:30" },
      { id: 2, name: "Siti Rahayu", status: "late", checkIn: "08:20:05", checkOut: "17:15:40" },
      { id: 3, name: "Budi Santoso", status: "present", checkIn: "07:58:22", checkOut: "17:05:18" },
      { id: 4, name: "Dewi Anggraini", status: "present", checkIn: "08:05:10", checkOut: "17:10:25" },
      { id: 5, name: "Joko Widodo", status: "present", checkIn: "08:00:45", checkOut: "17:00:30" },
    ],
  },
  {
    date: "2023-10-23",
    employees: [
      { id: 1, name: "Ahmad Rizki", status: "present", checkIn: "08:00:10", checkOut: "17:05:20" },
      { id: 2, name: "Siti Rahayu", status: "present", checkIn: "08:05:15", checkOut: "17:10:30" },
      { id: 3, name: "Budi Santoso", status: "present", checkIn: "08:02:45", checkOut: "17:15:10" },
      { id: 4, name: "Dewi Anggraini", status: "absent", checkIn: null, checkOut: null },
      { id: 5, name: "Joko Widodo", status: "late", checkIn: "08:25:30", checkOut: "17:30:15" },
    ],
  },
]

export default function TeamAttendancePage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [dateFilter, setDateFilter] = useState<Date | undefined>(undefined)
  const [activeTab, setActiveTab] = useState("today")

  // Format date as DD/MM/YYYY
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("id-ID", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    })
  }

  // Get day name
  const getDayName = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("id-ID", { weekday: "long" })
  }

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "present":
        return <Badge className="bg-green-500">Hadir</Badge>
      case "late":
        return <Badge className="bg-yellow-500">Terlambat</Badge>
      case "absent":
        return <Badge className="bg-red-500">Tidak Hadir</Badge>
      default:
        return <Badge className="bg-gray-500">Tidak Diketahui</Badge>
    }
  }

  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case "present":
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case "late":
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />
      case "absent":
        return <XCircle className="h-5 w-5 text-red-500" />
      default:
        return <AlertTriangle className="h-5 w-5 text-gray-500" />
    }
  }

  // Filter team attendance based on search query and status
  const filteredTeamAttendance = mockTeamAttendance.filter((employee) => {
    // Filter by search query (name or position)
    const matchesSearch =
      employee.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      employee.position.toLowerCase().includes(searchQuery.toLowerCase())

    // Filter by status
    const matchesStatus = statusFilter === "all" || employee.status === statusFilter

    return matchesSearch && matchesStatus
  })

  // Filter attendance history based on date
  const filteredAttendanceHistory = dateFilter
    ? mockAttendanceHistory.filter((record) => record.date === dateFilter.toISOString().split("T")[0])
    : mockAttendanceHistory

  // Reset filters
  const resetFilters = () => {
    setSearchQuery("")
    setStatusFilter("all")
    setDateFilter(undefined)
  }

  // Get summary counts
  const getSummaryCounts = (employees: typeof mockTeamAttendance) => {
    return {
      present: employees.filter((e) => e.status === "present").length,
      late: employees.filter((e) => e.status === "late").length,
      absent: employees.filter((e) => e.status === "absent").length,
      total: employees.length,
    }
  }

  const todaySummary = getSummaryCounts(mockTeamAttendance)

  return (
    <div className="flex flex-col min-h-screen bg-slate-50">
      <header className="p-4 flex justify-between items-center bg-white sticky top-0 z-10 shadow-sm">
        <div className="flex items-center">
          <Link href="/absensi" className="mr-3">
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <h1 className="text-lg font-semibold">Kehadiran Tim</h1>
        </div>
        <Link href="/absensi/team/report">
          <Button variant="outline" size="sm">
            Laporan
          </Button>
        </Link>
      </header>

      <main className="flex-1 p-4 pb-20">
        <Tabs defaultValue="today" className="mb-6" onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-2 w-full">
            <TabsTrigger value="today">Hari Ini</TabsTrigger>
            <TabsTrigger value="history">Riwayat</TabsTrigger>
          </TabsList>

          <TabsContent value="today" className="mt-4">
            <Card className="mb-4">
              <CardContent className="p-4">
                <div className="grid grid-cols-3 gap-2 text-center">
                  <div className="p-2">
                    <p className="text-2xl font-bold text-green-600">{todaySummary.present}</p>
                    <p className="text-sm text-gray-500">Hadir</p>
                  </div>
                  <div className="p-2">
                    <p className="text-2xl font-bold text-yellow-600">{todaySummary.late}</p>
                    <p className="text-sm text-gray-500">Terlambat</p>
                  </div>
                  <div className="p-2">
                    <p className="text-2xl font-bold text-red-600">{todaySummary.absent}</p>
                    <p className="text-sm text-gray-500">Tidak Hadir</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <div className="mb-4 space-y-3">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <Input
                  placeholder="Cari karyawan..."
                  className="pl-10"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>

              <div className="flex flex-wrap gap-2">
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Filter Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Semua Status</SelectItem>
                    <SelectItem value="present">Hadir</SelectItem>
                    <SelectItem value="late">Terlambat</SelectItem>
                    <SelectItem value="absent">Tidak Hadir</SelectItem>
                  </SelectContent>
                </Select>

                {(searchQuery || statusFilter !== "all") && (
                  <Button variant="ghost" onClick={resetFilters}>
                    Reset Filter
                  </Button>
                )}
              </div>
            </div>

            <div className="space-y-3">
              {filteredTeamAttendance.length > 0 ? (
                filteredTeamAttendance.map((employee) => (
                  <Card key={employee.id}>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <Avatar className="h-10 w-10">
                            <AvatarImage src={employee.avatar || "/placeholder.svg"} alt={employee.name} />
                            <AvatarFallback>{employee.name.substring(0, 2).toUpperCase()}</AvatarFallback>
                          </Avatar>
                          <div>
                            <h3 className="font-medium">{employee.name}</h3>
                            <p className="text-sm text-gray-500">{employee.position}</p>
                          </div>
                        </div>
                        {getStatusIcon(employee.status)}
                      </div>

                      <div className="mt-3 grid grid-cols-2 gap-4">
                        <div>
                          <div className="flex items-center text-gray-600">
                            <Clock className="h-4 w-4 mr-1" />
                            <p className="text-sm">Check-In</p>
                          </div>
                          <p className="font-medium">{employee.checkIn || "-"}</p>
                        </div>
                        <div>
                          <div className="flex items-center text-gray-600">
                            <Clock className="h-4 w-4 mr-1" />
                            <p className="text-sm">Check-Out</p>
                          </div>
                          <p className="font-medium">{employee.checkOut || "-"}</p>
                        </div>
                      </div>

                      {employee.location && (
                        <div className="mt-2 flex items-center text-gray-600">
                          <MapPin className="h-4 w-4 mr-1" />
                          <p className="text-sm">{employee.location}</p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <p>Tidak ada karyawan yang ditemukan</p>
                  <Button variant="link" onClick={resetFilters}>
                    Reset Filter
                  </Button>
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="history" className="mt-4">
            <div className="mb-4">
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" className="w-full flex items-center justify-between">
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 mr-2" />
                      {dateFilter ? formatDate(dateFilter.toISOString().split("T")[0]) : "Pilih Tanggal"}
                    </div>
                    {dateFilter && (
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 px-2"
                        onClick={(e) => {
                          e.stopPropagation()
                          setDateFilter(undefined)
                        }}
                      >
                        Reset
                      </Button>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <CalendarComponent mode="single" selected={dateFilter} onSelect={setDateFilter} initialFocus />
                </PopoverContent>
              </Popover>
            </div>

            <div className="space-y-6">
              {filteredAttendanceHistory.length > 0 ? (
                filteredAttendanceHistory.map((record, index) => (
                  <div key={index}>
                    <div className="flex items-center mb-3">
                      <h3 className="font-medium">{formatDate(record.date)}</h3>
                      <span className="text-sm text-gray-500 ml-2">({getDayName(record.date)})</span>
                    </div>

                    <div className="space-y-3">
                      {record.employees.map((employee) => (
                        <Card key={employee.id}>
                          <CardContent className="p-4">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-3">
                                <div>
                                  <h3 className="font-medium">{employee.name}</h3>
                                  {getStatusBadge(employee.status)}
                                </div>
                              </div>
                              <Link href={`/absensi/employee/${employee.id}?date=${record.date}`}>
                                <Button variant="ghost" size="sm">
                                  Detail
                                </Button>
                              </Link>
                            </div>

                            <div className="mt-3 grid grid-cols-2 gap-4">
                              <div>
                                <div className="flex items-center text-gray-600">
                                  <Clock className="h-4 w-4 mr-1" />
                                  <p className="text-sm">Check-In</p>
                                </div>
                                <p className="font-medium">{employee.checkIn || "-"}</p>
                              </div>
                              <div>
                                <div className="flex items-center text-gray-600">
                                  <Clock className="h-4 w-4 mr-1" />
                                  <p className="text-sm">Check-Out</p>
                                </div>
                                <p className="font-medium">{employee.checkOut || "-"}</p>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <p>Tidak ada data kehadiran yang ditemukan</p>
                  {dateFilter && (
                    <Button variant="link" onClick={() => setDateFilter(undefined)}>
                      Reset Tanggal
                    </Button>
                  )}
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </main>
    </div>
  )
}
