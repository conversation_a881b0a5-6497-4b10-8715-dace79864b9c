# Auto Cashbox Selection - Dokumentasi

## Overview

Sistem sekarang secara otomatis memilih cashbox yang sesuai untuk pembayaran berdasarkan payment method jika tidak ada cashboxId yang diberikan secara eksplisit.

## Fitur yang Diimplementasikan

### 1. Auto-Select Cashbox berdasarkan Payment Method

- **CASH** → Cashbox dengan type `TUNAI` (biasanya "Kas Utama")
- **TRANSFER, CREDIT_CARD, DEBIT_CARD, E_WALLET** → Cashbox dengan type `NON_TUNAI`
- **DEPOSIT** → Tidak perlu cashbox (dihandle oleh deposit service)

### 2. Prioritas Pemilihan Cashbox

1. **Jika cashboxId diberikan**: Gunakan cashbox yang ditentukan
2. **Jika cashboxId tidak diberikan**: Auto-select berdasarkan payment method
3. **Jika tidak ada cashbox yang sesuai**: Payment tetap diproses tanpa cashbox

### 3. Algoritma Pemilihan

```typescript
const getDefaultCashboxId = async (
  outletId: number,
  paymentMethod: string
): Promise<number | null> => {
  // Tentukan type cashbox berdasarkan payment method
  let cashboxType: 'TUNAI' | 'NON_TUNAI';

  if (paymentMethod === 'CASH') {
    cashboxType = 'TUNAI';
  } else if (['TRANSFER', 'CREDIT_CARD', 'DEBIT_CARD', 'E_WALLET'].includes(paymentMethod)) {
    cashboxType = 'NON_TUNAI';
  } else {
    return null; // Untuk DEPOSIT dan payment method lain
  }

  // Cari cashbox aktif pertama dengan type yang sesuai (berdasarkan createdAt)
  const cashbox = await prisma.cashbox.findFirst({
    where: { outletId, type: cashboxType, isActive: true },
    orderBy: { createdAt: 'asc' }
  });

  return cashbox?.id || null;
};
```

## Implementasi Details

### 1. Modifikasi pada `createOrder`

- Tambahkan auto-select cashbox sebelum payment processing
- Update cashbox balance menggunakan `finalCashboxId`
- Improved logging untuk tracking cashbox selection

### 2. Modifikasi pada `addPaymentToOrder`

- Implementasi logic yang sama untuk konsistensi
- Validasi cashbox yang dipilih sebelum payment processing
- Update cashbox balance dengan cashbox yang dipilih

### 3. Logging dan Monitoring

```typescript
logger.debug(
  `Auto-selected cashbox: ${cashbox.name} (ID: ${cashbox.id}) for payment method: ${paymentMethod}`
);
```

## Manfaat

### 1. User Experience

- **Seamless Payment**: User tidak perlu memilih cashbox secara manual
- **Consistent Behavior**: Pembayaran tunai selalu masuk ke "Kas Utama"
- **Error Prevention**: Mencegah pembayaran tanpa cashbox

### 2. Business Logic

- **Automatic Categorization**: Pembayaran otomatis masuk ke cashbox yang sesuai
- **Balance Tracking**: Saldo cashbox ter-update secara otomatis
- **Audit Trail**: Log yang jelas untuk tracking cashbox selection

### 3. Backward Compatibility

- **Existing Functionality**: Tidak mengubah behavior jika cashboxId sudah diberikan
- **Graceful Fallback**: Jika tidak ada cashbox yang sesuai, payment tetap diproses
- **No Breaking Changes**: API contract tetap sama

## Testing

### 1. Test Cases

1. **Pembayaran CASH tanpa cashboxId** → Harus masuk ke cashbox TUNAI
2. **Pembayaran TRANSFER tanpa cashboxId** → Harus masuk ke cashbox NON_TUNAI
3. **Pembayaran DEPOSIT** → Tidak perlu cashbox
4. **Pembayaran dengan cashboxId eksplisit** → Gunakan cashbox yang ditentukan

### 2. Edge Cases

1. **Tidak ada cashbox aktif** → Payment diproses tanpa cashbox
2. **Multiple cashbox dengan type sama** → Pilih yang pertama dibuat
3. **Cashbox tidak aktif** → Skip dan cari yang aktif

## Monitoring

### 1. Log Messages

- `Auto-selected cashbox: [name] (ID: [id]) for payment method: [method]`
- `No active [type] cashbox found for outlet [id]`
- `Updating cashbox balance: [cashboxId]`

### 2. Metrics to Track

- Jumlah pembayaran yang menggunakan auto-select
- Distribusi pembayaran per cashbox
- Error rate untuk cashbox selection

## Future Enhancements

1. **Configurable Rules**: Izinkan outlet mengatur rules pemilihan cashbox
2. **Load Balancing**: Distribusi pembayaran ke multiple cashbox
3. **Smart Selection**: Pilih cashbox berdasarkan balance atau usage pattern
