# Example Environment Configuration untuk SendGrid Provider
# Copy file ini ke .env dan sesuaikan dengan kredensial Anda

# Node environment
NODE_ENV=development
PORT=3000

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_ACCESS_EXPIRATION_MINUTES=30
JWT_REFRESH_EXPIRATION_DAYS=30
JWT_RESET_PASSWORD_EXPIRATION_MINUTES=10
JWT_VERIFY_EMAIL_EXPIRATION_MINUTES=10

# Email Provider Selection
EMAIL_PROVIDER=sendgrid

# SendGrid Configuration
SENDGRID_API_KEY=SG.your-sendgrid-api-key-here
SENDGRID_FROM_EMAIL=<EMAIL>

# SMTP Configuration (Optional - tidak digunakan jika EMAIL_PROVIDER=sendgrid)
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USERNAME=<EMAIL>
# SMTP_PASSWORD=your-gmail-app-password
# EMAIL_FROM=<EMAIL>

# Database
DATABASE_URL="postgresql://username:password@localhost:5432/laundry_app?schema=public"

# Instruksi Setup SendGrid:
# 1. Daftar di https://sendgrid.com/
# 2. Verifikasi email Anda
# 3. Pergi ke Settings > API Keys
# 4. Create API Key dengan "Mail Send" permissions
# 5. Copy API Key ke SENDGRID_API_KEY
# 6. Pergi ke Settings > Sender Authentication
# 7. Verify Single Sender dan gunakan email tersebut di SENDGRID_FROM_EMAIL 