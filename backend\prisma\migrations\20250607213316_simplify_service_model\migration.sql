/*
  Warnings:

  - You are about to drop the column `basePrice` on the `Service` table. All the data in the column will be lost.
  - You are about to drop the `OutletService` table. If the table is not empty, all the data it contains will be lost.
  - A unique constraint covering the columns `[outletId,name]` on the table `Service` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `outletId` to the `Service` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "OutletService" DROP CONSTRAINT "OutletService_outletId_fkey";

-- DropForeignKey
ALTER TABLE "OutletService" DROP CONSTRAINT "OutletService_serviceId_fkey";

-- AlterTable
ALTER TABLE "Service" DROP COLUMN "basePrice",
ADD COLUMN     "estimationHours" INTEGER,
ADD COLUMN     "outletId" INTEGER NOT NULL,
ADD COLUMN     "price" DOUBLE PRECISION NOT NULL DEFAULT 0;

-- DropTable
DROP TABLE "OutletService";

-- CreateIndex
CREATE UNIQUE INDEX "Service_outletId_name_key" ON "Service"("outletId", "name");

-- AddForeignKey
ALTER TABLE "Service" ADD CONSTRAINT "Service_outletId_fkey" FOREIGN KEY ("outletId") REFERENCES "Outlet"("id") ON DELETE CASCADE ON UPDATE CASCADE;
