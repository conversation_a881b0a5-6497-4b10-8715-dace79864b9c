"use client"

import type React from "react"

import { useState } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { ArrowLeft, Save, Clock, ImageIcon } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { toast } from "@/components/ui/use-toast"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

export default function AddServicePage() {
  const router = useRouter()
  const [formData, setFormData] = useState({
    name: "",
    price: "",
    unit: "Kg",
    category: "reguler",
    duration: "",
    durationType: "jam",
    description: "",
    isActive: true,
    // New fields
    processSteps: {
      cuci: true,
      kering: true,
      setrika: false,
      lipat: true,
      packaging: true,
    },
    labels: [],
    customLabel: "",
    group: "",
  })

  // Predefined labels for services
  const predefinedLabels = [
    "Pakaian",
    "Sepatu",
    "Tas",
    "Karpet",
    "Selimut",
    "Bed Cover",
    "Sprei",
    "Gorden",
    "Premium",
    "Hemat",
    "Cepat",
  ]

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSwitchChange = (checked: boolean) => {
    setFormData((prev) => ({ ...prev, isActive: checked }))
  }

  const handleProcessStepChange = (step: string, checked: boolean) => {
    setFormData((prev) => ({
      ...prev,
      processSteps: {
        ...prev.processSteps,
        [step]: checked,
      },
    }))
  }

  const handleLabelToggle = (label: string) => {
    setFormData((prev) => {
      const currentLabels = [...prev.labels]
      if (currentLabels.includes(label)) {
        return { ...prev, labels: currentLabels.filter((l) => l !== label) }
      } else {
        return { ...prev, labels: [...currentLabels, label] }
      }
    })
  }

  const addCustomLabel = () => {
    if (formData.customLabel.trim() && !formData.labels.includes(formData.customLabel.trim())) {
      setFormData((prev) => ({
        ...prev,
        labels: [...prev.labels, prev.customLabel.trim()],
        customLabel: "",
      }))
    }
  }

  const removeLabel = (label: string) => {
    setFormData((prev) => ({
      ...prev,
      labels: prev.labels.filter((l) => l !== label),
    }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // In a real app, you would save the service to your backend
    // For now, we'll just show a success message and redirect

    // Simulate API call
    setTimeout(() => {
      toast({
        title: "Layanan berhasil ditambahkan",
        description: `Layanan ${formData.name} telah berhasil ditambahkan.`,
      })
      router.push("/akun/services")
    }, 500)
  }

  return (
    <div className="flex flex-col min-h-screen bg-slate-50">
      <header className="p-4 flex justify-between items-center bg-white sticky top-0 z-10 shadow-sm">
        <div className="flex items-center">
          <Link href="/akun/services" className="mr-3">
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <h1 className="text-lg font-semibold">Tambah Layanan Baru</h1>
        </div>
        <Button type="submit" form="add-service-form" className="bg-blue-500 hover:bg-blue-600">
          <Save className="h-4 w-4 mr-2" /> Simpan
        </Button>
      </header>

      <main className="flex-1 p-4 pb-20">
        <form id="add-service-form" onSubmit={handleSubmit}>
          <Tabs defaultValue="basic" className="w-full">
            <TabsList className="grid w-full grid-cols-3 mb-4">
              <TabsTrigger value="basic">Informasi Dasar</TabsTrigger>
              <TabsTrigger value="process">Proses Produksi</TabsTrigger>
              <TabsTrigger value="grouping">Pengelompokan</TabsTrigger>
            </TabsList>

            <TabsContent value="basic">
              <Card className="p-4 space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Nama Layanan</Label>
                  <Input
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    placeholder="Contoh: Cuci + Setrika"
                    required
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="price">Harga</Label>
                    <div className="relative">
                      <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500">Rp</span>
                      <Input
                        id="price"
                        name="price"
                        type="number"
                        value={formData.price}
                        onChange={handleChange}
                        className="pl-10"
                        placeholder="0"
                        required
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="unit">Satuan</Label>
                    <Select
                      name="unit"
                      value={formData.unit}
                      onValueChange={(value) => handleSelectChange("unit", value)}
                    >
                      <SelectTrigger id="unit">
                        <SelectValue placeholder="Pilih satuan" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Kg">Kilogram (Kg)</SelectItem>
                        <SelectItem value="Pcs">Per Item (Pcs)</SelectItem>
                        <SelectItem value="Pasang">Pasang</SelectItem>
                        <SelectItem value="Meter">Meter</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Kategori Layanan</Label>
                  <RadioGroup
                    value={formData.category}
                    onValueChange={(value) => handleSelectChange("category", value)}
                    className="flex flex-wrap gap-4"
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="reguler" id="reguler" />
                      <Label htmlFor="reguler" className="cursor-pointer">
                        Reguler
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="express" id="express" />
                      <Label htmlFor="express" className="cursor-pointer">
                        Express
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="satuan" id="satuan" />
                      <Label htmlFor="satuan" className="cursor-pointer">
                        Satuan
                      </Label>
                    </div>
                  </RadioGroup>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="duration">Durasi Pengerjaan</Label>
                  <div className="flex gap-2">
                    <div className="relative flex-1">
                      <Clock className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-500" />
                      <Input
                        id="duration"
                        name="duration"
                        type="number"
                        value={formData.duration}
                        onChange={handleChange}
                        className="pl-10"
                        placeholder="Durasi"
                        required
                      />
                    </div>
                    <Select
                      name="durationType"
                      value={formData.durationType}
                      onValueChange={(value) => handleSelectChange("durationType", value)}
                      className="w-1/3"
                    >
                      <SelectTrigger id="durationType">
                        <SelectValue placeholder="Satuan" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="jam">Jam</SelectItem>
                        <SelectItem value="hari">Hari</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Deskripsi Layanan</Label>
                  <Textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleChange}
                    placeholder="Deskripsi singkat tentang layanan"
                    rows={3}
                  />
                </div>

                <div className="space-y-2">
                  <Label>Ikon Layanan</Label>
                  <div className="border-2 border-dashed rounded-md p-6 flex flex-col items-center justify-center bg-gray-50">
                    <ImageIcon className="h-10 w-10 text-gray-400 mb-2" />
                    <p className="text-sm text-gray-500 mb-2">Klik untuk mengunggah ikon layanan</p>
                    <p className="text-xs text-gray-400">PNG, JPG, atau SVG (maks. 2MB)</p>
                    <Button type="button" variant="outline" size="sm" className="mt-2">
                      Pilih File
                    </Button>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="isActive" className="cursor-pointer">
                    Status Layanan Aktif
                  </Label>
                  <Switch id="isActive" checked={formData.isActive} onCheckedChange={handleSwitchChange} />
                </div>
              </Card>
            </TabsContent>

            <TabsContent value="process">
              <Card className="p-4 space-y-4">
                <div className="space-y-2">
                  <Label className="text-base font-medium">Tahapan Proses Produksi</Label>
                  <p className="text-sm text-gray-500">Pilih tahapan proses yang diperlukan untuk layanan ini</p>

                  <div className="grid gap-3 pt-2">
                    <div className="flex items-center space-x-3">
                      <Checkbox
                        id="cuci"
                        checked={formData.processSteps.cuci}
                        onCheckedChange={(checked) => handleProcessStepChange("cuci", checked as boolean)}
                      />
                      <Label htmlFor="cuci" className="font-normal">
                        Cuci (Wash)
                      </Label>
                    </div>

                    <div className="flex items-center space-x-3">
                      <Checkbox
                        id="kering"
                        checked={formData.processSteps.kering}
                        onCheckedChange={(checked) => handleProcessStepChange("kering", checked as boolean)}
                      />
                      <Label htmlFor="kering" className="font-normal">
                        Kering (Dry)
                      </Label>
                    </div>

                    <div className="flex items-center space-x-3">
                      <Checkbox
                        id="setrika"
                        checked={formData.processSteps.setrika}
                        onCheckedChange={(checked) => handleProcessStepChange("setrika", checked as boolean)}
                      />
                      <Label htmlFor="setrika" className="font-normal">
                        Setrika (Iron)
                      </Label>
                    </div>

                    <div className="flex items-center space-x-3">
                      <Checkbox
                        id="lipat"
                        checked={formData.processSteps.lipat}
                        onCheckedChange={(checked) => handleProcessStepChange("lipat", checked as boolean)}
                      />
                      <Label htmlFor="lipat" className="font-normal">
                        Lipat (Fold)
                      </Label>
                    </div>

                    <div className="flex items-center space-x-3">
                      <Checkbox
                        id="packaging"
                        checked={formData.processSteps.packaging}
                        onCheckedChange={(checked) => handleProcessStepChange("packaging", checked as boolean)}
                      />
                      <Label htmlFor="packaging" className="font-normal">
                        Packaging
                      </Label>
                    </div>
                  </div>
                </div>

                <div className="pt-4">
                  <p className="text-sm text-gray-500 italic">
                    Tahapan proses yang dipilih akan digunakan untuk melacak progres pesanan dan membantu staf dalam
                    mengelola alur kerja.
                  </p>
                </div>
              </Card>
            </TabsContent>

            <TabsContent value="grouping">
              <Card className="p-4 space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="group">Grup Layanan</Label>
                  <Select
                    name="group"
                    value={formData.group}
                    onValueChange={(value) => handleSelectChange("group", value)}
                  >
                    <SelectTrigger id="group">
                      <SelectValue placeholder="Pilih grup layanan" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="pakaian">Pakaian</SelectItem>
                      <SelectItem value="rumah_tangga">Rumah Tangga</SelectItem>
                      <SelectItem value="sepatu_tas">Sepatu & Tas</SelectItem>
                      <SelectItem value="karpet">Karpet & Permadani</SelectItem>
                      <SelectItem value="lainnya">Lainnya</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Label Layanan</Label>
                  <p className="text-sm text-gray-500">
                    Pilih atau tambahkan label untuk memudahkan pencarian dan pengelompokan
                  </p>

                  <div className="flex flex-wrap gap-2 mt-2">
                    {predefinedLabels.map((label) => (
                      <Badge
                        key={label}
                        variant={formData.labels.includes(label) ? "default" : "outline"}
                        className="cursor-pointer"
                        onClick={() => handleLabelToggle(label)}
                      >
                        {label}
                      </Badge>
                    ))}
                  </div>

                  <div className="flex items-center gap-2 mt-3">
                    <Input
                      placeholder="Tambah label kustom"
                      value={formData.customLabel}
                      onChange={(e) => setFormData((prev) => ({ ...prev, customLabel: e.target.value }))}
                      onKeyDown={(e) => {
                        if (e.key === "Enter") {
                          e.preventDefault()
                          addCustomLabel()
                        }
                      }}
                    />
                    <Button
                      type="button"
                      variant="outline"
                      onClick={addCustomLabel}
                      disabled={!formData.customLabel.trim()}
                    >
                      Tambah
                    </Button>
                  </div>

                  {formData.labels.length > 0 && (
                    <div className="mt-4">
                      <Label>Label yang dipilih:</Label>
                      <div className="flex flex-wrap gap-2 mt-2">
                        {formData.labels.map((label) => (
                          <Badge key={label} variant="secondary" className="flex items-center gap-1">
                            {label}
                            <button
                              type="button"
                              className="ml-1 rounded-full h-4 w-4 inline-flex items-center justify-center text-xs hover:bg-gray-200"
                              onClick={() => removeLabel(label)}
                            >
                              ×
                            </button>
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </Card>
            </TabsContent>
          </Tabs>
        </form>
      </main>
    </div>
  )
}
