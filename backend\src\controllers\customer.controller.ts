import httpStatus from 'http-status';
import pick from '../utils/pick';
import ApiError from '../utils/ApiError';
import catchAsync from '../utils/catchAsync';
import { customerService } from '../services';
import { Request, Response } from 'express';

const createCustomer = catchAsync(async (req: Request, res: Response) => {
  const user = (req as any).user;
  const outletId = req.body.outletId || req.query.outletId;

  // Check if outletId is provided
  if (!outletId) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'outletId is required');
  }

  // Check if user has access to outlet (non-admin users can only access their assigned outlets)
  if (user.role !== 'ADMIN' && user.outletId && user.outletId !== parseInt(outletId)) {
    throw new ApiError(httpStatus.FORBIDDEN, 'Access denied to this outlet');
  }

  const customer = await customerService.createCustomer(req.body, parseInt(outletId));
  res.status(httpStatus.CREATED).send({ customer });
});

const getCustomers = catchAsync(async (req: Request, res: Response) => {
  const user = (req as any).user;
  const outletId = req.query.outletId;

  // Check if outletId is provided
  if (!outletId) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'outletId is required');
  }

  // Check if user has access to outlet (non-admin users can only access their assigned outlets)
  if (user.role !== 'ADMIN' && user.outletId && user.outletId !== parseInt(outletId as string)) {
    throw new ApiError(httpStatus.FORBIDDEN, 'Access denied to this outlet');
  }

  const filter = pick(req.query, [
    'search',
    'status',
    'customerType',
    'source',
    'labels',
    'provinceId',
    'cityId'
  ]);
  const options = pick(req.query, ['sortBy', 'limit', 'page']);

  const result = await customerService.queryCustomers(
    filter,
    options,
    parseInt(outletId as string),
    user.role
  );
  res.send(result);
});

const getCustomer = catchAsync(async (req: Request, res: Response) => {
  const user = (req as any).user;
  const outletId = req.query.outletId;
  const customerId = parseInt(req.params.customerId);

  // Check if outletId is provided
  if (!outletId) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'outletId is required');
  }

  // Check if user has access to outlet (non-admin users can only access their assigned outlets)
  if (user.role !== 'ADMIN' && user.outletId && user.outletId !== parseInt(outletId as string)) {
    throw new ApiError(httpStatus.FORBIDDEN, 'Access denied to this outlet');
  }

  const customer = await customerService.getCustomerById(
    customerId,
    parseInt(outletId as string),
    user.role
  );
  res.send(customer);
});

const updateCustomer = catchAsync(async (req: Request, res: Response) => {
  const user = (req as any).user;
  const outletId = req.body.outletId || req.query.outletId;
  const customerId = parseInt(req.params.customerId);

  // Check if outletId is provided
  if (!outletId) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'outletId is required');
  }

  // Check if user has access to outlet (non-admin users can only access their assigned outlets)
  if (user.role !== 'ADMIN' && user.outletId && user.outletId !== parseInt(outletId)) {
    throw new ApiError(httpStatus.FORBIDDEN, 'Access denied to this outlet');
  }

  const customer = await customerService.updateCustomerById(
    customerId,
    req.body,
    parseInt(outletId),
    user.role
  );
  res.send(customer);
});

const deleteCustomer = catchAsync(async (req: Request, res: Response) => {
  const user = (req as any).user;
  const outletId = req.query.outletId;
  const customerId = parseInt(req.params.customerId);

  // Check if outletId is provided
  if (!outletId) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'outletId is required');
  }

  // Check if user has access to outlet (non-admin users can only access their assigned outlets)
  if (user.role !== 'ADMIN' && user.outletId && user.outletId !== parseInt(outletId as string)) {
    throw new ApiError(httpStatus.FORBIDDEN, 'Access denied to this outlet');
  }

  await customerService.deleteCustomerById(customerId, parseInt(outletId as string), user.role);
  res.status(httpStatus.NO_CONTENT).send();
});

const createCustomerNote = catchAsync(async (req: Request, res: Response) => {
  const user = (req as any).user;
  const outletId = req.body.outletId || req.query.outletId;
  const customerId = parseInt(req.params.customerId);

  // Check if outletId is provided
  if (!outletId) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'outletId is required');
  }

  // Check if user has access to outlet (non-admin users can only access their assigned outlets)
  if (user.role !== 'ADMIN' && user.outletId && user.outletId !== parseInt(outletId)) {
    throw new ApiError(httpStatus.FORBIDDEN, 'Access denied to this outlet');
  }

  const note = await customerService.createCustomerNote(
    customerId,
    req.body,
    parseInt(outletId),
    user.role
  );
  res.status(httpStatus.CREATED).send(note);
});

const getCustomerNotes = catchAsync(async (req: Request, res: Response) => {
  const user = (req as any).user;
  const outletId = req.query.outletId;
  const customerId = parseInt(req.params.customerId);

  // Check if outletId is provided
  if (!outletId) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'outletId is required');
  }

  // Check if user has access to outlet (non-admin users can only access their assigned outlets)
  if (user.role !== 'ADMIN' && user.outletId && user.outletId !== parseInt(outletId as string)) {
    throw new ApiError(httpStatus.FORBIDDEN, 'Access denied to this outlet');
  }

  const notes = await customerService.getCustomerNotes(
    customerId,
    parseInt(outletId as string),
    user.role
  );
  res.send(notes);
});

export default {
  createCustomer,
  getCustomers,
  getCustomer,
  updateCustomer,
  deleteCustomer,
  createCustomerNote,
  getCustomerNotes
};
