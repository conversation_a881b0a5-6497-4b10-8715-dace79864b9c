"use client"

import { useState } from "react"
import Link from "next/link"
import { ArrowLeft, Plus, Search, Filter, MessageSquare, Clock, BarChart2, <PERSON><PERSON><PERSON>, History } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"

// Sample data for templates
const messageTemplates = [
  {
    id: "1",
    name: "<PERSON>n<PERSON>rma<PERSON> Pesanan",
    message:
      "<PERSON><PERSON> {nama}, pesanan laundry Anda dengan nomor {nomor_pesanan} telah kami terima. Terima kasih telah menggunakan jasa kami.",
    status: "active",
    category: "order",
    lastUsed: "2 jam yang lalu",
    usageCount: 128,
  },
  {
    id: "2",
    name: "<PERSON> <PERSON>esan<PERSON>",
    message:
      "<PERSON><PERSON> {nama}, pesanan laundry Anda dengan nomor {nomor_pesanan} saat ini dalam status {status_pesanan}. Estimasi selesai: {estimasi_selesai}.",
    status: "active",
    category: "order",
    lastUsed: "5 jam yang lalu",
    usageCount: 245,
  },
  {
    id: "3",
    name: "Pesanan Selesai",
    message:
      "Halo {nama}, pesanan laundry Anda dengan nomor {nomor_pesanan} telah selesai dan siap untuk diambil/diantar. Terima kasih.",
    status: "active",
    category: "order",
    lastUsed: "1 hari yang lalu",
    usageCount: 112,
  },
  {
    id: "4",
    name: "Pengingat Pembayaran",
    message:
      "Halo {nama}, ini adalah pengingat untuk pembayaran pesanan laundry Anda dengan nomor {nomor_pesanan} sebesar Rp {jumlah_tagihan}.",
    status: "active",
    category: "payment",
    lastUsed: "3 hari yang lalu",
    usageCount: 67,
  },
  {
    id: "5",
    name: "Promosi Bulanan",
    message: "Halo {nama}, dapatkan diskon 20% untuk layanan dry cleaning selama bulan ini. Gunakan kode: CLEAN20",
    status: "inactive",
    category: "promotion",
    lastUsed: "2 minggu yang lalu",
    usageCount: 320,
  },
]

// Sample data for automation rules
const automationRules = [
  {
    id: "1",
    name: "Konfirmasi Otomatis",
    trigger: "Pesanan Baru",
    template: "Konfirmasi Pesanan",
    status: "active",
    lastTriggered: "2 jam yang lalu",
    triggerCount: 128,
  },
  {
    id: "2",
    name: "Update Status",
    trigger: "Perubahan Status Pesanan",
    template: "Status Pesanan",
    status: "active",
    lastTriggered: "5 jam yang lalu",
    triggerCount: 245,
  },
  {
    id: "3",
    name: "Notifikasi Selesai",
    trigger: "Pesanan Selesai",
    template: "Pesanan Selesai",
    status: "active",
    lastTriggered: "1 hari yang lalu",
    triggerCount: 112,
  },
  {
    id: "4",
    name: "Pengingat Pembayaran",
    trigger: "Pembayaran Tertunda (24 jam)",
    template: "Pengingat Pembayaran",
    status: "active",
    lastTriggered: "3 hari yang lalu",
    triggerCount: 67,
  },
  {
    id: "5",
    name: "Promosi Bulanan",
    trigger: "Terjadwal (Bulanan)",
    template: "Promosi Bulanan",
    status: "inactive",
    lastTriggered: "2 minggu yang lalu",
    triggerCount: 320,
  },
]

// Sample data for message history
const messageHistory = [
  {
    id: "1",
    customer: "Ahmad Rizki",
    phone: "+6281234567890",
    template: "Konfirmasi Pesanan",
    status: "delivered",
    sentAt: "2 jam yang lalu",
  },
  {
    id: "2",
    customer: "Siti Nurhaliza",
    phone: "+6281234567891",
    template: "Status Pesanan",
    status: "delivered",
    sentAt: "3 jam yang lalu",
  },
  {
    id: "3",
    customer: "Budi Santoso",
    phone: "+6281234567892",
    template: "Pesanan Selesai",
    status: "delivered",
    sentAt: "5 jam yang lalu",
  },
  {
    id: "4",
    customer: "Dewi Kartika",
    phone: "+6281234567893",
    template: "Pengingat Pembayaran",
    status: "failed",
    sentAt: "1 hari yang lalu",
  },
  {
    id: "5",
    customer: "Eko Prasetyo",
    phone: "+6281234567894",
    template: "Promosi Bulanan",
    status: "delivered",
    sentAt: "2 hari yang lalu",
  },
]

export default function WhatsAppPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [activeTab, setActiveTab] = useState("templates")

  return (
    <div className="flex flex-col min-h-screen bg-slate-50">
      <header className="p-4 flex justify-between items-center bg-white sticky top-0 z-10 shadow-sm">
        <div className="flex items-center">
          <Link href="/akun" className="mr-3">
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <h1 className="text-lg font-semibold">WhatsApp Otomatis</h1>
        </div>
        <div className="flex items-center gap-2">
          {activeTab === "templates" && (
            <Link href="/akun/whatsapp/templates/add">
              <Button size="sm" className="bg-green-500 hover:bg-green-600">
                <Plus className="h-4 w-4 mr-1" /> Template
              </Button>
            </Link>
          )}
          {activeTab === "automation" && (
            <Link href="/akun/whatsapp/automation/add">
              <Button size="sm" className="bg-green-500 hover:bg-green-600">
                <Plus className="h-4 w-4 mr-1" /> Otomasi
              </Button>
            </Link>
          )}
          <Link href="/akun/whatsapp/settings">
            <Button size="sm" variant="outline">
              <Settings className="h-4 w-4" />
            </Button>
          </Link>
        </div>
      </header>

      <div className="p-4">
        <div className="flex items-center gap-2 mb-4">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
            <Input
              type="search"
              placeholder="Cari..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <Button variant="outline" size="icon">
            <Filter className="h-4 w-4" />
          </Button>
        </div>

        <Tabs defaultValue="templates" className="w-full" onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-4 mb-4">
            <TabsTrigger value="templates" className="flex items-center">
              <MessageSquare className="h-4 w-4 mr-1.5" /> Template
            </TabsTrigger>
            <TabsTrigger value="automation" className="flex items-center">
              <Clock className="h-4 w-4 mr-1.5" /> Otomasi
            </TabsTrigger>
            <TabsTrigger value="history" className="flex items-center">
              <History className="h-4 w-4 mr-1.5" /> Riwayat
            </TabsTrigger>
            <TabsTrigger value="analytics" className="flex items-center">
              <BarChart2 className="h-4 w-4 mr-1.5" /> Analitik
            </TabsTrigger>
          </TabsList>

          <TabsContent value="templates" className="space-y-4">
            {messageTemplates.map((template) => (
              <Link href={`/akun/whatsapp/templates/${template.id}`} key={template.id}>
                <Card className="hover:bg-slate-50 transition-colors">
                  <CardContent className="p-4">
                    <div className="flex justify-between items-start mb-2">
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <h3 className="font-medium">{template.name}</h3>
                          <Badge variant={template.status === "active" ? "default" : "secondary"} className="text-xs">
                            {template.status === "active" ? "Aktif" : "Nonaktif"}
                          </Badge>
                          <Badge variant="outline" className="text-xs">
                            {template.category === "order"
                              ? "Pesanan"
                              : template.category === "payment"
                                ? "Pembayaran"
                                : "Promosi"}
                          </Badge>
                        </div>
                        <p className="text-sm text-gray-500 mt-1 line-clamp-2">{template.message}</p>
                      </div>
                    </div>
                    <div className="flex justify-between text-xs text-gray-500 mt-2">
                      <span>Terakhir digunakan: {template.lastUsed}</span>
                      <span>Digunakan: {template.usageCount}x</span>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </TabsContent>

          <TabsContent value="automation" className="space-y-4">
            {automationRules.map((rule) => (
              <Link href={`/akun/whatsapp/automation/${rule.id}`} key={rule.id}>
                <Card className="hover:bg-slate-50 transition-colors">
                  <CardContent className="p-4">
                    <div className="flex justify-between items-start mb-2">
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <h3 className="font-medium">{rule.name}</h3>
                          <Badge variant={rule.status === "active" ? "default" : "secondary"} className="text-xs">
                            {rule.status === "active" ? "Aktif" : "Nonaktif"}
                          </Badge>
                        </div>
                        <div className="flex flex-col mt-1">
                          <p className="text-sm">
                            <span className="text-gray-500">Pemicu:</span> {rule.trigger}
                          </p>
                          <p className="text-sm">
                            <span className="text-gray-500">Template:</span> {rule.template}
                          </p>
                        </div>
                      </div>
                    </div>
                    <div className="flex justify-between text-xs text-gray-500 mt-2">
                      <span>Terakhir terpicu: {rule.lastTriggered}</span>
                      <span>Terpicu: {rule.triggerCount}x</span>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </TabsContent>

          <TabsContent value="history" className="space-y-4">
            {messageHistory.map((message) => (
              <Card key={message.id} className="hover:bg-slate-50 transition-colors">
                <CardContent className="p-4">
                  <div className="flex justify-between items-start mb-2">
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <h3 className="font-medium">{message.customer}</h3>
                        <Badge variant={message.status === "delivered" ? "default" : "destructive"} className="text-xs">
                          {message.status === "delivered" ? "Terkirim" : "Gagal"}
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-500">{message.phone}</p>
                      <p className="text-sm mt-1">
                        <span className="text-gray-500">Template:</span> {message.template}
                      </p>
                    </div>
                    <span className="text-xs text-gray-500">{message.sentAt}</span>
                  </div>
                </CardContent>
              </Card>
            ))}
          </TabsContent>

          <TabsContent value="analytics" className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <Card>
                <CardContent className="p-4">
                  <h3 className="text-sm font-medium text-gray-500 mb-1">Total Pesan Terkirim</h3>
                  <p className="text-2xl font-bold">872</p>
                  <p className="text-xs text-green-500">↑ 12% dari bulan lalu</p>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <h3 className="text-sm font-medium text-gray-500 mb-1">Tingkat Pengiriman</h3>
                  <p className="text-2xl font-bold">98.5%</p>
                  <p className="text-xs text-green-500">↑ 1.2% dari bulan lalu</p>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <h3 className="text-sm font-medium text-gray-500 mb-1">Template Terpopuler</h3>
                  <p className="text-lg font-medium">Status Pesanan</p>
                  <p className="text-xs text-gray-500">245 penggunaan</p>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <h3 className="text-sm font-medium text-gray-500 mb-1">Otomasi Terpopuler</h3>
                  <p className="text-lg font-medium">Update Status</p>
                  <p className="text-xs text-gray-500">245 pemicu</p>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardContent className="p-4">
                <h3 className="font-medium mb-4">Pengiriman Pesan (30 Hari Terakhir)</h3>
                <div className="h-40 bg-slate-100 rounded-md flex items-center justify-center">
                  <p className="text-gray-500">Grafik pengiriman pesan</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <h3 className="font-medium mb-4">Performa Template</h3>
                <div className="h-40 bg-slate-100 rounded-md flex items-center justify-center">
                  <p className="text-gray-500">Grafik performa template</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
