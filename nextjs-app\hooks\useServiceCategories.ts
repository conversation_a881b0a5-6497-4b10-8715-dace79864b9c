import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import {
  serviceCategoriesAPI,
  ServiceCategory,
  ServiceCategoryFilters,
  CreateServiceCategoryRequest,
  UpdateServiceCategoryRequest,
  ServiceCategorySelectItem,
} from '@/lib/api/service-categories';

// Query keys
const QUERY_KEYS = {
  serviceCategories: (filters: ServiceCategoryFilters) => [
    'serviceCategories',
    filters,
  ],
  serviceCategory: (id: number) => ['serviceCategory', id],
  serviceCategoriesSelect: ['serviceCategoriesSelect'],
  serviceCategoriesCount: (filters: { outletId?: number }) => [
    'serviceCategoriesCount',
    filters,
  ],
};

// Hooks for service categories
export const useServiceCategories = (filters: ServiceCategoryFilters = {}) => {
  return useQuery({
    queryKey: QUERY_KEYS.serviceCategories(filters),
    queryFn: () => serviceCategoriesAPI.getServiceCategories(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useServiceCategory = (id: number) => {
  return useQuery({
    queryKey: QUERY_KEYS.serviceCategory(id),
    queryFn: () => serviceCategoriesAPI.getServiceCategory(id),
    enabled: !!id,
  });
};

export const useServiceCategoriesForSelect = () => {
  return useQuery({
    queryKey: QUERY_KEYS.serviceCategoriesSelect,
    queryFn: () => serviceCategoriesAPI.getServiceCategoriesForSelect(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useServiceCategoriesCount = (
  filters: { outletId?: number } = {}
) => {
  return useQuery({
    queryKey: QUERY_KEYS.serviceCategoriesCount(filters),
    queryFn: () => serviceCategoriesAPI.getServiceCategoriesCount(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Mutations
export const useCreateServiceCategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateServiceCategoryRequest) =>
      serviceCategoriesAPI.createServiceCategory(data),
    onSuccess: (data) => {
      toast.success(data.message || 'Kategori layanan berhasil dibuat');
      queryClient.invalidateQueries({
        queryKey: ['serviceCategories'],
      });
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.serviceCategoriesSelect,
      });
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || 'Gagal membuat kategori layanan'
      );
    },
  });
};

export const useUpdateServiceCategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      id,
      data,
    }: {
      id: number;
      data: UpdateServiceCategoryRequest;
    }) => serviceCategoriesAPI.updateServiceCategory(id, data),
    onSuccess: (data) => {
      toast.success(data.message || 'Kategori layanan berhasil diperbarui');
      queryClient.invalidateQueries({
        queryKey: ['serviceCategories'],
      });
      queryClient.invalidateQueries({
        queryKey: ['serviceCategory', data.data.id],
      });
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.serviceCategoriesSelect,
      });
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || 'Gagal memperbarui kategori layanan'
      );
    },
  });
};

export const useDeleteServiceCategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => serviceCategoriesAPI.deleteServiceCategory(id),
    onSuccess: () => {
      toast.success('Kategori layanan berhasil dihapus');
      queryClient.invalidateQueries({
        queryKey: ['serviceCategories'],
      });
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.serviceCategoriesSelect,
      });
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || 'Gagal menghapus kategori layanan'
      );
    },
  });
};

// Helper functions
export const formatProductionProcess = (process: string[]): string => {
  if (!process || process.length === 0) return 'Tidak ada proses';

  const processNames: Record<string, string> = {
    sorting: 'Sortir',
    washing: 'Cuci',
    drying: 'Keringkan',
    ironing: 'Setrika',
    quality_control: 'Kontrol Kualitas',
    packaging: 'Kemas',
    delivery: 'Antar',
  };

  return process.map((p) => processNames[p] || p).join(' → ');
};

export const getServiceCategoryBadgeVariant = (
  servicesCount: number
): 'default' | 'secondary' | 'outline' => {
  if (servicesCount === 0) return 'outline';
  if (servicesCount < 5) return 'secondary';
  return 'default';
};
