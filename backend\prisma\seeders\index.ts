import { PrismaClient } from '@prisma/client';
import seedAdmin from './admin.seeder';
import seedLocation from './location.seeder';
import seedOutlet from './outlet.seeder';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');
  console.log('=====================================');

  try {
    // Run location seeder first (provinces and cities)
    await seedLocation();
    console.log('');

    // Run admin seeder
    await seedAdmin();
    console.log('');

    // Run outlet seeder
    await seedOutlet();
    console.log('');

    console.log('=====================================');
    console.log('🎉 All seeders completed successfully!');
    console.log('');
    console.log('📋 Summary:');
    console.log('   ✅ Location data (provinces & cities)');
    console.log('   ✅ Admin user');
    console.log('   ✅ Sample outlets');
    console.log('');
    console.log('🔐 Admin Login Credentials:');
    console.log('   📧 Email: <EMAIL>');
    console.log('   🔑 Password: admin123456');
  } catch (error) {
    console.error('❌ Seeding failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run seeder if called directly
if (require.main === module) {
  main();
}

export default main;
