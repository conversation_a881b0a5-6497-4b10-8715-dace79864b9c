import { Order, OrderItem, Payment, Prisma } from '@prisma/client';
import httpStatus from 'http-status';
import prisma from '../client';
import ApiError from '../utils/ApiError';
import { encryptPassword } from '../utils/encryption';
import { generateOrderNumber } from '../utils/orderUtils';
import logger from '../config/logger';
import customerDepositService from './customerDeposit.service';

/**
 * Helper function to get default cashbox based on payment method
 * @param {number} outletId
 * @param {string} paymentMethod
 * @returns {Promise<number | null>}
 */
const getDefaultCashboxId = async (
  outletId: number,
  paymentMethod: string
): Promise<number | null> => {
  try {
    // Tentukan type cashbox berdasarkan payment method
    let cashboxType: 'TUNAI' | 'NON_TUNAI';

    if (paymentMethod === 'CASH') {
      cashboxType = 'TUNAI';
    } else if (['TRANSFER', 'CREDIT_CARD', 'DEBIT_CARD', 'E_WALLET'].includes(paymentMethod)) {
      cashboxType = 'NON_TUNAI';
    } else {
      // Untuk payment method lain (seperti DEPOSIT), tidak perlu cashbox
      return null;
    }

    // Cari cashbox aktif pertama dengan type yang sesuai
    const cashbox = await prisma.cashbox.findFirst({
      where: {
        outletId,
        type: cashboxType,
        isActive: true
      },
      orderBy: {
        createdAt: 'asc' // Ambil yang pertama dibuat (biasanya default)
      }
    });

    if (!cashbox) {
      logger.warn(`No active ${cashboxType} cashbox found for outlet ${outletId}`);
      return null;
    }

    logger.debug(
      `Auto-selected cashbox: ${cashbox.name} (ID: ${cashbox.id}) for payment method: ${paymentMethod}`
    );
    return cashbox.id;
  } catch (error) {
    logger.error('Error getting default cashbox:', error);
    return null;
  }
};

/**
 * Create a new order
 * @param {Object} orderBody
 * @param {number} userId - User yang membuat order
 * @returns {Promise<Order>}
 */
const createOrder = async (
  orderBody: any,
  userId: number
): Promise<Order & { items: OrderItem[] }> => {
  const { customerId, items, perfumeId, promoId, outletId, payment, ...orderData } = orderBody;

  logger.info(`Creating order for customer ${customerId} at outlet ${outletId}`, {
    customerId,
    outletId,
    itemsCount: items.length,
    perfumeId,
    hasPayment: !!payment
  });

  // Verify customer exists and belongs to outlet
  const customerExists = await prisma.customer.findFirst({
    where: { id: customerId },
    select: { id: true, outletId: true, name: true }
  });

  if (!customerExists) {
    logger.error(`Customer not found: ${customerId}`);
    throw new ApiError(httpStatus.BAD_REQUEST, 'Customer not found');
  }

  if (customerExists.outletId !== outletId) {
    logger.error(`Customer ${customerId} does not belong to outlet ${outletId}`);
    throw new ApiError(httpStatus.FORBIDDEN, 'Customer does not belong to your outlet');
  }

  logger.debug(`Customer verified: ${customerExists.name} (${customerId})`);

  // Verify perfume exists and belongs to outlet if provided
  let perfumeData: {
    id: number;
    name: string;
    description: string | null;
    brand: string | null;
    scent: string | null;
  } | null = null;

  if (perfumeId) {
    perfumeData = await prisma.perfume.findFirst({
      where: {
        id: perfumeId,
        outletId: outletId,
        isActive: true
      },
      select: {
        id: true,
        name: true,
        description: true,
        brand: true,
        scent: true
      }
    });

    if (!perfumeData) {
      logger.error(`Perfume not found or inactive: ${perfumeId} at outlet ${outletId}`);
      throw new ApiError(httpStatus.BAD_REQUEST, 'Perfume not found or inactive');
    }

    logger.debug(`Perfume selected: ${perfumeData.name} (${perfumeId})`);
  }

  // Verify promotion exists and is active if provided
  let promotionData: {
    id: number;
    code: string;
    discountValue: number;
    discountType: string;
  } | null = null;

  if (promoId) {
    promotionData = await prisma.promotion.findFirst({
      where: {
        id: promoId,
        outletId: outletId,
        isActive: true,
        validFrom: { lte: new Date() },
        validUntil: { gte: new Date() }
      },
      select: {
        id: true,
        code: true,
        discountValue: true,
        discountType: true
      }
    });

    if (!promotionData) {
      logger.error(`Promotion not found or inactive: ${promoId} at outlet ${outletId}`);
      throw new ApiError(httpStatus.BAD_REQUEST, 'Promotion not found or inactive');
    }

    logger.debug(`Promotion selected: ${promotionData.code} (${promoId})`);
  }

  // Verify all services belong to outlet and get service data for denormalization
  const serviceIds = items.map((item: any) => item.serviceId);
  const outletServices = await prisma.service.findMany({
    where: {
      outletId: outletId,
      id: { in: serviceIds }
    },
    select: {
      id: true,
      name: true,
      description: true,
      price: true,
      unit: true,
      estimationHours: true,
      icon: true
    }
  });

  if (outletServices.length !== serviceIds.length) {
    logger.error(
      `Service validation failed. Expected ${serviceIds.length}, found ${outletServices.length}`,
      {
        expectedServices: serviceIds,
        foundServices: outletServices.map((s) => s.id)
      }
    );
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      'One or more services are not available in your outlet'
    );
  }

  logger.debug(`Services verified: ${outletServices.map((s) => s.name).join(', ')}`);

  // Create service map for easy lookup
  const serviceMap = new Map(outletServices.map((service) => [service.id, service]));

  // Calculate total price
  const originalTotalPrice = items.reduce((sum: number, item: any) => sum + item.subtotal, 0);

  // Calculate discount amount if promotion is applied
  let discountAmount = 0;
  if (promotionData) {
    if (promotionData.discountType === 'PERCENTAGE') {
      discountAmount = (originalTotalPrice * promotionData.discountValue) / 100;
    } else {
      discountAmount = promotionData.discountValue;
    }
  }

  // Final total price after discount
  const totalPrice = originalTotalPrice - discountAmount;

  // Validasi payment jika ada
  if (payment) {
    if (payment.amount > totalPrice) {
      throw new ApiError(httpStatus.BAD_REQUEST, 'Payment amount exceeds order total');
    }

    // Validasi deposit jika metode pembayaran DEPOSIT
    if (payment.method === 'DEPOSIT') {
      const customer = await prisma.customer.findFirst({
        where: { id: customerId, outletId },
        include: { financialData: true }
      });

      if (!customer?.financialData || Number(customer.financialData.deposit) < payment.amount) {
        throw new ApiError(httpStatus.BAD_REQUEST, 'Saldo deposit tidak mencukupi');
      }
    }

    // Validasi cashbox jika bukan deposit
    if (payment.cashboxId && payment.method !== 'DEPOSIT') {
      const cashbox = await prisma.cashbox.findFirst({
        where: { id: payment.cashboxId, outletId, isActive: true }
      });
      if (!cashbox) {
        throw new ApiError(httpStatus.NOT_FOUND, 'Cashbox tidak ditemukan atau tidak aktif');
      }
    }
  }

  // Generate order number
  const orderNumber = await generateOrderNumber(outletId);

  logger.info(`Generated order number: ${orderNumber}`, {
    originalTotalPrice,
    discountAmount,
    totalPrice,
    perfumeId: perfumeData?.id,
    promoId: promotionData?.id,
    paymentAmount: payment?.amount
  });

  // Create order with items in transaction (without payment)
  const order = await prisma.$transaction(async (tx) => {
    // Hitung payment status (tanpa payment dulu)
    let paymentStatus: 'UNPAID' | 'PARTIAL' | 'PAID' = 'UNPAID';
    let paidAmount = 0;

    const createdOrder = await tx.order.create({
      data: {
        orderNumber,
        outletId,
        customerId,
        totalPrice,
        paidAmount,
        paymentStatus,
        paymentMethod: payment?.method,
        perfumeId: perfumeData?.id,
        perfumeName: perfumeData?.name,
        perfumeDescription: perfumeData?.description,
        promotionId: promotionData?.id,
        promotionCode: promotionData?.code,
        promotionDiscount: discountAmount,
        status: 'PENDING',
        ...orderData
      }
    });

    logger.debug(`Order created with ID: ${createdOrder.id}`);

    // Create initial order status history
    await tx.orderStatusHistory.create({
      data: {
        orderId: createdOrder.id,
        previousStatus: null,
        newStatus: 'PENDING',
        changedBy: userId,
        notes: 'Order created'
      }
    });

    // Create order items
    const createdItems = await Promise.all(
      items.map((item: any) => {
        const serviceData = serviceMap.get(item.serviceId);
        if (!serviceData) {
          logger.error(`Service data not found in map: ${item.serviceId}`);
          throw new ApiError(httpStatus.BAD_REQUEST, `Service with ID ${item.serviceId} not found`);
        }

        logger.debug(`Creating order item: ${serviceData.name} x ${item.quantity}`);

        return tx.orderItem.create({
          data: {
            orderId: createdOrder.id,
            serviceId: item.serviceId,
            quantity: item.quantity,
            unit: item.unit || serviceData.unit,
            price: item.price,
            subtotal: item.subtotal,
            notes: item.notes,
            status: item.status || 'PENDING',
            serviceName: serviceData.name,
            serviceDescription: serviceData.description,
            serviceUnit: serviceData.unit,
            serviceEstimationHours: serviceData.estimationHours,
            icon: serviceData.icon
          }
        });
      })
    );

    logger.info(
      `Order ${createdOrder.orderNumber} created successfully with ${createdItems.length} items`,
      {
        orderId: createdOrder.id,
        orderNumber: createdOrder.orderNumber,
        customerId,
        totalPrice,
        paymentStatus,
        perfumeName: perfumeData?.name,
        promotionCode: promotionData?.code,
        promotionDiscount: createdOrder.promotionDiscount
      }
    );

    return { ...createdOrder, items: createdItems };
  });

  // Process payment setelah order dibuat (di luar transaction)
  if (payment) {
    // Auto-select cashbox jika tidak ada cashboxId yang diberikan
    let finalCashboxId = payment.cashboxId;
    if (!finalCashboxId && payment.method !== 'DEPOSIT') {
      finalCashboxId = await getDefaultCashboxId(outletId, payment.method);
    }

    logger.debug(`Processing payment: ${payment.method}`, {
      orderId: order.id,
      amount: payment.amount,
      method: payment.method,
      originalCashboxId: payment.cashboxId,
      finalCashboxId: finalCashboxId
    });

    try {
      if (payment.method === 'DEPOSIT') {
        logger.debug('Using deposit service for payment');
        // Gunakan deposit service untuk pembayaran
        await customerDepositService.payWithDeposit(
          customerId,
          outletId,
          order.id,
          payment.amount,
          userId,
          payment.reference || `Payment for order ${orderNumber}`
        );
      } else {
        logger.debug('Creating regular payment record');
        // Buat payment record biasa dalam transaction terpisah
        await prisma.$transaction(async (tx) => {
          await tx.payment.create({
            data: {
              orderId: order.id,
              amount: payment.amount,
              method: payment.method,
              cashboxId: finalCashboxId,
              reference: payment.reference,
              notes: payment.notes
            }
          });

          // Update order payment status
          const newPaidAmount = payment.amount;
          let newPaymentStatus: 'PARTIAL' | 'PAID' | 'UNPAID' = 'PARTIAL';

          logger.debug('Payment status calculation:', {
            newPaidAmount,
            totalPrice,
            originalTotalPrice,
            discountAmount,
            promotionCode: promotionData?.code
          });

          if (newPaidAmount >= totalPrice) {
            newPaymentStatus = 'PAID';
          } else if (newPaidAmount === 0) {
            newPaymentStatus = 'UNPAID';
          }

          await tx.order.update({
            where: { id: order.id },
            data: {
              paidAmount: newPaidAmount,
              paymentStatus: newPaymentStatus as any
            }
          });

          // Update cashbox balance jika ada cashboxId
          if (finalCashboxId) {
            logger.debug(`Updating cashbox balance: ${finalCashboxId}`);
            await tx.cashbox.update({
              where: { id: finalCashboxId },
              data: { balance: { increment: payment.amount } }
            });
          }
        });
      }

      logger.info(`Payment processed: ${payment.method} - ${payment.amount}`, {
        orderId: order.id,
        paymentMethod: payment.method,
        cashboxId: finalCashboxId
      });
    } catch (paymentError) {
      logger.error('Payment processing failed:', paymentError);
      // Jangan throw error, karena order sudah dibuat
      logger.warn('Order created but payment failed', {
        orderId: order.id,
        paymentError: paymentError instanceof Error ? paymentError.message : String(paymentError)
      });
    }
  }

  return order;
};

/**
 * Query for orders
 * @param {Object} filter - Mongo filter
 * @param {Object} options - Query options
 * @param {string} [options.sortBy] - Sort option in the format: sortField:(desc|asc)
 * @param {number} [options.limit] - Maximum number of results per page (default = 10)
 * @param {number} [options.page] - Current page (default = 1)
 * @param {number} outletId
 * @returns {Promise<QueryResult>}
 */
const queryOrders = async (filter: any, options: any) => {
  const search = filter.search;
  const sortBy = options.sortBy;
  const limit = options.limit || 10;
  const page = options.page || 1;
  const paymentStatus = filter.paymentStatus;
  const status = filter.status;
  const customerId = filter.customerId;
  const dateFrom = filter.dateFrom;
  const dateTo = filter.dateTo;
  const outletId = filter.outletId;
  const summaryFilter = filter.filter;
  const processingStatus = filter.processingStatus;

  // Build where condition
  const where: Prisma.OrderWhereInput = {
    outletId: outletId
  };

  if (outletId) {
    where.outletId = outletId;
  }

  // Add filters
  if (paymentStatus) {
    where.paymentStatus = paymentStatus;
  }

  if (status) {
    where.status = status;
  }

  if (customerId) {
    where.customerId = customerId;
  }

  // Handle summary filter (masuk, hari-ini, besok, lusa, terlambat)
  if (summaryFilter) {
    const now = new Date();
    const startOfToday = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const endOfToday = new Date(startOfToday.getTime() + 24 * 60 * 60 * 1000 - 1);
    const startOfTomorrow = new Date(startOfToday.getTime() + 24 * 60 * 60 * 1000);
    const endOfTomorrow = new Date(startOfTomorrow.getTime() + 24 * 60 * 60 * 1000 - 1);
    const startOfDayAfterTomorrow = new Date(startOfTomorrow.getTime() + 24 * 60 * 60 * 1000);
    const endOfDayAfterTomorrow = new Date(
      startOfDayAfterTomorrow.getTime() + 24 * 60 * 60 * 1000 - 1
    );

    switch (summaryFilter) {
      case 'masuk':
        // Orders that came in today
        where.createdAt = {
          gte: startOfToday,
          lte: endOfToday
        };
        break;

      case 'hari-ini':
        // Orders that should be completed today
        where.estimatedFinish = {
          gte: startOfToday,
          lte: endOfToday
        };
        // Only count active orders
        where.status = {
          in: ['KONFIRMASI', 'PICKUP', 'PENDING', 'PROCESSING']
        };
        break;

      case 'besok':
        // Orders that should be completed tomorrow
        where.estimatedFinish = {
          gte: startOfTomorrow,
          lte: endOfTomorrow
        };
        // Only count active orders
        where.status = {
          in: ['KONFIRMASI', 'PICKUP', 'PENDING', 'PROCESSING']
        };
        break;

      case 'lusa':
        // Orders that should be completed day after tomorrow
        where.estimatedFinish = {
          gte: startOfDayAfterTomorrow,
          lte: endOfDayAfterTomorrow
        };
        // Only count active orders
        where.status = {
          in: ['KONFIRMASI', 'PICKUP', 'PENDING', 'PROCESSING']
        };
        break;

      case 'terlambat':
        // Orders that are overdue
        where.estimatedFinish = {
          lt: startOfToday
        };
        // Only count active orders (not completed or cancelled)
        where.status = {
          in: ['KONFIRMASI', 'PICKUP', 'PENDING', 'PROCESSING']
        };
        break;
    }
  }

  // Handle processing status filter
  if (processingStatus) {
    where.items = {
      some: {
        status: processingStatus
      }
    };
  }

  if (dateFrom || dateTo) {
    // Only apply custom date range if no summary filter is active
    if (!summaryFilter) {
      where.createdAt = {};
      if (dateFrom) {
        where.createdAt.gte = new Date(dateFrom);
      }
      if (dateTo) {
        where.createdAt.lte = new Date(dateTo);
      }
    }
  }

  if (search) {
    where.OR = [
      { orderNumber: { contains: search, mode: 'insensitive' } },
      { notes: { contains: search, mode: 'insensitive' } },
      { customer: { name: { contains: search, mode: 'insensitive' } } }
    ];
  }

  // Build orderBy
  let orderBy: Prisma.OrderOrderByWithRelationInput = { createdAt: 'desc' };
  if (sortBy) {
    const [field, direction] = sortBy.split(':');
    orderBy = { [field]: direction === 'desc' ? 'desc' : 'asc' };
  }

  // Execute query
  const [orders, totalResults] = await Promise.all([
    prisma.order.findMany({
      where,
      orderBy,
      skip: (page - 1) * limit,
      take: limit,
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            phone: true
          }
        },
        items: {
          select: {
            id: true,
            quantity: true,
            unit: true,
            price: true,
            subtotal: true,
            notes: true,
            status: true,
            icon: true,
            // Denormalized service data
            serviceName: true,
            serviceDescription: true,
            serviceUnit: true,
            serviceEstimationHours: true,
            // Original service reference (optional)
            serviceId: true,
            service: {
              select: {
                id: true,
                name: true,
                unit: true
              }
            }
          }
        }
      }
    }),
    prisma.order.count({ where })
  ]);

  const totalPages = Math.ceil(totalResults / limit);

  return {
    results: orders,
    page,
    limit,
    totalPages,
    totalResults
  };
};

/**
 * Get order by id
 * @param {number} id
 * @param {number} outletId
 * @returns {Promise<Order>}
 */
const getOrderById = async (id: number, outletId?: number): Promise<Order | null> => {
  const order = await prisma.order.findFirst({
    where: {
      id,
      outletId: outletId || undefined
    },
    include: {
      customer: {
        select: {
          id: true,
          name: true,
          phone: true,
          email: true,
          address: true
        }
      },
      items: {
        select: {
          id: true,
          quantity: true,
          unit: true,
          price: true,
          subtotal: true,
          notes: true,
          status: true,
          icon: true,
          // Denormalized service data (prioritized)
          serviceName: true,
          serviceDescription: true,
          serviceUnit: true,
          serviceEstimationHours: true,
          // Original service reference
          serviceId: true,
          service: {
            select: {
              id: true,
              name: true,
              unit: true,
              description: true
            }
          }
        }
      },
      payments: {
        orderBy: {
          createdAt: 'desc'
        }
      },
      perfume: {
        select: {
          id: true,
          name: true,
          description: true,
          brand: true,
          scent: true
        }
      }
    }
  });

  if (order) {
    logger.debug(`Order retrieved: ${order.orderNumber}`, {
      orderId: id,
      status: order.status,
      itemsCount: order.items.length,
      perfumeName: order.perfumeName || order.perfume?.name
    });
  }

  return order;
};

/**
 * Update order by id
 * @param {number} orderId
 * @param {Object} updateBody
 * @param {number} outletId
 * @returns {Promise<Order>}
 */
const updateOrderById = async (
  orderId: number,
  updateBody: any,
  outletId: number
): Promise<Order> => {
  const order = await getOrderById(orderId, outletId);
  if (!order) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Order not found');
  }

  const updatedOrder = await prisma.order.update({
    where: {
      id: orderId
    },
    data: updateBody
  });

  return updatedOrder;
};

/**
 * Update order items (for orders without payments)
 * @param {number} orderId
 * @param {any} itemsData
 * @param {number} outletId
 * @param {number} userId
 * @returns {Promise<Order>}
 */
const updateOrderItems = async (
  orderId: number,
  itemsData: {
    toUpdate?: Array<{ id: number; quantity?: number; unit?: string; notes?: string }>;
    toDelete?: number[];
    toAdd?: Array<{ serviceId: number; quantity: number; unit: string; notes?: string }>;
  },
  outletId: number,
  userId: number
): Promise<Order> => {
  logger.info(`Updating order items for order ${orderId}`, {
    orderId,
    outletId,
    userId,
    updateCount: itemsData.toUpdate?.length || 0,
    deleteCount: itemsData.toDelete?.length || 0,
    addCount: itemsData.toAdd?.length || 0
  });

  // Verify order exists and belongs to outlet
  const order = await prisma.order.findFirst({
    where: { id: orderId, outletId },
    include: {
      items: true,
      payments: true
    }
  });

  if (!order) {
    logger.error(`Order not found: ${orderId} at outlet ${outletId}`);
    throw new ApiError(httpStatus.NOT_FOUND, 'Order not found');
  }

  // Check if order has payments - cannot modify items if payments exist
  if (order.payments && order.payments.length > 0) {
    logger.error(`Cannot modify order items - payments already exist for order ${orderId}`);
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      'Cannot modify order items - payments already exist'
    );
  }

  logger.debug(`Order ${orderId} verified, no payments found. Proceeding with item updates.`);

  // Perform updates in transaction
  const updatedOrder = await prisma.$transaction(async (tx) => {
    // Handle deletions
    if (itemsData.toDelete && itemsData.toDelete.length > 0) {
      logger.debug(`Deleting ${itemsData.toDelete.length} order items`, {
        itemIds: itemsData.toDelete
      });

      const deletedItems = await tx.orderItem.findMany({
        where: { id: { in: itemsData.toDelete }, orderId },
        select: { id: true, serviceName: true, quantity: true, unit: true }
      });

      await tx.orderItem.deleteMany({
        where: { id: { in: itemsData.toDelete }, orderId }
      });

      // Record deletion in order status history
      for (const deletedItem of deletedItems) {
        await tx.orderStatusHistory.create({
          data: {
            orderId,
            previousStatus: order.status,
            newStatus: order.status, // Status tidak berubah
            changedBy: userId,
            notes: `Deleted item: ${deletedItem.serviceName} (${deletedItem.quantity} ${deletedItem.unit})`
          }
        });
      }

      logger.debug(`Deleted ${deletedItems.length} order items and recorded history`);
    }

    // Handle updates
    if (itemsData.toUpdate && itemsData.toUpdate.length > 0) {
      logger.debug(`Updating ${itemsData.toUpdate.length} order items`);

      for (const updateItem of itemsData.toUpdate) {
        const currentItem = await tx.orderItem.findFirst({
          where: { id: updateItem.id, orderId },
          select: {
            id: true,
            serviceName: true,
            quantity: true,
            unit: true,
            price: true,
            serviceUnit: true
          }
        });

        if (!currentItem) {
          logger.warn(`Order item not found for update: ${updateItem.id}`);
          continue;
        }

        // Calculate new subtotal if quantity changed
        const newQuantity = updateItem.quantity ?? currentItem.quantity;
        const newUnit = updateItem.unit ?? currentItem.unit;
        const newSubtotal = newQuantity * currentItem.price;

        const updatedItem = await tx.orderItem.update({
          where: { id: updateItem.id },
          data: {
            quantity: newQuantity,
            unit: newUnit,
            subtotal: newSubtotal,
            notes: updateItem.notes,
            updatedAt: new Date()
          }
        });

        // Record update in order status history
        const changes = [];
        if (updateItem.quantity !== undefined && updateItem.quantity !== currentItem.quantity) {
          changes.push(`quantity: ${currentItem.quantity} → ${newQuantity}`);
        }
        if (updateItem.unit !== undefined && updateItem.unit !== currentItem.unit) {
          changes.push(`unit: ${currentItem.unit} → ${newUnit}`);
        }

        if (changes.length > 0) {
          await tx.orderStatusHistory.create({
            data: {
              orderId,
              previousStatus: order.status,
              newStatus: order.status, // Status tidak berubah
              changedBy: userId,
              notes: `Updated item ${currentItem.serviceName}: ${changes.join(', ')}`
            }
          });
        }

        logger.debug(`Updated order item ${updateItem.id}: ${changes.join(', ')}`);
      }
    }

    // Handle additions
    if (itemsData.toAdd && itemsData.toAdd.length > 0) {
      logger.debug(`Adding ${itemsData.toAdd.length} new order items`);

      // Verify all services belong to outlet
      const serviceIds = itemsData.toAdd.map((item) => item.serviceId);
      const outletServices = await tx.service.findMany({
        where: {
          outletId: outletId,
          id: { in: serviceIds }
        },
        select: {
          id: true,
          name: true,
          description: true,
          price: true,
          unit: true,
          estimationHours: true
        }
      });

      if (outletServices.length !== serviceIds.length) {
        logger.error(
          `Service validation failed for new items. Expected ${serviceIds.length}, found ${outletServices.length}`
        );
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          'One or more services are not available in your outlet'
        );
      }

      const serviceMap = new Map(outletServices.map((service) => [service.id, service]));

      const newItems = await Promise.all(
        itemsData.toAdd.map(async (item) => {
          const serviceData = serviceMap.get(item.serviceId);
          if (!serviceData) {
            throw new ApiError(
              httpStatus.BAD_REQUEST,
              `Service with ID ${item.serviceId} not found`
            );
          }

          const subtotal = item.quantity * serviceData.price;

          const newItem = await tx.orderItem.create({
            data: {
              orderId,
              serviceId: item.serviceId,
              quantity: item.quantity,
              unit: item.unit,
              price: serviceData.price,
              subtotal,
              notes: item.notes,
              status: 'PENDING',

              // Denormalized service data
              serviceName: serviceData.name,
              serviceDescription: serviceData.description,
              serviceUnit: serviceData.unit,
              serviceEstimationHours: serviceData.estimationHours
            }
          });

          // Record addition in order status history
          await tx.orderStatusHistory.create({
            data: {
              orderId,
              previousStatus: order.status,
              newStatus: order.status, // Status tidak berubah
              changedBy: userId,
              notes: `Added item: ${serviceData.name} (${item.quantity} ${item.unit})`
            }
          });

          logger.debug(`Added new order item: ${serviceData.name} x ${item.quantity} ${item.unit}`);

          return newItem;
        })
      );

      logger.debug(`Added ${newItems.length} new order items`);
    }

    // Recalculate order totals
    const allItems = await tx.orderItem.findMany({
      where: { orderId },
      select: { subtotal: true }
    });

    const newTotalPrice = allItems.reduce((sum, item) => sum + item.subtotal, 0);

    const updatedOrder = await tx.order.update({
      where: { id: orderId },
      data: {
        totalPrice: newTotalPrice,
        updatedAt: new Date()
      },
      include: {
        items: {
          select: {
            id: true,
            quantity: true,
            unit: true,
            price: true,
            subtotal: true,
            notes: true,
            status: true,
            serviceName: true,
            serviceDescription: true,
            serviceUnit: true,
            serviceEstimationHours: true,
            serviceId: true,
            service: {
              select: { name: true, unit: true }
            }
          }
        },
        customer: {
          select: {
            id: true,
            name: true,
            phone: true,
            email: true
          }
        },
        perfume: {
          select: {
            id: true,
            name: true,
            description: true,
            brand: true,
            scent: true
          }
        }
      }
    });

    logger.info(`Order items updated successfully for order ${orderId}`, {
      orderId,
      orderNumber: updatedOrder.orderNumber,
      newTotalPrice,
      totalItems: allItems.length
    });

    return updatedOrder;
  });

  return updatedOrder;
};

/**
 * Update order status by id
 * @param {number} orderId
 * @param {string} status
 * @param {number} outletId
 * @param {number} userId
 * @param {string} notes
 * @returns {Promise<Order>}
 */
const updateOrderStatus = async (
  orderId: number,
  status: string,
  outletId: number,
  userId: number,
  notes?: string
): Promise<Order> => {
  logger.info(`Updating order status: ${orderId} to ${status}`, {
    orderId,
    newStatus: status,
    userId,
    notes
  });

  const order = await prisma.order.findFirst({
    where: { id: orderId, outletId },
    include: {
      payments: true,
      customer: { select: { id: true } }
    }
  });

  if (!order) {
    logger.error(`Order not found for status update: ${orderId}`);
    throw new ApiError(httpStatus.NOT_FOUND, 'Order not found');
  }

  logger.debug(`Current order status: ${order.status} -> ${status}`, {
    orderNumber: order.orderNumber,
    currentStatus: order.status
  });

  const updateData: any = { status };

  // Set actualFinish when status changes to COMPLETED
  if (status === 'COMPLETED') {
    updateData.actualFinish = new Date();
  }

  // Update order status and create history in transaction
  const result = await prisma.$transaction(async (tx) => {
    // Handle CANCELLED status - cancel all order items and refund if paid
    if (status === 'CANCELLED') {
      // Cancel all order items
      await tx.orderItem.updateMany({
        where: { orderId },
        data: { status: 'CANCELLED' }
      });

      // If order has been paid (partial or full), refund to cashbox
      if (order.paidAmount > 0 && order.payments && order.payments.length > 0) {
        for (const payment of order.payments) {
          if (payment.cashboxId && payment.method !== 'DEPOSIT') {
            // Reduce cashbox balance by payment amount
            await tx.cashbox.update({
              where: { id: payment.cashboxId },
              data: { balance: { decrement: payment.amount } }
            });

            logger.info(
              `Refunded ${payment.amount} to cashbox ${payment.cashboxId} for cancelled order ${order.orderNumber}`
            );
          }

          // Handle deposit refund
          if (payment.method === 'DEPOSIT') {
            await tx.customerFinancial.update({
              where: { customerId: order.customerId },
              data: { deposit: { increment: payment.amount } }
            });

            // Create deposit transaction record
            await tx.customerDepositTransaction.create({
              data: {
                customerId: order.customerId,
                outletId: outletId,
                type: 'DEPOSIT',
                amount: payment.amount,
                reference: `Refund for cancelled order ${order.orderNumber}`,
                orderId: orderId,
                createdBy: userId
              }
            });

            logger.info(
              `Refunded ${payment.amount} deposit to customer ${order.customerId} for cancelled order ${order.orderNumber}`
            );
          }
        }

        // Update order payment status to REFUNDED
        updateData.paymentStatus = 'REFUNDED';
        updateData.paidAmount = 0;
      }

      logger.info(`Order ${order.orderNumber} cancelled with all items and payments refunded`);
    }
    // Update order status
    const updatedOrder = await tx.order.update({
      where: {
        id: orderId
      },
      data: updateData
    });

    // Create status history record
    await tx.orderStatusHistory.create({
      data: {
        orderId,
        previousStatus: order.status as any,
        newStatus: status as any,
        changedBy: userId,
        notes
      }
    });

    logger.info(
      `Order status updated successfully: ${order.orderNumber} ${order.status} -> ${status}`,
      {
        orderId,
        orderNumber: order.orderNumber,
        previousStatus: order.status,
        newStatus: status,
        userId
      }
    );

    return updatedOrder;
  });

  return result;
};

/**
 * Delete order by id
 * @param {number} orderId
 * @param {number} outletId
 * @returns {Promise<Order>}
 */
const deleteOrderById = async (orderId: number, outletId: number): Promise<void> => {
  const order = await getOrderById(orderId, outletId);
  if (!order) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Order not found');
  }

  await prisma.order.delete({
    where: {
      id: orderId
    }
  });
};

/**
 * Add payment to order
 * @param {number} orderId
 * @param {Object} paymentBody
 * @param {number} outletId
 * @param {number} userId
 * @returns {Promise<Payment>}
 */
const addPaymentToOrder = async (
  orderId: number,
  paymentBody: any,
  outletId: number,
  userId: number
): Promise<Payment> => {
  const order = await getOrderById(orderId, outletId);
  if (!order) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Order not found');
  }

  const { amount, method, cashboxId } = paymentBody;
  const remainingBalance = order.totalPrice - order.paidAmount;

  if (amount > remainingBalance) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Payment amount exceeds remaining balance');
  }

  // Jika metode pembayaran DEPOSIT, gunakan service deposit
  if (method === 'DEPOSIT') {
    const depositTx = await customerDepositService.payWithDeposit(
      order.customerId,
      outletId,
      orderId,
      amount,
      userId,
      paymentBody.reference || `Payment for order ${order.orderNumber}`
    );

    // Return payment record yang dibuat oleh deposit service
    const payment = await prisma.payment.findFirst({
      where: {
        orderId,
        reference: `DEPOSIT-${depositTx.id}`
      }
    });
    return payment!;
  }

  // Auto-select cashbox jika tidak ada cashboxId yang diberikan
  let finalCashboxId = cashboxId;
  if (!finalCashboxId && method !== 'DEPOSIT') {
    finalCashboxId = await getDefaultCashboxId(outletId, method);
  }

  // Validasi cashbox jika ada
  if (finalCashboxId) {
    const cashbox = await prisma.cashbox.findFirst({
      where: { id: finalCashboxId, outletId, isActive: true }
    });
    if (!cashbox) {
      throw new ApiError(httpStatus.NOT_FOUND, 'Cashbox tidak ditemukan atau tidak aktif');
    }
  }

  // Create payment and update order in transaction
  const result = await prisma.$transaction(async (tx) => {
    const payment = await tx.payment.create({
      data: {
        orderId,
        amount,
        method,
        cashboxId: finalCashboxId,
        reference: paymentBody.reference,
        notes: paymentBody.notes
      }
    });

    const newPaidAmount = order.paidAmount + amount;
    let newPaymentStatus: 'PARTIAL' | 'PAID' | 'UNPAID' = 'PARTIAL';

    if (newPaidAmount >= order.totalPrice) {
      newPaymentStatus = 'PAID';
    } else if (newPaidAmount === 0) {
      newPaymentStatus = 'UNPAID';
    }

    await tx.order.update({
      where: { id: orderId },
      data: {
        paidAmount: newPaidAmount,
        paymentStatus: newPaymentStatus as any,
        paymentMethod: method
      }
    });

    // Update cashbox balance jika ada cashboxId dan bukan deposit
    if (finalCashboxId && method !== 'DEPOSIT') {
      await tx.cashbox.update({
        where: { id: finalCashboxId },
        data: { balance: { increment: amount } }
      });
    }

    return payment;
  });

  return result;
};

/**
 * Update order item status by id
 * @param {number} orderId
 * @param {number} itemId
 * @param {string} status
 * @param {number} outletId
 * @param {number} userId
 * @param {string} notes
 * @returns {Promise<OrderItem>}
 */
const updateOrderItemStatus = async (
  orderId: number,
  itemId: number,
  status: string,
  outletId: number,
  userId: number,
  notes?: string
): Promise<any> => {
  logger.info(`Updating order item status: ${itemId} to ${status}`, {
    orderId,
    itemId,
    newStatus: status,
    userId,
    notes
  });

  // First verify order exists and belongs to outlet
  const order = await getOrderById(orderId, outletId);
  if (!order) {
    logger.error(`Order not found for item status update: ${orderId}`);
    throw new ApiError(httpStatus.NOT_FOUND, 'Order not found');
  }

  // Verify order item exists and belongs to the order
  const orderItem = await prisma.orderItem.findFirst({
    where: {
      id: itemId,
      orderId: orderId
    }
  });

  if (!orderItem) {
    logger.error(`Order item not found: ${itemId} in order ${orderId}`);
    throw new ApiError(httpStatus.NOT_FOUND, 'Order item not found');
  }

  logger.debug(`Updating item status: ${orderItem.serviceName} ${orderItem.status} -> ${status}`, {
    serviceName: orderItem.serviceName,
    currentStatus: orderItem.status
  });

  // Update order item status and create history in transaction
  const result = await prisma.$transaction(async (tx) => {
    // Update order item status
    const updatedOrderItem = await tx.orderItem.update({
      where: {
        id: itemId
      },
      data: {
        status: status as any
      }
    });

    // Create status history record
    await tx.orderItemStatusHistory.create({
      data: {
        orderItemId: itemId,
        orderId,
        previousStatus: orderItem.status as any,
        newStatus: status as any,
        changedBy: userId,
        notes
      }
    });

    // Check if all order items are completed to auto-update order status
    const allOrderItems = await tx.orderItem.findMany({
      where: {
        orderId: orderId
      },
      select: {
        status: true
      }
    });

    // Get order details to check if it has delivery date
    const orderDetails = await tx.order.findUnique({
      where: { id: orderId },
      select: { deliveryDate: true, status: true }
    });

    const allCompleted = allOrderItems.every((item) => item.status === 'COMPLETED');
    const hasProcessingItems = allOrderItems.some((item) =>
      ['WASHING', 'DRYING', 'IRONING', 'PACKING'].includes(item.status as string)
    );

    // Auto-update order status based on item statuses
    if (allCompleted) {
      // If all items completed, set order to READY or READY_FOR_PICKUP based on delivery date
      const newOrderStatus = orderDetails?.deliveryDate ? 'READY_FOR_PICKUP' : 'READY';

      await tx.order.update({
        where: { id: orderId },
        data: {
          status: newOrderStatus,
          actualFinish: new Date()
        }
      });
      logger.info(`Order auto-updated to ${newOrderStatus}: all items completed`, { orderId });
    } else if (hasProcessingItems && orderDetails?.status === 'PENDING') {
      // Set order to PROCESSING if any item moved from PENDING to processing stages
      await tx.order.update({
        where: { id: orderId },
        data: { status: 'PROCESSING' }
      });
      logger.info(`Order auto-updated to PROCESSING: has items in progress`, { orderId });
    }

    logger.info(
      `Order item status updated successfully: ${orderItem.serviceName} ${orderItem.status} -> ${status}`,
      {
        orderId,
        itemId,
        serviceName: orderItem.serviceName,
        previousStatus: orderItem.status,
        newStatus: status,
        userId
      }
    );

    return updatedOrderItem;
  });

  return result;
};

/**
 * Get order status history
 * @param {number} orderId
 * @param {number} outletId
 * @returns {Promise<Array>}
 */
const getOrderStatusHistory = async (orderId: number, outletId: number): Promise<any[]> => {
  // Verify order exists and belongs to outlet
  const order = await getOrderById(orderId, outletId);
  if (!order) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Order not found');
  }

  const history = await prisma.orderStatusHistory.findMany({
    where: {
      orderId: orderId
    },
    include: {
      user: {
        select: {
          id: true,
          name: true,
          email: true
        }
      }
    },
    orderBy: {
      createdAt: 'desc'
    }
  });

  return history;
};

/**
 * Get order item status history
 * @param {number} orderId
 * @param {number} itemId
 * @param {number} outletId
 * @returns {Promise<Array>}
 */
const getOrderItemStatusHistory = async (
  orderId: number,
  itemId: number,
  outletId: number
): Promise<any[]> => {
  // Verify order exists and belongs to outlet
  const order = await getOrderById(orderId, outletId);
  if (!order) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Order not found');
  }

  // Verify order item exists
  const orderItem = await prisma.orderItem.findFirst({
    where: {
      id: itemId,
      orderId: orderId
    }
  });

  if (!orderItem) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Order item not found');
  }

  const history = await prisma.orderItemStatusHistory.findMany({
    where: {
      orderItemId: itemId,
      orderId: orderId
    },
    include: {
      user: {
        select: {
          id: true,
          name: true,
          email: true
        }
      }
    },
    orderBy: {
      createdAt: 'desc'
    }
  });

  return history;
};

/**
 * Get complete order timeline (order + all items history)
 * @param {number} orderId
 * @param {number} outletId
 * @returns {Promise<Object>}
 */
const getOrderTimeline = async (orderId: number, outletId: number): Promise<any> => {
  // Verify order exists and belongs to outlet
  const order = await getOrderById(orderId, outletId);
  if (!order) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Order not found');
  }

  // Get order status history
  const orderHistory = await prisma.orderStatusHistory.findMany({
    where: {
      orderId: orderId
    },
    include: {
      user: {
        select: {
          id: true,
          name: true,
          email: true
        }
      }
    },
    orderBy: {
      createdAt: 'desc'
    }
  });

  // Get all order items status history
  const itemsHistory = await prisma.orderItemStatusHistory.findMany({
    where: {
      orderId: orderId
    },
    include: {
      user: {
        select: {
          id: true,
          name: true,
          email: true
        }
      },
      orderItem: {
        select: {
          id: true,
          serviceName: true,
          serviceUnit: true,
          quantity: true
        }
      }
    },
    orderBy: {
      createdAt: 'desc'
    }
  });

  // Combine and sort all events by timestamp
  const timeline = [
    ...orderHistory.map((h) => ({
      ...h,
      type: 'ORDER_STATUS',
      entityId: orderId,
      entityName: `Order ${order.orderNumber}`
    })),
    ...itemsHistory.map((h) => ({
      ...h,
      type: 'ORDER_ITEM_STATUS',
      entityId: h.orderItemId,
      entityName: `${h.orderItem.serviceName} (${h.orderItem.quantity} ${h.orderItem.serviceUnit})`
    }))
  ].sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

  return {
    orderId,
    orderNumber: order.orderNumber,
    timeline
  };
};

/**
 * Get order summary counts for dashboard
 * @param {any} user - Authenticated user
 * @returns {Promise<Object>}
 */
const getOrderSummary = async (
  user: any
): Promise<{
  masuk: number;
  hariIni: number;
  besok: number;
  lusa: number;
  terlambat: number;
}> => {
  try {
    // Get date ranges for today, tomorrow, and day after tomorrow
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const tomorrow = new Date(today.getTime() + 24 * 60 * 60 * 1000);
    const dayAfterTomorrow = new Date(today.getTime() + 48 * 60 * 60 * 1000);
    const endOfToday = new Date(today.getTime() + 24 * 60 * 60 * 1000 - 1);
    const endOfTomorrow = new Date(tomorrow.getTime() + 24 * 60 * 60 * 1000 - 1);
    const endOfDayAfterTomorrow = new Date(dayAfterTomorrow.getTime() + 24 * 60 * 60 * 1000 - 1);

    // Build base where condition based on user role
    let baseWhere: any = {};

    if (user.role === 'EMPLOYEE' && user.outletId) {
      // Employee can only see orders from their outlet
      baseWhere.outletId = user.outletId;
    } else if (user.role === 'OWNER') {
      // Owner can see orders from all their outlets
      const ownerOutlets = await prisma.outlet.findMany({
        where: { ownerId: user.id },
        select: { id: true }
      });
      const outletIds = ownerOutlets.map((outlet) => outlet.id);
      baseWhere.outletId = { in: outletIds };
    }
    // ADMIN can see all orders (no additional where condition)

    // Count orders that came in today (masuk)
    const masuk = await prisma.order.count({
      where: {
        ...baseWhere,
        createdAt: {
          gte: today,
          lte: endOfToday
        }
      }
    });

    // Count orders that should be completed today (hariIni)
    // Only count orders with status KONFIRMASI/PICKUP/PENDING/PROCESSING
    const hariIni = await prisma.order.count({
      where: {
        ...baseWhere,
        status: {
          in: ['KONFIRMASI', 'PICKUP', 'PENDING', 'PROCESSING']
        },
        estimatedFinish: {
          gte: today,
          lte: endOfToday
        }
      }
    });

    // Count orders that should be completed tomorrow (besok)
    const besok = await prisma.order.count({
      where: {
        ...baseWhere,
        status: {
          in: ['KONFIRMASI', 'PICKUP', 'PENDING', 'PROCESSING']
        },
        estimatedFinish: {
          gte: tomorrow,
          lte: endOfTomorrow
        }
      }
    });

    // Count orders that should be completed day after tomorrow (lusa)
    const lusa = await prisma.order.count({
      where: {
        ...baseWhere,
        status: {
          in: ['KONFIRMASI', 'PICKUP', 'PENDING', 'PROCESSING']
        },
        estimatedFinish: {
          gte: dayAfterTomorrow,
          lte: endOfDayAfterTomorrow
        }
      }
    });

    // Count overdue orders (terlambat)
    // Orders that should have been completed before now but still in progress
    const terlambat = await prisma.order.count({
      where: {
        ...baseWhere,
        status: {
          in: ['KONFIRMASI', 'PICKUP', 'PENDING', 'PROCESSING']
        },
        estimatedFinish: {
          lt: now
        }
      }
    });

    logger.info('Order summary calculated', {
      userId: user.id,
      userRole: user.role,
      outletId: user.outletId,
      summary: { masuk, hariIni, besok, lusa, terlambat }
    });

    return {
      masuk,
      hariIni,
      besok,
      lusa,
      terlambat
    };
  } catch (error) {
    logger.error('Error calculating order summary:', error);
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to calculate order summary');
  }
};

export default {
  createOrder,
  queryOrders,
  getOrderById,
  updateOrderById,
  updateOrderItems,
  updateOrderStatus,
  deleteOrderById,
  addPaymentToOrder,
  updateOrderItemStatus,
  getOrderStatusHistory,
  getOrderItemStatusHistory,
  getOrderTimeline,
  getOrderSummary
};
