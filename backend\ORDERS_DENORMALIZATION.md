# Order Denormalization & OrderItem Status - Data Produk Snapshot

## Overview

Implementasi pendekatan denormalisasi untuk menyimpan "snapshot" data produk/service pada saat pesanan dibuat dengan tambahan fitur status individual untuk setiap OrderItem. Ini memastikan independensi data pesanan dari perubahan service di masa depan dan tracking progress yang lebih detail.

## Perubahan Schema Database

### Order Model

Menambahkan field untuk parfum selection:

```prisma
model Order {
  // ... existing fields ...

  // Parfum selection
  perfumeId             Int?               // ID parfum yang dipilih
  perfumeName           String?            // Snapshot nama parfum saat order dibuat
  perfumeDescription    String?            // Snapshot deskripsi parfum

  // Relasi
  perfume               Perfume?           @relation(fields: [perfumeId], references: [id])
}
```

### Perfume Model

Model baru untuk manajemen parfum:

```prisma
model Perfume {
  id                    Int                @id @default(autoincrement())
  name                  String
  description           String?
  brand                 String?
  scent                 String?            // Jenis aroma (fresh, floral, citrus, dll)
  isActive              Boolean            @default(true)

  // Relasi
  outlet                Outlet             @relation(fields: [outletId], references: [id])
  outletId              Int
  orders                Order[]

  @@unique([outletId, name])
}
```

### OrderItem Model

Menambahkan field denormalized untuk menyimpan snapshot data service dan status individual:

```prisma
model OrderItem {
  // ... existing fields ...
  status                OrderItemStatus    @default(PENDING)

  // Denormalized service data (snapshot saat order dibuat)
  serviceName           String             // Snapshot nama service
  serviceDescription    String?            // Snapshot deskripsi service
  serviceUnit           String             // Snapshot unit service (kg, pcs, etc)
  serviceEstimationHours Int?              // Snapshot estimasi jam pengerjaan

  // Relasi - tetap simpan referensi ke service untuk keperluan analisis
  service               Service?           @relation(fields: [serviceId], references: [id])
  serviceId             Int?               // Optional untuk backward compatibility
}

enum OrderItemStatus {
  PENDING       // Menunggu diproses
  PROCESSING    // Sedang diproses
  WASHING       // Sedang dicuci
  DRYING        // Sedang dikeringkan
  IRONING       // Sedang disetrika
  READY         // Siap diambil
  COMPLETED     // Selesai
  CANCELLED     // Dibatalkan
}
```

## Fitur Baru - OrderItem Status

### 1. Individual Item Tracking

Setiap OrderItem memiliki status terpisah untuk tracking progress yang lebih detail:

- Status dapat diupdate secara individual per item
- Auto-update status Order berdasarkan status semua OrderItem
- Tracking progress yang lebih granular

### 2. Auto-Update Order Status

Logic otomatis untuk update status Order berdasarkan status OrderItem:

- Jika semua item COMPLETED → Order = READY + actualFinish
- Jika semua item READY/COMPLETED → Order = READY
- Jika ada item PROCESSING/WASHING/DRYING/IRONING → Order = PROCESSING

### 3. API Endpoint Baru

```
PATCH /orders/{orderId}/items/{itemId}/status
```

## Keuntungan Pendekatan Ini

1. **🔒 Data Independency**: Order data tidak terpengaruh jika service data berubah
2. **📋 Historical Accuracy**: Menyimpan data service persis seperti saat order dibuat
3. **🔄 Backward Compatibility**: Tetap ada referensi ke service asli
4. **⚡ Performance**: Mengurangi JOIN query kompleks
5. **💾 Data Integrity**: Snapshot memastikan konsistensi historis
6. **📊 Detailed Tracking**: Status individual per item untuk monitoring yang lebih baik
7. **🔍 Comprehensive Logging**: Logging detail di setiap operasi untuk audit trail
8. **🌸 Perfume Integration**: Sistem parfum terintegrasi dengan snapshot data

## API Endpoints

### 1. Update OrderItem Status

```http
PATCH /orders/{orderId}/items/{itemId}/status
Authorization: Bearer {token}
Content-Type: application/json

{
  "status": "PROCESSING",
  "notes": "Mulai proses pencucian"
}
```

### 2. Update Order Status

```http
PATCH /orders/{orderId}/status
Authorization: Bearer {token}
Content-Type: application/json

{
  "status": "PROCESSING",
  "notes": "Order dipindah ke tahap proses"
}
```

### 3. Get Order Status History

```http
GET /orders/{orderId}/history
Authorization: Bearer {token}
```

### 4. Get OrderItem Status History

```http
GET /orders/{orderId}/items/{itemId}/history
Authorization: Bearer {token}
```

### 5. Get Complete Order Timeline

```http
GET /orders/{orderId}/timeline
Authorization: Bearer {token}
```

**Response:**

```json
{
  "id": 1,
  "orderId": 123,
  "serviceId": 45,
  "status": "PROCESSING",
  "serviceName": "Cuci Setrika",
  "serviceUnit": "kg",
  "quantity": 2.5,
  "price": 10000,
  "subtotal": 25000,
  "updatedAt": "2024-01-15T10:30:00Z"
}
```

**Timeline Response Example:**

```json
{
  "orderId": 123,
  "orderNumber": "ORD/240115/001",
  "timeline": [
    {
      "id": 45,
      "type": "ORDER_ITEM_STATUS",
      "entityId": 456,
      "entityName": "Cuci Setrika (2.5 kg)",
      "previousStatus": "PROCESSING",
      "newStatus": "WASHING",
      "changedBy": 1,
      "notes": "Mulai proses pencucian",
      "createdAt": "2024-01-15T11:00:00Z",
      "user": {
        "id": 1,
        "name": "John Doe",
        "email": "<EMAIL>"
      }
    },
    {
      "id": 44,
      "type": "ORDER_STATUS",
      "entityId": 123,
      "entityName": "Order ORD/240115/001",
      "previousStatus": "PENDING",
      "newStatus": "PROCESSING",
      "changedBy": 1,
      "notes": "Order dipindah ke tahap proses",
      "createdAt": "2024-01-15T10:30:00Z",
      "user": {
        "id": 1,
        "name": "John Doe",
        "email": "<EMAIL>"
      }
    }
  ]
}
```

## Contoh Data Order Item

```json
{
  "id": 1,
  "orderId": 123,
  "serviceId": 45,
  "quantity": 2.5,
  "weight": 2.5,
  "price": 10000,
  "subtotal": 25000,
  "notes": "Setrika rapi",
  "status": "WASHING",

  // Denormalized service data (snapshot)
  "serviceName": "Cuci Setrika",
  "serviceDescription": "Layanan cuci dan setrika pakaian",
  "serviceUnit": "kg",
  "serviceEstimationHours": 24,

  // Original service reference (untuk analisis)
  "service": {
    "id": 45,
    "name": "Cuci Setrika Premium", // Mungkin sudah berubah
    "unit": "kg"
  }
}
```

## Status Flow OrderItem

```
PENDING → PROCESSING → WASHING → DRYING → IRONING → READY → COMPLETED
                                      ↓
                                  CANCELLED
```

## Migration Database

Perubahan schema sudah di-push ke database menggunakan:

```bash
npx prisma db push
npx prisma generate
```

## Best Practices

1. **Prioritas Data**: Gunakan denormalized data sebagai sumber utama
2. **Status Tracking**: Update status OrderItem sesuai progress aktual
3. **Auto-Update**: Manfaatkan auto-update Order status dari OrderItem
4. **Validation**: Pastikan denormalized data konsisten saat create order
5. **Monitoring**: Gunakan status individual untuk monitoring progress detail

## Backward Compatibility

- Existing orders tetap berfungsi dengan service relation
- New orders akan memiliki denormalized data dan status
- API response tetap konsisten untuk kedua tipe data
- Default status PENDING untuk OrderItem baru
