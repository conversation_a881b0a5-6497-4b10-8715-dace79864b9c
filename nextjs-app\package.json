{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack -p 3001", "build": "next build", "start": "next start -p 3001", "lint": "next lint"}, "dependencies": {"@auth/prisma-adapter": "^2.9.1", "@getbrevo/brevo": "^2.2.0", "@hello-pangea/dnd": "latest", "@hookform/resolvers": "^5.0.1", "@prisma/client": "^6.8.2", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "latest", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "latest", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "latest", "@radix-ui/react-tooltip": "1.1.6", "@tanstack/react-query": "^5.77.0", "autoprefixer": "^10.4.20", "axios": "^1.9.0", "bcrypt": "^6.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "latest", "embla-carousel-react": "8.5.1", "form-data": "^4.0.2", "input-otp": "1.4.1", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.454.0", "mailgun.js": "^12.0.1", "next": "15.2.4", "next-auth": "^4.24.11", "next-themes": "^0.4.4", "prisma": "^6.8.2", "react": "^19", "react-day-picker": "8.10.1", "react-dom": "^19", "react-hook-form": "^7.54.1", "react-resizable-panels": "^2.1.7", "recharts": "2.15.0", "sonner": "^1.7.4", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.6", "zod": "^3.25.23"}, "devDependencies": {"@tanstack/react-query-devtools": "^5.77.2", "@types/bcrypt": "^5.0.2", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "eslint-config-next": "15.3.4", "postcss": "^8", "tailwindcss": "^3.4.17", "typescript": "^5"}}