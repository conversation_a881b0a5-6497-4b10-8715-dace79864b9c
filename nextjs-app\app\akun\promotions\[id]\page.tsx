'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import {
  ArrowLeft,
  Calendar,
  Percent,
  DollarSign,
  Tag,
  Edit,
  Trash2,
  Users,
  BarChart2,
} from 'lucide-react';
import { format } from 'date-fns';
import { id } from 'date-fns/locale';

import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Progress } from '@/components/ui/progress';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { useToast } from '@/components/ui/use-toast';
import { usePromotion, useDeletePromotion } from '@/hooks/usePromotions';

export default function PromotionDetailPage({
  params,
}: {
  params: { id: string };
}) {
  const router = useRouter();
  const { toast } = useToast();
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  const promotionId = parseInt(params.id);
  const {
    data: promotion,
    isLoading,
    isError,
    refetch,
  } = usePromotion(promotionId);
  const deletePromotionMutation = useDeletePromotion();

  const handleDelete = async () => {
    try {
      await deletePromotionMutation.mutateAsync(promotionId);
      setShowDeleteDialog(false);

      toast({
        title: 'Berhasil',
        description: 'Promosi berhasil dihapus',
      });

      router.push('/akun/promotions');
    } catch (error: any) {
      console.error('Error deleting promotion:', error);
      toast({
        title: 'Error',
        description:
          error?.response?.data?.message || 'Gagal menghapus promosi',
        variant: 'destructive',
      });
    }
  };

  const getStatusBadge = (promo: any) => {
    const now = new Date();
    const validFrom = new Date(promo.validFrom);
    const validUntil = new Date(promo.validUntil);

    let status = 'inactive';
    if (promo.isActive) {
      if (now < validFrom) {
        status = 'upcoming';
      } else if (now >= validFrom && now <= validUntil) {
        status = 'active';
      } else {
        status = 'inactive';
      }
    }

    switch (status) {
      case 'active':
        return <Badge className="bg-green-500">Aktif</Badge>;
      case 'inactive':
        return <Badge className="bg-gray-500">Tidak Aktif</Badge>;
      case 'upcoming':
        return <Badge className="bg-blue-500">Akan Datang</Badge>;
      default:
        return <Badge className="bg-gray-500">Tidak Aktif</Badge>;
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'd MMMM yyyy', { locale: id });
    } catch (error) {
      return dateString;
    }
  };

  if (isLoading) {
    return (
      <div className="flex flex-col min-h-screen bg-slate-50">
        <header className="p-4 flex items-center bg-white sticky top-0 z-10 shadow-sm">
          <Link href="/akun/promotions" className="mr-3">
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <h1 className="text-xl font-semibold">Detail Promosi</h1>
        </header>

        <main className="flex-1 p-4 pb-20">
          <div className="space-y-6">
            {/* Loading skeleton */}
            <Card>
              <CardContent className="p-6">
                <div className="animate-pulse">
                  <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
                  <div className="h-4 bg-gray-200 rounded w-2/3 mb-6"></div>
                  <div className="grid grid-cols-2 gap-4">
                    {[...Array(6)].map((_, i) => (
                      <div key={i}>
                        <div className="h-3 bg-gray-200 rounded w-1/2 mb-2"></div>
                        <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </main>
      </div>
    );
  }

  if (isError || !promotion) {
    return (
      <div className="flex flex-col min-h-screen bg-slate-50">
        <header className="p-4 flex items-center bg-white sticky top-0 z-10 shadow-sm">
          <Link href="/akun/promotions" className="mr-3">
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <h1 className="text-xl font-semibold">Detail Promosi</h1>
        </header>

        <main className="flex-1 p-4 pb-20 flex items-center justify-center">
          <div className="text-center">
            <Tag className="h-12 w-12 mx-auto text-gray-300 mb-4" />
            <h3 className="text-lg font-medium mb-2">
              Promosi tidak ditemukan
            </h3>
            <p className="text-gray-500 mb-4">
              Promosi yang Anda cari tidak ada atau telah dihapus
            </p>
            <div className="flex gap-2 justify-center">
              <Button onClick={() => refetch()}>Coba Lagi</Button>
              <Button
                variant="outline"
                onClick={() => router.push('/akun/promotions')}
              >
                Kembali ke Daftar
              </Button>
            </div>
          </div>
        </main>
      </div>
    );
  }

  const usagePercentage =
    promotion.usageLimit && promotion.usageLimit > 0
      ? (promotion.usageCount / promotion.usageLimit) * 100
      : 0;

  return (
    <div className="flex flex-col min-h-screen bg-slate-50">
      <header className="p-4 flex items-center bg-white sticky top-0 z-10 shadow-sm">
        <Link href="/akun/promotions" className="mr-3">
          <ArrowLeft className="h-5 w-5" />
        </Link>
        <h1 className="text-xl font-semibold">Detail Promosi</h1>
      </header>

      <main className="flex-1 p-4 pb-20">
        <div className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardContent className="p-6">
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h2 className="text-2xl font-bold">{promotion.name}</h2>
                  <p className="text-gray-600 mt-1">{promotion.description}</p>
                </div>
                {getStatusBadge(promotion)}
              </div>

              <div className="grid grid-cols-2 sm:grid-cols-3 gap-6 mt-6">
                <div>
                  <p className="text-sm text-gray-500 flex items-center mb-1">
                    <Tag className="h-4 w-4 mr-1" />
                    Kode Promosi
                  </p>
                  <p className="font-semibold text-lg">{promotion.code}</p>
                </div>

                <div>
                  <p className="text-sm text-gray-500 flex items-center mb-1">
                    {promotion.discountType === 'PERCENTAGE' ? (
                      <Percent className="h-4 w-4 mr-1" />
                    ) : (
                      <DollarSign className="h-4 w-4 mr-1" />
                    )}
                    Nilai Diskon
                  </p>
                  <p className="font-semibold text-lg">
                    {promotion.discountType === 'PERCENTAGE'
                      ? `${promotion.discountValue}%`
                      : `Rp ${promotion.discountValue.toLocaleString()}`}
                  </p>
                </div>

                <div>
                  <p className="text-sm text-gray-500 mb-1">Min. Pembelian</p>
                  <p className="font-semibold text-lg">
                    {promotion.minOrderValue > 0
                      ? `Rp ${promotion.minOrderValue.toLocaleString()}`
                      : 'Tidak ada'}
                  </p>
                </div>

                {promotion.maxDiscountAmount && (
                  <div>
                    <p className="text-sm text-gray-500 mb-1">Max. Diskon</p>
                    <p className="font-semibold text-lg">
                      Rp {promotion.maxDiscountAmount.toLocaleString()}
                    </p>
                  </div>
                )}

                <div>
                  <p className="text-sm text-gray-500 flex items-center mb-1">
                    <Calendar className="h-4 w-4 mr-1" />
                    Periode Berlaku
                  </p>
                  <p className="font-semibold">
                    {formatDate(promotion.validFrom)}
                  </p>
                  <p className="text-sm text-gray-500">
                    s/d {formatDate(promotion.validUntil)}
                  </p>
                </div>

                <div>
                  <p className="text-sm text-gray-500 mb-1">
                    Khusus Pelanggan Baru
                  </p>
                  <p className="font-semibold text-lg">
                    {promotion.isFirstTimeOnly ? 'Ya' : 'Tidak'}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Usage Statistics */}
          <Card>
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold mb-4 flex items-center">
                <BarChart2 className="h-5 w-5 mr-2" />
                Statistik Penggunaan
              </h3>

              <div className="grid grid-cols-1 sm:grid-cols-3 gap-6">
                <div>
                  <p className="text-sm text-gray-500 flex items-center mb-1">
                    <Users className="h-4 w-4 mr-1" />
                    Total Penggunaan
                  </p>
                  <p className="text-2xl font-bold">
                    {promotion.usageCount}
                    {promotion.usageLimit && promotion.usageLimit > 0 && (
                      <span className="text-sm text-gray-500 font-normal">
                        /{promotion.usageLimit}
                      </span>
                    )}
                  </p>
                </div>

                {promotion.usageLimit && promotion.usageLimit > 0 && (
                  <div>
                    <p className="text-sm text-gray-500 mb-1">
                      Persentase Terpakai
                    </p>
                    <p className="text-2xl font-bold">
                      {usagePercentage.toFixed(1)}%
                    </p>
                  </div>
                )}

                <div>
                  <p className="text-sm text-gray-500 mb-1">Status</p>
                  <p className="text-lg font-semibold">
                    {promotion.isActive ? 'Aktif' : 'Tidak Aktif'}
                  </p>
                </div>
              </div>

              {promotion.usageLimit && promotion.usageLimit > 0 && (
                <div className="mt-6">
                  <div className="flex justify-between text-sm text-gray-500 mb-2">
                    <span>Progress Penggunaan</span>
                    <span>
                      {promotion.usageCount} dari {promotion.usageLimit}
                    </span>
                  </div>
                  <Progress value={usagePercentage} className="h-2" />
                </div>
              )}
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="flex gap-4">
            <Link
              href={`/akun/promotions/edit/${promotion.id}`}
              className="flex-1"
            >
              <Button className="w-full">
                <Edit className="h-4 w-4 mr-2" />
                Edit Promosi
              </Button>
            </Link>
            <Button
              variant="destructive"
              className="flex-1"
              onClick={() => setShowDeleteDialog(true)}
              disabled={deletePromotionMutation.isPending}
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Hapus Promosi
            </Button>
          </div>
        </div>
      </main>

      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Hapus Promosi</DialogTitle>
            <DialogDescription>
              Apakah Anda yakin ingin menghapus promosi "{promotion.name}"?
              Tindakan ini tidak dapat dibatalkan dan akan menghapus semua data
              terkait promosi ini.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowDeleteDialog(false)}
            >
              Batal
            </Button>
            <Button
              variant="destructive"
              onClick={handleDelete}
              disabled={deletePromotionMutation.isPending}
            >
              {deletePromotionMutation.isPending ? 'Menghapus...' : 'Hapus'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
