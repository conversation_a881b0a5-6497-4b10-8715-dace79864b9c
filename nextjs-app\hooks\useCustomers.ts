import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from '@/components/ui/use-toast';
import { useAuth } from '@/lib/auth-context';
import {
  customerAPI,
  type Customer,
  type CreateCustomerRequest,
  type UpdateCustomerRequest,
  type GetCustomersParams,
  type CreateCustomerNoteRequest,
  type CustomerNote,
} from '@/lib/api/customers';

// Hook untuk mengambil semua customers dengan pagination dan filter
export function useCustomers(params?: GetCustomersParams) {
  const { activeOutlet } = useAuth();

  return useQuery({
    queryKey: ['customers', activeOutlet?.id, params],
    queryFn: () =>
      activeOutlet
        ? customerAPI.getCustomers({ ...params, outletId: activeOutlet.id })
        : Promise.reject('No active outlet'),
    enabled: !!activeOutlet,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook untuk mengambil customer berdasarkan ID
export function useCustomer(id: number | null) {
  const { activeOutlet } = useAuth();

  return useQuery({
    queryKey: ['customers', activeOutlet?.id, id],
    queryFn: () =>
      activeOutlet && id
        ? customerAPI.getCustomer(id, activeOutlet.id)
        : Promise.reject('No active outlet or ID'),
    enabled: !!id && !!activeOutlet,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook untuk membuat customer baru
export function useCreateCustomer() {
  const queryClient = useQueryClient();
  const { activeOutlet } = useAuth();

  return useMutation({
    mutationFn: (data: CreateCustomerRequest) => {
      if (!activeOutlet) throw new Error('No active outlet');
      return customerAPI.createCustomer({ ...data, outletId: activeOutlet.id });
    },
    onSuccess: (newCustomer) => {
      // Invalidate dan refetch customers list
      queryClient.invalidateQueries({ queryKey: ['customers'] });

      toast({
        title: 'Berhasil',
        description: `Pelanggan ${newCustomer.name} berhasil ditambahkan.`,
      });
    },
    // Tidak langsung show error toast, biarkan component handle error
  });
}

// Hook untuk update customer
export function useUpdateCustomer() {
  const queryClient = useQueryClient();
  const { activeOutlet } = useAuth();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateCustomerRequest }) => {
      if (!activeOutlet) throw new Error('No active outlet');
      return customerAPI.updateCustomer(id, {
        ...data,
        outletId: activeOutlet.id,
      });
    },
    onSuccess: (updatedCustomer) => {
      // Invalidate customers list dan detail
      queryClient.invalidateQueries({ queryKey: ['customers'] });
      queryClient.setQueryData(
        ['customers', updatedCustomer.id],
        updatedCustomer
      );

      toast({
        title: 'Berhasil',
        description: `Data pelanggan ${updatedCustomer.name} berhasil diperbarui.`,
      });
    },
    // Tidak langsung show error toast, biarkan component handle error
  });
}

// Hook untuk delete customer
export function useDeleteCustomer() {
  const queryClient = useQueryClient();
  const { activeOutlet } = useAuth();

  return useMutation({
    mutationFn: (id: number) => {
      if (!activeOutlet) throw new Error('No active outlet');
      return customerAPI.deleteCustomer(id, activeOutlet.id);
    },
    onSuccess: () => {
      // Invalidate customers list
      queryClient.invalidateQueries({ queryKey: ['customers'] });

      toast({
        title: 'Berhasil',
        description: 'Pelanggan berhasil dihapus.',
      });
    },
    onError: (error: any) => {
      const message =
        error.response?.data?.message || 'Gagal menghapus pelanggan';
      toast({
        title: 'Error',
        description: message,
        variant: 'destructive',
      });
    },
  });
}

// Hook untuk mengambil customer notes
export function useCustomerNotes(customerId: number | null) {
  const { activeOutlet } = useAuth();

  return useQuery({
    queryKey: ['customers', activeOutlet?.id, customerId, 'notes'],
    queryFn: () =>
      activeOutlet && customerId
        ? customerAPI.getCustomerNotes(customerId, activeOutlet.id)
        : Promise.reject('No active outlet or customer ID'),
    enabled: !!customerId && !!activeOutlet,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

// Hook untuk membuat customer note
export function useCreateCustomerNote() {
  const queryClient = useQueryClient();
  const { activeOutlet } = useAuth();

  return useMutation({
    mutationFn: ({
      customerId,
      data,
    }: {
      customerId: number;
      data: CreateCustomerNoteRequest;
    }) => {
      if (!activeOutlet) throw new Error('No active outlet');
      return customerAPI.createCustomerNote(customerId, {
        ...data,
        outletId: activeOutlet.id,
      });
    },
    onSuccess: (_, { customerId }) => {
      // Invalidate customer notes
      queryClient.invalidateQueries({
        queryKey: ['customers', customerId, 'notes'],
      });

      toast({
        title: 'Berhasil',
        description: 'Catatan pelanggan berhasil ditambahkan.',
      });
    },
    onError: (error: any) => {
      const message =
        error.response?.data?.message || 'Gagal menambahkan catatan';
      toast({
        title: 'Error',
        description: message,
        variant: 'destructive',
      });
    },
  });
}

// Helper function untuk extract error message dari API response
export function getCustomerErrorMessage(error: any): string {
  if (error?.response?.data?.message) {
    return error.response.data.message;
  }

  if (error?.message) {
    return error.message;
  }

  return 'Terjadi kesalahan yang tidak diketahui';
}

// Helper function untuk extract field errors dari API response
export function getCustomerFieldErrors(error: any): Record<string, string> {
  const fieldErrors: Record<string, string> = {};

  if (error?.response?.data?.message) {
    const message = error.response.data.message;

    // Check for specific validation patterns
    const patterns = [
      // Duplicate validation
      {
        pattern: /phone.*already exists/i,
        field: 'phone',
        message: 'Nomor telepon sudah digunakan di outlet ini',
      },
      {
        pattern: /email.*already exists/i,
        field: 'email',
        message: 'Email sudah digunakan di outlet ini',
      },

      // Required field validation
      {
        pattern: /"name" is required/i,
        field: 'name',
        message: 'Nama wajib diisi',
      },
      {
        pattern: /at least phone or email is required/i,
        field: 'phone',
        message: 'Minimal nomor telepon atau email wajib diisi',
      },

      // Format validation
      {
        pattern: /"email" must be a valid email/i,
        field: 'email',
        message: 'Format email tidak valid',
      },
      {
        pattern: /"phone" with value .* fails to match the required pattern/i,
        field: 'phone',
        message:
          'Format nomor telepon tidak valid (gunakan format 08xxxxxxxxx)',
      },
      {
        pattern:
          /"address" length must be less than or equal to \d+ characters long/i,
        field: 'address',
        message: 'Alamat terlalu panjang (maksimal 500 karakter)',
      },
      {
        pattern: /"mapLink" must be a valid uri/i,
        field: 'mapLink',
        message: 'Link maps tidak valid',
      },
      {
        pattern:
          /"notes" length must be less than or equal to \d+ characters long/i,
        field: 'notes',
        message: 'Catatan terlalu panjang (maksimal 1000 karakter)',
      },

      // Number validation
      {
        pattern: /"latitude" must be greater than or equal to -90/i,
        field: 'latitude',
        message: 'Latitude harus antara -90 dan 90',
      },
      {
        pattern: /"latitude" must be less than or equal to 90/i,
        field: 'latitude',
        message: 'Latitude harus antara -90 dan 90',
      },
      {
        pattern: /"longitude" must be greater than or equal to -180/i,
        field: 'longitude',
        message: 'Longitude harus antara -180 dan 180',
      },
      {
        pattern: /"longitude" must be less than or equal to 180/i,
        field: 'longitude',
        message: 'Longitude harus antara -180 dan 180',
      },

      // Enum validation
      {
        pattern: /"status" must be one of/i,
        field: 'status',
        message: 'Status harus ACTIVE, INACTIVE, atau NEW',
      },
      {
        pattern: /"customerType" must be one of/i,
        field: 'customerType',
        message: 'Tipe pelanggan harus INDIVIDUAL atau CORPORATE',
      },

      // Length validation
      {
        pattern: /"name" length must be at least \d+ characters long/i,
        field: 'name',
        message: 'Nama terlalu pendek',
      },
      {
        pattern:
          /"name" length must be less than or equal to \d+ characters long/i,
        field: 'name',
        message: 'Nama terlalu panjang (maksimal 100 karakter)',
      },
      {
        pattern:
          /"source" length must be less than or equal to \d+ characters long/i,
        field: 'source',
        message: 'Sumber terlalu panjang (maksimal 100 karakter)',
      },

      // Province/City validation
      {
        pattern: /"provinceId" must be a positive number/i,
        field: 'provinceId',
        message: 'ID provinsi tidak valid',
      },
      {
        pattern: /"cityId" must be a positive number/i,
        field: 'cityId',
        message: 'ID kota tidak valid',
      },
    ];

    // Check each pattern
    for (const { pattern, field, message: msg } of patterns) {
      if (pattern.test(message)) {
        fieldErrors[field] = msg;
      }
    }

    // If no specific field error found but there's a validation error in the message,
    // try to extract field name from Joi error format
    if (Object.keys(fieldErrors).length === 0) {
      const joiFieldMatch = message.match(/"(\w+)"/);
      if (joiFieldMatch) {
        const fieldName = joiFieldMatch[1];
        fieldErrors[fieldName] = message;
      }
    }
  }

  return fieldErrors;
}

// Helper function untuk format currency Indonesia
export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
}

// Helper function untuk format customer status
export function getCustomerStatusBadge(status: string) {
  switch (status) {
    case 'ACTIVE':
      return { label: 'Aktif', variant: 'default' as const };
    case 'INACTIVE':
      return { label: 'Tidak Aktif', variant: 'secondary' as const };
    case 'NEW':
      return { label: 'Baru', variant: 'outline' as const };
    default:
      return { label: status, variant: 'outline' as const };
  }
}

// Helper function untuk format customer type
export function getCustomerTypeLabel(type: string): string {
  switch (type) {
    case 'INDIVIDUAL':
      return 'Individu';
    case 'CORPORATE':
      return 'Perusahaan';
    default:
      return type;
  }
}
