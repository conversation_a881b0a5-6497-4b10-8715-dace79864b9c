import Joi from 'joi';
import { DepositTransactionType } from '@prisma/client';

const depositIn = {
  body: Joi.object().keys({
    customerId: Joi.number().integer().positive().required(),
    outletId: Joi.number().integer().positive().required(),
    amount: Joi.number().positive().required(),
    cashboxId: Joi.number().integer().positive().required(),
    reference: Joi.string().max(255).optional()
  })
};

const depositOut = {
  body: Joi.object().keys({
    customerId: Joi.number().integer().positive().required(),
    outletId: Joi.number().integer().positive().required(),
    amount: Joi.number().positive().required(),
    cashboxId: Joi.number().integer().positive().required(),
    reference: Joi.string().max(255).optional()
  })
};

const payWithDeposit = {
  body: Joi.object().keys({
    customerId: Joi.number().integer().positive().required(),
    outletId: Joi.number().integer().positive().required(),
    orderId: Joi.number().integer().positive().required(),
    amount: Joi.number().positive().required(),
    reference: Joi.string().max(255).optional()
  })
};

const getDepositHistory = {
  params: Joi.object().keys({
    outletId: Joi.number().integer().positive().required(),
    customerId: Joi.number().integer().positive().required()
  }),
  query: Joi.object().keys({
    page: Joi.number().integer().min(1).optional(),
    limit: Joi.number().integer().min(1).max(100).optional(),
    type: Joi.string()
      .valid(...Object.values(DepositTransactionType))
      .optional(),
    dateFrom: Joi.date().iso().optional(),
    dateTo: Joi.date().iso().optional()
  })
};

const getDepositBalance = {
  params: Joi.object().keys({
    outletId: Joi.number().integer().positive().required(),
    customerId: Joi.number().integer().positive().required()
  })
};

export default {
  depositIn,
  depositOut,
  payWithDeposit,
  getDepositHistory,
  getDepositBalance
};
