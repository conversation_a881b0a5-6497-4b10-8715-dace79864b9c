'use client';

import { useState } from 'react';
import Link from 'next/link';
import {
  Users,
  Store,
  ShoppingBag,
  UserCog,
  BarChart2,
  User,
  LogOut,
  Settings,
  Bell,
  Gift,
  Package,
  Truck,
  FileText,
  MessageSquare,
  HelpCircle,
  Tag,
  Sprout,
  LayoutGrid,
  List,
  Columns,
  Building2,
  Wallet,
} from 'lucide-react';

import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import BottomNavigation from '@/components/bottom-navigation';
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';
import { useAuth } from '@/lib/auth-context';
import { useRouter } from 'next/navigation';

export default function AccountPage() {
  const [showConfirmLogout, setShowConfirmLogout] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list' | 'columns'>('grid');
  const { logout, user, activeOutlet } = useAuth();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  // Fungsi untuk mendapatkan inisial nama
  const getInitials = (name: string | null) => {
    if (!name) return 'U';
    return name
      .split(' ')
      .map((word) => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const menuItems = [
    {
      title: 'Kelola Pelanggan',
      icon: <Users className="h-6 w-6" />,
      href: '/akun/customers',
      color: 'bg-blue-100 text-blue-600',
    },
    {
      title: 'Kelola Outlet',
      icon: <Store className="h-6 w-6" />,
      href: '/akun/outlets',
      color: 'bg-green-100 text-green-600',
    },
    {
      title: 'Layanan & Produk',
      icon: <ShoppingBag className="h-6 w-6" />,
      href: '/akun/services',
      color: 'bg-purple-100 text-purple-600',
    },
    {
      title: 'Kelola Pegawai',
      icon: <UserCog className="h-6 w-6" />,
      href: '/akun/employees',
      color: 'bg-orange-100 text-orange-600',
    },
    {
      title: 'Keuangan',
      icon: <BarChart2 className="h-6 w-6" />,
      href: '/akun/finance',
      color: 'bg-red-100 text-red-600',
    },
    {
      title: 'Cashbox',
      icon: <Wallet className="h-6 w-6" />,
      href: '/akun/cashbox',
      color: 'bg-emerald-100 text-emerald-600',
    },
    {
      title: 'Profil',
      icon: <User className="h-6 w-6" />,
      href: '/akun/profile',
      color: 'bg-teal-100 text-teal-600',
    },
    {
      title: 'Pengaturan',
      icon: <Settings className="h-6 w-6" />,
      href: '/akun/settings',
      color: 'bg-gray-100 text-gray-600',
    },
    {
      title: 'Notifikasi',
      icon: <Bell className="h-6 w-6" />,
      href: '/akun/notifications',
      color: 'bg-yellow-100 text-yellow-600',
    },
    // {
    //   title: 'Program Loyalitas',
    //   icon: <Gift className="h-6 w-6" />,
    //   href: '/akun/loyalty',
    //   color: 'bg-pink-100 text-pink-600',
    // },
    {
      title: 'Kelola Promosi',
      icon: <Tag className="h-6 w-6" />,
      href: '/akun/promotions',
      color: 'bg-purple-100 text-purple-600',
    },
    // {
    //   title: 'WhatsApp',
    //   icon: <MessageSquare className="h-6 w-6" />,
    //   href: '/akun/whatsapp',
    //   color: 'bg-green-100 text-green-600',
    // },
    {
      title: 'Kelola Parfum',
      icon: <Sprout className="h-6 w-6" />,
      href: '/akun/perfumes',
      color: 'bg-emerald-100 text-emerald-600',
    },
    // {
    //   title: 'Inventaris',
    //   icon: <Package className="h-6 w-6" />,
    //   href: '/akun/inventory',
    //   color: 'bg-indigo-100 text-indigo-600',
    // },
    // {
    //   title: 'Supplier',
    //   icon: <Truck className="h-6 w-6" />,
    //   href: '/akun/suppliers',
    //   color: 'bg-cyan-100 text-cyan-600',
    // },
    // {
    //   title: 'Laporan',
    //   icon: <FileText className="h-6 w-6" />,
    //   href: '/akun/reports',
    //   color: 'bg-amber-100 text-amber-600',
    // },
    // {
    //   title: 'Pesan',
    //   icon: <MessageSquare className="h-6 w-6" />,
    //   href: '/akun/messages',
    //   color: 'bg-lime-100 text-lime-600',
    // },
    // {
    //   title: 'Bantuan',
    //   icon: <HelpCircle className="h-6 w-6" />,
    //   href: '/akun/help',
    //   color: 'bg-violet-100 text-violet-600',
    // },
    {
      title: 'Keluar',
      icon: <LogOut className="h-6 w-6" />,
      href: '#',
      color: 'bg-rose-100 text-rose-600',
      onClick: () => setShowConfirmLogout(true),
    },
  ];

  const handleLogout = async () => {
    setIsLoading(true);
    await logout();
    router.push('/auth/login');
    setIsLoading(false);
  };

  // Loading state
  if (!user || !activeOutlet) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <p>Loading...</p>
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen bg-slate-50">
      <header className="p-4 flex justify-between items-center bg-white shadow-sm">
        <h1 className="text-xl font-semibold">Akun Saya</h1>
        <Link href="/akun/notifications">
          <Button variant="ghost" size="icon">
            <Bell className="h-5 w-5" />
          </Button>
        </Link>
      </header>

      <main className="flex-1 p-4 pb-20">
        <Card className="p-4 mb-6">
          <div className="flex items-center">
            <Avatar className="h-16 w-16 mr-4">
              <AvatarImage
                src="/placeholder.svg?height=64&width=64"
                alt="Profile"
              />
              <AvatarFallback>{getInitials(user.name)}</AvatarFallback>
            </Avatar>
            <div className="flex-1">
              <div className="flex justify-between items-center">
                <div>
                  <h2 className="text-xl font-bold">{activeOutlet.name}</h2>
                  {activeOutlet.address && (
                    <p className="text-xs text-gray-400 mt-1">
                      {activeOutlet.address}
                    </p>
                  )}
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => router.push('/auth/select-outlet')}
                >
                  <Building2 className="h-4 w-4 mr-2" />
                  Ganti Outlet
                </Button>
              </div>
            </div>
          </div>
          <Separator className="my-4" />
          <div className="flex justify-between text-sm">
            <div className="text-center">
              <p className="font-semibold text-lg">
                {activeOutlet._count?.customers}
              </p>
              <p className="text-gray-500">Pelanggan</p>
            </div>

            <div className="text-center">
              <p className="font-semibold text-lg">
                {activeOutlet._count?.employees}
              </p>
              <p className="text-gray-500">Pegawai</p>
            </div>
            <div className="text-center">
              <p className="font-semibold text-lg">
                {activeOutlet._count?.orders}
              </p>
              <p className="text-gray-500">Order</p>
            </div>
          </div>
        </Card>

        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-semibold">Menu Utama</h2>
          <ToggleGroup
            type="single"
            value={viewMode}
            onValueChange={(value) => value && setViewMode(value as any)}
          >
            <ToggleGroupItem value="grid" aria-label="Grid View">
              <LayoutGrid className="h-4 w-4" />
            </ToggleGroupItem>
            <ToggleGroupItem value="list" aria-label="List View">
              <List className="h-4 w-4" />
            </ToggleGroupItem>
            <ToggleGroupItem value="columns" aria-label="Columns View">
              <Columns className="h-4 w-4" />
            </ToggleGroupItem>
          </ToggleGroup>
        </div>

        {viewMode === 'grid' && (
          <div className="grid grid-cols-2 sm:grid-cols-3 gap-4 mb-20">
            {menuItems.map((item, index) => (
              <Link
                key={index}
                href={item.href}
                onClick={item.onClick ? item.onClick : undefined}
                className="block"
              >
                <div className="border rounded-lg p-4 flex flex-col items-center text-center hover:shadow-md transition-shadow">
                  <div className={`rounded-full p-3 mb-2 ${item.color}`}>
                    {item.icon}
                  </div>
                  <span className="text-sm font-medium">{item.title}</span>
                </div>
              </Link>
            ))}
          </div>
        )}

        {viewMode === 'list' && (
          <div className="space-y-2 mb-20">
            {menuItems.map((item, index) => (
              <Link
                key={index}
                href={item.href}
                onClick={item.onClick ? item.onClick : undefined}
                className="block"
              >
                <div className="border rounded-lg p-3 flex items-center hover:shadow-md transition-shadow">
                  <div className={`rounded-full p-2 mr-3 ${item.color}`}>
                    {item.icon}
                  </div>
                  <span className="font-medium">{item.title}</span>
                </div>
              </Link>
            ))}
          </div>
        )}

        {viewMode === 'columns' && (
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-20">
            {menuItems.map((item, index) => (
              <Link
                key={index}
                href={item.href}
                onClick={item.onClick ? item.onClick : undefined}
                className="block"
              >
                <div className="border rounded-lg p-3 flex items-center hover:shadow-md transition-shadow">
                  <div className={`rounded-full p-2 mr-3 ${item.color}`}>
                    {item.icon}
                  </div>
                  <span className="font-medium">{item.title}</span>
                </div>
              </Link>
            ))}
          </div>
        )}
      </main>

      {showConfirmLogout && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <Card className="w-full max-w-sm p-6">
            <h2 className="text-xl font-semibold mb-4">Konfirmasi</h2>
            <p className="mb-6">
              Apakah Anda yakin ingin keluar dari aplikasi?
            </p>
            <div className="flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => setShowConfirmLogout(false)}
              >
                Batal
              </Button>
              <Button
                variant="destructive"
                onClick={handleLogout}
                disabled={isLoading}
              >
                {isLoading ? 'Memproses...' : 'Keluar'}
              </Button>
            </div>
          </Card>
        </div>
      )}

      <BottomNavigation activePage="akun" />
    </div>
  );
}
