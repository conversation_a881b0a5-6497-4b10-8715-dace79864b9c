'use client';

import { useState, useMemo } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import {
  ArrowLeft,
  InboxIcon,
  CalendarCheck,
  CalendarDays,
  CalendarRange,
  AlertCircle,
  Search,
  Filter,
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { useOrders } from '@/hooks/useOrders';

// Types
interface ProcessingInfo {
  total: number;
  totalWeight: number;
  totalUnits: number;
  totalMeters: number;
  pending: number;
  washing: number;
  drying: number;
  ironing: number;
  packing: number;
  completed: number;
}

const getTabIcon = (tab: string) => {
  switch (tab) {
    case 'masuk':
      return <InboxIcon className="h-4 w-4" />;
    case 'hari-ini':
      return <CalendarCheck className="h-4 w-4" />;
    case 'besok':
      return <CalendarDays className="h-4 w-4" />;
    case 'lusa':
      return <CalendarRange className="h-4 w-4" />;
    case 'terlambat':
      return <AlertCircle className="h-4 w-4" />;
    default:
      return null;
  }
};

const getTabColor = (tab: string) => {
  switch (tab) {
    case 'masuk':
      return 'text-blue-600';
    case 'hari-ini':
      return 'text-green-600';
    case 'besok':
      return 'text-orange-600';
    case 'lusa':
      return 'text-purple-600';
    case 'terlambat':
      return 'text-red-600';
    default:
      return 'text-gray-600';
  }
};

const getTabLabel = (tab: string) => {
  switch (tab) {
    case 'masuk':
      return 'Masuk';
    case 'hari-ini':
      return 'Hari Ini';
    case 'besok':
      return 'Besok';
    case 'lusa':
      return 'Lusa';
    case 'terlambat':
      return 'Terlambat';
    default:
      return tab;
  }
};

export default function OrderSummaryPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const initialTab = searchParams.get('filter') || 'masuk';

  const [activeTab, setActiveTab] = useState(initialTab);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedProcessingStatus, setSelectedProcessingStatus] = useState<
    string | null
  >(null);

  // Fetch orders for current tab
  const { data: ordersData, isLoading } = useOrders({
    filter: activeTab,
    search: searchQuery,
    processingStatus: selectedProcessingStatus || undefined,
    limit: 50, // Show more orders on summary page
  });

  const orders = ordersData?.results || [];

  // Calculate processing information
  const processingInfo: ProcessingInfo = useMemo(() => {
    const info: ProcessingInfo = {
      total: orders.length,
      totalWeight: 0,
      totalUnits: 0,
      totalMeters: 0,
      pending: 0,
      washing: 0,
      drying: 0,
      ironing: 0,
      packing: 0,
      completed: 0,
    };

    orders.forEach((order) => {
      order.items?.forEach((item) => {
        // Calculate totals based on unit
        if (item.unit === 'kg') {
          info.totalWeight += item.quantity;
        } else if (item.unit === 'pcs' || item.unit === 'satuan') {
          info.totalUnits += item.quantity;
        } else if (item.unit === 'M' || item.unit === 'm') {
          info.totalMeters += item.quantity;
        }

        // Count by processing status
        switch (item.status) {
          case 'PENDING':
            info.pending++;
            break;
          case 'WASHING':
            info.washing++;
            break;
          case 'DRYING':
            info.drying++;
            break;
          case 'IRONING':
            info.ironing++;
            break;
          case 'PACKING':
            info.packing++;
            break;
          case 'COMPLETED':
            info.completed++;
            break;
        }
      });
    });

    return info;
  }, [orders]);

  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
    setSelectedProcessingStatus(null); // Reset processing filter when changing tabs
    setSearchQuery(''); // Reset search when changing tabs
  };

  const handleProcessingFilter = (status: string | null) => {
    setSelectedProcessingStatus(status);
  };

  const formatOrderTime = (date: string) => {
    return new Date(date).toLocaleTimeString('id-ID', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false,
    });
  };

  const formatOrderDate = (date: string) => {
    return new Date(date).toLocaleDateString('id-ID', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  };

  const getOrderStatusColor = (status: string) => {
    switch (status) {
      case 'KONFIRMASI':
        return 'bg-blue-100 text-blue-800';
      case 'PICKUP':
        return 'bg-orange-100 text-orange-800';
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800';
      case 'PROCESSING':
        return 'bg-purple-100 text-purple-800';
      case 'READY':
        return 'bg-green-100 text-green-800';
      case 'COMPLETED':
        return 'bg-gray-100 text-gray-800';
      case 'CANCELLED':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="max-w-md mx-auto bg-background min-h-screen">
      {/* Header */}
      <div className="sticky top-0 z-10 bg-background border-b">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-3">
            <Button
              variant="ghost"
              size="icon"
              className="h-9 w-9"
              onClick={() => router.push('/dashboard')}
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <h1 className="text-xl font-semibold">Ringkasan Pesanan</h1>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <Tabs
        value={activeTab}
        onValueChange={handleTabChange}
        className="w-full"
      >
        <div className="px-4 pt-4">
          <TabsList className="grid grid-cols-5 w-full h-auto p-1">
            {['masuk', 'hari-ini', 'besok', 'lusa', 'terlambat'].map((tab) => (
              <TabsTrigger
                key={tab}
                value={tab}
                className="flex flex-col items-center gap-1 py-2 px-1 text-xs data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
              >
                <span className={getTabColor(tab)}>{getTabIcon(tab)}</span>
                <span className="leading-tight">{getTabLabel(tab)}</span>
              </TabsTrigger>
            ))}
          </TabsList>
        </div>

        {/* Content for each tab */}
        {['masuk', 'hari-ini', 'besok', 'lusa', 'terlambat'].map((tab) => (
          <TabsContent key={tab} value={tab} className="mt-0">
            <div className="p-4 space-y-4">
              {/* Processing Information */}
              <Card>
                <CardContent className="p-4">
                  <h3 className="font-medium text-sm mb-3 text-gray-700">
                    Informasi Pemrosesan
                  </h3>
                  <div className="grid grid-cols-2 gap-3 text-sm">
                    <div className="text-center">
                      <div className="text-lg font-semibold text-gray-800">
                        {processingInfo.total}
                      </div>
                      <div className="text-xs text-gray-500">Total Pesanan</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-semibold text-gray-800">
                        {processingInfo.totalWeight.toFixed(1)}kg
                      </div>
                      <div className="text-xs text-gray-500">Berat Total</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-semibold text-gray-800">
                        {processingInfo.totalUnits}
                      </div>
                      <div className="text-xs text-gray-500">Satuan</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-semibold text-gray-800">
                        {processingInfo.totalMeters.toFixed(1)}M
                      </div>
                      <div className="text-xs text-gray-500">Meter</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Search */}
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Cari pesanan..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>

              {/* Filter Buttons */}
              <div className="space-y-2">
                <div className="flex items-center gap-2 mb-2">
                  <Filter className="h-4 w-4 text-gray-500" />
                  <span className="text-sm font-medium text-gray-700">
                    Filter Status Pemrosesan
                  </span>
                </div>
                <div className="flex flex-wrap gap-2">
                  <Button
                    variant={
                      selectedProcessingStatus === null ? 'default' : 'outline'
                    }
                    size="sm"
                    onClick={() => handleProcessingFilter(null)}
                    className="text-xs h-8"
                  >
                    All = {processingInfo.total} Trx /{' '}
                    {processingInfo.totalWeight.toFixed(1)}kg /{' '}
                    {processingInfo.totalUnits} satuan /{' '}
                    {processingInfo.totalMeters.toFixed(1)}M
                  </Button>
                  <Button
                    variant={
                      selectedProcessingStatus === 'PENDING'
                        ? 'default'
                        : 'outline'
                    }
                    size="sm"
                    onClick={() => handleProcessingFilter('PENDING')}
                    className="text-xs h-8"
                  >
                    Pending ({processingInfo.pending})
                  </Button>
                  <Button
                    variant={
                      selectedProcessingStatus === 'WASHING'
                        ? 'default'
                        : 'outline'
                    }
                    size="sm"
                    onClick={() => handleProcessingFilter('WASHING')}
                    className="text-xs h-8"
                  >
                    Cuci ({processingInfo.washing})
                  </Button>
                  <Button
                    variant={
                      selectedProcessingStatus === 'DRYING'
                        ? 'default'
                        : 'outline'
                    }
                    size="sm"
                    onClick={() => handleProcessingFilter('DRYING')}
                    className="text-xs h-8"
                  >
                    Kering ({processingInfo.drying})
                  </Button>
                  <Button
                    variant={
                      selectedProcessingStatus === 'IRONING'
                        ? 'default'
                        : 'outline'
                    }
                    size="sm"
                    onClick={() => handleProcessingFilter('IRONING')}
                    className="text-xs h-8"
                  >
                    Setrika ({processingInfo.ironing})
                  </Button>
                  <Button
                    variant={
                      selectedProcessingStatus === 'PACKING'
                        ? 'default'
                        : 'outline'
                    }
                    size="sm"
                    onClick={() => handleProcessingFilter('PACKING')}
                    className="text-xs h-8"
                  >
                    Packing ({processingInfo.packing})
                  </Button>
                  <Button
                    variant={
                      selectedProcessingStatus === 'COMPLETED'
                        ? 'default'
                        : 'outline'
                    }
                    size="sm"
                    onClick={() => handleProcessingFilter('COMPLETED')}
                    className="text-xs h-8"
                  >
                    Selesai ({processingInfo.completed})
                  </Button>
                </div>
              </div>

              {/* Orders List */}
              <div className="space-y-3">
                {isLoading ? (
                  <div className="text-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                    <p className="text-sm text-muted-foreground mt-2">
                      Memuat pesanan...
                    </p>
                  </div>
                ) : orders.length === 0 ? (
                  <div className="text-center py-8">
                    <p className="text-sm text-muted-foreground">
                      {selectedProcessingStatus
                        ? `Tidak ada pesanan dengan status ${selectedProcessingStatus.toLowerCase()}`
                        : `Tidak ada pesanan ${getTabLabel(tab).toLowerCase()}`}
                    </p>
                  </div>
                ) : (
                  orders.map((order) => (
                    <Card
                      key={order.id}
                      className="cursor-pointer hover:shadow-md transition-shadow"
                      onClick={() => router.push(`/orders/${order.id}`)}
                    >
                      <CardContent className="p-4">
                        <div className="flex justify-between items-start mb-3">
                          <div className="flex items-center gap-3">
                            <Avatar className="h-8 w-8">
                              <AvatarFallback className="text-xs">
                                {order.customer?.name?.charAt(0) || 'U'}
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <div className="font-medium text-sm">
                                {order.customer?.name || 'Unknown'}
                              </div>
                              <div className="text-xs text-muted-foreground">
                                {order.orderNumber}
                              </div>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-sm font-medium">
                              Rp {order.totalPrice.toLocaleString()}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {formatOrderTime(order.createdAt)}
                            </div>
                          </div>
                        </div>

                        <div className="flex justify-between items-center">
                          <div className="flex flex-wrap gap-1">
                            <Badge
                              variant="secondary"
                              className={getOrderStatusColor(order.status)}
                            >
                              {order.status}
                            </Badge>
                            {order.items && order.items.length > 0 && (
                              <Badge variant="outline" className="text-xs">
                                {order.items.length} item
                              </Badge>
                            )}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            {formatOrderDate(order.createdAt)}
                          </div>
                        </div>

                        {/* Show items preview */}
                        {order.items && order.items.length > 0 && (
                          <div className="mt-2 pt-2 border-t border-gray-100">
                            <div className="text-xs text-muted-foreground">
                              {order.items.slice(0, 2).map((item, idx) => (
                                <span key={item.id}>
                                  {item.serviceName} ({item.quantity}{' '}
                                  {item.unit})
                                  {idx < Math.min(order.items.length, 2) - 1 &&
                                    ', '}
                                </span>
                              ))}
                              {order.items.length > 2 && (
                                <span> +{order.items.length - 2} lainnya</span>
                              )}
                            </div>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  ))
                )}
              </div>
            </div>
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
}
