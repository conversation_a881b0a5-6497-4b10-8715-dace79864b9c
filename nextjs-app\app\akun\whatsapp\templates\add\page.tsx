"use client"

import { useState } from "react"
import Link from "next/link"
import { ArrowLeft, Save } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"

export default function AddTemplatePage() {
  const [template, setTemplate] = useState({
    name: "",
    message: "",
    status: "active",
    category: "order",
    variables: [] as { name: string; description: string }[],
  })

  const [newVariable, setNewVariable] = useState({
    name: "",
    description: "",
  })

  const handleStatusChange = (checked: boolean) => {
    setTemplate({
      ...template,
      status: checked ? "active" : "inactive",
    })
  }

  const handleSave = () => {
    if (!template.name) {
      alert("Nama template tidak boleh kosong!")
      return
    }
    if (!template.message) {
      alert("Pesan template tidak boleh kosong!")
      return
    }
    alert("Template berhasil disimpan!")
  }

  const handleAddVariable = () => {
    if (!newVariable.name || !newVariable.description) {
      alert("Nama dan deskripsi variabel tidak boleh kosong!")
      return
    }

    setTemplate({
      ...template,
      variables: [...template.variables, newVariable],
    })
    setNewVariable({ name: "", description: "" })
  }

  const handleRemoveVariable = (index: number) => {
    const newVariables = [...template.variables]
    newVariables.splice(index, 1)
    setTemplate({ ...template, variables: newVariables })
  }

  const extractVariablesFromMessage = () => {
    const regex = /{([^}]+)}/g
    const matches = template.message.match(regex) || []
    const extractedVariables = matches.map((match) => {
      const name = match.replace(/{|}/g, "")
      return {
        name,
        description: `Variabel ${name}`,
      }
    })

    // Filter out duplicates and existing variables
    const existingNames = template.variables.map((v) => v.name)
    const newVars = extractedVariables.filter((v) => !existingNames.includes(v.name))

    if (newVars.length > 0) {
      setTemplate({
        ...template,
        variables: [...template.variables, ...newVars],
      })
    }
  }

  return (
    <div className="flex flex-col min-h-screen bg-slate-50">
      <header className="p-4 flex justify-between items-center bg-white sticky top-0 z-10 shadow-sm">
        <div className="flex items-center">
          <Link href="/akun/whatsapp" className="mr-3">
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <h1 className="text-lg font-semibold">Tambah Template Baru</h1>
        </div>
        <Button onClick={handleSave} className="bg-green-500 hover:bg-green-600">
          <Save className="h-4 w-4 mr-1" /> Simpan
        </Button>
      </header>

      <div className="p-4 pb-20">
        <Card className="mb-4">
          <CardContent className="p-4 space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">Nama Template</Label>
              <Input
                id="name"
                placeholder="Masukkan nama template"
                value={template.name}
                onChange={(e) => setTemplate({ ...template, name: e.target.value })}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="category">Kategori</Label>
              <Select
                value={template.category}
                onValueChange={(value) => setTemplate({ ...template, category: value })}
              >
                <SelectTrigger id="category">
                  <SelectValue placeholder="Pilih kategori" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="order">Pesanan</SelectItem>
                  <SelectItem value="payment">Pembayaran</SelectItem>
                  <SelectItem value="promotion">Promosi</SelectItem>
                  <SelectItem value="other">Lainnya</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="message">Pesan</Label>
              <Textarea
                id="message"
                rows={6}
                placeholder="Masukkan pesan template. Gunakan {variabel} untuk data dinamis."
                value={template.message}
                onChange={(e) => setTemplate({ ...template, message: e.target.value })}
                className="resize-none"
              />
              <div className="flex justify-between">
                <p className="text-xs text-gray-500">
                  Gunakan {"{variabel}"} untuk menyisipkan data dinamis. Contoh: {"{nama}"}, {"{nomor_pesanan}"}
                </p>
                <Button variant="link" size="sm" className="text-xs p-0 h-auto" onClick={extractVariablesFromMessage}>
                  Ekstrak Variabel
                </Button>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="status" className="cursor-pointer">
                Status Template
              </Label>
              <div className="flex items-center gap-2">
                <Switch id="status" checked={template.status === "active"} onCheckedChange={handleStatusChange} />
                <Badge variant={template.status === "active" ? "default" : "secondary"} className="text-xs">
                  {template.status === "active" ? "Aktif" : "Nonaktif"}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="mb-4">
          <CardContent className="p-4">
            <h3 className="font-medium mb-3">Variabel Template</h3>

            {template.variables.length > 0 ? (
              <div className="space-y-3 mb-4">
                {template.variables.map((variable, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <div className="flex-1">
                      <p className="text-sm font-medium">{variable.name}</p>
                      <p className="text-xs text-gray-500">{variable.description}</p>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-red-500 h-8 px-2"
                      onClick={() => handleRemoveVariable(index)}
                    >
                      Hapus
                    </Button>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-gray-500 mb-4">Belum ada variabel yang ditambahkan.</p>
            )}

            <div className="border-t pt-3">
              <h4 className="text-sm font-medium mb-2">Tambah Variabel Baru</h4>
              <div className="flex gap-2 mb-2">
                <div className="flex-1">
                  <Input
                    placeholder="Nama variabel"
                    value={newVariable.name}
                    onChange={(e) => setNewVariable({ ...newVariable, name: e.target.value })}
                  />
                </div>
                <div className="flex-1">
                  <Input
                    placeholder="Deskripsi"
                    value={newVariable.description}
                    onChange={(e) => setNewVariable({ ...newVariable, description: e.target.value })}
                  />
                </div>
              </div>
              <Button onClick={handleAddVariable} variant="outline" size="sm" className="w-full">
                Tambah Variabel
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
