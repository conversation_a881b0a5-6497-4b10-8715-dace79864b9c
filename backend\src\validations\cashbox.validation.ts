import Joi from 'joi';
import { CashboxType } from '@prisma/client';

const createCashbox = {
  params: Joi.object().keys({
    outletId: Joi.number().integer().positive().required()
  }),
  body: Joi.object().keys({
    name: Joi.string().required().min(1).max(100),
    type: Joi.string()
      .valid(...Object.values(CashboxType))
      .required(),
    isActive: Joi.boolean().optional().default(true),
    balance: Joi.number().min(0).optional().default(0)
  })
};

const getCashboxes = {
  params: Joi.object().keys({
    outletId: Joi.number().integer().positive().required()
  }),
  query: Joi.object().keys({
    type: Joi.string()
      .valid(...Object.values(CashboxType))
      .optional(),
    isActive: Joi.boolean().optional(),
    page: Joi.number().integer().min(1).optional(),
    limit: Joi.number().integer().min(1).max(100).optional()
  })
};

const getCashbox = {
  params: Joi.object().keys({
    outletId: Joi.number().integer().positive().required(),
    cashboxId: Joi.number().integer().positive().required()
  })
};

const updateCashbox = {
  params: Joi.object().keys({
    outletId: Joi.number().integer().positive().required(),
    cashboxId: Joi.number().integer().positive().required()
  }),
  body: Joi.object()
    .keys({
      name: Joi.string().min(1).max(100).optional(),
      type: Joi.string()
        .valid(...Object.values(CashboxType))
        .optional(),
      isActive: Joi.boolean().optional(),
      balance: Joi.number().min(0).optional()
    })
    .min(1)
};

const deleteCashbox = {
  params: Joi.object().keys({
    outletId: Joi.number().integer().positive().required(),
    cashboxId: Joi.number().integer().positive().required()
  })
};

const getCashboxBalance = {
  params: Joi.object().keys({
    outletId: Joi.number().integer().positive().required(),
    cashboxId: Joi.number().integer().positive().required()
  })
};

const adjustCashboxBalance = {
  params: Joi.object().keys({
    outletId: Joi.number().integer().positive().required(),
    cashboxId: Joi.number().integer().positive().required()
  }),
  body: Joi.object().keys({
    balance: Joi.number().min(0).required(),
    reason: Joi.string().required().min(1).max(500)
  })
};

export default {
  createCashbox,
  getCashboxes,
  getCashbox,
  updateCashbox,
  deleteCashbox,
  getCashboxBalance,
  adjustCashboxBalance
};
