"use client"

import { useState } from "react"
import Link from "next/link"
import {
  ArrowLeft,
  Trophy,
  Users,
  Clock,
  Star,
  Plus,
  Search,
  Calendar,
  CheckCircle,
  Bell,
  ChevronRight,
  Download,
  ThumbsUp,
  Award,
} from "lucide-react"
import { useRouter } from "next/navigation"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Ta<PERSON>, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import {
  Dialog,
  DialogContent,
  DialogDes<PERSON>,
  DialogFooter,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog"

// Mock data for challenges
const mockChallenges = {
  active: [
    {
      id: 1,
      name: "Laundry 5x",
      description: "Lakukan 5 kali transaksi dalam 1 bulan",
      reward: 500,
      duration: "30 hari",
      startDate: "01/04/2024",
      endDate: "30/04/2024",
      participants: 28,
      completions: 12,
      status: "active",
      progress: 42,
    },
    {
      id: 2,
      name: "Referral Challenge",
      description: "Referensikan 3 teman baru",
      reward: 750,
      duration: "60 hari",
      startDate: "15/03/2024",
      endDate: "15/05/2024",
      participants: 15,
      completions: 5,
      status: "active",
      progress: 33,
    },
    {
      id: 3,
      name: "Loyal Customer",
      description: "Lakukan transaksi setiap minggu selama 1 bulan",
      reward: 1000,
      duration: "30 hari",
      startDate: "01/04/2024",
      endDate: "30/04/2024",
      participants: 10,
      completions: 3,
      status: "active",
      progress: 30,
    },
  ],
  upcoming: [
    {
      id: 4,
      name: "Big Spender",
      description: "Belanjakan minimal Rp 500.000 dalam satu transaksi",
      reward: 800,
      duration: "30 hari",
      startDate: "01/05/2024",
      endDate: "31/05/2024",
      participants: 0,
      completions: 0,
      status: "upcoming",
      progress: 0,
    },
    {
      id: 5,
      name: "Weekend Warrior",
      description: "Lakukan 3 transaksi di akhir pekan",
      reward: 600,
      duration: "30 hari",
      startDate: "01/05/2024",
      endDate: "31/05/2024",
      participants: 0,
      completions: 0,
      status: "upcoming",
      progress: 0,
    },
  ],
  completed: [
    {
      id: 6,
      name: "March Madness",
      description: "Lakukan 3 transaksi dalam bulan Maret",
      reward: 500,
      duration: "31 hari",
      startDate: "01/03/2024",
      endDate: "31/03/2024",
      participants: 35,
      completions: 22,
      status: "completed",
      progress: 63,
    },
    {
      id: 7,
      name: "Valentine's Special",
      description: "Lakukan transaksi pada tanggal 14 Februari",
      reward: 300,
      duration: "1 hari",
      startDate: "14/02/2024",
      endDate: "14/02/2024",
      participants: 42,
      completions: 38,
      status: "completed",
      progress: 90,
    },
  ],
  achievements: [
    {
      id: 1,
      name: "First Order",
      description: "Melakukan order pertama",
      icon: <Star className="h-5 w-5" />,
      earnedBy: 95,
      category: "basic",
    },
    {
      id: 2,
      name: "Loyal Customer",
      description: "Melakukan 10 order",
      icon: <ThumbsUp className="h-5 w-5" />,
      earnedBy: 42,
      category: "loyalty",
    },
    {
      id: 3,
      name: "Big Spender",
      description: "Menghabiskan total Rp 1.000.000",
      icon: <Trophy className="h-5 w-5" />,
      earnedBy: 25,
      category: "spending",
    },
    {
      id: 4,
      name: "Referral Master",
      description: "Mereferensikan 5 teman",
      icon: <Award className="h-5 w-5" />,
      earnedBy: 8,
      category: "referral",
    },
    {
      id: 5,
      name: "Early Bird",
      description: "Melakukan order sebelum jam 8 pagi",
      icon: <Clock className="h-5 w-5" />,
      earnedBy: 15,
      category: "special",
    },
    {
      id: 6,
      name: "Night Owl",
      description: "Melakukan order setelah jam 10 malam",
      icon: <Clock className="h-5 w-5" />,
      earnedBy: 20,
      category: "special",
    },
    {
      id: 7,
      name: "Weekend Warrior",
      description: "Melakukan 5 order di akhir pekan",
      icon: <Calendar className="h-5 w-5" />,
      earnedBy: 30,
      category: "loyalty",
    },
    {
      id: 8,
      name: "Feedback Champion",
      description: "Memberikan 5 ulasan",
      icon: <ThumbsUp className="h-5 w-5" />,
      earnedBy: 18,
      category: "engagement",
    },
  ],
}

export default function ChallengesPage() {
  const router = useRouter()
  const [searchQuery, setSearchQuery] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [activeTab, setActiveTab] = useState("active")
  const [showChallengeDialog, setShowChallengeDialog] = useState(false)
  const [achievementCategoryFilter, setAchievementCategoryFilter] = useState("all")

  // Filter challenges based on search query and status filter
  const getFilteredChallenges = (challenges: any[]) => {
    return challenges.filter((challenge) => {
      const matchesSearch =
        searchQuery === "" ||
        challenge.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        challenge.description.toLowerCase().includes(searchQuery.toLowerCase())

      const matchesStatus = statusFilter === "all" || challenge.status === statusFilter

      return matchesSearch && matchesStatus
    })
  }

  // Filter achievements based on category
  const filteredAchievements = mockChallenges.achievements.filter(
    (achievement) => achievementCategoryFilter === "all" || achievement.category === achievementCategoryFilter,
  )

  return (
    <div className="flex flex-col min-h-screen bg-slate-50">
      <header className="p-4 flex justify-between items-center bg-white sticky top-0 z-10 shadow-sm">
        <div className="flex items-center">
          <Link href="/loyalty" className="mr-3">
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <h1 className="text-lg font-semibold">Tantangan & Pencapaian</h1>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.push("/loyalty/challenges/settings")}
            className="hidden sm:flex"
          >
            Pengaturan
          </Button>
          <Button onClick={() => setShowChallengeDialog(true)} className="bg-blue-500 hover:bg-blue-600" size="sm">
            <Plus className="h-4 w-4 mr-1" /> Tambah Tantangan
          </Button>
        </div>
      </header>

      <div className="p-4">
        <Tabs defaultValue="active" className="mb-4" onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-3 w-full">
            <TabsTrigger value="active">Aktif</TabsTrigger>
            <TabsTrigger value="achievements">Pencapaian</TabsTrigger>
            <TabsTrigger value="history">Riwayat</TabsTrigger>
          </TabsList>

          {/* Active Challenges Tab */}
          <TabsContent value="active" className="mt-4">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 gap-2">
              <div className="relative flex-1 w-full">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <Input
                  placeholder="Cari tantangan..."
                  className="pl-10"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Filter status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Status</SelectItem>
                  <SelectItem value="active">Aktif</SelectItem>
                  <SelectItem value="upcoming">Akan Datang</SelectItem>
                  <SelectItem value="completed">Selesai</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <h3 className="text-lg font-medium mb-3">Tantangan Aktif</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              {getFilteredChallenges(mockChallenges.active).map((challenge) => (
                <Card key={challenge.id} className="p-4">
                  <div className="flex justify-between items-start">
                    <div>
                      <div className="flex items-center gap-2">
                        <h3 className="font-medium">{challenge.name}</h3>
                        <Badge className="bg-green-500">Aktif</Badge>
                      </div>
                      <p className="text-sm text-gray-500 mt-1">{challenge.description}</p>
                      <div className="flex items-center gap-3 mt-2">
                        <div className="flex items-center gap-1 text-sm">
                          <Star className="h-4 w-4 text-yellow-500" />
                          <span className="font-medium">{challenge.reward} poin</span>
                        </div>
                        <div className="flex items-center gap-1 text-sm text-gray-500">
                          <Clock className="h-3 w-3" />
                          <span>{challenge.duration}</span>
                        </div>
                      </div>
                      <div className="flex items-center gap-1 text-sm text-gray-500 mt-1">
                        <Calendar className="h-3 w-3" />
                        <span>
                          {challenge.startDate} - {challenge.endDate}
                        </span>
                      </div>
                    </div>
                    <Badge variant="outline" className="flex items-center gap-1">
                      <Users className="h-3 w-3" />
                      {challenge.participants} peserta
                    </Badge>
                  </div>
                  <div className="mt-3">
                    <div className="flex justify-between text-xs text-gray-500 mb-1">
                      <span>{challenge.completions} selesai</span>
                      <span>{challenge.participants} peserta</span>
                    </div>
                    <Progress value={challenge.progress} className="h-2" />
                  </div>
                  <div className="mt-3 flex justify-end">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-blue-500"
                      onClick={() => router.push(`/loyalty/challenges/${challenge.id}`)}
                    >
                      Detail <ChevronRight className="h-4 w-4 ml-1" />
                    </Button>
                  </div>
                </Card>
              ))}
            </div>

            <h3 className="text-lg font-medium mb-3">Tantangan Mendatang</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-20">
              {getFilteredChallenges(mockChallenges.upcoming).map((challenge) => (
                <Card key={challenge.id} className="p-4">
                  <div className="flex justify-between items-start">
                    <div>
                      <div className="flex items-center gap-2">
                        <h3 className="font-medium">{challenge.name}</h3>
                        <Badge variant="outline" className="border-blue-500 text-blue-500">
                          Akan Datang
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-500 mt-1">{challenge.description}</p>
                      <div className="flex items-center gap-3 mt-2">
                        <div className="flex items-center gap-1 text-sm">
                          <Star className="h-4 w-4 text-yellow-500" />
                          <span className="font-medium">{challenge.reward} poin</span>
                        </div>
                        <div className="flex items-center gap-1 text-sm text-gray-500">
                          <Clock className="h-3 w-3" />
                          <span>{challenge.duration}</span>
                        </div>
                      </div>
                      <div className="flex items-center gap-1 text-sm text-gray-500 mt-1">
                        <Calendar className="h-3 w-3" />
                        <span>
                          {challenge.startDate} - {challenge.endDate}
                        </span>
                      </div>
                    </div>
                    <Button variant="outline" size="sm" className="flex items-center gap-1">
                      <Bell className="h-3 w-3" />
                      Ingatkan
                    </Button>
                  </div>
                  <div className="mt-3 flex justify-end">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-blue-500"
                      onClick={() => router.push(`/loyalty/challenges/${challenge.id}`)}
                    >
                      Detail <ChevronRight className="h-4 w-4 ml-1" />
                    </Button>
                  </div>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Achievements Tab */}
          <TabsContent value="achievements" className="mt-4">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 gap-2">
              <div className="relative flex-1 w-full">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <Input
                  placeholder="Cari pencapaian..."
                  className="pl-10"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              <Select value={achievementCategoryFilter} onValueChange={setAchievementCategoryFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Filter kategori" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Kategori</SelectItem>
                  <SelectItem value="basic">Dasar</SelectItem>
                  <SelectItem value="loyalty">Loyalitas</SelectItem>
                  <SelectItem value="spending">Pengeluaran</SelectItem>
                  <SelectItem value="referral">Referral</SelectItem>
                  <SelectItem value="special">Spesial</SelectItem>
                  <SelectItem value="engagement">Engagement</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-20">
              {filteredAchievements.map((achievement) => (
                <Card key={achievement.id} className="p-4">
                  <div className="flex gap-3">
                    <div className="w-12 h-12 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center">
                      {achievement.icon}
                    </div>
                    <div>
                      <h3 className="font-medium">{achievement.name}</h3>
                      <p className="text-sm text-gray-500">{achievement.description}</p>
                      <div className="flex items-center gap-1 text-sm text-gray-500 mt-1">
                        <ThumbsUp className="h-3 w-3" />
                        <span>Diraih oleh {achievement.earnedBy} pelanggan</span>
                      </div>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* History Tab */}
          <TabsContent value="history" className="mt-4">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium">Riwayat Tantangan</h3>
              <Button variant="outline" size="sm" onClick={() => router.push("/loyalty/challenges/export")}>
                <Download className="h-4 w-4 mr-1" /> Export
              </Button>
            </div>

            <div className="space-y-4 mb-20">
              {getFilteredChallenges(mockChallenges.completed).map((challenge) => (
                <Card key={challenge.id} className="p-4">
                  <div className="flex justify-between items-start">
                    <div>
                      <div className="flex items-center gap-2">
                        <h3 className="font-medium">{challenge.name}</h3>
                        <Badge variant="outline" className="border-gray-500 text-gray-500 flex items-center gap-1">
                          <CheckCircle className="h-3 w-3" /> Selesai
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-500 mt-1">{challenge.description}</p>
                      <div className="flex items-center gap-3 mt-2">
                        <div className="flex items-center gap-1 text-sm">
                          <Star className="h-4 w-4 text-yellow-500" />
                          <span className="font-medium">{challenge.reward} poin</span>
                        </div>
                        <div className="flex items-center gap-1 text-sm text-gray-500">
                          <Calendar className="h-3 w-3" />
                          <span>
                            {challenge.startDate} - {challenge.endDate}
                          </span>
                        </div>
                      </div>
                    </div>
                    <div className="text-sm text-gray-500 flex items-center">
                      <Users className="h-3 w-3 mr-1" />
                      {challenge.completions} dari {challenge.participants} selesai
                    </div>
                  </div>
                  <div className="mt-3">
                    <div className="flex justify-between text-xs text-gray-500 mb-1">
                      <span>{challenge.completions} selesai</span>
                      <span>{challenge.participants} peserta</span>
                    </div>
                    <Progress value={challenge.progress} className="h-2" />
                  </div>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </div>

      {/* Challenge Dialog */}
      <Dialog open={showChallengeDialog} onOpenChange={setShowChallengeDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Tambah Tantangan Baru</DialogTitle>
            <DialogDescription>Buat tantangan untuk meningkatkan engagement pelanggan</DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-2">
            <div className="space-y-2">
              <Label htmlFor="challenge-name">Nama Tantangan</Label>
              <Input id="challenge-name" placeholder="Masukkan nama tantangan" />
            </div>

            <div className="space-y-2">
              <Label htmlFor="challenge-description">Deskripsi</Label>
              <Textarea id="challenge-description" placeholder="Masukkan deskripsi tantangan" />
            </div>

            <div className="space-y-2">
              <Label htmlFor="challenge-reward">Reward (poin)</Label>
              <Input id="challenge-reward" type="number" placeholder="Masukkan jumlah poin" />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="start-date">Tanggal Mulai</Label>
                <Input id="start-date" type="date" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="end-date">Tanggal Selesai</Label>
                <Input id="end-date" type="date" />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="challenge-target">Target Level</Label>
              <Select defaultValue="all">
                <SelectTrigger id="challenge-target">
                  <SelectValue placeholder="Pilih target level" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Level</SelectItem>
                  <SelectItem value="bronze">Bronze</SelectItem>
                  <SelectItem value="silver">Silver</SelectItem>
                  <SelectItem value="gold">Gold</SelectItem>
                  <SelectItem value="platinum">Platinum</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="enable-notifications">Notifikasi</Label>
                <Switch id="enable-notifications" defaultChecked />
              </div>
              <p className="text-xs text-gray-500">Kirim notifikasi kepada pelanggan tentang tantangan ini</p>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowChallengeDialog(false)}>
              Batal
            </Button>
            <Button onClick={() => setShowChallengeDialog(false)}>Simpan</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
