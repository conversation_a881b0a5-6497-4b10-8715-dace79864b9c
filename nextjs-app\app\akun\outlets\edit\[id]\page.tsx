'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Save, MapPin, Trash2, Loader2 } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import {
  useOutlet,
  useUpdateOutlet,
  useDeleteOutlet,
} from '@/hooks/useOutlets';
import {
  useProvinces,
  useCitiesByProvince,
  useTimezones,
} from '@/hooks/useLocations';
import type { UpdateOutletRequest } from '@/lib/api/outlets';

export default function EditOutletPage() {
  const router = useRouter();
  const [outletId, setOutletId] = useState<string | null>(null);

  // Handle Next.js 15 params
  useEffect(() => {
    const getParams = async () => {
      const params = await Promise.resolve({
        id: window.location.pathname.split('/').pop(),
      });
      setOutletId(params.id || null);
    };
    getParams();
  }, []);

  const {
    data: outlet,
    isLoading: outletLoading,
    error: outletError,
  } = useOutlet(outletId ? parseInt(outletId) : 0);

  const updateOutletMutation = useUpdateOutlet();
  const deleteOutletMutation = useDeleteOutlet();

  // Fetch data dari API
  const { data: provinces, isLoading: provincesLoading } = useProvinces();
  const { data: timezones, isLoading: timezonesLoading } = useTimezones();

  const [formData, setFormData] = useState<UpdateOutletRequest>({
    name: '',
    address: '',
    province: '',
    city: '',
    timezone: 'Asia/Jakarta',
    phone: '',
    latitude: undefined,
    longitude: undefined,
    isActive: true,
  });

  const [selectedProvinceId, setSelectedProvinceId] = useState<number | null>(
    null
  );

  // Fetch cities berdasarkan provinsi yang dipilih
  const { data: cities, isLoading: citiesLoading } =
    useCitiesByProvince(selectedProvinceId);

  // Populate form when outlet data is loaded
  useEffect(() => {
    if (outlet) {
      setFormData({
        name: outlet.name,
        address: outlet.address,
        province: outlet.province,
        city: outlet.city,
        timezone: outlet.timezone,
        phone: outlet.phone,
        latitude: outlet.latitude,
        longitude: outlet.longitude,
        isActive: outlet.isActive,
      });
    }
  }, [outlet]);

  // Set province ID when provinces data is loaded and outlet has province
  useEffect(() => {
    if (provinces && outlet?.province) {
      const selectedProvince = provinces.find(
        (p) => p.name === outlet.province
      );
      setSelectedProvinceId(selectedProvince?.id || null);
    }
  }, [provinces, outlet?.province]);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]:
        name === 'latitude' || name === 'longitude'
          ? value === ''
            ? undefined
            : parseFloat(value)
          : value,
    }));
  };

  const handleSelectChange = (name: string, value: string) => {
    if (name === 'isActive') {
      setFormData((prev) => ({ ...prev, [name]: value === 'true' }));
    } else {
      setFormData((prev) => ({ ...prev, [name]: value }));
    }

    // Reset city when province changes
    if (name === 'province') {
      setFormData((prev) => ({ ...prev, city: '' }));

      // Find province ID untuk fetch cities
      const selectedProvince = provinces?.find((p) => p.name === value);
      setSelectedProvinceId(selectedProvince?.id || null);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!outletId) return;

    try {
      await updateOutletMutation.mutateAsync({
        id: parseInt(outletId),
        data: formData,
      });
      router.push('/akun/outlets');
    } catch (error) {
      // Error handling sudah ada di hook
    }
  };

  const handleDelete = async () => {
    if (!outletId) return;

    try {
      await deleteOutletMutation.mutateAsync(parseInt(outletId));
    router.push('/akun/outlets');
    } catch (error) {
      // Error handling sudah ada di hook
    }
  };

  // Loading state
  if (!outletId || outletLoading) {
    return (
      <div className="flex flex-col min-h-screen bg-slate-50">
        <header className="p-4 flex justify-between items-center bg-white sticky top-0 z-10 shadow-sm">
          <div className="flex items-center">
            <Link href="/akun/outlets" className="mr-3">
              <ArrowLeft className="h-5 w-5" />
            </Link>
            <h1 className="text-lg font-semibold">Edit Outlet</h1>
          </div>
        </header>
        <main className="flex-1 p-4 flex items-center justify-center">
          <div className="flex items-center gap-2">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span>Memuat data outlet...</span>
          </div>
        </main>
      </div>
    );
  }

  // Error state
  if (outletError || !outlet) {
    return (
      <div className="flex flex-col min-h-screen bg-slate-50">
        <header className="p-4 flex justify-between items-center bg-white sticky top-0 z-10 shadow-sm">
          <div className="flex items-center">
            <Link href="/akun/outlets" className="mr-3">
              <ArrowLeft className="h-5 w-5" />
            </Link>
            <h1 className="text-lg font-semibold">Edit Outlet</h1>
          </div>
        </header>
        <main className="flex-1 p-4 flex items-center justify-center">
          <div className="text-center">
            <h2 className="text-xl font-semibold mb-2">
              Outlet tidak ditemukan
            </h2>
            <p className="text-gray-600 mb-4">
              Outlet yang Anda cari tidak ditemukan atau telah dihapus.
            </p>
            <Link href="/akun/outlets">
              <Button>Kembali ke Daftar Outlet</Button>
            </Link>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen bg-slate-50">
      <header className="p-4 flex justify-between items-center bg-white sticky top-0 z-10 shadow-sm">
        <div className="flex items-center">
          <Link href="/akun/outlets" className="mr-3">
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <h1 className="text-lg font-semibold">Edit Outlet</h1>
        </div>
        <div className="flex gap-2">
          <AlertDialog>
            <AlertDialogTrigger asChild>
          <Button
                variant="destructive"
                size="sm"
                disabled={deleteOutletMutation.isPending}
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Hapus
          </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Hapus Outlet</AlertDialogTitle>
                <AlertDialogDescription>
                  Apakah Anda yakin ingin menghapus outlet "{outlet.name}"?
                  Tindakan ini tidak dapat dibatalkan dan akan menghapus semua
                  data terkait.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Batal</AlertDialogCancel>
                <AlertDialogAction
                  onClick={handleDelete}
                  className="bg-red-600 hover:bg-red-700"
                  disabled={deleteOutletMutation.isPending}
                >
                  {deleteOutletMutation.isPending ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Menghapus...
                    </>
                  ) : (
                    'Hapus'
                  )}
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
          <Button
            type="submit"
            form="edit-outlet-form"
            className="bg-blue-500 hover:bg-blue-600"
            disabled={updateOutletMutation.isPending}
          >
            <Save className="h-4 w-4 mr-2" />
            {updateOutletMutation.isPending ? 'Menyimpan...' : 'Simpan'}
          </Button>
        </div>
      </header>

      <main className="flex-1 p-4 pb-20">
        <form
          id="edit-outlet-form"
          onSubmit={handleSubmit}
          className="space-y-6"
        >
          {/* Informasi Dasar */}
          <Card className="p-6">
            <h2 className="text-lg font-semibold mb-4">Informasi Dasar</h2>
            <div className="space-y-4">
                <div className="space-y-2">
                <Label htmlFor="name">Nama Outlet *</Label>
                  <Input
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    placeholder="Contoh: Felis Laundry - Cabang Kemang"
                    required
                  />
                </div>

                <div className="space-y-2">
                <Label htmlFor="address">Alamat Lengkap *</Label>
                  <Textarea
                    id="address"
                    name="address"
                    value={formData.address}
                    onChange={handleChange}
                    placeholder="Masukkan alamat lengkap outlet"
                    rows={3}
                    required
                  />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="province">Provinsi *</Label>
                  <Select
                    value={formData.province}
                    onValueChange={(value) =>
                      handleSelectChange('province', value)
                    }
                    disabled={provincesLoading}
                  >
                    <SelectTrigger>
                      <SelectValue
                        placeholder={
                          provincesLoading ? 'Memuat...' : 'Pilih provinsi'
                        }
                      />
                    </SelectTrigger>
                    <SelectContent>
                      {provinces?.map((province) => (
                        <SelectItem key={province.id} value={province.name}>
                          {province.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="city">Kota/Kabupaten *</Label>
                  <Select
                    value={formData.city}
                    onValueChange={(value) => handleSelectChange('city', value)}
                    disabled={!formData.province || citiesLoading}
                  >
                    <SelectTrigger>
                      <SelectValue
                        placeholder={
                          !formData.province
                            ? 'Pilih provinsi dulu'
                            : citiesLoading
                            ? 'Memuat...'
                            : 'Pilih kota'
                        }
                      />
                    </SelectTrigger>
                    <SelectContent>
                      {cities?.map((city) => (
                        <SelectItem key={city.id} value={city.name}>
                          {city.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="timezone">Zona Waktu</Label>
                <Select
                  value={formData.timezone}
                  onValueChange={(value) =>
                    handleSelectChange('timezone', value)
                  }
                  disabled={timezonesLoading}
                >
                  <SelectTrigger>
                    <SelectValue
                      placeholder={
                        timezonesLoading ? 'Memuat...' : 'Pilih zona waktu'
                      }
                    />
                  </SelectTrigger>
                  <SelectContent>
                    {timezones?.map((tz) => (
                      <SelectItem key={tz.id} value={tz.id}>
                        <div className="flex flex-col">
                          <span className="font-medium">{tz.name}</span>
                          <span className="text-sm text-gray-500">
                            {tz.description}
                          </span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="phone">Nomor Telepon *</Label>
                  <Input
                    id="phone"
                    name="phone"
                    value={formData.phone}
                    onChange={handleChange}
                    placeholder="Contoh: 021-7654321"
                    required
                  />
                </div>

                <div className="space-y-2">
                <Label htmlFor="isActive">Status Outlet</Label>
                <Select
                  value={formData.isActive ? 'true' : 'false'}
                  onValueChange={(value) =>
                    handleSelectChange('isActive', value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="true">Aktif</SelectItem>
                    <SelectItem value="false">Tidak Aktif</SelectItem>
                  </SelectContent>
                </Select>
                  </div>
                </div>
              </Card>

          {/* Lokasi Maps */}
          <Card className="p-6">
            <h2 className="text-lg font-semibold mb-4 flex items-center gap-2">
              <MapPin className="h-5 w-5" />
              Lokasi Maps
            </h2>
            <div className="space-y-4">
                  <div className="h-48 bg-gray-100 rounded-md flex items-center justify-center">
                <p className="text-gray-500">Peta akan ditampilkan di sini</p>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="latitude">Latitude</Label>
                    <Input
                      id="latitude"
                      name="latitude"
                    type="number"
                    step="any"
                    value={formData.latitude || ''}
                      onChange={handleChange}
                      placeholder="Contoh: -6.2088"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="longitude">Longitude</Label>
                    <Input
                      id="longitude"
                      name="longitude"
                    type="number"
                    step="any"
                    value={formData.longitude || ''}
                      onChange={handleChange}
                      placeholder="Contoh: 106.8456"
                    />
                  </div>
                </div>

                <Button type="button" variant="outline" className="w-full">
                  Pilih Lokasi di Peta
                </Button>
            </div>
              </Card>
          </form>
      </main>
    </div>
  );
}
