import httpStatus from 'http-status';
import moment from 'moment';
import { AttendanceStatus } from '@prisma/client';
import prisma from '../client';
import ApiError from '../utils/ApiError';

interface CheckInData {
  userId: number;
  outletId: number;
  location?: string;
  pin?: string;
  photo?: string;
  notes?: string;
}

interface CheckOutData {
  userId: number;
  outletId: number;
  photo?: string;
  notes?: string;
}

/**
 * Check in user for attendance
 * @param {CheckInData} checkInData
 * @param {Date} currentTime - Optional current time for testing
 * @returns {Promise<Attendance>}
 */
const checkIn = async (checkInData: CheckInData, currentTime?: Date) => {
  const { userId, outletId, location, pin, photo, notes } = checkInData;

  // Get today's date
  const now = currentTime ? moment(currentTime) : moment();
  const today = now.clone().startOf('day').toDate();

  // Check if user already checked in today
  const existingAttendance = await prisma.attendance.findUnique({
    where: {
      userId_outletId_date: {
        userId,
        outletId,
        date: today
      }
    }
  });

  if (existingAttendance) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Anda sudah melakukan check-in hari ini');
  }

  // Get attendance settings for outlet
  const settings = await prisma.attendanceSettings.findUnique({
    where: { outletId }
  });

  if (!settings) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Pengaturan absensi tidak ditemukan');
  }

  // Validate PIN if required
  if (settings.requirePin && !pin) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'PIN wajib diisi');
  }

  // Validate photo if required
  if (settings.requirePhoto && !photo) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Foto wajib diupload');
  }

  // Find active schedule for current day
  const schedule = await findActiveSchedule(userId, outletId, now.toDate());

  // Calculate late status
  let status: AttendanceStatus = AttendanceStatus.PRESENT;
  let isLate = false;
  let lateByMinutes = 0;
  let scheduleId = null;

  if (schedule) {
    scheduleId = schedule.id;
    lateByMinutes = calculateLateMinutes(schedule, now.toDate());

    if (lateByMinutes > settings.defaultLateThreshold) {
      status = AttendanceStatus.LATE;
      isLate = true;
    }
  }

  // Create attendance record
  const attendance = await prisma.attendance.create({
    data: {
      userId,
      outletId,
      scheduleId,
      checkIn: now.toDate(),
      checkInPhoto: photo,
      location,
      notes,
      status,
      isLate,
      lateByMinutes,
      date: today
    },
    include: {
      user: {
        select: {
          id: true,
          name: true,
          email: true
        }
      },
      schedule: true
    }
  });

  return attendance;
};

/**
 * Check out user for attendance
 * @param {CheckOutData} checkOutData
 * @param {Date} currentTime - Optional current time for testing
 * @returns {Promise<Attendance>}
 */
const checkOut = async (checkOutData: CheckOutData, currentTime?: Date) => {
  const { userId, outletId, photo, notes } = checkOutData;

  // Get today's date
  const now = currentTime ? moment(currentTime) : moment();
  const today = now.clone().startOf('day').toDate();

  // Find existing attendance record for today
  const attendance = await prisma.attendance.findUnique({
    where: {
      userId_outletId_date: {
        userId,
        outletId,
        date: today
      }
    }
  });

  if (!attendance) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Anda belum melakukan check-in hari ini');
  }

  if (attendance.checkOut) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Anda sudah melakukan check-out hari ini');
  }

  // Get attendance settings for overtime calculation
  const settings = await prisma.attendanceSettings.findUnique({
    where: { outletId }
  });

  if (!settings) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Pengaturan absensi tidak ditemukan');
  }

  const checkInTime = moment(attendance.checkIn);

  // Calculate working hours with better precision
  const workingHours = now.diff(checkInTime, 'minutes') / 60;

  // Calculate overtime hours
  const overtimeHours = Math.max(0, workingHours - settings.overtimeThreshold);

  // Update attendance record
  const updatedAttendance = await prisma.attendance.update({
    where: { id: attendance.id },
    data: {
      checkOut: now.toDate(),
      checkOutPhoto: photo,
      workingHours: Math.round(workingHours * 100) / 100, // Round to 2 decimal places
      overtimeHours: Math.round(overtimeHours * 100) / 100,
      notes: notes || attendance.notes
    },
    include: {
      user: {
        select: {
          id: true,
          name: true,
          email: true
        }
      },
      schedule: true
    }
  });

  return updatedAttendance;
};

/**
 * Get today's attendance status for user
 * @param {number} userId
 * @param {number} outletId
 * @returns {Promise<Attendance | null>}
 */
const getTodayStatus = async (userId: number, outletId: number) => {
  const today = moment().startOf('day').toDate();

  const attendance = await prisma.attendance.findUnique({
    where: {
      userId_outletId_date: {
        userId,
        outletId,
        date: today
      }
    },
    include: {
      user: {
        select: {
          id: true,
          name: true,
          email: true
        }
      },
      schedule: true
    }
  });

  return attendance;
};

/**
 * Find active schedule for user on current day
 * @param {number} userId
 * @param {number} outletId
 * @returns {Promise<WorkSchedule | null>}
 */
const findActiveSchedule = async (userId: number, outletId: number, currentTime: Date) => {
  const currentDay = moment(currentTime).day();
  const now = moment(currentTime);

  const schedule = await prisma.workSchedule.findFirst({
    where: {
      userId,
      outletId,
      dayOfWeek: currentDay,
      isActive: true,
      effectiveFrom: {
        lte: now.toDate()
      },
      OR: [{ effectiveTo: null }, { effectiveTo: { gte: now.toDate() } }]
    },
    orderBy: {
      effectiveFrom: 'desc'
    }
  });

  return schedule;
};

/**
 * Calculate late minutes based on schedule
 * @param {Object} schedule
 * @param {Date} checkInTime
 * @returns {number}
 */
const calculateLateMinutes = (schedule: any, checkInTime: Date): number => {
  const checkInMoment = moment(checkInTime);

  // Parse schedule start time and set it to the same date as check-in
  const [hours, minutes] = schedule.startTime.split(':').map(Number);
  const scheduledStart = moment(checkInTime).hour(hours).minute(minutes).second(0).millisecond(0);

  const diffMinutes = checkInMoment.diff(scheduledStart, 'minutes');

  // Return 0 if early or on time, otherwise return the late minutes
  return Math.max(0, diffMinutes);
};

/**
 * Get attendance history for user
 * @param {number} userId
 * @param {number} outletId
 * @param {object} options
 * @returns {Promise<Attendance[]>}
 */
const getAttendanceHistory = async (userId: number, outletId: number, options: any = {}) => {
  const { startDate, endDate, status, limit = 30, offset = 0 } = options;

  const where: any = {
    userId,
    outletId
  };

  if (startDate && endDate) {
    where.date = {
      gte: startDate,
      lte: endDate
    };
  } else if (startDate) {
    where.date = {
      gte: startDate
    };
  } else if (endDate) {
    where.date = {
      lte: endDate
    };
  }

  if (status) {
    where.status = status;
  }

  const attendances = await prisma.attendance.findMany({
    where,
    include: {
      schedule: {
        select: {
          name: true,
          startTime: true,
          endTime: true
        }
      }
    },
    orderBy: {
      date: 'desc'
    },
    take: limit,
    skip: offset
  });

  return attendances;
};

/**
 * Get team attendance for specific date
 * @param {number} outletId
 * @param {Date} date
 * @returns {Promise<Attendance[]>}
 */
const getTeamAttendance = async (outletId: number, date: Date = new Date()) => {
  const targetDate = moment(date).startOf('day').toDate();
  const nextDay = moment(date).add(1, 'day').startOf('day').toDate();

  const attendances = await prisma.attendance.findMany({
    where: {
      outletId,
      date: {
        gte: targetDate,
        lt: nextDay
      }
    },
    include: {
      user: {
        select: {
          id: true,
          name: true,
          email: true
        }
      },
      schedule: {
        select: {
          name: true,
          startTime: true,
          endTime: true
        }
      }
    },
    orderBy: [{ status: 'asc' }, { checkIn: 'asc' }]
  });

  return attendances;
};

export default {
  checkIn,
  checkOut,
  getTodayStatus,
  findActiveSchedule,
  calculateLateMinutes,
  getAttendanceHistory,
  getTeamAttendance
};
