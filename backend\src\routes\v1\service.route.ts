import express from 'express';
import auth from '../../middlewares/auth';
import validate from '../../middlewares/validate';
import serviceValidation from '../../validations/service.validation';
import serviceController from '../../controllers/service.controller';

const router = express.Router();

router
  .route('/')
  .post(
    auth('manageServices'),
    validate(serviceValidation.createService),
    serviceController.createService
  )
  .get(auth('getServices'), validate(serviceValidation.getServices), serviceController.getServices);

router
  .route('/:serviceId')
  .get(auth('getServices'), validate(serviceValidation.getService), serviceController.getService)
  .patch(
    auth('manageServices'),
    validate(serviceValidation.updateService),
    serviceController.updateService
  )
  .delete(
    auth('manageServices'),
    validate(serviceValidation.deleteService),
    serviceController.deleteService
  );

export default router;

/**
 * @swagger
 * tags:
 *   name: Services
 *   description: Service management and retrieval
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     Service:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: Service ID
 *         name:
 *           type: string
 *           description: Service name
 *         description:
 *           type: string
 *           description: Service description
 *         price:
 *           type: number
 *           description: Service price
 *         unit:
 *           type: string
 *           enum: [kg, pcs, m², pair, lot]
 *           description: Service unit
 *         estimationHours:
 *           type: integer
 *           description: Estimated duration in hours
 *         isActive:
 *           type: boolean
 *           description: Service active status
 *         outletId:
 *           type: integer
 *           description: Outlet ID
 *         categoryId:
 *           type: integer
 *           nullable: true
 *           description: Service category ID
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Service creation timestamp
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Service last update timestamp
 *         outlet:
 *           type: object
 *           properties:
 *             id:
 *               type: integer
 *             name:
 *               type: string
 *           description: Outlet information
 *         category:
 *           type: object
 *           nullable: true
 *           properties:
 *             id:
 *               type: integer
 *             name:
 *               type: string
 *             description:
 *               type: string
 *           description: Service category information
 *       example:
 *         id: 1
 *         name: "Cuci Kering"
 *         description: "Layanan cuci dan kering pakaian"
 *         price: 8000
 *         unit: "kg"
 *         estimationHours: 24
 *         isActive: true
 *         outletId: 1
 *         categoryId: 1
 *         createdAt: "2024-01-01T00:00:00.000Z"
 *         updatedAt: "2024-01-01T00:00:00.000Z"
 */

/**
 * @swagger
 * /services:
 *   post:
 *     summary: Create a service
 *     description: Create a new service with outlet-specific pricing.
 *     tags: [Services]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - price
 *               - unit
 *               - outletId
 *             properties:
 *               name:
 *                 type: string
 *                 minLength: 1
 *                 maxLength: 100
 *                 description: Service name
 *               description:
 *                 type: string
 *                 maxLength: 500
 *                 description: Service description
 *               price:
 *                 type: number
 *                 minimum: 0
 *                 description: Service price
 *               unit:
 *                 type: string
 *                 enum: [kg, pcs, m², pair, lot]
 *                 description: Service unit
 *               estimationHours:
 *                 type: integer
 *                 minimum: 1
 *                 description: Estimated completion time in hours
 *               isActive:
 *                 type: boolean
 *                 description: Service active status
 *               outletId:
 *                 type: integer
 *                 minimum: 1
 *                 description: Outlet ID where this service belongs
 *               categoryId:
 *                 type: integer
 *                 minimum: 1
 *                 description: Service category ID (optional)
 *             example:
 *               name: "Cuci Kering"
 *               description: "Layanan cuci dan kering pakaian"
 *               price: 8000
 *               unit: "kg"
 *               estimationHours: 24
 *               isActive: true
 *               outletId: 1
 *               categoryId: 1
 *     responses:
 *       "201":
 *         description: Created
 *         content:
 *           application/json:
 *             schema:
 *                $ref: '#/components/schemas/Service'
 *       "400":
 *         $ref: '#/components/responses/BadRequest'
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "403":
 *         $ref: '#/components/responses/Forbidden'
 *
 *   get:
 *     summary: Get all services
 *     description: Retrieve all services with optional filtering and pagination.
 *     tags: [Services]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search by service name or description
 *       - in: query
 *         name: unit
 *         schema:
 *           type: string
 *           enum: [kg, pcs, m², pair, lot]
 *         description: Filter by service unit
 *       - in: query
 *         name: isActive
 *         schema:
 *           type: boolean
 *         description: Filter by active status
 *       - in: query
 *         name: outletId
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Filter services available for specific outlet
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *         description: sort by query in the form of field:desc/asc (ex. name:asc)
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         default: 10
 *         description: Maximum number of services
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number
 *     responses:
 *       "200":
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 results:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Service'
 *                 page:
 *                   type: integer
 *                   example: 1
 *                 limit:
 *                   type: integer
 *                   example: 10
 *                 totalPages:
 *                   type: integer
 *                   example: 1
 *                 totalResults:
 *                   type: integer
 *                   example: 1
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "403":
 *         $ref: '#/components/responses/Forbidden'
 */

/**
 * @swagger
 * /services/{serviceId}:
 *   get:
 *     summary: Get a service
 *     description: Retrieve a specific service by ID.
 *     tags: [Services]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: serviceId
 *         required: true
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Service ID
 *     responses:
 *       "200":
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *                $ref: '#/components/schemas/Service'
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "403":
 *         $ref: '#/components/responses/Forbidden'
 *       "404":
 *         $ref: '#/components/responses/NotFound'
 *
 *   patch:
 *     summary: Update a service
 *     description: Update service information. At least one field is required.
 *     tags: [Services]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: serviceId
 *         required: true
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Service ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             minProperties: 1
 *             properties:
 *               name:
 *                 type: string
 *                 minLength: 1
 *                 maxLength: 100
 *                 description: Service name
 *               description:
 *                 type: string
 *                 maxLength: 500
 *                 description: Service description
 *               price:
 *                 type: number
 *                 minimum: 0
 *                 description: Service price
 *               unit:
 *                 type: string
 *                 enum: [kg, pcs, m², pair, lot]
 *                 description: Service unit
 *               estimationHours:
 *                 type: integer
 *                 minimum: 1
 *                 description: Estimated completion time in hours
 *               isActive:
 *                 type: boolean
 *                 description: Service active status
 *               outletId:
 *                 type: integer
 *                 minimum: 1
 *                 description: Outlet ID where this service belongs
 *               categoryId:
 *                 type: integer
 *                 minimum: 1
 *                 description: Service category ID (optional)
 *             example:
 *               name: "Cuci Kering Premium"
 *               description: "Layanan cuci dan kering pakaian dengan deterjen premium"
 *               price: 10000
 *               estimationHours: 20
 *               isActive: true
 *               outletId: 1
 *               categoryId: 1
 *     responses:
 *       "200":
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *                $ref: '#/components/schemas/Service'
 *       "400":
 *         $ref: '#/components/responses/BadRequest'
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "403":
 *         $ref: '#/components/responses/Forbidden'
 *       "404":
 *         $ref: '#/components/responses/NotFound'
 *
 *   delete:
 *     summary: Delete a service
 *     description: Delete a service by ID.
 *     tags: [Services]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: serviceId
 *         required: true
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Service ID
 *     responses:
 *       "204":
 *         description: No content
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "403":
 *         $ref: '#/components/responses/Forbidden'
 *       "404":
 *         $ref: '#/components/responses/NotFound'
 */
