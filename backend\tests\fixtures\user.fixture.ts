import { faker } from '@faker-js/faker';
import bcrypt from 'bcryptjs';
import { Role } from '@prisma/client';
import prisma from '../../src/client';

const password = 'password1';
const salt = bcrypt.genSaltSync(8);
const hashedPassword = bcrypt.hashSync(password, salt);

export const userOne = {
  id: 1, // Will be overridden with actual ID from database
  name: faker.name.fullName(),
  email: faker.internet.email().toLowerCase(),
  phone: faker.phone.number('08##########'),
  password,
  role: Role.OWNER,
  isEmailVerified: true,
  isPhoneVerified: true,
  outletId: null as number | null // Will be set after outlet creation
};

export const userTwo = {
  id: 2, // Will be overridden with actual ID from database
  name: faker.name.fullName(),
  email: faker.internet.email().toLowerCase(),
  phone: faker.phone.number('08##########'),
  password,
  role: Role.OWNER,
  isEmailVerified: true,
  isPhoneVerified: true,
  outletId: null as number | null // Will be set after outlet creation
};

export const admin = {
  id: 3, // Will be overridden with actual ID from database
  name: faker.name.fullName(),
  email: faker.internet.email().toLowerCase(),
  phone: faker.phone.number('08##########'),
  password,
  role: Role.ADMIN,
  isEmailVerified: true,
  isPhoneVerified: true,
  outletId: null as number | null
};

export const employee = {
  id: 4, // Will be overridden with actual ID from database
  name: faker.name.fullName(),
  email: faker.internet.email().toLowerCase(),
  phone: faker.phone.number('08##########'),
  password,
  role: Role.EMPLOYEE,
  isEmailVerified: true,
  isPhoneVerified: true,
  outletId: null as number | null // Will be set after outlet creation
};

export const insertUsers = async (users: any[]) => {
  const createdUsers = [];
  for (const user of users) {
    const { id, ...userData } = user; // Remove id from data to let DB auto-generate
    const createdUser = await prisma.user.create({
      data: { ...userData, password: hashedPassword }
    });
    createdUsers.push(createdUser);
  }
  return createdUsers;
};

// Helper untuk create outlet
export const createOutletForOwner = async (ownerId: number) => {
  return await prisma.outlet.create({
    data: {
      name: `${faker.company.name()} Laundry`,
      address: faker.address.streetAddress(),
      province: 'DKI Jakarta',
      city: 'Jakarta Pusat',
      phone: faker.phone.number('021########'),
      ownerId: ownerId
    }
  });
};

// Helper untuk create employee dengan outlet
export const createEmployeeWithOutlet = async (outletId: number) => {
  const employeeData = {
    name: faker.name.fullName(),
    email: faker.internet.email().toLowerCase(),
    phone: faker.phone.number('08##########'),
    password: hashedPassword,
    role: Role.EMPLOYEE,
    isEmailVerified: true,
    isPhoneVerified: true,
    outletId: outletId
  };

  return await prisma.user.create({
    data: employeeData
  });
};

export { password };
