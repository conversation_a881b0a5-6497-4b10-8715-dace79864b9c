import { setCookie, get<PERSON><PERSON>ie, delete<PERSON>ookie } from './cookie-utils';

// Auth data sync utility
export function syncAuthToCookies(user: any, tokens: any) {
  if (typeof window === 'undefined') return;

  try {
    setCookie('user', JSON.stringify(user), 7);
    setCookie('tokens', JSON.stringify(tokens), 7);
  } catch (error) {
    console.error('Error syncing auth to cookies:', error);
  }
}

export function syncAuthToLocalStorage(user: any, tokens: any) {
  if (typeof window === 'undefined') return;

  try {
    localStorage.setItem('user', JSON.stringify(user));
    localStorage.setItem('tokens', JSON.stringify(tokens));

    // Also set individual token items for compatibility
    if (tokens?.access?.token) {
      localStorage.setItem('accessToken', tokens.access.token);
    }
    if (tokens?.refresh?.token) {
      localStorage.setItem('refreshToken', tokens.refresh.token);
    }
  } catch (error) {
    console.error('Error syncing auth to localStorage:', error);
  }
}

export function syncAuthData(user: any, tokens: any) {
  syncAuthToLocalStorage(user, tokens);
  syncAuthToCookies(user, tokens);
}

export function clearAuthData() {
  if (typeof window === 'undefined') return;

  // Clear localStorage
  localStorage.removeItem('user');
  localStorage.removeItem('tokens');
  localStorage.removeItem('accessToken');
  localStorage.removeItem('refreshToken');

  // Clear cookies
  deleteCookie('user');
  deleteCookie('tokens');
}

export function getAuthFromStorage(): { user: any; tokens: any } | null {
  if (typeof window === 'undefined') return null;

  try {
    const userStr = localStorage.getItem('user');
    const tokensStr = localStorage.getItem('tokens');

    if (userStr && tokensStr) {
      return {
        user: JSON.parse(userStr),
        tokens: JSON.parse(tokensStr),
      };
    }
  } catch (error) {
    console.error('Error reading auth from storage:', error);
  }

  return null;
}

export function getAuthFromCookies(): { user: any; tokens: any } | null {
  if (typeof window === 'undefined') return null;

  try {
    const userStr = getCookie('user');
    const tokensStr = getCookie('tokens');

    if (userStr && tokensStr) {
      return {
        user: JSON.parse(userStr),
        tokens: JSON.parse(tokensStr),
      };
    }
  } catch (error) {
    console.error('Error reading auth from cookies:', error);
  }

  return null;
}

export function ensureAuthSync() {
  if (typeof window === 'undefined') return;

  const storageAuth = getAuthFromStorage();
  const cookieAuth = getAuthFromCookies();

  // If localStorage has data but cookies don't, sync to cookies
  if (storageAuth && !cookieAuth) {
    syncAuthToCookies(storageAuth.user, storageAuth.tokens);
  }

  // If cookies have data but localStorage doesn't, sync to localStorage
  if (cookieAuth && !storageAuth) {
    syncAuthToLocalStorage(cookieAuth.user, cookieAuth.tokens);
  }
}
