import express from 'express';
import auth from '../../middlewares/auth';
import validate from '../../middlewares/validate';
import { promotionValidation } from '../../validations';
import { promotionController } from '../../controllers';

const router = express.Router();

router
  .route('/')
  .post(auth(), validate(promotionValidation.createPromotion), promotionController.createPromotion)
  .get(auth(), validate(promotionValidation.getPromotions), promotionController.getPromotions);

router
  .route('/validate')
  .post(
    auth(),
    validate(promotionValidation.validatePromotion),
    promotionController.validatePromotion
  );

router
  .route('/:promotionId')
  .get(auth(), validate(promotionValidation.getPromotion), promotionController.getPromotion)
  .patch(auth(), validate(promotionValidation.updatePromotion), promotionController.updatePromotion)
  .delete(
    auth(),
    validate(promotionValidation.deletePromotion),
    promotionController.deletePromotion
  );

export default router;

/**
 * @swagger
 * tags:
 *   name: Promotions
 *   description: Promotion management for laundry orders
 */

/**
 * @swagger
 * /promotions:
 *   post:
 *     summary: Create a new promotion
 *     description: Add a new promotion to the outlet's collection. Promotion codes must be unique within each outlet.
 *     tags: [Promotions]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - code
 *               - discountType
 *               - discountValue
 *               - validFrom
 *               - validUntil
 *               - outletId
 *             properties:
 *               name:
 *                 type: string
 *                 maxLength: 200
 *                 description: Promotion name
 *               code:
 *                 type: string
 *                 maxLength: 50
 *                 description: Promotion code (must be unique per outlet)
 *               description:
 *                 type: string
 *                 maxLength: 500
 *                 description: Promotion description
 *               discountType:
 *                 type: string
 *                 enum: [PERCENTAGE, FIXED]
 *                 description: Type of discount
 *               discountValue:
 *                 type: number
 *                 minimum: 0
 *                 description: Discount value (percentage or fixed amount)
 *               minOrderValue:
 *                 type: number
 *                 minimum: 0
 *                 default: 0
 *                 description: Minimum order value to use this promotion
 *               maxDiscountAmount:
 *                 type: number
 *                 minimum: 0
 *                 description: Maximum discount amount (for percentage type)
 *               isActive:
 *                 type: boolean
 *                 default: true
 *                 description: Whether the promotion is active
 *               validFrom:
 *                 type: string
 *                 format: date-time
 *                 description: Promotion start date
 *               validUntil:
 *                 type: string
 *                 format: date-time
 *                 description: Promotion end date
 *               usageLimit:
 *                 type: integer
 *                 minimum: 1
 *                 description: Usage limit (null for unlimited)
 *               isFirstTimeOnly:
 *                 type: boolean
 *                 default: false
 *                 description: Only for first-time customers
 *               applicableServices:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Array of service IDs (empty for all services)
 *               outletId:
 *                 type: integer
 *                 description: Outlet ID
 *             example:
 *               name: "Diskon Akhir Bulan"
 *               code: "ENDMONTH25"
 *               description: "Diskon 25% untuk semua layanan cuci"
 *               discountType: "PERCENTAGE"
 *               discountValue: 25
 *               minOrderValue: 50000
 *               maxDiscountAmount: 100000
 *               isActive: true
 *               validFrom: "2024-03-25T00:00:00Z"
 *               validUntil: "2024-04-05T23:59:59Z"
 *               usageLimit: 100
 *               isFirstTimeOnly: false
 *               applicableServices: []
 *               outletId: 1
 *     responses:
 *       "201":
 *         description: Created
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Promotion'
 *       "400":
 *         $ref: '#/components/responses/BadRequest'
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "403":
 *         $ref: '#/components/responses/Forbidden'
 *
 *   get:
 *     summary: Get all promotions
 *     description: Retrieve all promotions for the current outlet with optional filtering and pagination.
 *     tags: [Promotions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search in name, code, or description
 *       - in: query
 *         name: name
 *         schema:
 *           type: string
 *         description: Filter by promotion name
 *       - in: query
 *         name: code
 *         schema:
 *           type: string
 *         description: Filter by promotion code
 *       - in: query
 *         name: discountType
 *         schema:
 *           type: string
 *           enum: [PERCENTAGE, FIXED]
 *         description: Filter by discount type
 *       - in: query
 *         name: isActive
 *         schema:
 *           type: boolean
 *         description: Filter by active status
 *       - in: query
 *         name: isExpired
 *         schema:
 *           type: boolean
 *         description: Filter by expiration status
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *         description: Sort by query in the form of field:desc/asc (ex. name:asc, createdAt:desc)
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         default: 10
 *         description: Maximum number of promotions per page
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         default: 1
 *         description: Page number
 *       - in: query
 *         name: outletId
 *         schema:
 *           type: integer
 *         description: Outlet ID
 *     responses:
 *       "200":
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 results:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Promotion'
 *                 page:
 *                   type: integer
 *                   example: 1
 *                 limit:
 *                   type: integer
 *                   example: 10
 *                 totalPages:
 *                   type: integer
 *                   example: 1
 *                 totalResults:
 *                   type: integer
 *                   example: 5
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "403":
 *         $ref: '#/components/responses/Forbidden'
 */

/**
 * @swagger
 * /promotions/validate:
 *   post:
 *     summary: Validate promotion code
 *     description: Validate a promotion code for a specific order and customer.
 *     tags: [Promotions]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - code
 *               - orderTotal
 *               - customerId
 *               - outletId
 *             properties:
 *               code:
 *                 type: string
 *                 description: Promotion code to validate
 *               orderTotal:
 *                 type: number
 *                 minimum: 0
 *                 description: Total order amount
 *               customerId:
 *                 type: integer
 *                 description: Customer ID
 *               outletId:
 *                 type: integer
 *                 description: Outlet ID
 *             example:
 *               code: "ENDMONTH25"
 *               orderTotal: 150000
 *               customerId: 1
 *               outletId: 1
 *     responses:
 *       "200":
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 promotion:
 *                   $ref: '#/components/schemas/Promotion'
 *                 discountAmount:
 *                   type: number
 *                   description: Calculated discount amount
 *                 isValid:
 *                   type: boolean
 *                   description: Whether the promotion is valid
 *       "400":
 *         $ref: '#/components/responses/BadRequest'
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "404":
 *         $ref: '#/components/responses/NotFound'
 */

/**
 * @swagger
 * /promotions/{promotionId}:
 *   get:
 *     summary: Get promotion by ID
 *     description: Retrieve a specific promotion by its ID.
 *     tags: [Promotions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: promotionId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Promotion ID
 *     responses:
 *       "200":
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Promotion'
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "403":
 *         $ref: '#/components/responses/Forbidden'
 *       "404":
 *         $ref: '#/components/responses/NotFound'
 *
 *   patch:
 *     summary: Update promotion
 *     description: Update promotion information. At least one field must be provided.
 *     tags: [Promotions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: promotionId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Promotion ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 maxLength: 200
 *                 description: Promotion name
 *               code:
 *                 type: string
 *                 maxLength: 50
 *                 description: Promotion code
 *               description:
 *                 type: string
 *                 maxLength: 500
 *                 description: Promotion description
 *               discountType:
 *                 type: string
 *                 enum: [PERCENTAGE, FIXED]
 *                 description: Type of discount
 *               discountValue:
 *                 type: number
 *                 minimum: 0
 *                 description: Discount value
 *               minOrderValue:
 *                 type: number
 *                 minimum: 0
 *                 description: Minimum order value
 *               maxDiscountAmount:
 *                 type: number
 *                 minimum: 0
 *                 description: Maximum discount amount
 *               isActive:
 *                 type: boolean
 *                 description: Whether the promotion is active
 *               validFrom:
 *                 type: string
 *                 format: date-time
 *                 description: Promotion start date
 *               validUntil:
 *                 type: string
 *                 format: date-time
 *                 description: Promotion end date
 *               usageLimit:
 *                 type: integer
 *                 minimum: 1
 *                 description: Usage limit
 *               isFirstTimeOnly:
 *                 type: boolean
 *                 description: Only for first-time customers
 *               applicableServices:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Array of service IDs
 *               outletId:
 *                 type: integer
 *                 description: Outlet ID
 *             minProperties: 1
 *             example:
 *               name: "Diskon Akhir Bulan - Updated"
 *               isActive: false
 *               outletId: 1
 *     responses:
 *       "200":
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Promotion'
 *       "400":
 *         $ref: '#/components/responses/BadRequest'
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "403":
 *         $ref: '#/components/responses/Forbidden'
 *       "404":
 *         $ref: '#/components/responses/NotFound'
 *
 *   delete:
 *     summary: Delete promotion
 *     description: Delete a promotion. Cannot delete if the promotion is being used in any orders.
 *     tags: [Promotions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: promotionId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Promotion ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - outletId
 *             properties:
 *               outletId:
 *                 type: integer
 *                 description: Outlet ID
 *     responses:
 *       "204":
 *         description: No content - promotion deleted successfully
 *       "400":
 *         description: Bad request - promotion is being used in orders
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 400
 *                 message:
 *                   type: string
 *                   example: "Cannot delete promotion. It is being used in 5 order(s)."
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "403":
 *         $ref: '#/components/responses/Forbidden'
 *       "404":
 *         $ref: '#/components/responses/NotFound'
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     Promotion:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: Unique promotion identifier
 *         name:
 *           type: string
 *           description: Promotion name
 *         code:
 *           type: string
 *           description: Promotion code
 *         description:
 *           type: string
 *           description: Promotion description
 *         discountType:
 *           type: string
 *           enum: [PERCENTAGE, FIXED]
 *           description: Type of discount
 *         discountValue:
 *           type: number
 *           description: Discount value (percentage or fixed amount)
 *         minOrderValue:
 *           type: number
 *           description: Minimum order value to use this promotion
 *         maxDiscountAmount:
 *           type: number
 *           description: Maximum discount amount (for percentage type)
 *         isActive:
 *           type: boolean
 *           description: Whether the promotion is active
 *         validFrom:
 *           type: string
 *           format: date-time
 *           description: Promotion start date
 *         validUntil:
 *           type: string
 *           format: date-time
 *           description: Promotion end date
 *         usageLimit:
 *           type: integer
 *           description: Usage limit (null for unlimited)
 *         usageCount:
 *           type: integer
 *           description: Current usage count
 *         isFirstTimeOnly:
 *           type: boolean
 *           description: Only for first-time customers
 *         applicableServices:
 *           type: array
 *           items:
 *             type: string
 *           description: Array of service IDs (empty for all services)
 *         outletId:
 *           type: integer
 *           description: Outlet ID that owns this promotion
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Creation timestamp
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Last update timestamp
 *       example:
 *         id: 1
 *         name: "Diskon Akhir Bulan"
 *         code: "ENDMONTH25"
 *         description: "Diskon 25% untuk semua layanan cuci"
 *         discountType: "PERCENTAGE"
 *         discountValue: 25
 *         minOrderValue: 50000
 *         maxDiscountAmount: 100000
 *         isActive: true
 *         validFrom: "2024-03-25T00:00:00Z"
 *         validUntil: "2024-04-05T23:59:59Z"
 *         usageLimit: 100
 *         usageCount: 45
 *         isFirstTimeOnly: false
 *         applicableServices: []
 *         outletId: 1
 *         createdAt: "2024-01-15T08:00:00Z"
 *         updatedAt: "2024-01-15T08:00:00Z"
 */
