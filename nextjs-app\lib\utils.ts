import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';
import type {
  OrderStatus,
  OrderItemStatus,
  PaymentStatus,
} from '@/types/orders';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// Order Status Mapping untuk UI
export const getOrderStatusLabel = (status: string): string => {
  const statusLabels: Record<string, string> = {
    KONFIRMASI: 'Konfirmasi',
    PICKUP: 'Pickup',
    PENDING: 'Menunggu',
    PROCESSING: 'Diproses',
    READY: 'Siap',
    READY_FOR_PICKUP: 'Siap Pickup',
    COMPLETED: 'Selesai',
    CANCELLED: 'Dibatalkan',
  };
  return statusLabels[status] || status;
};

export const getOrderStatusColor = (status: string): string => {
  const statusColors: Record<string, string> = {
    KONFIRMASI: 'bg-blue-100 text-blue-800',
    PICKUP: 'bg-orange-100 text-orange-800',
    PENDING: 'bg-yellow-100 text-yellow-800',
    PROCESSING: 'bg-purple-100 text-purple-800',
    READY: 'bg-green-100 text-green-800',
    READY_FOR_PICKUP: 'bg-teal-100 text-teal-800',
    COMPLETED: 'bg-gray-100 text-gray-800',
    CANCELLED: 'bg-red-100 text-red-800',
  };
  return statusColors[status] || 'bg-gray-100 text-gray-800';
};

export const getOrderItemStatusLabel = (status: string): string => {
  const statusLabels: Record<string, string> = {
    PENDING: 'Menunggu',
    WASHING: 'Cuci',
    DRYING: 'Kering',
    IRONING: 'Setrika',
    PACKING: 'Kemas',
    COMPLETED: 'Selesai',
    CANCELLED: 'Batal',
  };
  return statusLabels[status] || status;
};

export const getOrderItemStatusColor = (status: string): string => {
  const statusColors: Record<string, string> = {
    PENDING: 'bg-yellow-100 text-yellow-800',
    WASHING: 'bg-blue-100 text-blue-800',
    DRYING: 'bg-orange-100 text-orange-800',
    IRONING: 'bg-purple-100 text-purple-800',
    PACKING: 'bg-teal-100 text-teal-800',
    COMPLETED: 'bg-green-100 text-green-800',
    CANCELLED: 'bg-red-100 text-red-800',
  };
  return statusColors[status] || 'bg-gray-100 text-gray-800';
};

export const getPaymentStatusLabel = (status: string): string => {
  const statusLabels: Record<string, string> = {
    UNPAID: 'Belum Bayar',
    PARTIAL: 'Bayar Sebagian',
    PAID: 'Lunas',
    REFUNDED: 'Dikembalikan',
  };
  return statusLabels[status] || status;
};

export const getPaymentStatusColor = (status: string): string => {
  const statusColors: Record<string, string> = {
    UNPAID: 'bg-red-100 text-red-800',
    PARTIAL: 'bg-yellow-100 text-yellow-800',
    PAID: 'bg-green-100 text-green-800',
    REFUNDED: 'bg-gray-100 text-gray-800',
  };
  return statusColors[status] || 'bg-gray-100 text-gray-800';
};

// Service Icon Mapping untuk Order Detail
export const SERVICE_ICONS = {
  shirt: 'shirt',
  pants: 'pants',
  bed: 'bed',
  package: 'package',
  default: 'shirt',
} as const;

// Order Status Badge Variants
export function getOrderStatusBadgeVariant(
  status: OrderStatus
): 'default' | 'secondary' | 'destructive' | 'outline' {
  switch (status) {
    case 'COMPLETED':
      return 'default';
    case 'CANCELLED':
      return 'destructive';
    case 'PROCESSING':
    case 'READY':
      return 'secondary';
    default:
      return 'outline';
  }
}

// Order Item Status Badge Variants
export function getOrderItemStatusBadgeVariant(
  status: OrderItemStatus
): 'default' | 'secondary' | 'destructive' | 'outline' {
  switch (status) {
    case 'COMPLETED':
      return 'default';
    case 'CANCELLED':
      return 'destructive';
    case 'WASHING':
    case 'DRYING':
    case 'IRONING':
    case 'PACKING':
      return 'secondary';
    default:
      return 'outline';
  }
}

// Payment Status Badge Variants
export function getPaymentStatusBadgeVariant(
  status: PaymentStatus
): 'default' | 'secondary' | 'destructive' | 'outline' {
  switch (status) {
    case 'PAID':
      return 'default';
    case 'REFUNDED':
      return 'destructive';
    case 'PARTIAL':
      return 'secondary';
    default:
      return 'outline';
  }
}

// Format Indonesian Currency
export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
  }).format(amount);
}

// Format Indonesian Date
export function formatDate(
  dateString: string,
  options?: Intl.DateTimeFormatOptions
): string {
  return new Date(dateString).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    ...options,
  });
}

// Format Indonesian DateTime
export function formatDateTime(dateString: string): string {
  return new Date(dateString).toLocaleString('id-ID', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
}

// Calculate days between dates
export function calculateDaysDifference(
  startDate: string,
  endDate: string
): number {
  const start = new Date(startDate);
  const end = new Date(endDate);
  const diffTime = Math.abs(end.getTime() - start.getTime());
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
}

// Check if order is overdue
export function isOrderOverdue(
  estimatedFinish?: string,
  actualFinish?: string
): boolean {
  if (!estimatedFinish) return false;

  const estimated = new Date(estimatedFinish);
  const actual = actualFinish ? new Date(actualFinish) : new Date();

  return actual > estimated;
}

// Get overdue days
export function getOverdueDays(
  estimatedFinish?: string,
  actualFinish?: string
): number {
  if (!estimatedFinish) return 0;

  const estimated = new Date(estimatedFinish);
  const actual = actualFinish ? new Date(actualFinish) : new Date();

  if (actual <= estimated) return 0;

  return Math.ceil(
    (actual.getTime() - estimated.getTime()) / (1000 * 60 * 60 * 24)
  );
}
