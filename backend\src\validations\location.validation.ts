import Joi from 'joi';

const getProvinces = {
  query: Joi.object().keys({
    sortBy: Joi.string().valid('name', 'code').default('name'),
    sortOrder: Joi.string().valid('asc', 'desc').default('asc')
  })
};

const getCities = {
  query: Joi.object().keys({
    provinceId: Joi.number().integer().positive(),
    includeProvince: Joi.boolean().default(false),
    sortBy: Joi.string().valid('name', 'code').default('name'),
    sortOrder: Joi.string().valid('asc', 'desc').default('asc')
  })
};

const getCitiesByProvince = {
  params: Joi.object().keys({
    provinceId: Joi.number().integer().positive().required()
  }),
  query: Joi.object().keys({
    sortBy: Joi.string().valid('name', 'code').default('name'),
    sortOrder: Joi.string().valid('asc', 'desc').default('asc')
  })
};

const searchLocations = {
  query: Joi.object().keys({
    q: Joi.string().min(2).required(),
    limit: Joi.number().integer().min(1).max(50).default(10)
  })
};

const getTimezones = {
  query: Joi.object().keys({})
};

export default {
  getProvinces,
  getCities,
  getCitiesByProvince,
  searchLocations,
  getTimezones
};
