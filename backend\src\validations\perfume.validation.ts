import Joi from 'joi';

const createPerfume = {
  body: Joi.object().keys({
    name: Joi.string().required().max(100),
    description: Joi.string().optional().max(500),
    brand: Joi.string().optional().max(100),
    scent: Joi.string().optional().max(100), // fresh, floral, citrus, woody, etc
    isActive: Joi.boolean().optional().default(true),
    outletId: Joi.number().integer().positive().required(),
    isPopular: Joi.boolean().optional().default(false),
    isNew: Joi.boolean().optional().default(false)
  })
};

const getPerfumes = {
  query: Joi.object().keys({
    search: Joi.string().optional().allow(''),
    name: Joi.string().optional(),
    brand: Joi.string().optional(),
    scent: Joi.string().optional(),
    isActive: Joi.boolean().optional(),
    sortBy: Joi.string().optional(),
    limit: Joi.number().integer().min(1).max(100).optional(),
    page: Joi.number().integer().min(1).optional(),
    outletId: Joi.number().integer().positive().optional(),
    isPopular: Joi.boolean().optional(),
    isNew: Joi.boolean().optional()
  })
};

const getPerfume = {
  params: Joi.object().keys({
    perfumeId: Joi.number().integer().required()
  })
};

const updatePerfume = {
  params: Joi.object().keys({
    perfumeId: Joi.number().integer().required()
  }),
  body: Joi.object()
    .keys({
      name: Joi.string().max(100),
      description: Joi.string().max(500).allow(null, ''),
      brand: Joi.string().max(100).allow(null, ''),
      scent: Joi.string().max(100).allow(null, ''),
      isActive: Joi.boolean(),
      outletId: Joi.number().integer().positive().required(),
      isPopular: Joi.boolean(),
      isNew: Joi.boolean()
    })
    .min(1) // At least one field must be provided
};

const deletePerfume = {
  params: Joi.object().keys({
    perfumeId: Joi.number().integer().required()
  }),
  body: Joi.object().keys({
    outletId: Joi.number().integer().positive().required()
  })
};

export default {
  createPerfume,
  getPerfumes,
  getPerfume,
  updatePerfume,
  deletePerfume
};
