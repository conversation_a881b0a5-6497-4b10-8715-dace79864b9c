import prisma from '../client';
import { DepositTransactionType } from '@prisma/client';
import ApiError from '../utils/ApiError';
import httpStatus from 'http-status';

/**
 * Setor deposit customer
 * @param {number} customerId
 * @param {number} outletId
 * @param {number} amount
 * @param {number} cashboxId
 * @param {number} userId
 * @param {string} [reference]
 * @returns {Promise<CustomerDepositTransaction>}
 */
export const depositIn = async (
  customerId: number,
  outletId: number,
  amount: number,
  cashboxId: number,
  userId: number,
  reference?: string
) => {
  if (amount <= 0) throw new ApiError(httpStatus.BAD_REQUEST, 'Jumlah setor harus lebih dari 0');

  // Validasi customer exists
  const customer = await prisma.customer.findFirst({
    where: { id: customerId, outletId },
    include: { financialData: true }
  });
  if (!customer) throw new ApiError(httpStatus.NOT_FOUND, 'Customer tidak ditemukan');

  // Validasi cashbox exists dan aktif
  const cashbox = await prisma.cashbox.findFirst({
    where: { id: cashboxId, outletId, isActive: true }
  });
  if (!cashbox)
    throw new ApiError(httpStatus.NOT_FOUND, 'Cashbox tidak ditemukan atau tidak aktif');

  return await prisma.$transaction(async (tx) => {
    // Tambah transaksi deposit
    const depositTx = await tx.customerDepositTransaction.create({
      data: {
        customerId,
        outletId,
        type: DepositTransactionType.DEPOSIT,
        amount,
        cashboxId,
        createdBy: userId,
        reference
      },
      include: {
        customer: { select: { name: true } },
        cashbox: { select: { name: true } },
        user: { select: { name: true } }
      }
    });

    // Update saldo deposit customer
    await tx.customerFinancial.update({
      where: { customerId },
      data: { deposit: { increment: amount } }
    });

    // Update saldo cashbox
    await tx.cashbox.update({
      where: { id: cashboxId },
      data: { balance: { increment: amount } }
    });

    return depositTx;
  });
};

/**
 * Tarik deposit customer
 * @param {number} customerId
 * @param {number} outletId
 * @param {number} amount
 * @param {number} cashboxId
 * @param {number} userId
 * @param {string} [reference]
 * @returns {Promise<CustomerDepositTransaction>}
 */
export const depositOut = async (
  customerId: number,
  outletId: number,
  amount: number,
  cashboxId: number,
  userId: number,
  reference?: string
) => {
  if (amount <= 0) throw new ApiError(httpStatus.BAD_REQUEST, 'Jumlah tarik harus lebih dari 0');

  // Validasi customer dan saldo deposit
  const customer = await prisma.customer.findFirst({
    where: { id: customerId, outletId },
    include: { financialData: true }
  });
  if (!customer) throw new ApiError(httpStatus.NOT_FOUND, 'Customer tidak ditemukan');
  if (!customer.financialData || Number(customer.financialData.deposit) < amount) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Saldo deposit tidak mencukupi');
  }

  // Validasi cashbox exists dan aktif
  const cashbox = await prisma.cashbox.findFirst({
    where: { id: cashboxId, outletId, isActive: true }
  });
  if (!cashbox)
    throw new ApiError(httpStatus.NOT_FOUND, 'Cashbox tidak ditemukan atau tidak aktif');
  if (cashbox.balance < amount) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Saldo cashbox tidak mencukupi');
  }

  return await prisma.$transaction(async (tx) => {
    // Tambah transaksi withdraw
    const depositTx = await tx.customerDepositTransaction.create({
      data: {
        customerId,
        outletId,
        type: DepositTransactionType.WITHDRAW,
        amount: -amount, // Negatif untuk withdraw
        cashboxId,
        createdBy: userId,
        reference
      },
      include: {
        customer: { select: { name: true } },
        cashbox: { select: { name: true } },
        user: { select: { name: true } }
      }
    });

    // Update saldo deposit customer
    await tx.customerFinancial.update({
      where: { customerId },
      data: { deposit: { decrement: amount } }
    });

    // Update saldo cashbox
    await tx.cashbox.update({
      where: { id: cashboxId },
      data: { balance: { decrement: amount } }
    });

    return depositTx;
  });
};

/**
 * Pembayaran order menggunakan deposit
 * @param {number} customerId
 * @param {number} outletId
 * @param {number} orderId
 * @param {number} amount
 * @param {number} userId
 * @param {string} [reference]
 * @param {any} [tx] - Transaction context (optional)
 * @returns {Promise<CustomerDepositTransaction>}
 */
export const payWithDeposit = async (
  customerId: number,
  outletId: number,
  orderId: number,
  amount: number,
  userId: number,
  reference?: string,
  tx?: any
) => {
  if (amount <= 0)
    throw new ApiError(httpStatus.BAD_REQUEST, 'Jumlah pembayaran harus lebih dari 0');

  // Validasi customer dan saldo deposit
  const customer = await (tx || prisma).customer.findFirst({
    where: { id: customerId, outletId },
    include: { financialData: true }
  });
  if (!customer) throw new ApiError(httpStatus.NOT_FOUND, 'Customer tidak ditemukan');
  if (!customer.financialData || Number(customer.financialData.deposit) < amount) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Saldo deposit tidak mencukupi');
  }

  // Validasi order exists
  const order = await (tx || prisma).order.findFirst({
    where: { id: orderId, outletId, customerId }
  });
  if (!order) throw new ApiError(httpStatus.NOT_FOUND, 'Order tidak ditemukan');

  const remainingBalance = order.totalPrice - order.paidAmount;
  if (amount > remainingBalance) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Jumlah pembayaran melebihi sisa tagihan');
  }

  // Fungsi untuk melakukan operasi deposit
  const executeDepositPayment = async (transactionContext: any) => {
    // Tambah transaksi payment
    const depositTx = await transactionContext.customerDepositTransaction.create({
      data: {
        customerId,
        outletId,
        orderId,
        type: DepositTransactionType.PAYMENT,
        amount: -amount, // Negatif untuk payment
        createdBy: userId,
        reference
      },
      include: {
        customer: { select: { name: true } },
        order: { select: { orderNumber: true } },
        user: { select: { name: true } }
      }
    });

    // Update saldo deposit customer
    await transactionContext.customerFinancial.update({
      where: { customerId },
      data: { deposit: { decrement: amount } }
    });

    // Update order payment
    const newPaidAmount = order.paidAmount + amount;
    let newPaymentStatus: 'PARTIAL' | 'PAID' | 'UNPAID' = 'PARTIAL';

    if (newPaidAmount >= order.totalPrice) {
      newPaymentStatus = 'PAID';
    } else if (newPaidAmount === 0) {
      newPaymentStatus = 'UNPAID';
    }

    await transactionContext.order.update({
      where: { id: orderId },
      data: {
        paidAmount: newPaidAmount,
        paymentStatus: newPaymentStatus as any
      }
    });

    // Buat record Payment untuk tracking
    await transactionContext.payment.create({
      data: {
        orderId,
        amount,
        method: 'DEPOSIT',
        reference: `DEPOSIT-${depositTx.id}`,
        notes: 'Pembayaran menggunakan saldo deposit'
      }
    });

    return depositTx;
  };

  // Jika ada transaction context, gunakan itu. Jika tidak, buat transaction baru
  if (tx) {
    return await executeDepositPayment(tx);
  } else {
    return await prisma.$transaction(async (newTx) => {
      return await executeDepositPayment(newTx);
    });
  }
};

/**
 * Get deposit transaction history
 * @param {number} customerId
 * @param {number} outletId
 * @param {Object} options
 * @returns {Promise<Object>}
 */
export const getDepositHistory = async (
  customerId: number,
  outletId: number,
  options: {
    page?: number;
    limit?: number;
    type?: DepositTransactionType;
    dateFrom?: Date;
    dateTo?: Date;
  } = {}
) => {
  const page = options.page ?? 1;
  const limit = options.limit ?? 10;

  const whereClause: any = {
    customerId,
    outletId
  };

  if (options.type) {
    whereClause.type = options.type;
  }

  if (options.dateFrom || options.dateTo) {
    whereClause.createdAt = {};
    if (options.dateFrom) {
      whereClause.createdAt.gte = options.dateFrom;
    }
    if (options.dateTo) {
      whereClause.createdAt.lte = options.dateTo;
    }
  }

  const [transactions, total] = await Promise.all([
    prisma.customerDepositTransaction.findMany({
      where: whereClause,
      include: {
        customer: { select: { name: true } },
        cashbox: { select: { name: true, type: true } },
        order: { select: { orderNumber: true } },
        user: { select: { name: true } }
      },
      skip: (page - 1) * limit,
      take: limit,
      orderBy: { createdAt: 'desc' }
    }),
    prisma.customerDepositTransaction.count({ where: whereClause })
  ]);

  return {
    results: transactions,
    page,
    limit,
    totalPages: Math.ceil(total / limit),
    totalResults: total
  };
};

/**
 * Get customer deposit balance
 * @param {number} customerId
 * @param {number} outletId
 * @returns {Promise<number>}
 */
export const getDepositBalance = async (customerId: number, outletId: number) => {
  const customer = await prisma.customer.findFirst({
    where: { id: customerId, outletId },
    include: { financialData: true }
  });

  if (!customer) throw new ApiError(httpStatus.NOT_FOUND, 'Customer tidak ditemukan');

  return Number(customer.financialData?.deposit || 0);
};

export default {
  depositIn,
  depositOut,
  payWithDeposit,
  getDepositHistory,
  getDepositBalance
};
