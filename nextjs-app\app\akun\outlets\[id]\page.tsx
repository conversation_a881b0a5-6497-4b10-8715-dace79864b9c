'use client';

import React from 'react';
import Link from 'next/link';
import {
  ArrowLeft,
  Edit,
  MapPin,
  Phone,
  Clock,
  Mail,
  Users,
  Package,
  Info,
  Loader2,
} from 'lucide-react';
import { useRouter } from 'next/navigation';

import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  useOutlet,
  useOutletServices,
  useOutletStats,
} from '@/hooks/useOutlets';

export default function OutletDetailPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const router = useRouter();
  const [outletId, setOutletId] = React.useState<number | null>(null);

  React.useEffect(() => {
    params.then((resolvedParams) => {
      setOutletId(parseInt(resolvedParams.id));
    });
  }, [params]);

  const {
    data: outlet,
    isLoading: outletLoading,
    error: outletError,
  } = useOutlet(outletId || 0);

  const { data: services, isLoading: servicesLoading } = useOutletServices(
    outletId || 0
  );

  const { data: stats, isLoading: statsLoading } = useOutletStats(
    outletId || 0
  );

  if (outletLoading || outletId === null) {
    return (
      <div className="flex flex-col min-h-screen bg-slate-50">
        <header className="p-4 flex justify-between items-center bg-white sticky top-0 z-10 shadow-sm">
          <div className="flex items-center">
            <Link href="/akun/outlets" className="mr-3">
              <ArrowLeft className="h-5 w-5" />
            </Link>
            <h1 className="text-lg font-semibold">Detail Outlet</h1>
          </div>
        </header>
        <main className="flex-1 flex items-center justify-center">
          <div className="flex items-center gap-2">
            <Loader2 className="h-4 w-4 animate-spin" />
            <span>Memuat data outlet...</span>
          </div>
        </main>
      </div>
    );
  }

  if (outletError || !outlet) {
    return (
      <div className="flex flex-col min-h-screen bg-slate-50">
        <header className="p-4 flex justify-between items-center bg-white sticky top-0 z-10 shadow-sm">
          <div className="flex items-center">
            <Link href="/akun/outlets" className="mr-3">
              <ArrowLeft className="h-5 w-5" />
            </Link>
            <h1 className="text-lg font-semibold">Detail Outlet</h1>
          </div>
        </header>
        <main className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <h2 className="text-lg font-semibold text-gray-900 mb-2">
              Outlet tidak ditemukan
            </h2>
            <p className="text-gray-500 mb-4">
              Outlet yang Anda cari tidak ditemukan atau telah dihapus.
            </p>
            <Button onClick={() => router.push('/akun/outlets')}>
              Kembali ke Daftar Outlet
            </Button>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen bg-slate-50">
      <header className="p-4 flex justify-between items-center bg-white sticky top-0 z-10 shadow-sm">
        <div className="flex items-center">
          <Link href="/akun/outlets" className="mr-3">
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <h1 className="text-lg font-semibold">Detail Outlet</h1>
        </div>
        <Button
          onClick={() => router.push(`/akun/outlets/edit/${outletId}`)}
          className="bg-blue-500 hover:bg-blue-600"
        >
          <Edit className="h-4 w-4 mr-2" /> Edit
        </Button>
      </header>

      <main className="flex-1 p-4 pb-20">
        <Card className="p-4 mb-4">
          <div className="flex flex-col">
            <div className="flex items-center gap-2 mb-1">
              <h2 className="text-xl font-semibold">{outlet.name}</h2>
              <Badge variant={outlet.isActive ? 'default' : 'secondary'}>
                {outlet.isActive ? 'Aktif' : 'Tidak Aktif'}
              </Badge>
            </div>
            <div className="space-y-2 mt-2">
              <div className="flex items-center text-sm text-gray-500">
                <MapPin className="h-4 w-4 mr-2" />
                {outlet.address}
              </div>
              <div className="flex items-center text-sm text-gray-500">
                <Phone className="h-4 w-4 mr-2" />
                {outlet.phone}
              </div>
              <div className="flex items-center text-sm text-gray-500">
                <Clock className="h-4 w-4 mr-2" />
                {outlet.timezone}
              </div>
            </div>
          </div>
        </Card>

        <div className="grid grid-cols-3 gap-4 mb-4">
          <Card className="p-4">
            <h3 className="text-sm font-medium text-gray-500 mb-1">
              Total Order
            </h3>
            {statsLoading ? (
              <div className="flex items-center gap-2">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span className="text-sm">Loading...</span>
              </div>
            ) : (
              <p className="text-2xl font-bold">
                {stats?.totalOrders?.toLocaleString() || '0'}
              </p>
            )}
          </Card>
          <Card className="p-4">
            <h3 className="text-sm font-medium text-gray-500 mb-1">
              Total Pendapatan
            </h3>
            {statsLoading ? (
              <div className="flex items-center gap-2">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span className="text-sm">Loading...</span>
              </div>
            ) : (
              <p className="text-2xl font-bold">
                Rp {stats?.totalRevenue?.toLocaleString() || '0'}
              </p>
            )}
          </Card>
          <Card className="p-4">
            <h3 className="text-sm font-medium text-gray-500 mb-1">
              Jumlah Pelanggan
            </h3>
            {statsLoading ? (
              <div className="flex items-center gap-2">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span className="text-sm">Loading...</span>
              </div>
            ) : (
              <p className="text-2xl font-bold">
                {stats?.totalCustomers?.toLocaleString() || '0'}
              </p>
            )}
          </Card>
        </div>

        <Tabs defaultValue="info" className="mb-4">
          <TabsList className="grid grid-cols-2 w-full">
            <TabsTrigger value="info">Informasi</TabsTrigger>
            <TabsTrigger value="services">Layanan</TabsTrigger>
          </TabsList>

          <TabsContent value="info" className="mt-4">
            <Card className="p-4">
              <h3 className="font-medium flex items-center gap-2 mb-3">
                <Info className="h-4 w-4" /> Informasi Outlet
              </h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-500">ID Outlet</span>
                  <span className="font-medium">{outlet.id}</span>
                </div>
                <Separator />
                <div className="flex justify-between">
                  <span className="text-gray-500">Nama Outlet</span>
                  <span className="font-medium">{outlet.name}</span>
                </div>
                <Separator />
                <div className="flex justify-between">
                  <span className="text-gray-500">Alamat</span>
                  <span className="font-medium text-right max-w-[200px]">
                    {outlet.address}
                  </span>
                </div>
                <Separator />
                <div className="flex justify-between">
                  <span className="text-gray-500">Provinsi</span>
                  <span className="font-medium">{outlet.province}</span>
                </div>
                <Separator />
                <div className="flex justify-between">
                  <span className="text-gray-500">Kota</span>
                  <span className="font-medium">{outlet.city}</span>
                </div>
                <Separator />
                <div className="flex justify-between">
                  <span className="text-gray-500">Zona Waktu</span>
                  <span className="font-medium">{outlet.timezone}</span>
                </div>
                <Separator />
                <div className="flex justify-between">
                  <span className="text-gray-500">Telepon</span>
                  <span className="font-medium">{outlet.phone}</span>
                </div>
                <Separator />
                <div className="flex justify-between">
                  <span className="text-gray-500">Status</span>
                  <Badge variant={outlet.isActive ? 'default' : 'secondary'}>
                    {outlet.isActive ? 'Aktif' : 'Tidak Aktif'}
                  </Badge>
                </div>
                <Separator />
                <div className="flex justify-between">
                  <span className="text-gray-500">Tanggal Dibuat</span>
                  <span className="font-medium">
                    {new Date(outlet.createdAt).toLocaleDateString('id-ID')}
                  </span>
                </div>
                {outlet.latitude && outlet.longitude && (
                  <>
                    <Separator />
                    <div className="flex justify-between">
                      <span className="text-gray-500">Koordinat</span>
                      <span className="font-medium">
                        {outlet.latitude}, {outlet.longitude}
                      </span>
                    </div>
                  </>
                )}
              </div>
            </Card>

            {outlet.latitude && outlet.longitude && (
            <Card className="p-4 mt-4">
              <h3 className="font-medium mb-3">Lokasi Outlet</h3>
              <div className="h-48 bg-gray-100 rounded-md flex items-center justify-center">
                <p className="text-gray-500">Peta akan ditampilkan di sini</p>
              </div>
            </Card>
            )}
          </TabsContent>

          <TabsContent value="services" className="mt-4">
            <Card className="p-4">
              <h3 className="font-medium flex items-center gap-2 mb-3">
                <Package className="h-4 w-4" /> Layanan yang Tersedia
              </h3>
              {servicesLoading ? (
                <div className="flex items-center gap-2">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span>Memuat layanan...</span>
                </div>
              ) : services && services.length > 0 ? (
              <div className="space-y-3">
                  {services.map((outletService) => (
                    <div
                      key={outletService.id}
                      className="flex justify-between items-center"
                    >
                      <div className="flex-1">
                        <div className="font-medium">
                          {outletService.service.name}
                        </div>
                        {outletService.service.description && (
                          <div className="text-sm text-gray-500">
                            {outletService.service.description}
                          </div>
                        )}
                        <div className="text-sm text-gray-600">
                          Rp {outletService.price.toLocaleString()} per{' '}
                          {outletService.service.unit}
                        </div>
                        {outletService.estimationHours && (
                          <div className="text-sm text-gray-500">
                            Estimasi: {outletService.estimationHours} jam
                          </div>
                        )}
                      </div>
                      <Badge
                        variant={
                          outletService.isAvailable ? 'default' : 'secondary'
                        }
                      >
                        {outletService.isAvailable
                          ? 'Tersedia'
                          : 'Tidak Tersedia'}
                    </Badge>
                  </div>
                ))}
              </div>
              ) : (
                <div className="text-center py-8">
                  <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">
                    Belum ada layanan yang tersedia di outlet ini
                  </p>
                  </div>
              )}
            </Card>
          </TabsContent>
        </Tabs>
      </main>
    </div>
  );
}
