import { Promotion, Prisma } from '@prisma/client';
import httpStatus from 'http-status';
import prisma from '../client';
import ApiError from '../utils/ApiError';
import logger from '../config/logger';

/**
 * Create a promotion
 * @param {Object} promotionBody
 * @returns {Promise<Promotion>}
 */
const createPromotion = async (promotionBody: any): Promise<Promotion> => {
  const { outletId } = promotionBody;
  logger.info(`Creating promotion: ${promotionBody.name} at outlet ${outletId}`, {
    name: promotionBody.name,
    code: promotionBody.code,
    outletId
  });

  // Check if promotion code already exists in this outlet
  const existingPromotion = await prisma.promotion.findFirst({
    where: {
      code: promotionBody.code,
      outletId: outletId
    }
  });

  // Check outletId exists
  const outlet = await prisma.outlet.findUnique({
    where: {
      id: outletId
    }
  });

  if (!outlet) {
    logger.error(`Outlet not found: ${outletId}`);
    throw new ApiError(httpStatus.NOT_FOUND, 'Outlet not found');
  }

  if (existingPromotion) {
    logger.error(`Promotion code already exists: ${promotionBody.code} at outlet ${outletId}`);
    throw new ApiError(httpStatus.BAD_REQUEST, 'Promotion code already exists in this outlet');
  }

  // Validate discount value based on type
  if (promotionBody.discountType === 'PERCENTAGE' && promotionBody.discountValue > 100) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Percentage discount cannot exceed 100%');
  }

  const promotion = await prisma.promotion.create({
    data: {
      ...promotionBody,
      outletId
    }
  });

  logger.info(`Promotion created successfully: ${promotion.name} (${promotion.id})`, {
    promotionId: promotion.id,
    name: promotion.name,
    code: promotion.code,
    outletId
  });

  return promotion;
};

/**
 * Query for promotions
 * @param {Object} filter - Mongo filter
 * @param {Object} options - Query options
 * @returns {Promise<QueryResult>}
 */
const queryPromotions = async (filter: any, options: any) => {
  const search = filter.search;
  const sortBy = options.sortBy;
  const limit = options.limit || 10;
  const page = options.page || 1;
  const outletId = filter.outletId;
  const isActive = filter.isActive;
  const isExpired = filter.isExpired;

  logger.debug(`Querying promotions for outlet ${outletId}`, {
    search,
    sortBy,
    limit,
    page
  });

  // Build where condition
  const where: Prisma.PromotionWhereInput = {
    outletId: outletId
  };

  if (search) {
    where.OR = [
      { name: { contains: search, mode: 'insensitive' } },
      { code: { contains: search, mode: 'insensitive' } },
      { description: { contains: search, mode: 'insensitive' } }
    ];
  }

  if (typeof isActive === 'boolean') {
    where.isActive = isActive;
  }

  if (typeof isExpired === 'boolean') {
    const now = new Date();
    if (isExpired) {
      where.validUntil = { lt: now };
    } else {
      where.validUntil = { gte: now };
    }
  }

  if (filter.discountType) {
    where.discountType = filter.discountType;
  }

  // Build orderBy
  let orderBy: Prisma.PromotionOrderByWithRelationInput = { createdAt: 'desc' };
  if (sortBy) {
    const [field, direction] = sortBy.split(':');
    orderBy = { [field]: direction === 'desc' ? 'desc' : 'asc' };
  }

  // Execute query
  const [promotions, totalResults] = await Promise.all([
    prisma.promotion.findMany({
      where,
      orderBy,
      skip: (page - 1) * limit,
      take: limit
    }),
    prisma.promotion.count({ where })
  ]);

  const totalPages = Math.ceil(totalResults / limit);

  logger.debug(`Found ${totalResults} promotions for outlet ${outletId}`);

  return {
    results: promotions,
    page,
    limit,
    totalPages,
    totalResults
  };
};

/**
 * Get promotion by id
 * @param {number} id
 * @returns {Promise<Promotion>}
 */
const getPromotionById = async (id: number): Promise<Promotion | null> => {
  const promotion = await prisma.promotion.findFirst({
    where: {
      id
    }
  });

  if (promotion) {
    logger.debug(`Promotion retrieved: ${promotion.name} (${promotion.id})`);
  } else {
    logger.warn(`Promotion not found: ${id}`);
  }

  return promotion;
};

/**
 * Update promotion by id
 * @param {number} promotionId
 * @param {Object} updateBody
 * @param {number} outletId
 * @returns {Promise<Promotion>}
 */
const updatePromotionById = async (
  promotionId: number,
  updateBody: any,
  outletId: number
): Promise<Promotion> => {
  logger.info(`Updating promotion: ${promotionId}`, {
    promotionId,
    updateFields: Object.keys(updateBody),
    outletId
  });

  const promotion = await getPromotionById(promotionId);
  if (!promotion || promotion.outletId !== outletId) {
    logger.error(`Promotion not found for update: ${promotionId} at outlet ${outletId}`);
    throw new ApiError(httpStatus.NOT_FOUND, 'Promotion not found');
  }

  // Check if updating code and it already exists
  if (updateBody.code && updateBody.code !== promotion.code) {
    const existingPromotion = await prisma.promotion.findFirst({
      where: {
        code: updateBody.code,
        outletId: outletId,
        id: { not: promotionId }
      }
    });

    if (existingPromotion) {
      logger.error(`Promotion code already exists: ${updateBody.code} at outlet ${outletId}`);
      throw new ApiError(httpStatus.BAD_REQUEST, 'Promotion code already exists in this outlet');
    }
  }

  // Validate discount value based on type
  if (updateBody.discountType === 'PERCENTAGE' && updateBody.discountValue > 100) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Percentage discount cannot exceed 100%');
  }

  const updatedPromotion = await prisma.promotion.update({
    where: {
      id: promotionId
    },
    data: updateBody
  });

  logger.info(`Promotion updated successfully: ${updatedPromotion.name} (${promotionId})`, {
    promotionId,
    name: updatedPromotion.name,
    outletId
  });

  return updatedPromotion;
};

/**
 * Delete promotion by id
 * @param {number} promotionId
 * @param {number} outletId
 * @returns {Promise<void>}
 */
const deletePromotionById = async (promotionId: number, outletId: number): Promise<void> => {
  logger.info(`Deleting promotion: ${promotionId}`, {
    promotionId,
    outletId
  });

  const promotion = await getPromotionById(promotionId);
  if (!promotion || promotion.outletId !== outletId) {
    logger.error(`Promotion not found for deletion: ${promotionId} at outlet ${outletId}`);
    throw new ApiError(httpStatus.NOT_FOUND, 'Promotion not found');
  }

  // Check if promotion is being used in any orders
  const ordersUsingPromotion = await prisma.order.count({
    where: {
      promotionId: promotionId,
      outletId: outletId
    }
  });

  if (ordersUsingPromotion > 0) {
    logger.warn(`Cannot delete promotion ${promotionId}: used in ${ordersUsingPromotion} orders`);
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      `Cannot delete promotion. It is being used in ${ordersUsingPromotion} order(s).`
    );
  }

  await prisma.promotion.delete({
    where: {
      id: promotionId
    }
  });

  logger.info(`Promotion deleted successfully: ${promotion.name} (${promotionId})`, {
    promotionId,
    name: promotion.name,
    outletId
  });
};

/**
 * Validate and apply promotion
 * @param {string} code
 * @param {number} orderTotal
 * @param {number} customerId
 * @param {number} outletId
 * @returns {Promise<Object>}
 */
const validatePromotion = async (
  code: string,
  orderTotal: number,
  customerId: number,
  outletId: number
): Promise<{ promotion: Promotion; discountAmount: number }> => {
  logger.info(`Validating promotion: ${code}`, {
    code,
    orderTotal,
    customerId,
    outletId
  });

  // Find promotion
  const promotion = await prisma.promotion.findFirst({
    where: {
      code: code.toUpperCase(),
      outletId: outletId,
      isActive: true,
      validFrom: { lte: new Date() },
      validUntil: { gte: new Date() }
    }
  });

  if (!promotion) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Promotion code not found or expired');
  }

  // Check usage limit
  if (promotion.usageLimit && promotion.usageCount >= promotion.usageLimit) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Promotion usage limit exceeded');
  }

  // Check minimum order value
  if (orderTotal < promotion.minOrderValue) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      `Minimum order value is Rp ${promotion.minOrderValue.toLocaleString()}`
    );
  }

  // Check if first time only
  if (promotion.isFirstTimeOnly) {
    const customerOrderCount = await prisma.order.count({
      where: {
        customerId: customerId,
        outletId: outletId,
        status: { not: 'CANCELLED' }
      }
    });

    if (customerOrderCount > 0) {
      throw new ApiError(httpStatus.BAD_REQUEST, 'This promotion is only for first-time customers');
    }
  }

  // Calculate discount amount
  let discountAmount = 0;
  if (promotion.discountType === 'PERCENTAGE') {
    discountAmount = orderTotal * (promotion.discountValue / 100);
    // Apply max discount limit if set
    if (promotion.maxDiscountAmount && discountAmount > promotion.maxDiscountAmount) {
      discountAmount = promotion.maxDiscountAmount;
    }
  } else {
    discountAmount = promotion.discountValue;
  }

  // Ensure discount doesn't exceed order total
  if (discountAmount > orderTotal) {
    discountAmount = orderTotal;
  }

  logger.info(`Promotion validated successfully: ${promotion.name}`, {
    promotionId: promotion.id,
    discountAmount,
    orderTotal
  });

  return { promotion, discountAmount };
};

/**
 * Increment promotion usage count
 * @param {number} promotionId
 * @returns {Promise<void>}
 */
const incrementUsageCount = async (promotionId: number): Promise<void> => {
  await prisma.promotion.update({
    where: { id: promotionId },
    data: { usageCount: { increment: 1 } }
  });
};

export default {
  createPromotion,
  queryPromotions,
  getPromotionById,
  updatePromotionById,
  deletePromotionById,
  validatePromotion,
  incrementUsageCount
};
