import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function seedOutlet() {
  console.log('🏢 Seeding outlets...');

  try {
    // Get admin user untuk assign outlet
    const adminUser = await prisma.user.findFirst({
      where: { email: '<EMAIL>' }
    });

    if (!adminUser) {
      console.log('❌ Admin user not found. Please run admin seeder first.');
      return;
    }

    // Get some provinces and cities for outlet locations
    const jakarta = await prisma.province.findFirst({ where: { name: 'DKI Jakarta' } });
    const bandung = await prisma.province.findFirst({ where: { name: 'Jawa Barat' } });

    const jakartaCity = await prisma.city.findFirst({
      where: { name: 'Jakarta Selatan', provinceId: jakarta?.id }
    });
    const bandungCity = await prisma.city.findFirst({
      where: { name: 'Bandung', provinceId: bandung?.id }
    });

    // Sample outlets data
    const outletsData = [
      {
        name: 'Super Laundry Kemang',
        address: 'Jl. Kemang Raya No. 123, Jakarta Selatan',
        province: 'DKI Jakarta',
        city: 'Jakarta Selatan',
        phone: '021-71234567',
        latitude: -6.2641,
        longitude: 106.8133,
        timezone: 'Asia/Jakarta',
        isActive: true,
        ownerId: adminUser.id,
        provinceId: jakarta?.id,
        cityId: jakartaCity?.id
      },
      {
        name: 'Super Laundry Senayan',
        address: 'Jl. Asia Afrika No. 8, Jakarta Pusat',
        province: 'DKI Jakarta',
        city: 'Jakarta Pusat',
        phone: '021-71234568',
        latitude: -6.2087,
        longitude: 106.8456,
        timezone: 'Asia/Jakarta',
        isActive: true,
        ownerId: adminUser.id,
        provinceId: jakarta?.id,
        cityId: jakartaCity?.id
      },
      {
        name: 'Super Laundry Bandung',
        address: 'Jl. Dago No. 45, Bandung',
        province: 'Jawa Barat',
        city: 'Bandung',
        phone: '022-2034567',
        latitude: -6.8915,
        longitude: 107.6107,
        timezone: 'Asia/Jakarta',
        isActive: true,
        ownerId: adminUser.id,
        provinceId: bandung?.id,
        cityId: bandungCity?.id
      }
    ];

    // Create outlets
    for (const outletData of outletsData) {
      const existingOutlet = await prisma.outlet.findFirst({
        where: { name: outletData.name }
      });

      if (existingOutlet) {
        console.log(`   🔄 Outlet "${outletData.name}" already exists, skipping...`);
        continue;
      }

      const outlet = await prisma.outlet.create({
        data: outletData
      });

      console.log(`   ✅ Created outlet: ${outlet.name}`);
    }

    console.log('🎉 Outlet seeding completed!');
  } catch (error) {
    console.error('❌ Error seeding outlets:', error);
    throw error;
  }
}
