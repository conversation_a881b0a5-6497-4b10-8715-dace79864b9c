"use client"

import { useState } from "react"
import Link from "next/link"
import { ArrowLeft, Save, Trash2, Co<PERSON>, Send } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

// Sample template data
const templateData = {
  id: "1",
  name: "Konfirmasi Pesanan",
  message:
    "Halo {nama}, pesanan laundry Anda dengan nomor {nomor_pesanan} telah kami terima. Terima kasih telah menggunakan jasa kami.",
  status: "active",
  category: "order",
  lastUsed: "2 jam yang lalu",
  usageCount: 128,
  variables: [
    { name: "nama", description: "Nama pelanggan" },
    { name: "nomor_pesanan", description: "Nomor pesanan" },
  ],
}

export default function TemplateDetailPage({ params }: { params: { id: string } }) {
  const [template, setTemplate] = useState(templateData)
  const [previewData, setPreviewData] = useState({
    nama: "Ahmad Rizki",
    nomor_pesanan: "ORD-12345",
  })

  const handleStatusChange = (checked: boolean) => {
    setTemplate({
      ...template,
      status: checked ? "active" : "inactive",
    })
  }

  const handleSave = () => {
    alert("Template berhasil disimpan!")
  }

  const handleDelete = () => {
    if (confirm("Apakah Anda yakin ingin menghapus template ini?")) {
      alert("Template berhasil dihapus!")
    }
  }

  const handleDuplicate = () => {
    alert("Template berhasil diduplikasi!")
  }

  const handleSendTest = () => {
    alert("Pesan uji berhasil dikirim!")
  }

  const getPreviewMessage = () => {
    let previewMessage = template.message
    Object.entries(previewData).forEach(([key, value]) => {
      previewMessage = previewMessage.replace(new RegExp(`{${key}}`, "g"), value)
    })
    return previewMessage
  }

  return (
    <div className="flex flex-col min-h-screen bg-slate-50">
      <header className="p-4 flex justify-between items-center bg-white sticky top-0 z-10 shadow-sm">
        <div className="flex items-center">
          <Link href="/akun/whatsapp" className="mr-3">
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <h1 className="text-lg font-semibold">Detail Template</h1>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={handleDelete} className="text-red-500 hover:text-red-600">
            <Trash2 className="h-4 w-4 mr-1" /> Hapus
          </Button>
          <Button variant="outline" size="sm" onClick={handleDuplicate}>
            <Copy className="h-4 w-4 mr-1" /> Duplikat
          </Button>
          <Button onClick={handleSave} size="sm" className="bg-green-500 hover:bg-green-600">
            <Save className="h-4 w-4 mr-1" /> Simpan
          </Button>
        </div>
      </header>

      <div className="p-4 pb-20">
        <Tabs defaultValue="edit">
          <TabsList className="grid grid-cols-2 mb-4">
            <TabsTrigger value="edit">Edit Template</TabsTrigger>
            <TabsTrigger value="preview">Pratinjau</TabsTrigger>
          </TabsList>

          <TabsContent value="edit" className="space-y-4">
            <Card>
              <CardContent className="p-4 space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Nama Template</Label>
                  <Input
                    id="name"
                    value={template.name}
                    onChange={(e) => setTemplate({ ...template, name: e.target.value })}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="category">Kategori</Label>
                  <Select
                    value={template.category}
                    onValueChange={(value) => setTemplate({ ...template, category: value })}
                  >
                    <SelectTrigger id="category">
                      <SelectValue placeholder="Pilih kategori" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="order">Pesanan</SelectItem>
                      <SelectItem value="payment">Pembayaran</SelectItem>
                      <SelectItem value="promotion">Promosi</SelectItem>
                      <SelectItem value="other">Lainnya</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="message">Pesan</Label>
                  <Textarea
                    id="message"
                    rows={6}
                    value={template.message}
                    onChange={(e) => setTemplate({ ...template, message: e.target.value })}
                    className="resize-none"
                  />
                  <p className="text-xs text-gray-500">
                    Gunakan {"{variabel}"} untuk menyisipkan data dinamis. Contoh: {"{nama}"}, {"{nomor_pesanan}"}
                  </p>
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="status" className="cursor-pointer">
                    Status Template
                  </Label>
                  <div className="flex items-center gap-2">
                    <Switch id="status" checked={template.status === "active"} onCheckedChange={handleStatusChange} />
                    <Badge variant={template.status === "active" ? "default" : "secondary"} className="text-xs">
                      {template.status === "active" ? "Aktif" : "Nonaktif"}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <h3 className="font-medium mb-3">Variabel Template</h3>
                <div className="space-y-3">
                  {template.variables.map((variable, index) => (
                    <div key={index} className="flex items-start gap-3">
                      <div className="flex-1 space-y-1">
                        <Label htmlFor={`var-${index}`}>Nama Variabel</Label>
                        <Input id={`var-${index}`} value={variable.name} readOnly className="bg-gray-50" />
                      </div>
                      <div className="flex-1 space-y-1">
                        <Label htmlFor={`desc-${index}`}>Deskripsi</Label>
                        <Input
                          id={`desc-${index}`}
                          value={variable.description}
                          onChange={(e) => {
                            const newVariables = [...template.variables]
                            newVariables[index].description = e.target.value
                            setTemplate({ ...template, variables: newVariables })
                          }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <h3 className="font-medium mb-3">Statistik Penggunaan</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-gray-500">Terakhir digunakan</p>
                    <p className="font-medium">{template.lastUsed}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Total penggunaan</p>
                    <p className="font-medium">{template.usageCount}x</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="preview" className="space-y-4">
            <Card>
              <CardContent className="p-4 space-y-4">
                <h3 className="font-medium">Data Pratinjau</h3>
                <div className="space-y-3">
                  {template.variables.map((variable, index) => (
                    <div key={index} className="space-y-1">
                      <Label htmlFor={`preview-${variable.name}`}>{variable.description}</Label>
                      <Input
                        id={`preview-${variable.name}`}
                        value={previewData[variable.name as keyof typeof previewData] || ""}
                        onChange={(e) => {
                          setPreviewData({
                            ...previewData,
                            [variable.name]: e.target.value,
                          })
                        }}
                      />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <h3 className="font-medium mb-3">Pratinjau Pesan</h3>
                <div className="bg-green-50 p-3 rounded-lg border border-green-100 mb-4">
                  <p className="whitespace-pre-line">{getPreviewMessage()}</p>
                </div>
                <Button onClick={handleSendTest} className="w-full bg-green-500 hover:bg-green-600">
                  <Send className="h-4 w-4 mr-2" /> Kirim Pesan Uji
                </Button>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
