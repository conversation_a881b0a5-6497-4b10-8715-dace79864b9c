import jwt from 'jsonwebtoken';
import moment, { Moment } from 'moment';
import httpStatus from 'http-status';
import config from '../config/config';
import userService from './user.service';
import ApiError from '../utils/ApiError';
import { Token, TokenType } from '@prisma/client';
import prisma from '../client';
import { AuthTokensResponse } from '../types/response';
import logger from '../config/logger';

/**
 * Generate token
 * @param {number} userId
 * @param {Moment} expires
 * @param {string} type
 * @param {string} [secret]
 * @returns {string}
 */
const generateToken = (
  userId: number,
  expires: Moment,
  type: TokenType,
  secret = config.jwt.secret
): string => {
  logger.debug(`[TOKEN_SERVICE] Generating token - UserID: ${userId}, Type: ${type}, ExpiresAt: ${expires.toISOString()}`);

  try {
    const payload = {
      sub: userId,
      iat: moment().unix(),
      exp: expires.unix(),
      type
    };
    const token = jwt.sign(payload, secret);
    
    logger.debug(`[TOKEN_SERVICE] Token generated successfully - UserID: ${userId}, Type: ${type}, TokenLength: ${token.length}`);
    return token;
  } catch (error: any) {
    logger.error(`[TOKEN_SERVICE] Token generation failed - UserID: ${userId}, Type: ${type}, Error: ${error.message}`);
    throw error;
  }
};

/**
 * Save a token
 * @param {string} token
 * @param {number} userId
 * @param {Moment} expires
 * @param {string} type
 * @param {boolean} [blacklisted]
 * @returns {Promise<Token>}
 */
const saveToken = async (
  token: string,
  userId: number,
  expires: Moment,
  type: TokenType,
  blacklisted = false
): Promise<Token> => {
  logger.debug(`[TOKEN_SERVICE] Saving token to database - UserID: ${userId}, Type: ${type}, ExpiresAt: ${expires.toISOString()}, Blacklisted: ${blacklisted}`);

  try {
    const createdToken = await prisma.token.create({
      data: {
        token,
        userId: userId,
        expires: expires.toDate(),
        type,
        blacklisted
      }
    });
    
    logger.debug(`[TOKEN_SERVICE] Token saved successfully - TokenID: ${createdToken.id}, UserID: ${userId}, Type: ${type}`);
    return createdToken;
  } catch (error: any) {
    logger.error(`[TOKEN_SERVICE] Token save failed - UserID: ${userId}, Type: ${type}, Error: ${error.message}`);
    throw error;
  }
};

/**
 * Verify token and return token doc (or throw an error if it is not valid)
 * @param {string} token
 * @param {string} type
 * @returns {Promise<Token>}
 */
const verifyToken = async (token: string, type: TokenType): Promise<Token> => {
  logger.debug(`[TOKEN_SERVICE] Verifying token - Type: ${type}, TokenLength: ${token.length}`);

  try {
    const payload = jwt.verify(token, config.jwt.secret);
    const userId = Number(payload.sub);
    
    logger.debug(`[TOKEN_SERVICE] JWT payload verified - UserID: ${userId}, Type: ${type}`);
    
    const tokenData = await prisma.token.findFirst({
      where: { token, type, userId, blacklisted: false }
    });
    
    if (!tokenData) {
      logger.warn(`[TOKEN_SERVICE] Token verification failed - Token not found in database - UserID: ${userId}, Type: ${type}`);
      throw new Error('Token not found');
    }
    
    logger.debug(`[TOKEN_SERVICE] Token verified successfully - TokenID: ${tokenData.id}, UserID: ${userId}, Type: ${type}`);
    return tokenData;
  } catch (error: any) {
    if (error.name === 'JsonWebTokenError') {
      logger.warn(`[TOKEN_SERVICE] Token verification failed - Invalid JWT - Type: ${type}, Error: ${error.message}`);
    } else if (error.name === 'TokenExpiredError') {
      logger.warn(`[TOKEN_SERVICE] Token verification failed - JWT expired - Type: ${type}, Error: ${error.message}`);
    } else {
      logger.error(`[TOKEN_SERVICE] Token verification error - Type: ${type}, Error: ${error.message}`);
    }
    throw error;
  }
};

/**
 * Generate auth tokens
 * @param {User} user
 * @returns {Promise<AuthTokensResponse>}
 */
const generateAuthTokens = async (user: { id: number }): Promise<AuthTokensResponse> => {
  logger.debug(`[TOKEN_SERVICE] Starting auth tokens generation - UserID: ${user.id}`);

  try {
    const accessTokenExpires = moment().add(config.jwt.accessExpirationMinutes, 'minutes');
    const accessToken = generateToken(user.id, accessTokenExpires, TokenType.ACCESS);

    logger.debug(`[TOKEN_SERVICE] Access token generated - UserID: ${user.id}, ExpiresAt: ${accessTokenExpires.toISOString()}`);

    const refreshTokenExpires = moment().add(config.jwt.refreshExpirationDays, 'days');
    const refreshToken = generateToken(user.id, refreshTokenExpires, TokenType.REFRESH);
    await saveToken(refreshToken, user.id, refreshTokenExpires, TokenType.REFRESH);

    logger.debug(`[TOKEN_SERVICE] Refresh token generated and saved - UserID: ${user.id}, ExpiresAt: ${refreshTokenExpires.toISOString()}`);

    logger.info(`[TOKEN_SERVICE] Auth tokens generated successfully - UserID: ${user.id}, AccessExpires: ${accessTokenExpires.toISOString()}, RefreshExpires: ${refreshTokenExpires.toISOString()}`);

    return {
      access: {
        token: accessToken,
        expires: accessTokenExpires.toDate()
      },
      refresh: {
        token: refreshToken,
        expires: refreshTokenExpires.toDate()
      }
    };
  } catch (error: any) {
    logger.error(`[TOKEN_SERVICE] Auth tokens generation failed - UserID: ${user.id}, Error: ${error.message}`);
    throw error;
  }
};

/**
 * Generate reset password token
 * @param {string} email
 * @returns {Promise<string>}
 */
const generateResetPasswordToken = async (email: string): Promise<string> => {
  logger.debug(`[TOKEN_SERVICE] Starting reset password token generation - Email: ${email}`);

  try {
    const user = await userService.getUserByEmail(email);
    if (!user) {
      logger.warn(`[TOKEN_SERVICE] Reset password token generation failed - User not found - Email: ${email}`);
      throw new ApiError(httpStatus.NOT_FOUND, 'No users found with this email');
    }

    logger.debug(`[TOKEN_SERVICE] User found for reset password - UserID: ${user.id}, Email: ${email}`);

    const expires = moment().add(config.jwt.resetPasswordExpirationMinutes, 'minutes');
    const resetPasswordToken = generateToken(user.id as number, expires, TokenType.RESET_PASSWORD);
    await saveToken(resetPasswordToken, user.id as number, expires, TokenType.RESET_PASSWORD);

    logger.info(`[TOKEN_SERVICE] Reset password token generated successfully - UserID: ${user.id}, Email: ${email}, ExpiresAt: ${expires.toISOString()}`);
    return resetPasswordToken;
  } catch (error: any) {
    if (error instanceof ApiError) {
      throw error;
    }
    logger.error(`[TOKEN_SERVICE] Reset password token generation error - Email: ${email}, Error: ${error.message}`);
    throw error;
  }
};

/**
 * Generate verify email token
 * @param {User} user
 * @returns {Promise<string>}
 */
const generateVerifyEmailToken = async (user: { id: number }): Promise<string> => {
  logger.debug(`[TOKEN_SERVICE] Starting verify email token generation - UserID: ${user.id}`);

  try {
    const expires = moment().add(config.jwt.verifyEmailExpirationMinutes, 'minutes');
    const verifyEmailToken = generateToken(user.id, expires, TokenType.VERIFY_EMAIL);
    await saveToken(verifyEmailToken, user.id, expires, TokenType.VERIFY_EMAIL);

    logger.info(`[TOKEN_SERVICE] Verify email token generated successfully - UserID: ${user.id}, ExpiresAt: ${expires.toISOString()}`);
    return verifyEmailToken;
  } catch (error: any) {
    logger.error(`[TOKEN_SERVICE] Verify email token generation error - UserID: ${user.id}, Error: ${error.message}`);
    throw error;
  }
};

export default {
  generateToken,
  saveToken,
  verifyToken,
  generateAuthTokens,
  generateResetPasswordToken,
  generateVerifyEmailToken
};
