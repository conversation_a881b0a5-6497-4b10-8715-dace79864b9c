import request from 'supertest';
import { faker } from '@faker-js/faker';
import httpStatus from 'http-status';
import app from '../../src/app';
import setupTestDB from '../utils/setupTestDb';
import { describe, beforeEach, test, expect, jest } from '@jest/globals';
import { userOne, admin, insertUsers, createOutletForOwner } from '../fixtures/user.fixture';
import { Role } from '@prisma/client';
import prisma from '../../src/client';
import { tokenService } from '../../src/services';

// Set timeout for all tests in this file
jest.setTimeout(20000);

setupTestDB();

describe('Employee Management', () => {
  let ownerAccessToken: string;
  let adminAccessToken: string;
  let outlet: any;
  let dbOwner: any;
  let dbAdmin: any;

  beforeEach(async () => {
    // Create owner and admin
    await insertUsers([userOne, admin]);
    dbOwner = await prisma.user.findUnique({ where: { email: userOne.email } });
    dbAdmin = await prisma.user.findUnique({ where: { email: admin.email } });

    // Create outlet for owner
    outlet = await createOutletForOwner(dbOwner.id);

    // Generate tokens
    const ownerTokens = await tokenService.generateAuthTokens({ id: dbOwner.id });
    const adminTokens = await tokenService.generateAuthTokens({ id: dbAdmin.id });
    ownerAccessToken = ownerTokens.access.token;
    adminAccessToken = adminTokens.access.token;
  }, 15000);

  describe('POST /v1/users/employees', () => {
    let newEmployee: {
      name: string;
      email: string;
      phone: string;
      password: string;
      outletId: number;
    };

    beforeEach(() => {
      newEmployee = {
        name: faker.name.fullName(),
        email: faker.internet.email().toLowerCase(),
        phone: faker.phone.number('08##########'),
        password: 'password1',
        outletId: outlet.id
      };
    });

    test('should return 201 and create employee if owner creates for own outlet', async () => {
      const res = await request(app)
        .post('/v1/users/employees')
        .set('Authorization', `Bearer ${ownerAccessToken}`)
        .send(newEmployee)
        .expect(httpStatus.CREATED);

      expect(res.body).not.toHaveProperty('password');
      expect(res.body).toMatchObject({
        id: expect.anything(),
        name: newEmployee.name,
        email: newEmployee.email,
        phone: newEmployee.phone,
        role: Role.EMPLOYEE,
        outletId: outlet.id,
        isActive: true
      });

      const dbEmployee = await prisma.user.findUnique({ where: { id: res.body.id } });
      expect(dbEmployee).toBeDefined();
      expect(dbEmployee?.role).toBe(Role.EMPLOYEE);
      expect(dbEmployee?.outletId).toBe(outlet.id);
    });

    test('should return 403 if owner tries to create employee for outlet not owned', async () => {
      // Create another owner and outlet
      const anotherOwner = {
        name: faker.name.fullName(),
        email: faker.internet.email().toLowerCase(),
        phone: faker.phone.number('08##########'),
        password: 'password1',
        role: Role.OWNER,
        isEmailVerified: false
      };
      await insertUsers([anotherOwner]);
      const dbAnotherOwner = await prisma.user.findUnique({ where: { email: anotherOwner.email } });
      const anotherOutlet = await createOutletForOwner(dbAnotherOwner!.id);

      newEmployee.outletId = anotherOutlet.id;

      await request(app)
        .post('/v1/users/employees')
        .set('Authorization', `Bearer ${ownerAccessToken}`)
        .send(newEmployee)
        .expect(httpStatus.FORBIDDEN);
    });

    test('should return 201 if admin creates employee for any outlet', async () => {
      const res = await request(app)
        .post('/v1/users/employees')
        .set('Authorization', `Bearer ${adminAccessToken}`)
        .send(newEmployee)
        .expect(httpStatus.CREATED);

      expect(res.body.role).toBe(Role.EMPLOYEE);
      expect(res.body.outletId).toBe(outlet.id);
    });

    test('should return 400 if email already exists', async () => {
      newEmployee.email = userOne.email;

      await request(app)
        .post('/v1/users/employees')
        .set('Authorization', `Bearer ${ownerAccessToken}`)
        .send(newEmployee)
        .expect(httpStatus.BAD_REQUEST);
    });

    test('should return 400 if phone already exists', async () => {
      newEmployee.phone = userOne.phone;

      await request(app)
        .post('/v1/users/employees')
        .set('Authorization', `Bearer ${ownerAccessToken}`)
        .send(newEmployee)
        .expect(httpStatus.BAD_REQUEST);
    });

    test('should return 400 if outlet does not exist', async () => {
      newEmployee.outletId = 999;

      await request(app)
        .post('/v1/users/employees')
        .set('Authorization', `Bearer ${adminAccessToken}`)
        .send(newEmployee)
        .expect(httpStatus.BAD_REQUEST);
    });

    test('should return 401 if not authenticated', async () => {
      await request(app)
        .post('/v1/users/employees')
        .send(newEmployee)
        .expect(httpStatus.UNAUTHORIZED);
    });
  });

  describe('GET /v1/users/employees', () => {
    let employee1: any;
    let employee2: any;

    beforeEach(async () => {
      // Create employees for the outlet
      employee1 = await prisma.user.create({
        data: {
          name: faker.name.fullName(),
          email: faker.internet.email().toLowerCase(),
          phone: faker.phone.number('08##########'),
          password: 'hashedpassword',
          role: Role.EMPLOYEE,
          outletId: outlet.id
        }
      });

      employee2 = await prisma.user.create({
        data: {
          name: faker.name.fullName(),
          email: faker.internet.email().toLowerCase(),
          phone: faker.phone.number('08##########'),
          password: 'hashedpassword',
          role: Role.EMPLOYEE,
          outletId: outlet.id
        }
      });
    });

    test('should return 200 and employees if owner gets employees from own outlet', async () => {
      const res = await request(app)
        .get(`/v1/users/employees?outletId=${outlet.id}`)
        .set('Authorization', `Bearer ${ownerAccessToken}`)
        .expect(httpStatus.OK);

      expect(res.body.results).toHaveLength(2);
      expect(res.body.results[0]).toMatchObject({
        id: expect.anything(),
        name: expect.anything(),
        email: expect.anything(),
        role: Role.EMPLOYEE,
        outletId: outlet.id
      });
      expect(res.body.results[0]).not.toHaveProperty('password');
    });

    test('should return 200 and all employees if admin gets employees', async () => {
      const res = await request(app)
        .get('/v1/users/employees')
        .set('Authorization', `Bearer ${adminAccessToken}`)
        .expect(httpStatus.OK);

      expect(res.body.results.length).toBeGreaterThanOrEqual(2);
    });

    test('should return 403 if owner tries to get employees from outlet not owned', async () => {
      // Create another owner and outlet with employee
      const anotherOwner = {
        name: faker.name.fullName(),
        email: faker.internet.email().toLowerCase(),
        phone: faker.phone.number('08##########'),
        password: 'password1',
        role: Role.OWNER,
        isEmailVerified: false
      };
      await insertUsers([anotherOwner]);
      const dbAnotherOwner = await prisma.user.findUnique({ where: { email: anotherOwner.email } });
      const anotherOutlet = await createOutletForOwner(dbAnotherOwner!.id);

      await request(app)
        .get(`/v1/users/employees?outletId=${anotherOutlet.id}`)
        .set('Authorization', `Bearer ${ownerAccessToken}`)
        .expect(httpStatus.FORBIDDEN);
    });

    test('should return 401 if not authenticated', async () => {
      await request(app).get('/v1/users/employees').expect(httpStatus.UNAUTHORIZED);
    });
  });

  describe('PUT /v1/users/employees/:employeeId', () => {
    let employee: any;
    let anotherOutlet: any;

    beforeEach(async () => {
      // Create employee
      employee = await prisma.user.create({
        data: {
          name: faker.name.fullName(),
          email: faker.internet.email().toLowerCase(),
          phone: faker.phone.number('08##########'),
          password: 'hashedpassword',
          role: Role.EMPLOYEE,
          outletId: outlet.id
        }
      });

      // Create another outlet for the same owner
      anotherOutlet = await createOutletForOwner(dbOwner.id);
    });

    test('should return 200 and update employee if owner updates employee in own outlet', async () => {
      const updateData = {
        name: 'Updated Name',
        email: faker.internet.email().toLowerCase(),
        outletId: anotherOutlet.id // Transfer to another outlet
      };

      const res = await request(app)
        .put(`/v1/users/employees/${employee.id}`)
        .set('Authorization', `Bearer ${ownerAccessToken}`)
        .send(updateData)
        .expect(httpStatus.OK);

      expect(res.body).toMatchObject({
        id: employee.id,
        name: updateData.name,
        email: updateData.email,
        outletId: anotherOutlet.id
      });

      const dbEmployee = await prisma.user.findUnique({ where: { id: employee.id } });
      expect(dbEmployee?.outletId).toBe(anotherOutlet.id);
    });

    test('should return 403 if owner tries to update employee not in own outlet', async () => {
      // Create another owner and outlet with employee
      const anotherOwner = {
        name: faker.name.fullName(),
        email: faker.internet.email().toLowerCase(),
        phone: faker.phone.number('08##########'),
        password: 'password1',
        role: Role.OWNER,
        isEmailVerified: false
      };
      await insertUsers([anotherOwner]);
      const dbAnotherOwner = await prisma.user.findUnique({ where: { email: anotherOwner.email } });
      const anotherOwnerOutlet = await createOutletForOwner(dbAnotherOwner!.id);

      const anotherEmployee = await prisma.user.create({
        data: {
          name: faker.name.fullName(),
          email: faker.internet.email().toLowerCase(),
          phone: faker.phone.number('08##########'),
          password: 'hashedpassword',
          role: Role.EMPLOYEE,
          outletId: anotherOwnerOutlet.id
        }
      });

      await request(app)
        .put(`/v1/users/employees/${anotherEmployee.id}`)
        .set('Authorization', `Bearer ${ownerAccessToken}`)
        .send({ name: 'Updated Name' })
        .expect(httpStatus.FORBIDDEN);
    });

    test('should return 200 if admin updates any employee', async () => {
      const updateData = { name: 'Admin Updated Name' };

      const res = await request(app)
        .put(`/v1/users/employees/${employee.id}`)
        .set('Authorization', `Bearer ${adminAccessToken}`)
        .send(updateData)
        .expect(httpStatus.OK);

      expect(res.body.name).toBe(updateData.name);
    });

    test('should return 401 if not authenticated', async () => {
      await request(app)
        .put(`/v1/users/employees/${employee.id}`)
        .send({ name: 'Updated Name' })
        .expect(httpStatus.UNAUTHORIZED);
    });
  });

  describe('DELETE /v1/users/employees/:employeeId', () => {
    let employee: any;

    beforeEach(async () => {
      employee = await prisma.user.create({
        data: {
          name: faker.name.fullName(),
          email: faker.internet.email().toLowerCase(),
          phone: faker.phone.number('08##########'),
          password: 'hashedpassword',
          role: Role.EMPLOYEE,
          outletId: outlet.id
        }
      });
    });

    test('should return 200 and soft delete employee if owner deletes employee in own outlet', async () => {
      await request(app)
        .delete(`/v1/users/employees/${employee.id}`)
        .set('Authorization', `Bearer ${ownerAccessToken}`)
        .expect(httpStatus.OK);

      const dbEmployee = await prisma.user.findUnique({ where: { id: employee.id } });
      expect(dbEmployee?.isActive).toBe(false);
    });

    test('should return 403 if owner tries to delete employee not in own outlet', async () => {
      // Create another owner and outlet with employee
      const anotherOwner = {
        name: faker.name.fullName(),
        email: faker.internet.email().toLowerCase(),
        phone: faker.phone.number('08##########'),
        password: 'password1',
        role: Role.OWNER,
        isEmailVerified: false
      };
      await insertUsers([anotherOwner]);
      const dbAnotherOwner = await prisma.user.findUnique({ where: { email: anotherOwner.email } });
      const anotherOwnerOutlet = await createOutletForOwner(dbAnotherOwner!.id);

      const anotherEmployee = await prisma.user.create({
        data: {
          name: faker.name.fullName(),
          email: faker.internet.email().toLowerCase(),
          phone: faker.phone.number('08##########'),
          password: 'hashedpassword',
          role: Role.EMPLOYEE,
          outletId: anotherOwnerOutlet.id
        }
      });

      await request(app)
        .delete(`/v1/users/employees/${anotherEmployee.id}`)
        .set('Authorization', `Bearer ${ownerAccessToken}`)
        .expect(httpStatus.FORBIDDEN);
    });

    test('should return 200 if admin deletes any employee', async () => {
      await request(app)
        .delete(`/v1/users/employees/${employee.id}`)
        .set('Authorization', `Bearer ${adminAccessToken}`)
        .expect(httpStatus.OK);

      const dbEmployee = await prisma.user.findUnique({ where: { id: employee.id } });
      expect(dbEmployee?.isActive).toBe(false);
    });

    test('should return 401 if not authenticated', async () => {
      await request(app)
        .delete(`/v1/users/employees/${employee.id}`)
        .expect(httpStatus.UNAUTHORIZED);
    });
  });
});
