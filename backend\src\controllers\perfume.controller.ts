import httpStatus from 'http-status';
import catchAsync from '../utils/catchAsync';
import { perfumeService } from '../services';
import pick from '../utils/pick';
import ApiError from '../utils/ApiError';
import logger from '../config/logger';
import { Request, Response } from 'express';

const createPerfume = catchAsync(async (req: Request, res: Response) => {
  const perfumeData = { ...req.body };

  logger.info('Creating perfume', {
    userId: (req as any).user?.id,
    outletId: perfumeData.outletId,
    perfumeName: req.body.name
  });

  const perfume = await perfumeService.createPerfume(perfumeData);

  logger.info('Perfume created successfully', {
    userId: (req as any).user?.id,
    perfumeId: perfume.id,
    perfumeName: perfume.name
  });

  res.status(httpStatus.CREATED).send(perfume);
});

const getPerfumes = catchAsync(async (req, res) => {
  const filter = pick(req.query, ['name', 'brand', 'scent', 'isActive', 'outletId']);
  const options = pick(req.query, ['sortBy', 'limit', 'page']);

  logger.debug('Fetching perfumes', {
    userId: (req as any).user?.id,
    outletId: filter.outletId,
    filter,
    options
  });

  const result = await perfumeService.queryPerfumes(filter, options);

  logger.debug('Perfumes fetched successfully', {
    userId: (req as any).user?.id,
    outletId: filter.outletId,
    count: result.results.length,
    totalResults: result.totalResults
  });

  res.send(result);
});

const getPerfume = catchAsync(async (req, res) => {
  const perfumeId = parseInt(req.params.perfumeId);

  logger.debug('Fetching perfume by ID', {
    userId: (req as any).user?.id,
    perfumeId
  });

  const perfume = await perfumeService.getPerfumeById(perfumeId);
  if (!perfume) {
    logger.warn('Perfume not found', {
      userId: (req as any).user?.id,
      perfumeId
    });
    throw new ApiError(httpStatus.NOT_FOUND, 'Perfume not found');
  }

  logger.debug('Perfume found', {
    userId: (req as any).user?.id,
    perfumeId: perfume.id,
    perfumeName: perfume.name
  });

  res.send(perfume);
});

const updatePerfume = catchAsync(async (req, res) => {
  const perfumeId = parseInt(req.params.perfumeId);

  logger.info('Updating perfume', {
    userId: (req as any).user?.id,
    perfumeId,
    updateData: req.body
  });

  const perfume = await perfumeService.updatePerfumeById(perfumeId, req.body, req.body.outletId);

  logger.info('Perfume updated successfully', {
    userId: (req as any).user?.id,
    perfumeId: perfume.id,
    perfumeName: perfume.name
  });

  res.send(perfume);
});

const deletePerfume = catchAsync(async (req, res) => {
  const perfumeId = parseInt(req.params.perfumeId);

  logger.info('Deleting perfume', {
    userId: (req as any).user?.id,
    perfumeId
  });

  await perfumeService.deletePerfumeById(perfumeId, req.body.outletId);

  logger.info('Perfume deleted successfully', {
    userId: (req as any).user?.id,
    perfumeId
  });

  res.status(httpStatus.NO_CONTENT).send();
});

export default {
  createPerfume,
  getPerfumes,
  getPerfume,
  updatePerfume,
  deletePerfume
};
