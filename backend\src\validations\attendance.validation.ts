import Joi from 'joi';
import { AttendanceStatus } from '@prisma/client';

const checkIn = {
  body: Joi.object().keys({
    outletId: Joi.number().integer().positive().required(),
    location: Joi.string().optional(),
    pin: Joi.string().when('$requirePin', {
      is: true,
      then: Joi.required(),
      otherwise: Joi.optional()
    }),
    notes: Joi.string().optional()
  })
};

const checkOut = {
  body: Joi.object().keys({
    outletId: Joi.number().integer().positive().required(),
    notes: Joi.string().optional()
  })
};

const getTodayStatus = {
  query: Joi.object().keys({
    outletId: Joi.number().integer().positive().required()
  })
};

const getAttendanceHistory = {
  query: Joi.object().keys({
    outletId: Joi.number().integer().positive().required(),
    startDate: Joi.date().iso().optional(),
    endDate: Joi.date().iso().min(Joi.ref('startDate')).optional(),
    status: Joi.string()
      .valid(...Object.values(AttendanceStatus))
      .optional(),
    limit: Joi.number().integer().min(1).max(100).optional(),
    page: Joi.number().integer().min(1).optional(),
    sortBy: Joi.string().optional()
  })
};

const getTeamAttendance = {
  query: Joi.object().keys({
    outletId: Joi.number().integer().positive().required(),
    date: Joi.date().iso().optional()
  })
};

const createWorkSchedule = {
  body: Joi.object().keys({
    userId: Joi.number().integer().positive().required(),
    outletId: Joi.number().integer().positive().required(),
    name: Joi.string().required(),
    dayOfWeek: Joi.number().integer().min(0).max(6).required(), // 0=Sunday, 6=Saturday
    startTime: Joi.string()
      .pattern(/^([01]\d|2[0-3]):([0-5]\d)$/)
      .required(), // HH:MM format
    endTime: Joi.string()
      .pattern(/^([01]\d|2[0-3]):([0-5]\d)$/)
      .required(), // HH:MM format
    breakStartTime: Joi.string()
      .pattern(/^([01]\d|2[0-3]):([0-5]\d)$/)
      .optional(), // HH:MM format
    breakEndTime: Joi.string()
      .pattern(/^([01]\d|2[0-3]):([0-5]\d)$/)
      .when('breakStartTime', {
        is: Joi.exist(),
        then: Joi.required(),
        otherwise: Joi.optional()
      }), // HH:MM format
    effectiveFrom: Joi.date().iso().required(),
    effectiveTo: Joi.date().iso().min(Joi.ref('effectiveFrom')).optional()
  })
};

const getWorkSchedules = {
  query: Joi.object().keys({
    outletId: Joi.number().integer().positive().required(),
    userId: Joi.number().integer().positive().optional()
  })
};

const updateAttendanceSettings = {
  params: Joi.object().keys({
    outletId: Joi.number().integer().positive().required()
  }),
  body: Joi.object().keys({
    requirePin: Joi.boolean().optional(),
    requirePhoto: Joi.boolean().optional(),
    allowLateCheckIn: Joi.boolean().optional(),
    defaultLateThreshold: Joi.number().integer().min(0).max(120).optional(), // Max 2 hours
    allowEarlyCheckOut: Joi.boolean().optional(),
    autoCheckOut: Joi.boolean().optional(),
    autoCheckOutTime: Joi.string()
      .pattern(/^([01]\d|2[0-3]):([0-5]\d)$/)
      .optional(), // HH:MM format
    geoFencing: Joi.boolean().optional(),
    maxDistance: Joi.number().min(0).max(1000).optional(), // Max 1KM
    overtimeThreshold: Joi.number().min(1).max(24).optional() // Max 24 hours
  })
};

const getAttendanceSettings = {
  query: Joi.object().keys({
    outletId: Joi.number().integer().positive().required()
  })
};

export default {
  checkIn,
  checkOut,
  getTodayStatus,
  getAttendanceHistory,
  getTeamAttendance,
  createWorkSchedule,
  getWorkSchedules,
  updateAttendanceSettings,
  getAttendanceSettings
};
