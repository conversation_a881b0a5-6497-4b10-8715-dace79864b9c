'use client';

import type React from 'react';
import { useState, useMemo } from 'react';
import {
  ArrowLeft,
  Clock,
  Calendar,
  AlertCircle,
  Printer,
  Share2,
  Edit,
  Trash2,
  User,
  Phone,
  CheckCircle,
  X,
  CreditCard,
  Wallet,
  Coins,
  History,
  Loader2,
  PackageOpen,
  Shirt,
  Wind,
  Truck,
  StickyNote,
  Sparkles,
  PenIcon as Pants,
  Bed,
  MessageCircle,
  Settings,
  Receipt,
  ClipboardList,
  MapPin,
  Map,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetClose,
} from '@/components/ui/sheet';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { useRouter, useParams } from 'next/navigation';
import {
  useOrder,
  useUpdateOrderItemStatus,
  useUpdateOrderStatus,
  useAddPayment,
} from '@/hooks/useOrders';
import { toast } from '@/components/ui/use-toast';
import type { OrderItemStatus, PaymentMethod } from '@/types/orders';
import {
  getOrderStatusLabel,
  getPaymentStatusLabel,
  getPaymentMethodLabel,
} from '@/lib/api/orders';
import { formatDateTime, getOverdueDays, isOrderOverdue } from '@/lib/utils';

// Legacy types untuk fitur frontend yang belum ada di backend
type ProcessStatus =
  | 'none'
  | 'cuci'
  | 'kering'
  | 'setrika'
  | 'packing'
  | 'selesai';

// Mapping function dari backend OrderItemStatus ke ProcessStatus (untuk UI legacy)
const mapBackendToProcessStatus = (
  backendStatus: OrderItemStatus
): ProcessStatus => {
  switch (backendStatus) {
    case 'PENDING':
      return 'none';
    case 'WASHING':
      return 'cuci';
    case 'DRYING':
      return 'kering';
    case 'IRONING':
      return 'setrika';
    case 'PACKING':
      return 'packing';
    case 'COMPLETED':
      return 'selesai';
    case 'CANCELLED':
      return 'selesai'; // Fallback
    default:
      return 'none';
  }
};

// Mapping function dari ProcessStatus ke backend OrderItemStatus
const mapProcessToBackendStatus = (
  processStatus: ProcessStatus
): OrderItemStatus => {
  switch (processStatus) {
    case 'none':
      return 'PENDING';
    case 'cuci':
      return 'WASHING';
    case 'kering':
      return 'DRYING';
    case 'setrika':
      return 'IRONING';
    case 'packing':
      return 'PACKING';
    case 'selesai':
      return 'COMPLETED';
    default:
      return 'PENDING';
  }
};

// Icon mapping dari string ke React component
const getServiceIcon = (iconName?: string): React.ReactNode => {
  switch (iconName) {
    case 'shirt':
      return <Shirt className="h-4 w-4 text-blue-500" />;
    case 'pants':
      return <Pants className="h-4 w-4 text-indigo-500" />;
    case 'bed':
      return <Bed className="h-4 w-4 text-amber-500" />;
    case 'package':
      return <PackageOpen className="h-4 w-4 text-green-500" />;
    default:
      return <Shirt className="h-4 w-4 text-blue-500" />;
  }
};

// Interface untuk UI legacy (fitur yang belum ada di backend)
interface OrderItemUI {
  id: number;
  name: string;
  price: number;
  quantity: number;
  unitPrice: number;
  status: ProcessStatus;
  icon: React.ReactNode;
  timeline: {
    status: string;
    time: string;
  }[];
}

export default function OrderDetails() {
  const router = useRouter();
  const params = useParams();
  const orderId = parseInt(params.id as string);

  // API hooks
  const { data: order, isLoading, error } = useOrder(orderId);
  const updateOrderItemStatus = useUpdateOrderItemStatus();
  const updateOrderStatus = useUpdateOrderStatus();
  const addPayment = useAddPayment();

  // UI State
  const [activeTab, setActiveTab] = useState('detail');
  const [isPaymentSheetOpen, setIsPaymentSheetOpen] = useState(false);
  const [confirmDialog, setConfirmDialog] = useState<{
    isOpen: boolean;
    itemId: number | null;
    nextStatus: ProcessStatus;
  }>({ isOpen: false, itemId: null, nextStatus: 'none' });
  const [timelineSheet, setTimelineSheet] = useState<{
    isOpen: boolean;
    itemId: number | null;
  }>({ isOpen: false, itemId: null });
  const [isPrintDialogOpen, setIsPrintDialogOpen] = useState(false);

  // Convert backend data ke UI format
  const orderItemsUI = useMemo((): OrderItemUI[] => {
    if (!order?.items) return [];

    // Sort items by ID to maintain consistent order
    const sortedItems = [...order.items].sort((a, b) => a.id - b.id);

    return sortedItems.map((item) => ({
      id: item.id,
      name: item.serviceName || 'Service',
      price: item.subtotal,
      quantity: item.quantity,
      unitPrice: item.price,
      status: mapBackendToProcessStatus(item.status),
      icon: getServiceIcon(item.icon),
      timeline: [
        {
          status: 'Diterima',
          time: new Date(order.createdAt).toLocaleString('id-ID'),
        },
      ], // Legacy timeline - nanti bisa diintegrasikan dengan API timeline
    }));
  }, [order]);

  // Check payment status
  const isPaid = order?.paymentStatus === 'PAID';
  const isPartiallyPaid = order?.paymentStatus === 'PARTIAL';

  // Loading state
  if (isLoading) {
    return (
      <div className="max-w-md mx-auto bg-background min-h-screen flex items-center justify-center">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="h-8 w-8 animate-spin" />
          <p className="text-sm text-muted-foreground">
            Memuat detail pesanan...
          </p>
        </div>
      </div>
    );
  }

  // Error state
  if (error || !order) {
    return (
      <div className="max-w-md mx-auto bg-background min-h-screen flex items-center justify-center">
        <div className="flex flex-col items-center gap-4 text-center">
          <AlertCircle className="h-8 w-8 text-destructive" />
          <div>
            <p className="font-medium">Gagal memuat pesanan</p>
            <p className="text-sm text-muted-foreground">
              {error?.message || 'Pesanan tidak ditemukan'}
            </p>
          </div>
          <Button onClick={() => router.back()}>Kembali</Button>
        </div>
      </div>
    );
  }

  const handlePayment = async (method: PaymentMethod) => {
    try {
      await addPayment.mutateAsync({
        id: orderId,
        data: {
          amount: order.totalPrice - order.paidAmount,
          method,
        },
      });
      setIsPaymentSheetOpen(false);
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Gagal memproses pembayaran',
        variant: 'destructive',
      });
    }
  };

  const getNextStatus = (currentStatus: ProcessStatus): ProcessStatus => {
    switch (currentStatus) {
      case 'none':
        return 'cuci';
      case 'cuci':
        return 'kering';
      case 'kering':
        return 'setrika';
      case 'setrika':
        return 'packing';
      case 'packing':
        return 'selesai';
      default:
        return 'selesai';
    }
  };

  const getStatusLabel = (status: ProcessStatus): string => {
    switch (status) {
      case 'none':
        return 'Belum Diproses';
      case 'cuci':
        return 'Proses Cuci';
      case 'kering':
        return 'Proses Kering';
      case 'setrika':
        return 'Proses Setrika';
      case 'packing':
        return 'Proses Packing';
      case 'selesai':
        return 'Selesai';
      default:
        return 'Proses';
    }
  };

  const getNextStatusLabel = (status: ProcessStatus): string => {
    switch (status) {
      case 'none':
        return 'Proses Cuci';
      case 'cuci':
        return 'Proses Kering';
      case 'kering':
        return 'Proses Setrika';
      case 'setrika':
        return 'Proses Packing';
      case 'packing':
        return 'Selesai';
      case 'selesai':
        return 'Selesai';
      default:
        return 'Proses';
    }
  };

  const getStatusIcon = (status: ProcessStatus) => {
    switch (status) {
      case 'none':
        return <Loader2 className="h-4 w-4 mr-1" />;
      case 'cuci':
        return <Wind className="h-4 w-4 mr-1" />;
      case 'kering':
        return <Wind className="h-4 w-4 mr-1" />;
      case 'setrika':
        return <Shirt className="h-4 w-4 mr-1" />;
      case 'packing':
        return <PackageOpen className="h-4 w-4 mr-1" />;
      case 'selesai':
        return <CheckCircle className="h-4 w-4 mr-1" />;
      default:
        return <Loader2 className="h-4 w-4 mr-1" />;
    }
  };

  const handleProcessConfirm = async () => {
    if (confirmDialog.itemId === null) return;

    try {
      const nextBackendStatus = mapProcessToBackendStatus(
        confirmDialog.nextStatus
      );

      await updateOrderItemStatus.mutateAsync({
        orderId,
        itemId: confirmDialog.itemId,
        data: { status: nextBackendStatus },
      });

      setConfirmDialog({ isOpen: false, itemId: null, nextStatus: 'none' });
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Gagal memperbarui status item',
        variant: 'destructive',
      });
    }
  };

  const handleProcessClick = (itemId: number) => {
    const item = orderItemsUI.find((i) => i.id === itemId);
    if (!item) return;

    const nextStatus = getNextStatus(item.status);
    setConfirmDialog({
      isOpen: true,
      itemId,
      nextStatus,
    });
  };

  const handleTimelineClick = (itemId: number) => {
    setTimelineSheet({
      isOpen: true,
      itemId,
    });
  };

  const selectedItem =
    timelineSheet.itemId !== null
      ? orderItemsUI.find((i) => i.id === timelineSheet.itemId)
      : null;

  const handleOpenMaps = () => {
    // Legacy feature - bisa dikembangkan nanti dengan data alamat dari customer
    if (order.customer?.address) {
      const mapsUrl = `https://maps.google.com/maps?q=${encodeURIComponent(
        order.customer.address
      )}`;
      window.open(mapsUrl, '_blank');
    }
  };

  const handleCancelOrder = async () => {
    try {
      await updateOrderStatus.mutateAsync({
        id: orderId,
        data: { status: 'CANCELLED' },
      });
      toast({
        title: 'Berhasil',
        description: 'Pesanan berhasil dibatalkan',
      });
      router.push('/orders?tab=batal');
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Gagal membatalkan pesanan',
        variant: 'destructive',
      });
    }
  };

  // Calculate delivery info (legacy feature)
  const isPickupDelivery = !!(order.pickupDate || order.deliveryDate);
  const isPickup = !!order.pickupDate;
  const isDelivery = !!order.deliveryDate;

  return (
    <div className="max-w-md mx-auto bg-background min-h-screen pb-16">
      {/* Header */}
      <div className="sticky top-0 z-10 bg-background border-b">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-3">
            <Button
              variant="ghost"
              size="icon"
              className="h-9 w-9"
              onClick={() => router.back()}
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <h1 className="text-xl font-semibold">Detail Pesanan</h1>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="icon"
              className="h-9 w-9 text-muted-foreground"
              onClick={() => setIsPrintDialogOpen(true)}
            >
              <Printer className="h-5 w-5" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="h-9 w-9 text-muted-foreground"
            >
              <Share2 className="h-5 w-5" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="h-9 w-9 text-muted-foreground"
            >
              <Edit className="h-5 w-5" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="h-9 w-9 text-destructive"
              onClick={handleCancelOrder}
            >
              <Trash2 className="h-5 w-5" />
            </Button>
          </div>
        </div>
      </div>

      {/* Combined Order Summary & Customer Info Card */}
      <div className="p-4">
        <Card
          className="border-none shadow-sm"
          onClick={() => router.push(`/akun/customers/${order.customerId}`)}
        >
          <CardContent className="p-4">
            <div className="flex justify-between items-start mb-4">
              <h2 className="text-xl font-bold text-blue-600">
                {order.orderNumber}
              </h2>
              <Badge
                variant={isPaid ? 'default' : 'destructive'}
                className="rounded-full px-3"
              >
                {getPaymentStatusLabel(order.paymentStatus)}
              </Badge>
            </div>

            <div className="space-y-3 text-sm">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                <span className="text-muted-foreground">Tanggal Order:</span>
                <span className="font-medium">
                  {formatDateTime(order.createdAt)}
                </span>
              </div>

              {order.estimatedFinish && (
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                  <span className="text-muted-foreground">
                    Estimasi Selesai:
                  </span>
                  <span className="font-medium">
                    {formatDateTime(order.estimatedFinish)}
                  </span>
                </div>
              )}

              {isOrderOverdue(order.estimatedFinish, order.actualFinish) && (
                <div className="flex items-center gap-2 text-destructive">
                  <AlertCircle className="h-4 w-4 flex-shrink-0" />
                  <span>
                    Terlambat:{' '}
                    {getOverdueDays(order.estimatedFinish, order.actualFinish)}{' '}
                    Hari
                  </span>
                </div>
              )}

              {/* Customer Info */}
              <div className="pt-3 border-t mt-2">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex flex-col">
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4 text-muted-foreground" />
                      <span className="font-medium">
                        {order.customer?.name}
                      </span>
                    </div>
                    {order.customer?.phone && (
                      <div className="text-sm text-muted-foreground ml-6">
                        {order.customer.phone}
                      </div>
                    )}
                  </div>
                  <div className="flex gap-2">
                    {order.customer?.phone && (
                      <>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8"
                          onClick={(e) => {
                            e.stopPropagation();
                            window.open(
                              `tel:${order.customer?.phone}`,
                              '_blank'
                            );
                          }}
                        >
                          <Phone className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8"
                          onClick={(e) => {
                            e.stopPropagation();
                            window.open(
                              `https://wa.me/${order.customer?.phone?.replace(
                                /\D/g,
                                ''
                              )}`,
                              '_blank'
                            );
                          }}
                        >
                          <MessageCircle className="h-4 w-4" />
                        </Button>
                      </>
                    )}
                  </div>
                </div>
              </div>

              {/* Payment Button in Summary Card */}
              {!isPaid && (
                <div className="pt-3">
                  <Button
                    className="w-full h-10 text-sm font-medium"
                    onClick={(e) => {
                      e.stopPropagation();
                      setIsPaymentSheetOpen(true);
                    }}
                  >
                    <CreditCard className="h-4 w-4 mr-2" />
                    Bayar
                  </Button>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs */}
      <div className="px-4">
        <Tabs
          defaultValue="detail"
          className="w-full"
          onValueChange={setActiveTab}
        >
          <TabsList className="grid grid-cols-2 w-full bg-muted/50">
            <TabsTrigger
              value="detail"
              className={activeTab === 'detail' ? 'font-medium' : ''}
            >
              Detail
            </TabsTrigger>
            <TabsTrigger
              value="timeline"
              className={activeTab === 'timeline' ? 'font-medium' : ''}
            >
              Timeline
            </TabsTrigger>
          </TabsList>

          <TabsContent value="detail" className="mt-4 space-y-4">
            {/* Pickup/Delivery Information */}
            {isPickupDelivery && (
              <Card>
                <CardHeader className="pb-2 pt-4 px-4">
                  <h2 className="text-base font-semibold">
                    Informasi Antar Jemput
                  </h2>
                </CardHeader>
                <CardContent className="px-4 pb-4 pt-0">
                  <div className="space-y-3">
                    {/* Pickup & Delivery Schedule */}
                    <div className="grid grid-cols-2 gap-3">
                      {isPickup && order.pickupDate && (
                        <div className="p-3 border rounded-md">
                          <div className="flex items-center gap-2 mb-1">
                            <Calendar className="h-4 w-4 text-blue-500" />
                            <span className="text-sm font-medium">Jemput</span>
                          </div>
                          <div className="text-sm">
                            {new Date(order.pickupDate).toLocaleDateString(
                              'id-ID'
                            )}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {new Date(order.pickupDate).toLocaleTimeString(
                              'id-ID',
                              { hour: '2-digit', minute: '2-digit' }
                            )}
                          </div>
                        </div>
                      )}

                      {isDelivery && order.deliveryDate && (
                        <div className="p-3 border rounded-md">
                          <div className="flex items-center gap-2 mb-1">
                            <Calendar className="h-4 w-4 text-green-500" />
                            <span className="text-sm font-medium">Antar</span>
                          </div>
                          <div className="text-sm">
                            {new Date(order.deliveryDate).toLocaleDateString(
                              'id-ID'
                            )}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {new Date(order.deliveryDate).toLocaleTimeString(
                              'id-ID',
                              { hour: '2-digit', minute: '2-digit' }
                            )}
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Delivery Address */}
                    {isDelivery && order.customer?.address && (
                      <div className="p-3 border rounded-md">
                        <div className="flex items-center gap-2 mb-1">
                          <MapPin className="h-4 w-4 text-blue-500" />
                          <span className="text-sm font-medium">
                            Alamat Pengiriman
                          </span>
                        </div>
                        <div className="text-sm">{order.customer.address}</div>
                        <Button
                          variant="outline"
                          size="sm"
                          className="w-full mt-2"
                          onClick={handleOpenMaps}
                        >
                          <Map className="h-4 w-4 mr-2" />
                          Buka Maps
                        </Button>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Order Items */}
            <Card>
              <CardHeader className="pb-2 pt-4 px-4">
                <h3 className="text-base font-semibold">Item Pesanan</h3>
              </CardHeader>
              <CardContent className="px-4 pb-0 pt-0">
                <div className="divide-y">
                  {orderItemsUI.map((item) => (
                    <div key={item.id} className="py-2.5">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          {item.icon}
                          <span className="font-medium">{item.name}</span>
                        </div>

                        <Badge
                          variant={
                            item.status === 'selesai' ? 'success' : 'outline'
                          }
                          className="text-xs h-5 px-2"
                        >
                          {getStatusLabel(item.status)}
                        </Badge>
                      </div>
                      <div className="flex items-center justify-between mt-1 mb-2">
                        <div className="text-xs text-muted-foreground">
                          {item.quantity} Pcs x Rp{' '}
                          {item.unitPrice.toLocaleString()}
                        </div>
                        <span className=" text-xs text-muted-foreground">
                          Rp {item.price.toLocaleString()}
                        </span>
                      </div>
                      <div className="flex gap-1.5">
                        <Button
                          size="sm"
                          variant="outline"
                          className="text-xs h-7 px-2 flex-1"
                          onClick={() => handleProcessClick(item.id)}
                          disabled={
                            item.status === 'selesai' ||
                            updateOrderItemStatus.isPending
                          }
                        >
                          {updateOrderItemStatus.isPending &&
                          confirmDialog.itemId === item.id ? (
                            <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                          ) : (
                            getStatusIcon(item.status)
                          )}
                          {getNextStatusLabel(item.status)}
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          className="text-xs h-7 px-2 flex-1"
                          onClick={() => handleTimelineClick(item.id)}
                        >
                          <History className="h-4 w-4 mr-1" />
                          Timeline
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Additional Info */}
            <Card>
              <CardContent className="p-4 space-y-4">
                {/* Perfume Choice */}
                {(order.perfume?.name || order.perfumeName) && (
                  <div className="flex items-start gap-2">
                    <Sparkles className="h-4 w-4 text-pink-500 mt-0.5 flex-shrink-0" />
                    <div>
                      <div className="text-sm font-medium">Parfum</div>
                      <div className="text-sm text-muted-foreground">
                        {order.perfume?.name || order.perfumeName}
                      </div>
                      {order.perfume?.description && (
                        <div className="text-xs text-muted-foreground mt-1">
                          {order.perfume.description}
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Notes */}
                {order.notes && (
                  <div className="flex items-start gap-2">
                    <StickyNote className="h-4 w-4 text-amber-500 mt-0.5 flex-shrink-0" />
                    <div>
                      <div className="text-sm font-medium">Catatan</div>
                      <div className="text-sm text-muted-foreground">
                        {order.notes}
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Payment Summary */}
            <Card>
              <CardContent className="p-4">
                <div className="flex justify-between text-sm mb-1.5">
                  <span>Subtotal</span>
                  <span className="font-medium">
                    Rp{' '}
                    {(
                      order.totalPrice + (order.promotionDiscount || 0)
                    ).toLocaleString()}
                  </span>
                </div>
                {order.promotionCode &&
                  order.promotionDiscount &&
                  order.promotionDiscount > 0 && (
                    <div className="flex justify-between text-sm mb-1.5 text-green-600">
                      <span className="flex items-center gap-1">
                        <Sparkles className="h-4 w-4" />
                        Promo {order.promotionCode}
                      </span>
                      <span className="font-medium">
                        -Rp {order.promotionDiscount.toLocaleString()}
                      </span>
                    </div>
                  )}
                <div className="flex justify-between text-sm mb-1.5">
                  <span>Biaya Antar</span>
                  <span className="font-medium">Rp 0</span>
                </div>
                <div className="flex justify-between font-semibold text-base mt-2 pt-2 border-t">
                  <span>Total</span>
                  <span>Rp {order.totalPrice.toLocaleString()}</span>
                </div>

                <div className="flex justify-between text-sm mt-3 pt-2 border-t">
                  <span>Status Pembayaran</span>
                  <Badge
                    variant={isPaid ? 'success' : 'destructive'}
                    className="rounded-full px-3 text-xs"
                  >
                    {getPaymentStatusLabel(order.paymentStatus)}
                  </Badge>
                </div>
                {(isPaid || isPartiallyPaid) && order.paymentMethod && (
                  <div className="flex justify-between text-sm mt-2">
                    <span>Metode Pembayaran</span>
                    <span className="font-medium">
                      {getPaymentMethodLabel(order.paymentMethod)}
                    </span>
                  </div>
                )}
                {isPartiallyPaid && (
                  <div className="flex justify-between text-sm mt-2">
                    <span>Dibayar</span>
                    <span className="font-medium">
                      Rp {order.paidAmount.toLocaleString()}
                    </span>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="timeline" className="mt-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex flex-col space-y-4">
                  <div className="flex gap-3">
                    <div className="relative">
                      <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                        <CheckCircle className="h-5 w-5 text-blue-600" />
                      </div>
                      <div className="absolute top-8 bottom-0 left-1/2 w-0.5 -ml-0.5 bg-blue-100"></div>
                    </div>
                    <div>
                      <p className="font-medium">Order Diterima</p>
                      <p className="text-sm text-muted-foreground">
                        {formatDateTime(order.createdAt)}
                      </p>
                    </div>
                  </div>

                  <div className="flex gap-3">
                    <div className="h-8 w-8 rounded-full bg-gray-100 flex items-center justify-center">
                      <div className="h-2 w-2 rounded-full bg-gray-400"></div>
                    </div>
                    <div>
                      <p className="font-medium text-muted-foreground">
                        Proses
                      </p>
                      <p className="text-sm text-muted-foreground">Menunggu</p>
                    </div>
                  </div>

                  <div className="flex gap-3">
                    <div className="h-8 w-8 rounded-full bg-gray-100 flex items-center justify-center">
                      <div className="h-2 w-2 rounded-full bg-gray-400"></div>
                    </div>
                    <div>
                      <p className="font-medium text-muted-foreground">
                        Siap Ambil
                      </p>
                      <p className="text-sm text-muted-foreground">Menunggu</p>
                    </div>
                  </div>

                  <div className="flex gap-3">
                    <div className="h-8 w-8 rounded-full bg-gray-100 flex items-center justify-center">
                      <div className="h-2 w-2 rounded-full bg-gray-400"></div>
                    </div>
                    <div>
                      <p className="font-medium text-muted-foreground">
                        Selesai
                      </p>
                      <p className="text-sm text-muted-foreground">Menunggu</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      {/* Action Buttons */}
      <div className="p-4 mt-4 space-y-3">
        {!isPaid && (
          <Button
            className="w-full h-12 text-base font-medium"
            onClick={() => setIsPaymentSheetOpen(true)}
          >
            <CreditCard className="h-5 w-5 mr-2" />
            {isPartiallyPaid ? 'Bayar Sisa' : 'Bayar'}
          </Button>
        )}

        {order.status !== 'COMPLETED' && order.status !== 'CANCELLED' && (
          <Button
            variant="destructive"
            className="w-full h-12 text-base font-medium"
            onClick={handleCancelOrder}
            disabled={updateOrderStatus.isPending}
          >
            {updateOrderStatus.isPending ? (
              <Loader2 className="h-5 w-5 mr-2 animate-spin" />
            ) : (
              <Trash2 className="h-5 w-5 mr-2" />
            )}
            Batalkan Pesanan
          </Button>
        )}
      </div>

      {/* Payment Sheet */}
      <Sheet open={isPaymentSheetOpen} onOpenChange={setIsPaymentSheetOpen}>
        <SheetContent side="bottom" className="h-auto max-h-[90vh]">
          <SheetHeader className="mb-4">
            <div className="flex items-center justify-between">
              <SheetTitle>Pilih Metode Pembayaran</SheetTitle>
              <SheetClose asChild>
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <X className="h-4 w-4" />
                </Button>
              </SheetClose>
            </div>
          </SheetHeader>

          <div className="space-y-3">
            <Button
              variant="outline"
              className="w-full h-14 justify-start text-base font-normal"
              onClick={() => handlePayment('CASH')}
            >
              <Coins className="h-5 w-5 mr-3 text-green-500" />
              Tunai
            </Button>

            <Button
              variant="outline"
              className="w-full h-14 justify-start text-base font-normal"
              onClick={() => handlePayment('TRANSFER')}
            >
              <CreditCard className="h-5 w-5 mr-3 text-blue-500" />
              Transfer
            </Button>

            <Button
              variant="outline"
              className="w-full h-14 justify-start text-base font-normal"
              onClick={() => handlePayment('E_WALLET')}
            >
              <CreditCard className="h-5 w-5 mr-3 text-purple-500" />
              E-Wallet
            </Button>

            <Button
              variant="outline"
              className="w-full h-14 justify-start text-base font-normal"
              onClick={() => handlePayment('DEPOSIT')}
            >
              <Wallet className="h-5 w-5 mr-3 text-amber-500" />
              Deposit
            </Button>
          </div>
        </SheetContent>
      </Sheet>

      {/* Process Confirmation Dialog */}
      <AlertDialog
        open={confirmDialog.isOpen}
        onOpenChange={(open) =>
          !open && setConfirmDialog((prev) => ({ ...prev, isOpen: false }))
        }
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              Konfirmasi {getStatusLabel(confirmDialog.nextStatus)}
            </AlertDialogTitle>
            <AlertDialogDescription>
              Apakah Anda yakin ingin memproses item ini ke tahap{' '}
              {getStatusLabel(confirmDialog.nextStatus).toLowerCase()}?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={updateOrderItemStatus.isPending}>
              Batal
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleProcessConfirm}
              disabled={updateOrderItemStatus.isPending}
            >
              {updateOrderItemStatus.isPending ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Memproses...
                </>
              ) : (
                'Konfirmasi'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Item Timeline Sheet */}
      <Sheet
        open={timelineSheet.isOpen}
        onOpenChange={(open) =>
          !open && setTimelineSheet((prev) => ({ ...prev, isOpen: false }))
        }
      >
        <SheetContent side="bottom" className="h-auto max-h-[90vh]">
          <SheetHeader className="mb-4">
            <div className="flex items-center justify-between">
              <SheetTitle>Timeline {selectedItem?.name}</SheetTitle>
              <SheetClose asChild>
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <X className="h-4 w-4" />
                </Button>
              </SheetClose>
            </div>
          </SheetHeader>

          <div className="flex flex-col space-y-4">
            {selectedItem?.timeline.map((event, index) => (
              <div key={index} className="flex gap-3">
                <div className="relative">
                  <div
                    className={`h-8 w-8 rounded-full ${
                      index === 0 ? 'bg-blue-100' : 'bg-gray-100'
                    } flex items-center justify-center`}
                  >
                    {index === 0 ? (
                      <CheckCircle className="h-5 w-5 text-blue-600" />
                    ) : (
                      <div className="h-2 w-2 rounded-full bg-gray-400"></div>
                    )}
                  </div>
                  {index < selectedItem.timeline.length - 1 && (
                    <div className="absolute top-8 bottom-0 left-1/2 w-0.5 -ml-0.5 bg-blue-100"></div>
                  )}
                </div>
                <div>
                  <p className="font-medium">{event.status}</p>
                  <p className="text-sm text-muted-foreground">{event.time}</p>
                </div>
              </div>
            ))}
          </div>
        </SheetContent>
      </Sheet>
      {/* Print Options Dialog */}
      <AlertDialog open={isPrintDialogOpen} onOpenChange={setIsPrintDialogOpen}>
        <AlertDialogContent className="max-w-[350px]">
          <AlertDialogHeader>
            <div className="flex items-center justify-between">
              <AlertDialogTitle>Pilih Jenis Nota</AlertDialogTitle>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={(e) => {
                  e.stopPropagation();
                  setIsPrintDialogOpen(false);
                  router.push('/settings/receipt');
                }}
              >
                <Settings className="h-4 w-4" />
              </Button>
            </div>
          </AlertDialogHeader>
          <div className="grid gap-3 py-4">
            <Button
              className="w-full h-12 justify-start text-base font-normal"
              onClick={() => {
                // Handle print customer receipt
                setIsPrintDialogOpen(false);
                // Print logic would go here
              }}
            >
              <Receipt className="h-5 w-5 mr-3" />
              Nota Pelanggan
            </Button>
            <Button
              className="w-full h-12 justify-start text-base font-normal"
              onClick={() => {
                // Handle print production receipt
                setIsPrintDialogOpen(false);
                // Print logic would go here
              }}
            >
              <ClipboardList className="h-5 w-5 mr-3" />
              Nota Produksi
            </Button>
          </div>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
