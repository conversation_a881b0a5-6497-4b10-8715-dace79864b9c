'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from './auth-context';

interface AuthGuardProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  requireAuth?: boolean;
  redirectTo?: string;
  allowedRoles?: string[];
}

export function AuthGuard({
  children,
  fallback = <div>Loading...</div>,
  requireAuth = true,
  redirectTo = '/auth/login',
  allowedRoles = [],
}: AuthGuardProps) {
  const { user, isLoading, isAuthenticated } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading) {
      if (requireAuth && !isAuthenticated) {
        router.push(redirectTo);
        return;
      }

      if (
        allowedRoles.length > 0 &&
        user &&
        !allowedRoles.includes(user.role)
      ) {
        router.push('/unauthorized');
        return;
      }
    }
  }, [
    isLoading,
    isAuthenticated,
    user,
    router,
    requireAuth,
    redirectTo,
    allowedRoles,
  ]);

  // Show loading while checking auth
  if (isLoading) {
    return <>{fallback}</>;
  }

  // Show content if authorized
  if (requireAuth && !isAuthenticated) {
    return <>{fallback}</>;
  }

  if (allowedRoles.length > 0 && user && !allowedRoles.includes(user.role)) {
    return <div>Unauthorized</div>;
  }

  return <>{children}</>;
}

// HOC untuk component yang memerlukan auth
export function withAuth<P extends object>(
  Component: React.ComponentType<P>,
  options: {
    requireAuth?: boolean;
    allowedRoles?: string[];
    redirectTo?: string;
  } = {}
) {
  const WrappedComponent = (props: P) => {
    return (
      <AuthGuard {...options}>
        <Component {...props} />
      </AuthGuard>
    );
  };

  WrappedComponent.displayName = `withAuth(${
    Component.displayName || Component.name
  })`;

  return WrappedComponent;
}

// Hook untuk protected API calls
export function useAuthApiCall() {
  const { isAuthenticated, tokens } = useAuth();

  const makeApiCall = async (
    url: string,
    options: RequestInit = {}
  ): Promise<Response> => {
    if (!isAuthenticated || !tokens?.access?.token) {
      throw new Error('Not authenticated');
    }

    const headers = {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${tokens.access.token}`,
      ...options.headers,
    };

    return fetch(url, {
      ...options,
      headers,
    });
  };

  return { makeApiCall, isAuthenticated };
}

interface AuthRedirectProps {
  children: React.ReactNode;
  redirectTo?: string;
  requireAuth?: boolean;
  fallback?: React.ReactNode;
}

export function AuthRedirect({
  children,
  redirectTo = '/',
  requireAuth = false,
  fallback = (
    <div className="flex items-center justify-center min-h-screen">
      <p>Loading...</p>
    </div>
  ),
}: AuthRedirectProps) {
  const { isAuthenticated, isLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading) {
      if (requireAuth && !isAuthenticated) {
        router.push('/auth/login');
      } else if (!requireAuth && isAuthenticated) {
        router.push(redirectTo);
      }
    }
  }, [isLoading, isAuthenticated, requireAuth, redirectTo, router]);

  // Show loading while checking auth
  if (isLoading) {
    return <>{fallback}</>;
  }

  // Redirect scenarios - show fallback to prevent flash
  if ((!requireAuth && isAuthenticated) || (requireAuth && !isAuthenticated)) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}
