"use client"

import { useState } from "react"
import Link from "next/link"
import { ArrowLeft, Plus, Download, TrendingUp, TrendingDown, DollarSign, Calendar } from "lucide-react"
import { useRouter } from "next/navigation"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

// Mock data for financial transactions
const mockTransactions = [
  {
    id: 1,
    date: "24/03/2025",
    description: "Pembayaran Order #TRX/250324/014",
    amount: 125000,
    type: "income",
    category: "order",
    paymentMethod: "cash",
  },
  {
    id: 2,
    date: "23/03/2025",
    description: "Pembayaran Order #TRX/250323/009",
    amount: 85000,
    type: "income",
    category: "order",
    paymentMethod: "transfer",
  },
  {
    id: 3,
    date: "22/03/2025",
    description: "Pembelian Deterjen",
    amount: 250000,
    type: "expense",
    category: "supplies",
    paymentMethod: "cash",
  },
  {
    id: 4,
    date: "21/03/2025",
    description: "Pembayaran Listrik",
    amount: 500000,
    type: "expense",
    category: "utilities",
    paymentMethod: "transfer",
  },
  {
    id: 5,
    date: "20/03/2025",
    description: "Pembayaran Order #TRX/250320/005",
    amount: 150000,
    type: "income",
    category: "order",
    paymentMethod: "cash",
  },
  {
    id: 6,
    date: "19/03/2025",
    description: "Gaji Pegawai - Maret 2025",
    amount: 3500000,
    type: "expense",
    category: "salary",
    paymentMethod: "transfer",
  },
  {
    id: 7,
    date: "18/03/2025",
    description: "Pembayaran Order #TRX/250318/003",
    amount: 95000,
    type: "income",
    category: "order",
    paymentMethod: "qris",
  },
  {
    id: 8,
    date: "17/03/2025",
    description: "Pembelian Plastik Packaging",
    amount: 150000,
    type: "expense",
    category: "supplies",
    paymentMethod: "cash",
  },
]

export default function FinancePage() {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState("semua")
  const [period, setPeriod] = useState("this-month")

  // Filter transactions based on active tab
  const filteredTransactions = mockTransactions.filter((transaction) => {
    if (activeTab === "semua") return true
    if (activeTab === "income" && transaction.type === "income") return true
    if (activeTab === "expense" && transaction.type === "expense") return true
    return false
  })

  // Calculate summary
  const totalIncome = mockTransactions
    .filter((t) => t.type === "income")
    .reduce((sum, transaction) => sum + transaction.amount, 0)
  const totalExpense = mockTransactions
    .filter((t) => t.type === "expense")
    .reduce((sum, transaction) => sum + transaction.amount, 0)
  const balance = totalIncome - totalExpense

  return (
    <div className="flex flex-col min-h-screen bg-slate-50">
      <header className="p-4 flex justify-between items-center bg-white sticky top-0 z-10 shadow-sm">
        <div className="flex items-center">
          <Link href="/akun" className="mr-3">
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <h1 className="text-lg font-semibold">Keuangan</h1>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="icon">
            <Download className="h-4 w-4" />
          </Button>
          <Link href="/akun/finance/add">
            <Button size="sm" className="bg-blue-500 hover:bg-blue-600">
              <Plus className="h-4 w-4 mr-1" /> Tambah
            </Button>
          </Link>
        </div>
      </header>

      <main className="flex-1 p-4 pb-20">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-semibold">Ringkasan Keuangan</h2>
          <Select value={period} onValueChange={setPeriod}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Pilih Periode" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="today">Hari Ini</SelectItem>
              <SelectItem value="this-week">Minggu Ini</SelectItem>
              <SelectItem value="this-month">Bulan Ini</SelectItem>
              <SelectItem value="last-month">Bulan Lalu</SelectItem>
              <SelectItem value="this-year">Tahun Ini</SelectItem>
              <SelectItem value="custom">Kustom</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="grid grid-cols-3 gap-4 mb-6">
          <Card className="p-4">
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-sm font-medium text-gray-500">Pemasukan</h3>
              <TrendingUp className="h-4 w-4 text-green-500" />
            </div>
            <p className="text-2xl font-bold">Rp {totalIncome.toLocaleString()}</p>
          </Card>
          <Card className="p-4">
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-sm font-medium text-gray-500">Pengeluaran</h3>
              <TrendingDown className="h-4 w-4 text-red-500" />
            </div>
            <p className="text-2xl font-bold">Rp {totalExpense.toLocaleString()}</p>
          </Card>
          <Card className="p-4">
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-sm font-medium text-gray-500">Saldo</h3>
              <DollarSign className="h-4 w-4 text-blue-500" />
            </div>
            <p className="text-2xl font-bold">Rp {balance.toLocaleString()}</p>
          </Card>
        </div>

        <Tabs defaultValue="semua" className="mb-4" onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-3 w-full">
            <TabsTrigger value="semua">Semua</TabsTrigger>
            <TabsTrigger value="income">Pemasukan</TabsTrigger>
            <TabsTrigger value="expense">Pengeluaran</TabsTrigger>
          </TabsList>
        </Tabs>

        <div className="space-y-4">
          {filteredTransactions.map((transaction) => (
            <Card key={transaction.id} className="p-4">
              <div className="flex justify-between items-start">
                <div>
                  <div className="flex items-center gap-2">
                    <Badge
                      variant={transaction.type === "income" ? "default" : "destructive"}
                      className={
                        transaction.type === "income"
                          ? "bg-green-500 hover:bg-green-600"
                          : "bg-red-500 hover:bg-red-600"
                      }
                    >
                      {transaction.type === "income" ? "Pemasukan" : "Pengeluaran"}
                    </Badge>
                    <Badge variant="outline">{transaction.category}</Badge>
                  </div>
                  <h3 className="font-medium mt-1">{transaction.description}</h3>
                  <div className="flex items-center text-sm text-gray-500 mt-1">
                    <Calendar className="h-3 w-3 mr-1" />
                    {transaction.date}
                  </div>
                </div>
                <div className="text-right">
                  <p
                    className={`font-bold text-lg ${transaction.type === "income" ? "text-green-600" : "text-red-600"}`}
                  >
                    {transaction.type === "income" ? "+" : "-"} Rp {transaction.amount.toLocaleString()}
                  </p>
                  <p className="text-sm text-gray-500">{transaction.paymentMethod}</p>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </main>
    </div>
  )
}
