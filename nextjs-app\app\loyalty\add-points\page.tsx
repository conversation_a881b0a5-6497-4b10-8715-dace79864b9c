"use client"

import type React from "react"

import { useState } from "react"
import Link from "next/link"
import { ArrowLeft, Save, Search, Star, Plus, Minus } from "lucide-react"
import { useRouter } from "next/navigation"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"

// Mock data for customers
const mockCustomers = [
  {
    id: 1,
    name: "<PERSON>",
    phone: "081234567890",
    points: 2500,
    tier: "Silver",
  },
  {
    id: 2,
    name: "<PERSON><PERSON>hayu",
    phone: "081234567891",
    points: 4200,
    tier: "Gold",
  },
  {
    id: 3,
    name: "<PERSON><PERSON>",
    phone: "081234567892",
    points: 850,
    tier: "Bronze",
  },
  {
    id: 4,
    name: "<PERSON><PERSON>",
    phone: "081234567893",
    points: 7500,
    tier: "Platinum",
  },
]

export default function AddPointsPage() {
  const router = useRouter()
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCustomer, setSelectedCustomer] = useState<number | null>(null)
  const [pointsAction, setPointsAction] = useState("add")
  const [pointsAmount, setPointsAmount] = useState("")
  const [notes, setNotes] = useState("")

  // Filter customers based on search query
  const filteredCustomers = mockCustomers.filter(
    (customer) =>
      customer.name.toLowerCase().includes(searchQuery.toLowerCase()) || customer.phone.includes(searchQuery),
  )

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (!selectedCustomer || !pointsAmount) return

    // In a real app, you would update the customer's points in your backend
    const action = pointsAction === "add" ? "ditambahkan ke" : "dikurangi dari"
    alert(`${pointsAmount} poin telah ${action} akun ${mockCustomers.find((c) => c.id === selectedCustomer)?.name}`)
    router.push("/loyalty")
  }

  const getTierColor = (tier: string) => {
    switch (tier) {
      case "Bronze":
        return "bg-amber-600"
      case "Silver":
        return "bg-gray-400"
      case "Gold":
        return "bg-yellow-500"
      case "Platinum":
        return "bg-blue-600"
      default:
        return "bg-gray-500"
    }
  }

  return (
    <div className="flex flex-col min-h-screen bg-slate-50">
      <header className="p-4 flex justify-between items-center bg-white sticky top-0 z-10 shadow-sm">
        <div className="flex items-center">
          <Link href="/loyalty" className="mr-3">
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <h1 className="text-lg font-semibold">Tambah/Kurangi Poin</h1>
        </div>
        <Button
          type="submit"
          form="add-points-form"
          className="bg-blue-500 hover:bg-blue-600"
          disabled={!selectedCustomer || !pointsAmount}
        >
          <Save className="h-4 w-4 mr-2" /> Simpan
        </Button>
      </header>

      <main className="flex-1 p-4 pb-20">
        <form id="add-points-form" onSubmit={handleSubmit}>
          <Card className="p-4 mb-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>Pilih Pelanggan</Label>
                <div className="relative mb-2">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <Input
                    placeholder="Cari pelanggan..."
                    className="pl-10"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>

                <div className="max-h-60 overflow-y-auto space-y-2 border rounded-md p-2">
                  {filteredCustomers.length > 0 ? (
                    filteredCustomers.map((customer) => (
                      <div
                        key={customer.id}
                        className={`p-2 rounded-md cursor-pointer ${
                          selectedCustomer === customer.id
                            ? "bg-blue-50 border border-blue-200"
                            : "hover:bg-gray-50 border border-transparent"
                        }`}
                        onClick={() => setSelectedCustomer(customer.id)}
                      >
                        <div className="flex justify-between items-center">
                          <div>
                            <div className="flex items-center gap-2">
                              <h3 className="font-medium">{customer.name}</h3>
                              <Badge className={`${getTierColor(customer.tier)} text-white`}>{customer.tier}</Badge>
                            </div>
                            <p className="text-sm text-gray-500">{customer.phone}</p>
                          </div>
                          <div className="flex items-center gap-1">
                            <Star className="h-4 w-4 text-yellow-500" />
                            <span className="font-medium">{customer.points} poin</span>
                          </div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-4 text-gray-500">Tidak ada pelanggan yang ditemukan</div>
                  )}
                </div>
              </div>

              {selectedCustomer && (
                <>
                  <div className="space-y-2">
                    <Label>Aksi</Label>
                    <RadioGroup value={pointsAction} onValueChange={setPointsAction} className="flex gap-4">
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="add" id="add" />
                        <Label htmlFor="add" className="cursor-pointer flex items-center gap-1">
                          <Plus className="h-4 w-4 text-green-500" /> Tambah Poin
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="subtract" id="subtract" />
                        <Label htmlFor="subtract" className="cursor-pointer flex items-center gap-1">
                          <Minus className="h-4 w-4 text-red-500" /> Kurangi Poin
                        </Label>
                      </div>
                    </RadioGroup>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="pointsAmount">Jumlah Poin</Label>
                    <div className="relative">
                      <Star className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-yellow-500" />
                      <Input
                        id="pointsAmount"
                        type="number"
                        value={pointsAmount}
                        onChange={(e) => setPointsAmount(e.target.value)}
                        className="pl-10"
                        placeholder="0"
                        min="1"
                        required
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="notes">Catatan (Opsional)</Label>
                    <Textarea
                      id="notes"
                      value={notes}
                      onChange={(e) => setNotes(e.target.value)}
                      placeholder="Contoh: Poin dari transaksi #TRX/250324/014"
                      rows={3}
                    />
                  </div>
                </>
              )}
            </div>
          </Card>
        </form>
      </main>
    </div>
  )
}
