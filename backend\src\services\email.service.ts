import nodemailer from 'nodemailer';
import sgMail from '@sendgrid/mail';
import config from '../config/config';
import logger from '../config/logger';
import { EmailOptions, EmailSendResult, EmailProvider } from '../types/email.types';

const transport = nodemailer.createTransport(config.email.smtp);

// Initialize SendGrid
if (config.sendgrid.apiKey) {
  sgMail.setApiKey(config.sendgrid.apiKey);
}

/* istanbul ignore next */
if (config.env !== 'test') {
  transport
    .verify()
    .then(() => logger.info('Connected to email server'))
    .catch(() =>
      logger.warn(
        'Unable to connect to email server. Make sure you have configured the SMTP options in .env'
      )
    );
}

/**
 * Get current email provider from config
 */
const getCurrentProvider = (): EmailProvider => {
  return config.email.provider as EmailProvider;
};

/**
 * Send an email using SMTP
 * @param {string} to
 * @param {string} subject
 * @param {string} text
 * @returns {Promise<void>}
 */
const sendEmailWithSMTP = async (to: string, subject: string, text: string): Promise<void> => {
  const msg = { from: config.email.from, to, subject, text };
  await transport.sendMail(msg);
};

/**
 * Send an email using SendGrid
 * @param {string} to
 * @param {string} subject
 * @param {string} text
 * @param {string} html - optional HTML content
 * @returns {Promise<EmailSendResult>}
 */
const sendEmailWithSendGrid = async (
  to: string,
  subject: string,
  text: string,
  html?: string
): Promise<EmailSendResult> => {
  const msg = {
    to,
    from: config.sendgrid.fromEmail,
    subject,
    text,
    html
  };

  try {
    const result = await sgMail.send(msg);
    logger.info(`Email sent successfully to ${to} using SendGrid`);
    return {
      success: true,
      messageId: result[0].headers['x-message-id'] as string
    };
  } catch (error) {
    logger.error('SendGrid email error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};

/**
 * Smart email function - automatically chooses provider based on config
 * @param {string} to
 * @param {string} subject
 * @param {string} text
 * @param {string} html - optional HTML content
 * @returns {Promise<EmailSendResult>}
 */
const sendEmail = async (
  to: string,
  subject: string,
  text: string,
  html?: string
): Promise<EmailSendResult> => {
  const provider = getCurrentProvider();

  logger.info(`Sending email using provider: ${provider}`);

  try {
    if (provider === 'sendgrid') {
      return await sendEmailWithSendGrid(to, subject, text, html);
    } else {
      await sendEmailWithSMTP(to, subject, text);
      return { success: true };
    }
  } catch (error) {
    logger.error(`Email sending failed with ${provider}:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};

/**
 * Send reset password email using configured provider
 * @param {string} to
 * @param {string} token
 * @returns {Promise<EmailSendResult>}
 */
const sendResetPasswordEmail = async (to: string, token: string): Promise<EmailSendResult> => {
  const subject = 'Reset Password - Laundry App';
  const resetPasswordUrl = `http://localhost:3001/auth/reset-password?token=${token}`;
  const text = `Dear user,
To reset your password, click on this link: ${resetPasswordUrl}
If you did not request any password resets, then ignore this email.`;

  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #333;">Reset Your Password</h2>
      <p>Dear user,</p>
      <p>We received a request to reset your password for your Laundry App account.</p>
      <div style="text-align: center; margin: 30px 0;">
        <a href="${resetPasswordUrl}"
           style="background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">
          Reset Password
        </a>
      </div>
      <p>If you did not request this password reset, please ignore this email.</p>
      <p>This link will expire in 10 minutes for security reasons.</p>
      <hr style="border: none; border-top: 1px solid #eee; margin: 20px 0;">
      <p style="color: #666; font-size: 12px;">This email was sent from Laundry App</p>
    </div>
  `;

  return await sendEmail(to, subject, text, html);
};

/**
 * Send verification email using configured provider
 * @param {string} to
 * @param {string} token
 * @returns {Promise<EmailSendResult>}
 */
const sendVerificationEmail = async (to: string, token: string): Promise<EmailSendResult> => {
  const subject = 'Email Verification - Laundry App';
  const verificationEmailUrl = `http://link-to-app/verify-email?token=${token}`;
  const text = `Dear user,
To verify your email, click on this link: ${verificationEmailUrl}`;

  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #333;">Verify Your Email Address</h2>
      <p>Dear user,</p>
      <p>Thank you for registering with Laundry App! Please verify your email address to complete your registration.</p>
      <div style="text-align: center; margin: 30px 0;">
        <a href="${verificationEmailUrl}"
           style="background-color: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">
          Verify Email
        </a>
      </div>
      <p>If you did not create an account with us, please ignore this email.</p>
      <p>This verification link will expire in 10 minutes.</p>
      <hr style="border: none; border-top: 1px solid #eee; margin: 20px 0;">
      <p style="color: #666; font-size: 12px;">This email was sent from Laundry App</p>
    </div>
  `;

  return await sendEmail(to, subject, text, html);
};

/**
 * Send verification OTP email using configured provider
 * @param {string} to
 * @param {string} otp
 * @returns {Promise<EmailSendResult>}
 */
const sendVerificationOTPEmail = async (to: string, otp: string): Promise<EmailSendResult> => {
  const subject = 'Email Verification Code - Laundry App';
  const text = `Dear user,
Your email verification code is: ${otp}
This code will expire in 10 minutes.`;

  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #333;">Email Verification Code</h2>
      <p>Dear user,</p>
      <p>Thank you for registering with Laundry App! Please use the verification code below to complete your registration.</p>
      <div style="text-align: center; margin: 30px 0;">
        <div style="background-color: #f8f9fa; border: 2px dashed #28a745; padding: 20px; border-radius: 10px; display: inline-block;">
          <h1 style="color: #28a745; margin: 0; font-size: 36px; letter-spacing: 8px; font-family: 'Courier New', monospace;">
            ${otp}
          </h1>
        </div>
      </div>
      <p style="color: #666;">Enter this code in the verification form to activate your account.</p>
      <p>If you did not create an account with us, please ignore this email.</p>
      <p><strong>This verification code will expire in 10 minutes.</strong></p>
      <hr style="border: none; border-top: 1px solid #eee; margin: 20px 0;">
      <p style="color: #666; font-size: 12px;">This email was sent from Laundry App</p>
    </div>
  `;

  return await sendEmail(to, subject, text, html);
};

/**
 * Send welcome email using configured provider
 * @param {string} to
 * @param {string} userName
 * @returns {Promise<EmailSendResult>}
 */
const sendWelcomeEmail = async (to: string, userName = 'user'): Promise<EmailSendResult> => {
  const subject = 'Selamat Datang di Laundry App';
  const text = `Halo ${userName}, terima kasih telah bergabung dengan layanan laundry kami!`;

  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #333;">Selamat Datang! 👕</h2>
      <p>Halo ${userName}!</p>
      <p>Terima kasih telah bergabung dengan <strong>Laundry App</strong>.</p>
      <p>Kami siap membantu kebutuhan laundry Anda dengan layanan terbaik.</p>
      <div style="background-color: #f8f9fa; padding: 20px; margin: 20px 0; border-radius: 5px;">
        <h3>Fitur yang bisa Anda nikmati:</h3>
        <ul>
          <li>📱 Order melalui aplikasi</li>
          <li>🚚 Pickup & delivery gratis</li>
          <li>💧 Cuci dengan deterjen premium</li>
          <li>⚡ Layanan express tersedia</li>
        </ul>
      </div>
      <hr style="border: none; border-top: 1px solid #eee; margin: 20px 0;">
      <p style="color: #666; font-size: 12px;">Email ini dikirim dari Laundry App</p>
    </div>
  `;

  return await sendEmail(to, subject, text, html);
};

// Legacy functions for backward compatibility
const sendResetPasswordEmailWithSendGrid = sendResetPasswordEmail;
const sendVerificationEmailWithSendGrid = sendVerificationEmail;

export default {
  transport,
  getCurrentProvider,
  sendEmail,
  sendEmailWithSMTP,
  sendEmailWithSendGrid,
  sendResetPasswordEmail,
  sendVerificationEmail,
  sendVerificationOTPEmail,
  sendWelcomeEmail,
  // Legacy functions
  sendResetPasswordEmailWithSendGrid,
  sendVerificationEmailWithSendGrid
};
