import httpStatus from 'http-status';
import { Prisma, User } from '@prisma/client';
import prisma from '../client';
import ApiError from '../utils/ApiError';

const createServiceCategory = async (serviceCategoryBody: any, user: User) => {
  const { outletId, ...categoryData } = serviceCategoryBody;

  if (user.role !== 'ADMIN') {
    const userOutlets = await prisma.outlet.findMany({
      where: { ownerId: user.id },
      select: { id: true }
    });

    const allowedOutletIds = userOutlets.map((outlet) => outlet.id);

    if (!allowedOutletIds.includes(outletId)) {
      throw new ApiError(
        httpStatus.FORBIDDEN,
        'You can only create service categories for your own outlets'
      );
    }
  }

  const existingCategory = await prisma.serviceCategory.findFirst({
    where: {
      outletId,
      name: categoryData.name
    }
  });

  if (existingCategory) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      'Service category with this name already exists in this outlet'
    );
  }

  const serviceCategory = await prisma.serviceCategory.create({
    data: {
      ...categoryData,
      outletId,
      productionProcess: categoryData.productionProcess || []
    }
  });

  return serviceCategory;
};

const queryServiceCategories = async (filter: any, options: any, user: User) => {
  const { outletId, search, estimationHours } = filter;
  console.log('🚀 ~ queryServiceCategories ~ estimationHours:', estimationHours);
  console.log('🚀 ~ queryServiceCategories ~ search:', search);

  if (user.role !== 'ADMIN') {
    const userOutlets = await prisma.outlet.findMany({
      where: { ownerId: user.id },
      select: { id: true }
    });

    const allowedOutletIds = userOutlets.map((outlet) => outlet.id);

    if (!allowedOutletIds.includes(outletId)) {
      throw new ApiError(
        httpStatus.FORBIDDEN,
        'You can only access service categories for your own outlets'
      );
    }
  }

  const where: Prisma.ServiceCategoryWhereInput = {
    outletId: outletId
  };

  if (search) {
    where.OR = [
      { name: { contains: search, mode: 'insensitive' } },
      { description: { contains: search, mode: 'insensitive' } }
    ];
  }

  const page = options.page ?? 1;
  const limit = options.limit ?? 10;
  const sortBy = options.sortBy ?? 'name:asc';

  const [sortField, sortOrder] = sortBy.split(':');

  const orderBy: Prisma.ServiceCategoryOrderByWithRelationInput = {};
  (orderBy as any)[sortField] = sortOrder || 'asc';

  const serviceCategories = await prisma.serviceCategory.findMany({
    where,
    include: {
      outlet: {
        select: {
          id: true,
          name: true
        }
      },
      services: {
        where: estimationHours ? { estimationHours } : undefined,
        select: {
          id: true,
          name: true,
          isActive: true,
          estimationHours: true,
          price: true,
          unit: true
        },
        orderBy: {
          estimationHours: 'asc'
        }
      },
      _count: {
        select: {
          services: true
        }
      }
    },
    orderBy,
    skip: (page - 1) * limit,
    take: limit
  });

  const totalResults = await prisma.serviceCategory.count({ where });
  const totalPages = Math.ceil(totalResults / limit);

  return {
    results: serviceCategories,
    page,
    limit,
    totalPages,
    totalResults
  };
};

const getServiceCategoryById = async (id: number, user: User, outletId: number) => {
  if (user.role !== 'ADMIN') {
    const userOutlets = await prisma.outlet.findMany({
      where: { ownerId: user.id },
      select: { id: true }
    });

    const allowedOutletIds = userOutlets.map((outlet) => outlet.id);

    if (!allowedOutletIds.includes(outletId)) {
      throw new ApiError(
        httpStatus.FORBIDDEN,
        'You can only access service categories for your own outlets'
      );
    }
  }

  const serviceCategory = await prisma.serviceCategory.findFirst({
    where: {
      id,
      outletId
    },
    include: {
      outlet: {
        select: {
          id: true,
          name: true
        }
      },
      services: {
        select: {
          id: true,
          name: true,
          isActive: true
        },
        orderBy: { name: 'asc' }
      }
    }
  });

  return serviceCategory;
};

const updateServiceCategoryById = async (
  serviceCategoryId: number,
  updateBody: any,
  user: User
) => {
  const serviceCategory = await getServiceCategoryById(
    serviceCategoryId,
    user,
    updateBody.outletId || 0
  );
  if (!serviceCategory) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Service category not found');
  }

  if (user.role !== 'ADMIN') {
    const userOutlets = await prisma.outlet.findMany({
      where: { ownerId: user.id },
      select: { id: true }
    });

    const allowedOutletIds = userOutlets.map((outlet) => outlet.id);

    if (!allowedOutletIds.includes(serviceCategory.outletId)) {
      throw new ApiError(
        httpStatus.FORBIDDEN,
        'You can only update service categories for your own outlets'
      );
    }
  }

  if (updateBody.name && updateBody.name !== serviceCategory.name) {
    const existingCategory = await prisma.serviceCategory.findFirst({
      where: {
        outletId: serviceCategory.outletId,
        name: updateBody.name,
        id: { not: serviceCategoryId }
      }
    });

    if (existingCategory) {
      throw new ApiError(
        httpStatus.BAD_REQUEST,
        'Service category with this name already exists in this outlet'
      );
    }
  }

  const updatedServiceCategory = await prisma.serviceCategory.update({
    where: { id: serviceCategoryId },
    data: updateBody
  });

  return {
    data: updatedServiceCategory,
    message: 'Service category updated successfully',
    success: true
  };
};

const deleteServiceCategoryById = async (
  serviceCategoryId: number,
  user: User,
  outletId: number
) => {
  const serviceCategory = await getServiceCategoryById(serviceCategoryId, user, outletId);
  if (!serviceCategory) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Service category not found');
  }

  if (user.role !== 'ADMIN') {
    const userOutlets = await prisma.outlet.findMany({
      where: { ownerId: user.id },
      select: { id: true }
    });

    const allowedOutletIds = userOutlets.map((outlet) => outlet.id);

    if (!allowedOutletIds.includes(serviceCategory.outletId)) {
      throw new ApiError(
        httpStatus.FORBIDDEN,
        'You can only delete service categories for your own outlets'
      );
    }
  }

  if (serviceCategory.services && serviceCategory.services.length > 0) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      'Cannot delete service category that has services. Remove or reassign services first.'
    );
  }

  await prisma.serviceCategory.delete({
    where: { id: serviceCategoryId }
  });
};

const getServiceCategoriesForSelect = async (user: User, outletId: number) => {
  if (user.role !== 'ADMIN') {
    const userOutlets = await prisma.outlet.findMany({
      where: { ownerId: user.id },
      select: { id: true }
    });

    const allowedOutletIds = userOutlets.map((outlet) => outlet.id);

    if (!allowedOutletIds.includes(outletId)) {
      throw new ApiError(
        httpStatus.FORBIDDEN,
        'You can only access service categories for your own outlets'
      );
    }
  }

  return await prisma.serviceCategory.findMany({
    where: { outletId },
    select: {
      id: true,
      name: true,
      description: true,
      _count: {
        select: {
          services: true
        }
      }
    },
    orderBy: { name: 'asc' }
  });
};
const queryCount = async (user: User, outletId: number) => {
  if (user.role !== 'ADMIN') {
    const userOutlets = await prisma.outlet.findMany({
      where: { ownerId: user.id },
      select: { id: true }
    });

    const allowedOutletIds = userOutlets.map((outlet) => outlet.id);

    if (!allowedOutletIds.includes(outletId)) {
      throw new ApiError(
        httpStatus.FORBIDDEN,
        'You can only access service categories for your own outlets'
      );
    }
  }

  const serviceCounts = await prisma.service.groupBy({
    by: ['estimationHours'],
    where: {
      outletId: outletId
    },
    _count: {
      _all: true
    }
  });

  return serviceCounts.map((item) => ({
    estimateHour: item.estimationHours,
    count: item._count._all
  }));
};

export default {
  createServiceCategory,
  queryServiceCategories,
  getServiceCategoryById,
  updateServiceCategoryById,
  deleteServiceCategoryById,
  getServiceCategoriesForSelect,
  queryCount
};
