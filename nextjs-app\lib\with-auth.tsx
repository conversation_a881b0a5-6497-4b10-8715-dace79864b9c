'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from './auth-context';

interface WithAuthOptions {
  redirectTo?: string;
  requireAuth?: boolean;
}

export function withAuth<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  options: WithAuthOptions = {}
) {
  const { redirectTo = '/auth/login', requireAuth = true } = options;

  return function AuthenticatedComponent(props: P) {
    const { isAuthenticated, isLoading } = useAuth();
    const router = useRouter();

    useEffect(() => {
      if (!isLoading) {
        if (requireAuth && !isAuthenticated) {
          // Redirect to login if authentication is required but user is not authenticated
          router.push(redirectTo);
        } else if (!requireAuth && isAuthenticated) {
          // Redirect authenticated users away from auth pages
          router.push('/dashboard');
        }
      }
    }, [isAuthenticated, isLoading, router]);

    // Show loading while checking authentication
    if (isLoading) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
        </div>
      );
    }

    // Don't render the component if authentication check fails
    if (requireAuth && !isAuthenticated) {
      return null;
    }

    if (!requireAuth && isAuthenticated) {
      return null;
    }

    return <WrappedComponent {...props} />;
  };
}

// Convenience functions for common use cases
export const withRequiredAuth = <P extends object>(
  WrappedComponent: React.ComponentType<P>
) => withAuth(WrappedComponent, { requireAuth: true });

export const withGuestOnly = <P extends object>(
  WrappedComponent: React.ComponentType<P>
) => withAuth(WrappedComponent, { requireAuth: false });
