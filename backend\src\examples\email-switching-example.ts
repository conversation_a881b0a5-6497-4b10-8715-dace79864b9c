/**
 * Contoh switching antara Email Provider (SMTP vs SendGrid)
 * File ini menunjukkan bagaimana email service otomatis memilih provider
 */

import emailService from '../services/email.service';
import logger from '../config/logger';
import config from '../config/config';

/**
 * Demonstrasi auto-switching email provider
 */
export const demonstrateAutoSwitching = async () => {
  logger.info('=== EMAIL PROVIDER SWITCHING DEMO ===');

  // Check current provider
  const currentProvider = emailService.getCurrentProvider();
  logger.info(`Current email provider: ${currentProvider}`);
  logger.info(`Config email provider: ${config.email.provider}`);

  // Test basic email sending
  try {
    const result = await emailService.sendEmail(
      '<EMAIL>',
      'Test Email - Auto Provider Selection',
      'Email ini dikirim menggunakan provider yang dipilih secara otomatis.',
      `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2>Email Provider Test</h2>
        <p>Email ini dikirim menggunakan: <strong>${currentProvider}</strong></p>
        <p>Timestamp: ${new Date().toISOString()}</p>
        <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px;">
          <p><strong>Provider Info:</strong></p>
          <ul>
            <li>Current Provider: ${currentProvider}</li>
            <li>Auto-selected: Yes</li>
            <li>Fallback Available: Yes</li>
          </ul>
        </div>
      </div>
      `
    );

    if (result.success) {
      logger.info(`✅ Email sent successfully using ${currentProvider}`);
      if (result.messageId) {
        logger.info(`Message ID: ${result.messageId}`);
      }
    } else {
      logger.error(`❌ Email failed: ${result.error}`);
    }
  } catch (error) {
    logger.error('Email sending error:', error);
  }
};

/**
 * Test semua fungsi email dengan provider yang dipilih
 */
export const testAllEmailFunctions = async () => {
  logger.info('=== TESTING ALL EMAIL FUNCTIONS ===');

  const currentProvider = emailService.getCurrentProvider();
  logger.info(`Testing with provider: ${currentProvider}`);

  // Test welcome email
  logger.info('1. Testing welcome email...');
  try {
    const welcomeResult = await emailService.sendWelcomeEmail('<EMAIL>', 'John Doe');
    logger.info(`Welcome email: ${welcomeResult.success ? '✅ Success' : '❌ Failed'}`);
  } catch (error) {
    logger.error('Welcome email error:', error);
  }

  // Test reset password email
  logger.info('2. Testing reset password email...');
  try {
    const resetResult = await emailService.sendResetPasswordEmail(
      '<EMAIL>',
      'sample-reset-token-123'
    );
    logger.info(`Reset password email: ${resetResult.success ? '✅ Success' : '❌ Failed'}`);
  } catch (error) {
    logger.error('Reset password email error:', error);
  }

  // Test verification email
  logger.info('3. Testing verification email...');
  try {
    const verifyResult = await emailService.sendVerificationEmail(
      '<EMAIL>',
      'sample-verification-token-456'
    );
    logger.info(`Verification email: ${verifyResult.success ? '✅ Success' : '❌ Failed'}`);
  } catch (error) {
    logger.error('Verification email error:', error);
  }
};

/**
 * Demonstrasi penggunaan provider spesifik (untuk testing)
 */
export const testSpecificProviders = async () => {
  logger.info('=== TESTING SPECIFIC PROVIDERS ===');

  // Test SMTP directly
  logger.info('Testing SMTP provider directly...');
  try {
    await emailService.sendEmailWithSMTP(
      '<EMAIL>',
      'Direct SMTP Test',
      'Email ini dikirim langsung menggunakan SMTP/nodemailer.'
    );
    logger.info('✅ SMTP email sent successfully');
  } catch (error) {
    logger.error('❌ SMTP email failed:', error);
  }

  // Test SendGrid directly
  logger.info('Testing SendGrid provider directly...');
  try {
    const sgResult = await emailService.sendEmailWithSendGrid(
      '<EMAIL>',
      'Direct SendGrid Test',
      'Email ini dikirim langsung menggunakan SendGrid.',
      '<h2>SendGrid Direct Test</h2><p>Email ini dikirim langsung menggunakan SendGrid API.</p>'
    );

    if (sgResult.success) {
      logger.info('✅ SendGrid email sent successfully');
      if (sgResult.messageId) {
        logger.info(`SendGrid Message ID: ${sgResult.messageId}`);
      }
    } else {
      logger.error(`❌ SendGrid email failed: ${sgResult.error}`);
    }
  } catch (error) {
    logger.error('❌ SendGrid email error:', error);
  }
};

/**
 * Monitoring provider performance
 */
export const monitorProviderPerformance = async () => {
  logger.info('=== PROVIDER PERFORMANCE MONITORING ===');

  const testEmails = ['<EMAIL>', '<EMAIL>', '<EMAIL>'];

  const startTime = Date.now();
  let successCount = 0;
  let failCount = 0;

  for (const email of testEmails) {
    try {
      const result = await emailService.sendEmail(
        email,
        `Performance Test - ${new Date().toISOString()}`,
        'Email untuk testing performance provider.',
        `<p>Test email untuk <strong>${email}</strong></p>`
      );

      if (result.success) {
        successCount++;
        logger.info(`✅ Email to ${email}: Success`);
      } else {
        failCount++;
        logger.error(`❌ Email to ${email}: Failed - ${result.error}`);
      }
    } catch (error) {
      failCount++;
      logger.error(`❌ Email to ${email}: Error - ${error}`);
    }
  }

  const endTime = Date.now();
  const duration = endTime - startTime;

  logger.info('=== PERFORMANCE RESULTS ===');
  logger.info(`Provider: ${emailService.getCurrentProvider()}`);
  logger.info(`Total emails: ${testEmails.length}`);
  logger.info(`Successful: ${successCount}`);
  logger.info(`Failed: ${failCount}`);
  logger.info(`Success rate: ${((successCount / testEmails.length) * 100).toFixed(2)}%`);
  logger.info(`Total time: ${duration}ms`);
  logger.info(`Average time per email: ${(duration / testEmails.length).toFixed(2)}ms`);
};

/**
 * Complete email system test
 */
export const completeEmailTest = async () => {
  logger.info('🚀 Starting complete email system test...');

  await demonstrateAutoSwitching();
  await testAllEmailFunctions();
  await testSpecificProviders();
  await monitorProviderPerformance();

  logger.info('✨ Complete email system test finished!');
};

export default {
  demonstrateAutoSwitching,
  testAllEmailFunctions,
  testSpecificProviders,
  monitorProviderPerformance,
  completeEmailTest
};
