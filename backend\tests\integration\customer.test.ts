import request from 'supertest';
import { faker } from '@faker-js/faker';
import httpStatus from 'http-status';
import moment from 'moment';
import app from '../../src/app';
import config from '../../src/config/config';
import { tokenService } from '../../src/services';
import { setupTestDBOptimized } from '../utils/setupTestDb';
import { describe, beforeEach, test, expect, jest, afterAll } from '@jest/globals';
import {
  userOne,
  admin,
  insertUsers,
  createOutletForOwner,
  createEmployeeWithOutlet
} from '../fixtures/user.fixture';
import {
  customerOne,
  customerTwo,
  customerThree,
  customerInactive,
  customerMinimal,
  financialDataOne,
  financialDataTwo,
  noteOne,
  noteTwo,
  insertCustomers,
  insertCustomerWithFinancial,
  insertCustomerNotes,
  generateCustomerData
} from '../fixtures/customer.fixture';
import { Role, TokenType, CustomerStatus, CustomerType } from '@prisma/client';
import prisma from '../../src/client';

// Reduce timeout for faster tests
jest.setTimeout(30000);

setupTestDBOptimized();

describe('Customer routes', () => {
  let ownerAccessToken: string;
  let adminAccessToken: string;
  let employeeAccessToken: string;
  let outletId: number;
  let employeeOutletId: number;
  let ownerUser: any;
  let adminUser: any;
  let employeeUser: any;

  beforeEach(async () => {
    // Insert users with unique emails to avoid conflicts
    const uniqueUserOne = {
      ...userOne,
      email: `owner.${Math.floor(Math.random() * 100000)}@test.com`,
      phone: `081${Math.floor(Math.random() * 100000000)
        .toString()
        .padStart(8, '0')}`
    };

    const uniqueAdmin = {
      ...admin,
      email: `admin.${Math.floor(Math.random() * 100000)}@test.com`,
      phone: `082${Math.floor(Math.random() * 100000000)
        .toString()
        .padStart(8, '0')}`
    };

    await insertUsers([uniqueUserOne, uniqueAdmin]);

    // Get inserted users to get their actual IDs
    const dbOwner = await prisma.user.findUnique({ where: { email: uniqueUserOne.email } });
    const dbAdmin = await prisma.user.findUnique({ where: { email: uniqueAdmin.email } });

    // Create outlets
    const outlet = await createOutletForOwner(dbOwner!.id);
    outletId = outlet.id;

    const empOutlet = await createOutletForOwner(dbAdmin!.id);
    employeeOutletId = empOutlet.id;
    const employee = await createEmployeeWithOutlet(employeeOutletId);

    // Update owner user with outletId - PENTING: Owner harus punya outletId
    await prisma.user.update({
      where: { id: dbOwner!.id },
      data: { outletId: outletId }
    });

    // Admin tidak perlu outletId karena role ADMIN bisa akses semua
    // Tapi untuk konsistensi, kita set juga
    await prisma.user.update({
      where: { id: dbAdmin!.id },
      data: { outletId: empOutlet.id }
    });

    // Get updated user data
    ownerUser = await prisma.user.findUnique({ where: { id: dbOwner!.id } });
    adminUser = await prisma.user.findUnique({ where: { id: dbAdmin!.id } });
    employeeUser = await prisma.user.findUnique({ where: { id: employee.id } });

    // Generate tokens
    ownerAccessToken = tokenService.generateToken(
      ownerUser!.id,
      moment().add(config.jwt.accessExpirationMinutes, 'minutes'),
      TokenType.ACCESS
    );

    adminAccessToken = tokenService.generateToken(
      adminUser!.id,
      moment().add(config.jwt.accessExpirationMinutes, 'minutes'),
      TokenType.ACCESS
    );

    employeeAccessToken = tokenService.generateToken(
      employeeUser!.id,
      moment().add(config.jwt.accessExpirationMinutes, 'minutes'),
      TokenType.ACCESS
    );
  });

  describe('POST /v1/customers', () => {
    let newCustomer: any;

    beforeEach(() => {
      newCustomer = {
        name: 'John Doe',
        phone: `081${Date.now().toString().slice(-8)}`,
        email: `john.doe.${Date.now()}@example.com`,
        address: 'Jl. Test No. 123',
        customerType: CustomerType.INDIVIDUAL,
        source: 'Referral',
        labels: ['VIP']
      };
    });

    test('should return 201 and successfully create customer if request data is ok', async () => {
      const res = await request(app)
        .post('/v1/customers')
        .set('Authorization', `Bearer ${ownerAccessToken}`)
        .send(newCustomer)
        .expect(httpStatus.CREATED);

      expect(res.body.customer).not.toHaveProperty('password');
      expect(res.body.customer).toMatchObject({
        id: expect.anything(),
        name: newCustomer.name,
        phone: newCustomer.phone,
        email: newCustomer.email,
        address: newCustomer.address,
        status: CustomerStatus.ACTIVE,
        customerType: newCustomer.customerType,
        source: newCustomer.source,
        labels: newCustomer.labels,
        totalOrders: 0,
        outletId: outletId
      });

      expect(res.body.customer.financialData).toMatchObject({
        totalSpent: '0',
        loyaltyPoints: 0,
        deposit: '0',
        debt: '0',
        cashback: '0'
      });

      const dbCustomer = await prisma.customer.findUnique({
        where: { id: res.body.customer.id },
        include: { financialData: true }
      });
      expect(dbCustomer).toBeDefined();
      expect(dbCustomer?.financialData).toBeDefined();
    });

    test('should return 201 and create customer with minimal data (no phone, no address)', async () => {
      const minimalCustomer = {
        name: 'Jane Doe',
        email: `jane.doe.${Date.now()}@example.com`
      };

      const res = await request(app)
        .post('/v1/customers')
        .set('Authorization', `Bearer ${ownerAccessToken}`)
        .send(minimalCustomer)
        .expect(httpStatus.CREATED);

      expect(res.body.customer).toMatchObject({
        name: minimalCustomer.name,
        email: minimalCustomer.email,
        phone: null,
        address: null,
        status: CustomerStatus.ACTIVE,
        customerType: CustomerType.INDIVIDUAL
      });
    });

    test('should return 400 error if name is missing', async () => {
      delete newCustomer.name;

      await request(app)
        .post('/v1/customers')
        .set('Authorization', `Bearer ${ownerAccessToken}`)
        .send(newCustomer)
        .expect(httpStatus.BAD_REQUEST);
    });

    test('should return 400 error if both phone and email are missing', async () => {
      delete newCustomer.phone;
      delete newCustomer.email;

      const res = await request(app)
        .post('/v1/customers')
        .set('Authorization', `Bearer ${ownerAccessToken}`)
        .send(newCustomer)
        .expect(httpStatus.BAD_REQUEST);

      // Check for custom validation message
      expect(res.body.message).toContain('custom validation');
    });

    test('should return 400 error if phone is already used in same outlet', async () => {
      const uniqueCustomerOne = {
        ...customerOne,
        phone: `081${Date.now().toString().slice(-8)}`,
        email: `customer1.${Date.now()}@example.com`
      };
      await insertCustomers([uniqueCustomerOne], outletId);
      newCustomer.phone = uniqueCustomerOne.phone;

      await request(app)
        .post('/v1/customers')
        .set('Authorization', `Bearer ${ownerAccessToken}`)
        .send(newCustomer)
        .expect(httpStatus.BAD_REQUEST);
    });

    test('should return 400 error if email is already used in same outlet', async () => {
      const uniqueCustomerOne = {
        ...customerOne,
        phone: `081${Date.now().toString().slice(-8)}`,
        email: `customer1.${Date.now()}@example.com`
      };
      await insertCustomers([uniqueCustomerOne], outletId);
      newCustomer.email = uniqueCustomerOne.email;

      await request(app)
        .post('/v1/customers')
        .set('Authorization', `Bearer ${ownerAccessToken}`)
        .send(newCustomer)
        .expect(httpStatus.BAD_REQUEST);
    });

    test('should allow same phone/email in different outlets', async () => {
      const uniqueCustomerOne = {
        ...customerOne,
        phone: `081${Date.now().toString().slice(-8)}`,
        email: `customer1.${Date.now()}@example.com`
      };
      await insertCustomers([uniqueCustomerOne], employeeOutletId);
      newCustomer.phone = uniqueCustomerOne.phone;
      newCustomer.email = uniqueCustomerOne.email;

      await request(app)
        .post('/v1/customers')
        .set('Authorization', `Bearer ${ownerAccessToken}`)
        .send(newCustomer)
        .expect(httpStatus.CREATED);
    });

    test('should return 401 error if access token is missing', async () => {
      await request(app).post('/v1/customers').send(newCustomer).expect(httpStatus.UNAUTHORIZED);
    });

    test('should allow employee to create customer in their outlet', async () => {
      await request(app)
        .post('/v1/customers')
        .set('Authorization', `Bearer ${employeeAccessToken}`)
        .send(newCustomer)
        .expect(httpStatus.CREATED);
    });
  });

  describe('GET /v1/customers', () => {
    beforeEach(async () => {
      const uniqueCustomers = [
        {
          ...customerOne,
          phone: `081${Date.now().toString().slice(-8)}`,
          email: `c1.${Date.now()}@example.com`
        },
        {
          ...customerTwo,
          phone: `082${Date.now().toString().slice(-8)}`,
          email: `c2.${Date.now()}@example.com`
        },
        {
          ...customerThree,
          phone: `083${Date.now().toString().slice(-8)}`,
          email: `c3.${Date.now()}@example.com`
        }
      ];
      await insertCustomers(uniqueCustomers, outletId);

      const uniqueInactive = {
        ...customerInactive,
        phone: `084${Date.now().toString().slice(-8)}`,
        email: `inactive.${Date.now()}@example.com`
      };
      await insertCustomers([uniqueInactive], employeeOutletId);
    });

    test('should return 200 and apply the default query options', async () => {
      const res = await request(app)
        .get('/v1/customers')
        .set('Authorization', `Bearer ${ownerAccessToken}`)
        .expect(httpStatus.OK);

      expect(res.body).toEqual({
        results: expect.any(Array),
        page: 1,
        limit: 10,
        totalPages: 1,
        totalResults: 3
      });

      expect(res.body.results).toHaveLength(3);
      expect(res.body.results[0]).toMatchObject({
        id: expect.anything(),
        name: expect.any(String),
        status: expect.any(String),
        customerType: expect.any(String),
        outletId: outletId
      });
    });

    test('should return 200 and apply search filter', async () => {
      const res = await request(app)
        .get('/v1/customers')
        .set('Authorization', `Bearer ${ownerAccessToken}`)
        .query({ search: 'Budi' })
        .expect(httpStatus.OK);

      expect(res.body.totalResults).toBe(1);
      expect(res.body.results[0].name).toContain('Budi');
    });

    test('should return 200 and apply status filter', async () => {
      const res = await request(app)
        .get('/v1/customers')
        .set('Authorization', `Bearer ${ownerAccessToken}`)
        .query({ status: CustomerStatus.ACTIVE })
        .expect(httpStatus.OK);

      expect(res.body.totalResults).toBe(3);
      res.body.results.forEach((customer: any) => {
        expect(customer.status).toBe(CustomerStatus.ACTIVE);
      });
    });

    test('should return 200 and apply customerType filter', async () => {
      const res = await request(app)
        .get('/v1/customers')
        .set('Authorization', `Bearer ${ownerAccessToken}`)
        .query({ customerType: CustomerType.CORPORATE })
        .expect(httpStatus.OK);

      expect(res.body.totalResults).toBe(1);
      expect(res.body.results[0].customerType).toBe(CustomerType.CORPORATE);
    });

    test('should return 200 and apply sorting', async () => {
      const res = await request(app)
        .get('/v1/customers')
        .set('Authorization', `Bearer ${ownerAccessToken}`)
        .query({ sortBy: 'name:desc' })
        .expect(httpStatus.OK);

      expect(res.body.results[0].name).toBe('Siti Rahayu');
    });

    test('should return 200 and apply pagination', async () => {
      const res = await request(app)
        .get('/v1/customers')
        .set('Authorization', `Bearer ${ownerAccessToken}`)
        .query({ page: 1, limit: 2 })
        .expect(httpStatus.OK);

      expect(res.body.results).toHaveLength(2);
      expect(res.body.page).toBe(1);
      expect(res.body.limit).toBe(2);
      expect(res.body.totalPages).toBe(2);
    });

    test('should return 401 error if access token is missing', async () => {
      await request(app).get('/v1/customers').expect(httpStatus.UNAUTHORIZED);
    });

    test('should return only customers from user outlet', async () => {
      const res = await request(app)
        .get('/v1/customers')
        .set('Authorization', `Bearer ${ownerAccessToken}`)
        .expect(httpStatus.OK);

      expect(res.body.totalResults).toBe(3);
      res.body.results.forEach((customer: any) => {
        expect(customer.outletId).toBe(outletId);
      });
    });

    test('should allow admin to access all customers', async () => {
      const res = await request(app)
        .get('/v1/customers')
        .set('Authorization', `Bearer ${adminAccessToken}`)
        .expect(httpStatus.OK);

      expect(res.body.totalResults).toBeGreaterThanOrEqual(2);
    });

    test('should allow admin to access customer from any outlet', async () => {
      const customers = await prisma.customer.findMany();
      const customer = customers[0];

      const res = await request(app)
        .get(`/v1/customers/${customer.id}`)
        .set('Authorization', `Bearer ${adminAccessToken}`)
        .expect(httpStatus.OK);

      expect(res.body.id).toBe(customer.id);
    });
  });

  describe('GET /v1/customers/:customerId', () => {
    let customer: any;

    beforeEach(async () => {
      const uniqueCustomerOne = {
        ...customerOne,
        phone: `081${Date.now().toString().slice(-8)}`,
        email: `customer.${Date.now()}@example.com`
      };
      const customers = await insertCustomers([uniqueCustomerOne], outletId);
      customer = customers[0];
      await insertCustomerNotes(customer.id, [noteOne, noteTwo]);
    });

    test('should return 200 and the customer object if data is ok', async () => {
      const res = await request(app)
        .get(`/v1/customers/${customer.id}`)
        .set('Authorization', `Bearer ${ownerAccessToken}`)
        .expect(httpStatus.OK);

      expect(res.body).toMatchObject({
        id: customer.id,
        name: customer.name,
        phone: customer.phone,
        email: customer.email,
        address: customer.address,
        status: customer.status,
        customerType: customer.customerType,
        outletId: outletId
      });

      expect(res.body.financialData).toBeDefined();
      expect(res.body.customerNotes).toHaveLength(2);
    });

    test('should return 401 error if access token is missing', async () => {
      await request(app).get(`/v1/customers/${customer.id}`).expect(httpStatus.UNAUTHORIZED);
    });

    test('should return 404 error if user tries to access customer from different outlet', async () => {
      // Create customer in different outlet
      const otherCustomer = await insertCustomers(
        [
          {
            ...customerOne,
            phone: `085${Date.now().toString().slice(-8)}`,
            email: `other.${Date.now()}@example.com`
          }
        ],
        employeeOutletId
      );

      await request(app)
        .get(`/v1/customers/${otherCustomer[0].id}`)
        .set('Authorization', `Bearer ${ownerAccessToken}`)
        .expect(httpStatus.NOT_FOUND);
    });
  });

  describe('PATCH /v1/customers/:customerId', () => {
    let customer: any;

    beforeEach(async () => {
      const uniqueCustomerOne = {
        ...customerOne,
        phone: `081${Date.now().toString().slice(-8)}`,
        email: `customer.${Date.now()}@example.com`
      };
      const customers = await insertCustomers([uniqueCustomerOne], outletId);
      customer = customers[0];
    });

    test('should return 200 and successfully update customer if data is ok', async () => {
      const updateBody = {
        name: 'Updated Name',
        phone: `089${Date.now().toString().slice(-8)}`,
        email: `updated.${Date.now()}@example.com`,
        address: 'Updated Address',
        status: CustomerStatus.INACTIVE,
        labels: ['Updated', 'VIP']
      };

      const res = await request(app)
        .patch(`/v1/customers/${customer.id}`)
        .set('Authorization', `Bearer ${ownerAccessToken}`)
        .send(updateBody)
        .expect(httpStatus.OK);

      expect(res.body).toMatchObject({
        id: customer.id,
        name: updateBody.name,
        phone: updateBody.phone,
        email: updateBody.email,
        address: updateBody.address,
        status: updateBody.status,
        labels: updateBody.labels
      });

      const dbCustomer = await prisma.customer.findUnique({ where: { id: customer.id } });
      expect(dbCustomer).toMatchObject(updateBody);
    });

    test('should return 400 error if phone is already used by another customer in same outlet', async () => {
      const uniqueCustomerTwo = {
        ...customerTwo,
        phone: `082${Date.now().toString().slice(-8)}`,
        email: `customer2.${Date.now()}@example.com`
      };
      await insertCustomers([uniqueCustomerTwo], outletId);

      const updateBody = { phone: uniqueCustomerTwo.phone };

      await request(app)
        .patch(`/v1/customers/${customer.id}`)
        .set('Authorization', `Bearer ${ownerAccessToken}`)
        .send(updateBody)
        .expect(httpStatus.BAD_REQUEST);
    });

    test('should return 400 error if email is already used by another customer in same outlet', async () => {
      const uniqueCustomerTwo = {
        ...customerTwo,
        phone: `082${Date.now().toString().slice(-8)}`,
        email: `customer2.${Date.now()}@example.com`
      };
      await insertCustomers([uniqueCustomerTwo], outletId);

      const updateBody = { email: uniqueCustomerTwo.email };

      await request(app)
        .patch(`/v1/customers/${customer.id}`)
        .set('Authorization', `Bearer ${ownerAccessToken}`)
        .send(updateBody)
        .expect(httpStatus.BAD_REQUEST);
    });

    test('should return 401 error if access token is missing', async () => {
      const updateBody = { name: 'Updated Name' };

      await request(app)
        .patch(`/v1/customers/${customer.id}`)
        .send(updateBody)
        .expect(httpStatus.UNAUTHORIZED);
    });

    test('should return 404 error if user tries to access customer from different outlet', async () => {
      // Create customer in different outlet
      const otherCustomer = await insertCustomers(
        [
          {
            ...customerOne,
            phone: `085${Date.now().toString().slice(-8)}`,
            email: `other.${Date.now()}@example.com`
          }
        ],
        employeeOutletId
      );

      const updateBody = { name: 'Updated Name' };

      await request(app)
        .patch(`/v1/customers/${otherCustomer[0].id}`)
        .set('Authorization', `Bearer ${ownerAccessToken}`)
        .send(updateBody)
        .expect(httpStatus.NOT_FOUND);
    });
  });

  describe('DELETE /v1/customers/:customerId', () => {
    let customer: any;

    beforeEach(async () => {
      const uniqueCustomerOne = {
        ...customerOne,
        phone: `081${Date.now().toString().slice(-8)}`,
        email: `customer.${Date.now()}@example.com`
      };
      const customers = await insertCustomers([uniqueCustomerOne], outletId);
      customer = customers[0];
    });

    test('should return 204 and delete customer', async () => {
      await request(app)
        .delete(`/v1/customers/${customer.id}`)
        .set('Authorization', `Bearer ${ownerAccessToken}`)
        .expect(httpStatus.NO_CONTENT);

      const dbCustomer = await prisma.customer.findUnique({ where: { id: customer.id } });
      expect(dbCustomer).toBeNull();
    });

    test('should return 401 error if access token is missing', async () => {
      await request(app).delete(`/v1/customers/${customer.id}`).expect(httpStatus.UNAUTHORIZED);
    });

    test('should return 404 error if user tries to access customer from different outlet', async () => {
      // Create customer in different outlet
      const otherCustomer = await insertCustomers(
        [
          {
            ...customerOne,
            phone: `085${Date.now().toString().slice(-8)}`,
            email: `other.${Date.now()}@example.com`
          }
        ],
        employeeOutletId
      );

      await request(app)
        .delete(`/v1/customers/${otherCustomer[0].id}`)
        .set('Authorization', `Bearer ${ownerAccessToken}`)
        .expect(httpStatus.NOT_FOUND);
    });
  });

  describe('Customer Financial Data', () => {
    let customer: any;

    beforeEach(async () => {
      const uniqueCustomerOne = {
        ...customerOne,
        phone: `081${Date.now().toString().slice(-8)}`,
        email: `customer.${Date.now()}@example.com`
      };
      customer = await insertCustomerWithFinancial(uniqueCustomerOne, financialDataOne, outletId);
    });

    test('should include financial data in customer response', async () => {
      const res = await request(app)
        .get(`/v1/customers/${customer.id}`)
        .set('Authorization', `Bearer ${ownerAccessToken}`)
        .expect(httpStatus.OK);

      expect(res.body.financialData).toMatchObject({
        totalSpent: financialDataOne.totalSpent.toString(),
        loyaltyPoints: financialDataOne.loyaltyPoints,
        deposit: financialDataOne.deposit.toString(),
        debt: financialDataOne.debt.toString(),
        cashback: financialDataOne.cashback.toString(),
        preferredPaymentMethod: financialDataOne.preferredPaymentMethod,
        creditLimit: financialDataOne.creditLimit.toString()
      });
    });

    test('should update financial data when updating customer', async () => {
      const updateBody = {
        financialData: {
          deposit: 200000,
          loyaltyPoints: 1000
        }
      };

      const res = await request(app)
        .patch(`/v1/customers/${customer.id}`)
        .set('Authorization', `Bearer ${ownerAccessToken}`)
        .send(updateBody)
        .expect(httpStatus.OK);

      expect(res.body.financialData.deposit).toBe('200000');
      expect(res.body.financialData.loyaltyPoints).toBe(1000);
    });
  });

  describe('Customer Notes', () => {
    let customer: any;

    beforeEach(async () => {
      const uniqueCustomerOne = {
        ...customerOne,
        phone: `081${Date.now().toString().slice(-8)}`,
        email: `customer.${Date.now()}@example.com`
      };
      const customers = await insertCustomers([uniqueCustomerOne], outletId);
      customer = customers[0];
    });

    test('should add customer note', async () => {
      const noteData = {
        text: 'Pelanggan baru yang potensial',
        author: 'Manager'
      };

      const res = await request(app)
        .post(`/v1/customers/${customer.id}/notes`)
        .set('Authorization', `Bearer ${ownerAccessToken}`)
        .send(noteData)
        .expect(httpStatus.CREATED);

      expect(res.body).toMatchObject({
        id: expect.anything(),
        text: noteData.text,
        author: noteData.author,
        customerId: customer.id
      });
    });

    test('should get customer notes', async () => {
      await insertCustomerNotes(customer.id, [noteOne, noteTwo]);

      const res = await request(app)
        .get(`/v1/customers/${customer.id}/notes`)
        .set('Authorization', `Bearer ${ownerAccessToken}`)
        .expect(httpStatus.OK);

      expect(res.body).toHaveLength(2);
      expect(res.body[0]).toMatchObject({
        text: expect.any(String),
        author: expect.any(String),
        customerId: customer.id
      });
    });
  });

  describe('Admin Access', () => {
    beforeEach(async () => {
      const uniqueCustomerOne = {
        ...customerOne,
        phone: `081${Date.now().toString().slice(-8)}`,
        email: `customer1.${Date.now()}@example.com`
      };
      const uniqueCustomerTwo = {
        ...customerTwo,
        phone: `082${Date.now().toString().slice(-8)}`,
        email: `customer2.${Date.now()}@example.com`
      };
      await insertCustomers([uniqueCustomerOne], outletId);
      await insertCustomers([uniqueCustomerTwo], employeeOutletId);
    });

    test('should allow admin to access all customers', async () => {
      const res = await request(app)
        .get('/v1/customers')
        .set('Authorization', `Bearer ${adminAccessToken}`)
        .expect(httpStatus.OK);

      expect(res.body.totalResults).toBe(2);
    });

    test('should allow admin to access customer from any outlet', async () => {
      const customers = await prisma.customer.findMany();
      const customer = customers[0];

      const res = await request(app)
        .get(`/v1/customers/${customer.id}`)
        .set('Authorization', `Bearer ${adminAccessToken}`)
        .expect(httpStatus.OK);

      expect(res.body.id).toBe(customer.id);
    });
  });
});
