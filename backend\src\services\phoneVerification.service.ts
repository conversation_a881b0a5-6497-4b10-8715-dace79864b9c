import httpStatus from 'http-status';
import prisma from '../client';
import ApiError from '../utils/ApiError';
import config from '../config/config';
import logger from '../config/logger';

/**
 * Generate random 6-digit verification code
 * @returns {string}
 */
const generateVerificationCode = (): string => {
  return Math.floor(100000 + Math.random() * 900000).toString();
};

/**
 * Send phone verification code
 * @param {string} phone
 * @returns {Promise<string>}
 */
const sendPhoneVerificationCode = async (phone: string): Promise<string> => {
  logger.debug(
    `[PHONE_VERIFICATION] Starting phone verification process - Phone: ${phone.substring(0, 4)}***`
  );

  try {
    // Generate verification code
    const code = generateVerificationCode();
    const expiresAt = new Date(
      Date.now() + config.verification.phoneVerificationExpirationMinutes * 60 * 1000
    );

    logger.debug(
      `[PHONE_VERIFICATION] Generated verification code - Phone: ${phone.substring(
        0,
        4
      )}***, Code: ${code.substring(0, 2)}***, ExpiresAt: ${expiresAt.toISOString()}`
    );

    // Invalidate previous codes for this phone
    const invalidatedResult = await prisma.phoneVerification.updateMany({
      where: {
        phone,
        isUsed: false,
        expiresAt: {
          gt: new Date()
        }
      },
      data: {
        isUsed: true
      }
    });

    if (invalidatedResult.count > 0) {
      logger.debug(
        `[PHONE_VERIFICATION] Invalidated ${
          invalidatedResult.count
        } previous codes - Phone: ${phone.substring(0, 4)}***`
      );
    }

    // Save new verification code
    await prisma.phoneVerification.create({
      data: {
        phone,
        code,
        expiresAt,
        isUsed: false
      }
    });

    logger.debug(
      `[PHONE_VERIFICATION] Verification code saved to database - Phone: ${phone.substring(
        0,
        4
      )}***`
    );

    // TODO: Integrate with SMS service (Twilio, etc.)
    // For now, just log the code for development
    if (config.env === 'development' || config.env === 'test') {
      logger.info(
        `[PHONE_VERIFICATION] Development mode - Phone verification code for ${phone.substring(
          0,
          4
        )}***: ${code}`
      );
    }

    // In production, send SMS here
    // await smsService.sendSMS(phone, `Your verification code is: ${code}`);

    logger.info(
      `[PHONE_VERIFICATION] Phone verification code sent successfully - Phone: ${phone.substring(
        0,
        4
      )}***`
    );
    return code;
  } catch (error: any) {
    logger.error(
      `[PHONE_VERIFICATION] Failed to send phone verification code - Phone: ${phone.substring(
        0,
        4
      )}***, Error: ${error.message}`
    );
    throw error;
  }
};

/**
 * Verify phone with code
 * @param {string} phone
 * @param {string} code
 * @param {number} userId
 * @returns {Promise<void>}
 */
const verifyPhoneCode = async (phone: string, code: string, userId: number): Promise<void> => {
  logger.debug(
    `[PHONE_VERIFICATION] Starting phone code verification - Phone: ${phone.substring(
      0,
      4
    )}***, Code: ${code.substring(0, 2)}***, UserID: ${userId}`
  );

  try {
    // Find valid verification code
    const verificationRecord = await prisma.phoneVerification.findFirst({
      where: {
        phone,
        code,
        isUsed: false,
        expiresAt: {
          gt: new Date()
        }
      }
    });

    if (!verificationRecord) {
      logger.warn(
        `[PHONE_VERIFICATION] Phone verification failed - Invalid or expired code - Phone: ${phone.substring(
          0,
          4
        )}***, Code: ${code.substring(0, 2)}***, UserID: ${userId}`
      );
      throw new ApiError(httpStatus.BAD_REQUEST, 'Invalid or expired verification code');
    }

    logger.debug(
      `[PHONE_VERIFICATION] Valid verification code found - Phone: ${phone.substring(
        0,
        4
      )}***, Code: ${code.substring(0, 2)}***, UserID: ${userId}, RecordID: ${
        verificationRecord.id
      }`
    );

    // Mark code as used
    await prisma.phoneVerification.update({
      where: {
        id: verificationRecord.id
      },
      data: {
        isUsed: true
      }
    });

    logger.debug(
      `[PHONE_VERIFICATION] Verification code marked as used - RecordID: ${verificationRecord.id}`
    );

    // Update user phone verification status
    await prisma.user.update({
      where: {
        id: userId
      },
      data: {
        isPhoneVerified: true
      }
    });

    logger.info(
      `[PHONE_VERIFICATION] Phone verification successful - Phone: ${phone.substring(
        0,
        4
      )}***, UserID: ${userId}`
    );
  } catch (error: any) {
    if (error instanceof ApiError) {
      throw error;
    }
    logger.error(
      `[PHONE_VERIFICATION] Phone verification error - Phone: ${phone.substring(
        0,
        4
      )}***, Code: ${code.substring(0, 2)}***, UserID: ${userId}, Error: ${error.message}`
    );
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Phone verification failed');
  }
};

/**
 * Check if phone is already verified by another user
 * @param {string} phone
 * @param {number} excludeUserId
 * @returns {Promise<boolean>}
 */
const isPhoneAlreadyVerified = async (phone: string, excludeUserId?: number): Promise<boolean> => {
  logger.debug(
    `[PHONE_VERIFICATION] Checking if phone is already verified - Phone: ${phone.substring(
      0,
      4
    )}***, ExcludeUserID: ${excludeUserId || 'none'}`
  );

  try {
    const existingUser = await prisma.user.findFirst({
      where: {
        phone,
        isPhoneVerified: true,
        ...(excludeUserId && { id: { not: excludeUserId } })
      }
    });

    const isAlreadyVerified = !!existingUser;

    if (isAlreadyVerified) {
      logger.warn(
        `[PHONE_VERIFICATION] Phone already verified by another user - Phone: ${phone.substring(
          0,
          4
        )}***, ExistingUserID: ${existingUser.id}`
      );
    } else {
      logger.debug(
        `[PHONE_VERIFICATION] Phone is available for verification - Phone: ${phone.substring(
          0,
          4
        )}***`
      );
    }

    return isAlreadyVerified;
  } catch (error: any) {
    logger.error(
      `[PHONE_VERIFICATION] Error checking phone verification status - Phone: ${phone.substring(
        0,
        4
      )}***, Error: ${error.message}`
    );
    throw error;
  }
};

/**
 * Clean up expired verification codes
 * @returns {Promise<number>}
 */
const cleanupExpiredCodes = async (): Promise<number> => {
  logger.debug(`[PHONE_VERIFICATION] Starting cleanup of expired verification codes`);

  try {
    const result = await prisma.phoneVerification.deleteMany({
      where: {
        expiresAt: {
          lt: new Date()
        }
      }
    });

    logger.info(
      `[PHONE_VERIFICATION] Cleanup completed - Deleted ${result.count} expired verification codes`
    );
    return result.count;
  } catch (error: any) {
    logger.error(`[PHONE_VERIFICATION] Error during cleanup - Error: ${error.message}`);
    throw error;
  }
};

export default {
  generateVerificationCode,
  sendPhoneVerificationCode,
  verifyPhoneCode,
  isPhoneAlreadyVerified,
  cleanupExpiredCodes
};
