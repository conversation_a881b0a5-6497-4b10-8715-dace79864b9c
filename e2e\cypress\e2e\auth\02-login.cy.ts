/// <reference types="cypress" />
import {
  generateTestUser,
  apiHelpers,
  waitHelpers,
} from '../../support/commands';

describe('User Login', () => {
  let registeredUser: any;

  before(() => {
    // Create verified user for login tests
    const testUser = generateTestUser({
      email: '<EMAIL>',
      phone: '081234567890',
    });

    cy.createTestUser(testUser).then((user) => {
      registeredUser = { ...testUser, ...user };
      // Verify email in database
      cy.verifyUserInDatabase(user.id);
    });
  });

  beforeEach(() => {
    cy.clearAuthData();
    cy.visit('/auth/login');
    waitHelpers.forPageLoad();
  });

  describe('Successful Login', () => {
    it('should login with valid credentials and redirect to dashboard', () => {
      cy.get('[data-testid=email-input]').type(registeredUser.email);
      cy.get('[data-testid=password-input]').type(registeredUser.password);
      cy.get('[data-testid=login-button]').click();

      // Should redirect to dashboard
      cy.url().should('include', '/dashboard');
      cy.contains('Dashboard').should('be.visible');
    });

    it('should login and redirect to callback URL if provided', () => {
      const callbackUrl = '/orders';
      cy.visit(`/auth/login?callbackUrl=${encodeURIComponent(callbackUrl)}`);

      cy.get('[data-testid=email-input]').type(registeredUser.email);
      cy.get('[data-testid=password-input]').type(registeredUser.password);
      cy.get('[data-testid=login-button]').click();

      // Should redirect to callback URL
      cy.url().should('include', callbackUrl);
    });

    it('should auto-redirect if user already authenticated', () => {
      // Login first
      cy.loginAs(registeredUser.email, registeredUser.password);

      // Try to visit login page
      cy.visit('/auth/login');

      // Should redirect to dashboard
      cy.url().should('include', '/dashboard');
    });

    it('should persist login state after page refresh', () => {
      // Login
      cy.get('[data-testid=email-input]').type(registeredUser.email);
      cy.get('[data-testid=password-input]').type(registeredUser.password);
      cy.get('[data-testid=login-button]').click();

      cy.url().should('include', '/dashboard');

      // Refresh page
      cy.reload();

      // Should still be logged in
      cy.url().should('include', '/dashboard');
    });
  });

  describe('Failed Login Attempts', () => {
    it('should show error for invalid email', () => {
      cy.get('[data-testid=email-input]').type('<EMAIL>');
      cy.get('[data-testid=password-input]').type('password123');
      cy.get('[data-testid=login-button]').click();

      cy.contains('Email atau password salah').should('be.visible');
    });

    it('should show error for wrong password', () => {
      cy.get('[data-testid=email-input]').type(registeredUser.email);
      cy.get('[data-testid=password-input]').type('wrongpassword');
      cy.get('[data-testid=login-button]').click();

      cy.contains('Email atau password salah').should('be.visible');
    });

    it('should show error for unverified email', () => {
      // Create unverified user
      const unverifiedUser = generateTestUser({
        email: '<EMAIL>',
      });

      cy.createTestUser(unverifiedUser);

      cy.get('[data-testid=email-input]').type(unverifiedUser.email);
      cy.get('[data-testid=password-input]').type(unverifiedUser.password);
      cy.get('[data-testid=login-button]').click();

      cy.contains('Please verify your email').should('be.visible');
      // Should stay on login page or redirect to verification
      cy.url().should(
        'satisfy',
        (url) =>
          url.includes('/auth/login') || url.includes('/auth/verify-request')
      );
    });

    it('should show error for inactive account', () => {
      // Test for inactive account scenario
      cy.intercept('POST', '**/auth/login', {
        statusCode: 401,
        body: { message: 'Account is inactive' },
      }).as('inactiveLogin');

      cy.get('[data-testid=email-input]').type(registeredUser.email);
      cy.get('[data-testid=password-input]').type(registeredUser.password);
      cy.get('[data-testid=login-button]').click();

      cy.contains('Account is inactive').should('be.visible');
    });
  });

  describe('Form Validation', () => {
    it('should show validation errors for empty fields', () => {
      cy.get('[data-testid=login-button]').click();

      cy.contains('Email wajib diisi').should('be.visible');
      cy.contains('Password wajib diisi').should('be.visible');
    });

    it('should show validation error for invalid email format', () => {
      cy.get('[data-testid=email-input]').type('invalid-email');
      cy.get('[data-testid=password-input]').type('password123');
      cy.get('[data-testid=login-button]').click();

      cy.contains('Format email tidak valid').should('be.visible');
    });

    it('should show validation error for short password', () => {
      cy.get('[data-testid=email-input]').type('<EMAIL>');
      cy.get('[data-testid=password-input]').type('123');
      cy.get('[data-testid=login-button]').click();

      cy.contains('Password minimal 6 karakter').should('be.visible');
    });
  });

  describe('Password Visibility Toggle', () => {
    it('should toggle password visibility', () => {
      const password = 'testpassword123';

      cy.get('[data-testid=password-input]').type(password);

      // Password should be hidden by default
      cy.get('[data-testid=password-input]').should(
        'have.attr',
        'type',
        'password'
      );

      // Click toggle button
      cy.get('[data-testid=password-toggle]').click();

      // Password should be visible
      cy.get('[data-testid=password-input]').should(
        'have.attr',
        'type',
        'text'
      );
      cy.get('[data-testid=password-input]').should('have.value', password);

      // Click toggle again
      cy.get('[data-testid=password-toggle]').click();

      // Password should be hidden again
      cy.get('[data-testid=password-input]').should(
        'have.attr',
        'type',
        'password'
      );
    });
  });

  describe('Loading States', () => {
    it('should show loading state during login', () => {
      // Intercept and delay login API call
      cy.intercept('POST', '**/auth/login', { delay: 1000 }).as('loginCall');

      cy.get('[data-testid=email-input]').type(registeredUser.email);
      cy.get('[data-testid=password-input]').type(registeredUser.password);
      cy.get('[data-testid=login-button]').click();

      // Should show loading state
      cy.get('[data-testid=login-button]').should('contain', 'Memproses');
      cy.get('[data-testid=login-button]').should('be.disabled');
    });

    it('should disable form fields during login', () => {
      // Intercept and delay login API call
      cy.intercept('POST', '**/auth/login', { delay: 1000 }).as('loginCall');

      cy.get('[data-testid=email-input]').type(registeredUser.email);
      cy.get('[data-testid=password-input]').type(registeredUser.password);
      cy.get('[data-testid=login-button]').click();

      // Form fields should be disabled
      cy.get('[data-testid=email-input]').should('be.disabled');
      cy.get('[data-testid=password-input]').should('be.disabled');
    });

    it('should show loading state during auth check on page load', () => {
      // Set up localStorage with auth data
      cy.window().then((win) => {
        win.localStorage.setItem('user', JSON.stringify(registeredUser));
        win.localStorage.setItem(
          'tokens',
          JSON.stringify({
            access: {
              token: 'fake-token',
              expires: new Date(Date.now() + 3600000),
            },
            refresh: {
              token: 'fake-refresh',
              expires: new Date(Date.now() + 86400000),
            },
          })
        );
      });

      cy.visit('/auth/login');

      // Should show loading state initially
      cy.contains('Loading...').should('be.visible');
    });
  });

  describe('Navigation', () => {
    it('should navigate to register page when clicking register link', () => {
      cy.get('[data-testid=register-link]').click();
      cy.url().should('include', '/auth/register');
    });

    it('should navigate to forgot password page when clicking forgot password link', () => {
      cy.get('[data-testid=forgot-password-link]').click();
      cy.url().should('include', '/auth/forgot-password');
    });
  });

  describe('Accessibility', () => {
    it('should be keyboard navigable', () => {
      // Tab through form elements
      cy.get('body').tab();
      cy.focused().should('have.attr', 'data-testid', 'email-input');

      cy.focused().tab();
      cy.focused().should('have.attr', 'data-testid', 'password-input');

      cy.focused().tab();
      cy.focused().should('have.attr', 'data-testid', 'login-button');
    });

    it('should support Enter key to submit form', () => {
      cy.get('[data-testid=email-input]').type(registeredUser.email);
      cy.get('[data-testid=password-input]').type(registeredUser.password);

      // Press Enter on password field
      cy.get('[data-testid=password-input]').type('{enter}');

      // Should trigger login
      cy.url().should('include', '/dashboard');
    });
  });

  describe('Error Recovery', () => {
    it('should clear error message when user starts typing', () => {
      // Trigger error first
      cy.get('[data-testid=email-input]').type('<EMAIL>');
      cy.get('[data-testid=password-input]').type('wrongpassword');
      cy.get('[data-testid=login-button]').click();

      cy.contains('Email atau password salah').should('be.visible');

      // Start typing in email field
      cy.get('[data-testid=email-input]').clear().type('<EMAIL>');

      // Error should be cleared
      cy.contains('Email atau password salah').should('not.exist');
    });

    it('should allow retry after network error', () => {
      // Simulate network error first
      cy.intercept('POST', '**/auth/login', {
        statusCode: 500,
        body: { message: 'Network error' },
      }).as('networkError');

      cy.get('[data-testid=email-input]').type(registeredUser.email);
      cy.get('[data-testid=password-input]').type(registeredUser.password);
      cy.get('[data-testid=login-button]').click();

      cy.contains('Network error').should('be.visible');

      // Fix network and retry
      cy.intercept('POST', '**/auth/login').as('retryLogin');
      cy.get('[data-testid=login-button]').click();

      // Should succeed this time
      cy.url().should('include', '/dashboard');
    });
  });
});
