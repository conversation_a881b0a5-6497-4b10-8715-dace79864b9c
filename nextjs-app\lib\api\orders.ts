import axios from 'axios';
import type {
  Order,
  CreateOrderRequest,
  UpdateOrderRequest,
  UpdateOrderStatusRequest,
  UpdateOrderItemStatusRequest,
  UpdateOrderItemsRequest,
  AddPaymentRequest,
  GetOrdersParams,
  GetOrdersResponse,
  OrderStatusHistory,
  OrderItemStatusHistory,
  OrderTimeline,
  Payment,
} from '@/types/orders';

const API_BASE_URL =
  process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/v1';

// Helper function to get token from localStorage or cookies
function getAccessToken(): string | null {
  // First try localStorage
  if (typeof window !== 'undefined') {
    const token = localStorage.getItem('accessToken');
    if (token) return token;

    // If not in localStorage, try to get from cookies via auth context
    const tokensStr = localStorage.getItem('tokens');
    if (tokensStr) {
      try {
        const tokens = JSON.parse(tokensStr);
        return tokens?.access?.token || null;
      } catch (error) {
        console.error('Error parsing tokens from localStorage:', error);
      }
    }
  }

  return null;
}

// Helper function to get refresh token
function getRefreshToken(): string | null {
  if (typeof window !== 'undefined') {
    const token = localStorage.getItem('refreshToken');
    if (token) return token;

    const tokensStr = localStorage.getItem('tokens');
    if (tokensStr) {
      try {
        const tokens = JSON.parse(tokensStr);
        return tokens?.refresh?.token || null;
      } catch (error) {
        console.error('Error parsing tokens from localStorage:', error);
      }
    }
  }

  return null;
}

// Create axios instance with base configuration
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = getAccessToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor to handle token refresh
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        const refreshToken = getRefreshToken();
        if (!refreshToken) {
          throw new Error('No refresh token');
        }

        const response = await axios.post(
          `${API_BASE_URL}/auth/refresh-tokens`,
          {
            refreshToken,
          }
        );

        const { access, refresh } = response.data;

        // Update both localStorage formats for compatibility
        localStorage.setItem('accessToken', access.token);
        localStorage.setItem('refreshToken', refresh.token);
        localStorage.setItem('tokens', JSON.stringify(response.data));

        // Update cookies for middleware
        document.cookie = `tokens=${JSON.stringify(
          response.data
        )}; path=/; max-age=${7 * 24 * 60 * 60}`;

        return api(originalRequest);
      } catch (refreshError) {
        console.error('Token refresh failed:', refreshError);
        localStorage.removeItem('accessToken');
        localStorage.removeItem('refreshToken');
        localStorage.removeItem('tokens');
        localStorage.removeItem('user');

        // Clear cookies
        document.cookie =
          'tokens=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
        document.cookie =
          'user=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';

        window.location.href = '/auth/login';
        return Promise.reject(refreshError);
      }
    }

    return Promise.reject(error);
  }
);

// Orders API functions
export const orderAPI = {
  // Get all orders with filters and pagination
  async getOrders(params?: GetOrdersParams): Promise<GetOrdersResponse> {
    const response = await api.get('/orders', { params });
    return response.data;
  },

  // Get single order by ID
  async getOrder(id: number): Promise<Order> {
    const response = await api.get(`/orders/${id}`);
    return response.data;
  },

  // Create new order
  async createOrder(data: CreateOrderRequest): Promise<Order> {
    const response = await api.post('/orders', data);
    // Backend returns {success: true, message: '...', data: Order}
    return response.data.data;
  },

  // Update order
  async updateOrder(id: number, data: UpdateOrderRequest): Promise<Order> {
    const response = await api.patch(`/orders/${id}`, data);
    return response.data;
  },

  // Delete order
  async deleteOrder(id: number): Promise<void> {
    await api.delete(`/orders/${id}`);
  },

  // Update order status
  async updateOrderStatus(
    id: number,
    data: UpdateOrderStatusRequest
  ): Promise<Order> {
    const response = await api.patch(`/orders/${id}/status`, data);
    return response.data;
  },

  // Update order items
  async updateOrderItems(
    id: number,
    data: UpdateOrderItemsRequest
  ): Promise<Order> {
    const response = await api.patch(`/orders/${id}/items`, data);
    return response.data;
  },

  // Update order item status
  async updateOrderItemStatus(
    orderId: number,
    itemId: number,
    data: UpdateOrderItemStatusRequest
  ): Promise<any> {
    const response = await api.patch(
      `/orders/${orderId}/items/${itemId}/status`,
      data
    );
    return response.data;
  },

  // Add payment to order
  async addPayment(id: number, data: AddPaymentRequest): Promise<Payment> {
    const response = await api.post(`/orders/${id}/payments`, data);
    return response.data;
  },

  // Get order status history
  async getOrderStatusHistory(id: number): Promise<OrderStatusHistory[]> {
    const response = await api.get(`/orders/${id}/history`);
    return response.data;
  },

  // Get order item status history
  async getOrderItemStatusHistory(
    orderId: number,
    itemId: number
  ): Promise<OrderItemStatusHistory[]> {
    const response = await api.get(
      `/orders/${orderId}/items/${itemId}/history`
    );
    return response.data;
  },

  // Get complete order timeline
  async getOrderTimeline(id: number): Promise<OrderTimeline> {
    const response = await api.get(`/orders/${id}/timeline`);
    return response.data;
  },

  // Get order summary counts for dashboard
  async getOrderSummary(): Promise<{
    masuk: number;
    hariIni: number;
    besok: number;
    lusa: number;
    terlambat: number;
  }> {
    const response = await api.get('/orders/summary/counts');
    return response.data;
  },
};

// Helper functions for UI
export function getOrderStatusLabel(status: string): string {
  const statusLabels: Record<string, string> = {
    PENDING: 'Menunggu',
    PROCESSING: 'Diproses',
    WASHING: 'Dicuci',
    DRYING: 'Dikeringkan',
    IRONING: 'Disetrika',
    READY: 'Siap Diambil',
    DELIVERED: 'Selesai',
    CANCELLED: 'Dibatalkan',
  };
  return statusLabels[status] || status;
}

export function getPaymentStatusLabel(status: string): string {
  const statusLabels: Record<string, string> = {
    UNPAID: 'Belum Bayar',
    PARTIAL: 'Bayar Sebagian',
    PAID: 'Lunas',
    REFUNDED: 'Dikembalikan',
  };
  return statusLabels[status] || status;
}

export function getPaymentMethodLabel(method: string): string {
  const methodLabels: Record<string, string> = {
    CASH: 'Tunai',
    TRANSFER: 'Transfer',
    CREDIT_CARD: 'Kartu Kredit',
    DEBIT_CARD: 'Kartu Debit',
    E_WALLET: 'E-Wallet',
    DEPOSIT: 'Deposit',
  };
  return methodLabels[method] || method;
}

export function getOrderStatusBadgeVariant(
  status: string
): 'default' | 'secondary' | 'destructive' | 'outline' {
  switch (status) {
    case 'DELIVERED':
      return 'default';
    case 'CANCELLED':
      return 'destructive';
    case 'PENDING':
      return 'outline';
    default:
      return 'secondary';
  }
}

export function getPaymentStatusBadgeVariant(
  status: string
): 'default' | 'secondary' | 'destructive' | 'outline' {
  switch (status) {
    case 'PAID':
      return 'default';
    case 'UNPAID':
      return 'destructive';
    case 'PARTIAL':
      return 'outline';
    case 'REFUNDED':
      return 'secondary';
    default:
      return 'outline';
  }
}

export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
}

export function formatDate(dateString: string): string {
  return new Intl.DateTimeFormat('id-ID', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(new Date(dateString));
}

// Helper function untuk extract error message dari API response
export function getOrderErrorMessage(error: any): string {
  if (error?.response?.data?.message) {
    return error.response.data.message;
  }

  if (error?.message) {
    return error.message;
  }

  return 'Terjadi kesalahan yang tidak diketahui';
}

// Helper function untuk extract field errors dari API response
export function getOrderFieldErrors(error: any): Record<string, string> {
  const fieldErrors: Record<string, string> = {};

  if (error?.response?.data?.errors) {
    const errors = error.response.data.errors;
    for (const err of errors) {
      if (err.path && err.message) {
        fieldErrors[err.path] = err.message;
      }
    }
  }

  return fieldErrors;
}
