'use client';

import { useState } from 'react';
import Link from 'next/link';
import {
  ArrowLeft,
  Award,
  Search,
  Plus,
  Info,
  ChevronRight,
  Star,
  Clock,
  TrendingUp,
  Gift,
  Users,
  Calendar,
  BarChart2,
  Filter,
  Download,
  Upload,
  Settings,
  Zap,
  Crown,
  Percent,
  Tag,
  Truck,
  Coffee,
  Cake,
  Share2,
  UserPlus,
  MessageSquare,
  Mail,
  Smartphone,
  Copy,
} from 'lucide-react';
import { useRouter } from 'next/navigation';

import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';

// Mock data for loyalty program
const mockLoyaltyProgram = {
  tiers: [
    {
      name: 'Bronze',
      pointsRequired: 0,
      benefits: ['Poin setiap transaksi', 'Diskon ulang tahun 5%'],
      color: 'bg-amber-600',
      customers: 45,
      icon: <Award className="h-5 w-5" />,
    },
    {
      name: 'Silver',
      pointsRequired: 1000,
      benefits: [
        'Poin setiap transaksi',
        'Diskon ulang tahun 10%',
        'Gratis antar-jemput 1x/bulan',
      ],
      color: 'bg-gray-400',
      customers: 28,
      icon: <Award className="h-5 w-5" />,
    },
    {
      name: 'Gold',
      pointsRequired: 3000,
      benefits: [
        'Poin setiap transaksi',
        'Diskon ulang tahun 15%',
        'Gratis antar-jemput 2x/bulan',
        'Prioritas pengerjaan',
      ],
      color: 'bg-yellow-500',
      customers: 15,
      icon: <Crown className="h-5 w-5" />,
    },
    {
      name: 'Platinum',
      pointsRequired: 6000,
      benefits: [
        'Poin setiap transaksi',
        'Diskon ulang tahun 20%',
        'Gratis antar-jemput unlimited',
        'Prioritas pengerjaan',
        'Diskon 5% setiap transaksi',
      ],
      color: 'bg-blue-600',
      customers: 7,
      icon: <Zap className="h-5 w-5" />,
    },
  ],
  rewards: [
    {
      id: 1,
      name: 'Diskon 10%',
      description: 'Diskon 10% untuk order berikutnya',
      pointsRequired: 200,
      image: '/placeholder.svg',
      isAvailable: true,
      category: 'diskon',
      redemptions: 45,
      icon: <Percent className="h-5 w-5" />,
    },
    {
      id: 2,
      name: 'Gratis Cuci 2kg',
      description: 'Gratis layanan cuci 2kg untuk order berikutnya',
      pointsRequired: 500,
      image: '/placeholder.svg',
      isAvailable: true,
      category: 'layanan',
      redemptions: 32,
      icon: <Tag className="h-5 w-5" />,
    },
    {
      id: 3,
      name: 'Gratis Antar-Jemput',
      description: 'Gratis layanan antar-jemput untuk 3 order berikutnya',
      pointsRequired: 750,
      image: '/placeholder.svg',
      isAvailable: true,
      category: 'pengiriman',
      redemptions: 28,
      icon: <Truck className="h-5 w-5" />,
    },
    {
      id: 4,
      name: 'Diskon 25%',
      description: 'Diskon 25% untuk order berikutnya',
      pointsRequired: 1000,
      image: '/placeholder.svg',
      isAvailable: true,
      category: 'diskon',
      redemptions: 15,
      icon: <Percent className="h-5 w-5" />,
    },
    {
      id: 5,
      name: 'Voucher Rp 50.000',
      description: 'Voucher senilai Rp 50.000 untuk order berikutnya',
      pointsRequired: 1500,
      image: '/placeholder.svg',
      isAvailable: false,
      category: 'voucher',
      redemptions: 8,
      icon: <Tag className="h-5 w-5" />,
    },
    {
      id: 6,
      name: 'Kopi Gratis',
      description: 'Kopi gratis saat menunggu di outlet',
      pointsRequired: 100,
      image: '/placeholder.svg',
      isAvailable: true,
      category: 'lainnya',
      redemptions: 67,
      icon: <Coffee className="h-5 w-5" />,
    },
    {
      id: 7,
      name: 'Kue Ulang Tahun',
      description: 'Kue ulang tahun gratis di bulan ulang tahun',
      pointsRequired: 1200,
      image: '/placeholder.svg',
      isAvailable: true,
      category: 'lainnya',
      redemptions: 12,
      icon: <Cake className="h-5 w-5" />,
    },
  ],
  customers: [
    {
      id: 1,
      name: 'Ahmad Rizki',
      phone: '081234567890',
      points: 2500,
      tier: 'Silver',
      transactions: 15,
      joinDate: '15/01/2023',
      lastTransaction: '23/03/2024',
      pointsHistory: [
        { date: '23/03/2024', amount: 150, description: 'Order #TRX-123' },
        { date: '15/02/2024', amount: 200, description: 'Order #TRX-098' },
        {
          date: '05/01/2024',
          amount: -500,
          description: 'Redeem: Gratis Cuci 2kg',
        },
      ],
      referrals: 2,
    },
    {
      id: 2,
      name: 'Siti Rahayu',
      phone: '081234567891',
      points: 4200,
      tier: 'Gold',
      transactions: 28,
      joinDate: '20/03/2023',
      lastTransaction: '18/03/2024',
      pointsHistory: [
        { date: '18/03/2024', amount: 250, description: 'Order #TRX-145' },
        { date: '02/03/2024', amount: 180, description: 'Order #TRX-132' },
        {
          date: '15/02/2024',
          amount: -750,
          description: 'Redeem: Gratis Antar-Jemput',
        },
      ],
      referrals: 5,
    },
    {
      id: 3,
      name: 'Budi Santoso',
      phone: '081234567892',
      points: 850,
      tier: 'Bronze',
      transactions: 7,
      joinDate: '05/05/2023',
      lastTransaction: '10/03/2024',
      pointsHistory: [
        { date: '10/03/2024', amount: 120, description: 'Order #TRX-135' },
        { date: '25/02/2024', amount: 100, description: 'Order #TRX-128' },
        { date: '10/02/2024', amount: 80, description: 'Order #TRX-115' },
      ],
      referrals: 0,
    },
    {
      id: 4,
      name: 'Dewi Anggraini',
      phone: '081234567893',
      points: 7500,
      tier: 'Platinum',
      transactions: 42,
      joinDate: '10/07/2023',
      lastTransaction: '25/03/2024',
      pointsHistory: [
        { date: '25/03/2024', amount: 300, description: 'Order #TRX-150' },
        { date: '15/03/2024', amount: 280, description: 'Order #TRX-142' },
        {
          date: '05/03/2024',
          amount: -1000,
          description: 'Redeem: Diskon 25%',
        },
      ],
      referrals: 8,
    },
    {
      id: 5,
      name: 'Rudi Hermawan',
      phone: '081234567894',
      points: 1200,
      tier: 'Silver',
      transactions: 12,
      joinDate: '15/08/2023',
      lastTransaction: '20/03/2024',
      pointsHistory: [
        { date: '20/03/2024', amount: 150, description: 'Order #TRX-148' },
        { date: '10/03/2024', amount: 130, description: 'Order #TRX-136' },
        { date: '28/02/2024', amount: -200, description: 'Redeem: Diskon 10%' },
      ],
      referrals: 1,
    },
    {
      id: 6,
      name: 'Rina Wijaya',
      phone: '081234567895',
      points: 3800,
      tier: 'Gold',
      transactions: 25,
      joinDate: '05/09/2023',
      lastTransaction: '22/03/2024',
      pointsHistory: [
        { date: '22/03/2024', amount: 220, description: 'Order #TRX-149' },
        { date: '12/03/2024', amount: 200, description: 'Order #TRX-140' },
        { date: '02/03/2024', amount: 180, description: 'Order #TRX-131' },
      ],
      referrals: 3,
    },
  ],
  campaigns: [
    {
      id: 1,
      name: 'Double Points Weekend',
      description:
        'Dapatkan poin 2x lipat untuk setiap transaksi di akhir pekan',
      startDate: '01/04/2024',
      endDate: '30/04/2024',
      status: 'active',
      targetTier: 'all',
    },
    {
      id: 2,
      name: 'Birthday Month Bonus',
      description:
        'Bonus 500 poin untuk pelanggan yang berulang tahun bulan ini',
      startDate: '01/04/2024',
      endDate: '30/04/2024',
      status: 'active',
      targetTier: 'all',
    },
    {
      id: 3,
      name: 'Gold & Platinum Special',
      description:
        'Bonus 1000 poin untuk pelanggan Gold & Platinum yang melakukan 3 transaksi dalam bulan ini',
      startDate: '01/04/2024',
      endDate: '30/04/2024',
      status: 'active',
      targetTier: 'gold,platinum',
    },
    {
      id: 4,
      name: 'Welcome Bonus',
      description: 'Bonus 300 poin untuk pelanggan baru',
      startDate: '01/03/2024',
      endDate: '31/03/2024',
      status: 'ended',
      targetTier: 'all',
    },
  ],
  analytics: {
    totalPoints: 125000,
    pointsRedeemed: 45000,
    activeCustomers: 95,
    redemptionRate: 72,
    monthlyGrowth: 15,
    popularRewards: [
      { name: 'Kopi Gratis', redemptions: 67 },
      { name: 'Diskon 10%', redemptions: 45 },
      { name: 'Gratis Cuci 2kg', redemptions: 32 },
    ],
    tierDistribution: [
      { tier: 'Bronze', percentage: 47 },
      { tier: 'Silver', percentage: 30 },
      { tier: 'Gold', percentage: 16 },
      { tier: 'Platinum', percentage: 7 },
    ],
    monthlyRedemptions: [
      { month: 'Jan', count: 28 },
      { month: 'Feb', count: 35 },
      { month: 'Mar', count: 42 },
    ],
  },
  referrals: {
    totalReferrals: 19,
    activeReferrals: 15,
    conversionRate: 78,
    pointsPerReferral: 250,
    topReferrers: [
      { id: 4, name: 'Dewi Anggraini', referrals: 8 },
      { id: 2, name: 'Siti Rahayu', referrals: 5 },
      { id: 6, name: 'Rina Wijaya', referrals: 3 },
    ],
    recentReferrals: [
      {
        date: '25/03/2024',
        referrer: 'Dewi Anggraini',
        referee: 'Joko Susanto',
        status: 'completed',
      },
      {
        date: '22/03/2024',
        referrer: 'Siti Rahayu',
        referee: 'Andi Pratama',
        status: 'completed',
      },
      {
        date: '18/03/2024',
        referrer: 'Rina Wijaya',
        referee: 'Maya Sari',
        status: 'pending',
      },
    ],
  },
};

export default function LoyaltyPage() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState('customers');
  const [filterOpen, setFilterOpen] = useState(false);
  const [customerTierFilter, setCustomerTierFilter] = useState('all');
  const [rewardCategoryFilter, setRewardCategoryFilter] = useState('all');
  const [campaignStatusFilter, setCampaignStatusFilter] = useState('all');
  const [showAnalyticsDialog, setShowAnalyticsDialog] = useState(false);
  const [showCampaignDialog, setShowCampaignDialog] = useState(false);
  const [showPointsRuleDialog, setShowPointsRuleDialog] = useState(false);
  const [showReferralDialog, setShowReferralDialog] = useState(false);
  const [referralMethod, setReferralMethod] = useState('whatsapp');

  // Filter customers based on search query and tier filter
  const filteredCustomers = mockLoyaltyProgram.customers.filter((customer) => {
    const matchesSearch =
      customer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      customer.phone.includes(searchQuery);
    const matchesTier =
      customerTierFilter === 'all' ||
      customer.tier.toLowerCase() === customerTierFilter.toLowerCase();
    return matchesSearch && matchesTier;
  });

  // Filter rewards based on category
  const filteredRewards = mockLoyaltyProgram.rewards.filter(
    (reward) =>
      rewardCategoryFilter === 'all' || reward.category === rewardCategoryFilter
  );

  // Filter campaigns based on status
  const filteredCampaigns = mockLoyaltyProgram.campaigns.filter(
    (campaign) =>
      campaignStatusFilter === 'all' || campaign.status === campaignStatusFilter
  );

  const getTierColor = (tier: string) => {
    const tierObj = mockLoyaltyProgram.tiers.find((t) => t.name === tier);
    return tierObj ? tierObj.color : 'bg-gray-500';
  };

  const getTierIcon = (tier: string) => {
    const tierObj = mockLoyaltyProgram.tiers.find((t) => t.name === tier);
    return tierObj ? tierObj.icon : <Award className="h-5 w-5" />;
  };

  return (
    <div className="flex flex-col min-h-screen bg-slate-50">
      <header className="p-4 flex justify-between items-center bg-white sticky top-0 z-10 shadow-sm">
        <div className="flex items-center">
          <Link href="/akun" className="mr-3">
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <h1 className="text-lg font-semibold">Program Loyalitas</h1>
        </div>
        <div className="flex items-center gap-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="hidden sm:flex">
                <Settings className="h-4 w-4 mr-1" /> Pengaturan
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Pengaturan Loyalitas</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => setShowPointsRuleDialog(true)}>
                <Star className="h-4 w-4 mr-2" /> Aturan Poin
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => router.push('/akun/loyalty/settings')}
              >
                <Info className="h-4 w-4 mr-2" /> Pengaturan Program
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setShowAnalyticsDialog(true)}>
                <BarChart2 className="h-4 w-4 mr-2" /> Analitik Loyalitas
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <Download className="h-4 w-4 mr-2" /> Export Data
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Upload className="h-4 w-4 mr-2" /> Import Data
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <Button
            onClick={() => router.push('/akun/loyalty/add-points')}
            className="bg-blue-500 hover:bg-blue-600"
            size="sm"
          >
            <Plus className="h-4 w-4 mr-1" /> Tambah Poin
          </Button>
        </div>
      </header>

      <div className="p-4">
        <Tabs
          defaultValue="customers"
          className="mb-4"
          onValueChange={setActiveTab}
        >
          <TabsList className="grid grid-cols-4 w-full">
            <TabsTrigger value="customers">Pelanggan</TabsTrigger>
            <TabsTrigger value="rewards">Rewards</TabsTrigger>
            <TabsTrigger value="tiers">Level</TabsTrigger>
            <TabsTrigger value="campaigns">Kampanye</TabsTrigger>
          </TabsList>
          {/* Customers Tab */}
          <TabsContent value="customers" className="mt-0">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 gap-2">
              <div className="relative flex-1 w-full">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <Input
                  placeholder="Cari pelanggan..."
                  className="pl-10"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              <Sheet open={filterOpen} onOpenChange={setFilterOpen}>
                <SheetTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-1"
                  >
                    <Filter className="h-4 w-4" />
                    Filter
                    {customerTierFilter !== 'all' && (
                      <Badge className="ml-1 bg-blue-500">
                        {customerTierFilter}
                      </Badge>
                    )}
                  </Button>
                </SheetTrigger>
                <SheetContent>
                  <SheetHeader>
                    <SheetTitle>Filter Pelanggan</SheetTitle>
                    <SheetDescription>
                      Filter pelanggan berdasarkan level loyalitas
                    </SheetDescription>
                  </SheetHeader>
                  <div className="py-4 space-y-4">
                    <div className="space-y-2">
                      <Label>Level Loyalitas</Label>
                      <Select
                        value={customerTierFilter}
                        onValueChange={setCustomerTierFilter}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Pilih level" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">Semua Level</SelectItem>
                          {mockLoyaltyProgram.tiers.map((tier) => (
                            <SelectItem
                              key={tier.name}
                              value={tier.name.toLowerCase()}
                            >
                              {tier.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="flex justify-end gap-2 mt-6">
                      <Button
                        variant="outline"
                        onClick={() => setCustomerTierFilter('all')}
                      >
                        Reset
                      </Button>
                      <Button onClick={() => setFilterOpen(false)}>
                        Terapkan
                      </Button>
                    </div>
                  </div>
                </SheetContent>
              </Sheet>
            </div>

            <div className="space-y-4 mb-20">
              {filteredCustomers.length > 0 ? (
                filteredCustomers.map((customer) => (
                  <Card key={customer.id} className="p-4">
                    <div className="flex justify-between items-start">
                      <div>
                        <div className="flex items-center gap-2">
                          <h3 className="font-medium">{customer.name}</h3>
                          <Badge
                            className={`${getTierColor(
                              customer.tier
                            )} text-white flex items-center gap-1`}
                          >
                            {getTierIcon(customer.tier)}
                            {customer.tier}
                          </Badge>
                        </div>
                        <p className="text-sm text-gray-500">
                          {customer.phone}
                        </p>
                        <div className="mt-2">
                          <div className="flex items-center gap-1 text-sm">
                            <Star className="h-4 w-4 text-yellow-500" />
                            <span className="font-medium">
                              {customer.points} poin
                            </span>
                          </div>
                          <div className="flex items-center gap-1 text-sm text-gray-500 mt-1">
                            <Clock className="h-3 w-3" />
                            <span>Bergabung {customer.joinDate}</span>
                          </div>
                          <div className="flex items-center gap-1 text-sm text-gray-500 mt-1">
                            <Calendar className="h-3 w-3" />
                            <span>
                              Transaksi terakhir {customer.lastTransaction}
                            </span>
                          </div>
                          <div className="flex items-center gap-1 text-sm text-gray-500 mt-1">
                            <UserPlus className="h-3 w-3" />
                            <span>{customer.referrals} referral</span>
                          </div>
                        </div>
                      </div>
                      <div className="flex flex-col gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-blue-500"
                          onClick={() =>
                            router.push(`/akun/loyalty/customer/${customer.id}`)
                          }
                        >
                          Detail <ChevronRight className="h-4 w-4 ml-1" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-green-600"
                          onClick={() => setShowReferralDialog(true)}
                        >
                          <Share2 className="h-4 w-4 mr-1" /> Referral
                        </Button>
                      </div>
                    </div>

                    {/* Progress to next tier */}
                    {customer.tier !== 'Platinum' && (
                      <div className="mt-3">
                        <div className="flex justify-between text-xs text-gray-500 mb-1">
                          <span>{customer.points} poin</span>
                          <span>
                            {mockLoyaltyProgram.tiers.find(
                              (t) => t.name === customer.tier
                            )?.pointsRequired +
                              (customer.tier === 'Bronze'
                                ? 1000
                                : customer.tier === 'Silver'
                                ? 2000
                                : 3000)}{' '}
                            poin
                          </span>
                        </div>
                        <Progress
                          value={
                            customer.tier === 'Bronze'
                              ? (customer.points / 1000) * 100
                              : customer.tier === 'Silver'
                              ? ((customer.points - 1000) / 2000) * 100
                              : ((customer.points - 3000) / 3000) * 100
                          }
                          className="h-2"
                        />
                        <p className="text-xs text-gray-500 mt-1 text-right">
                          {customer.tier === 'Bronze'
                            ? `${1000 - customer.points} poin lagi untuk Silver`
                            : customer.tier === 'Silver'
                            ? `${3000 - customer.points} poin lagi untuk Gold`
                            : `${
                                6000 - customer.points
                              } poin lagi untuk Platinum`}
                        </p>
                      </div>
                    )}
                  </Card>
                ))
              ) : (
                <div className="text-center py-8 text-gray-500">
                  Tidak ada pelanggan yang ditemukan
                </div>
              )}
            </div>
          </TabsContent>

          {/* Rewards Tab */}
          <TabsContent value="rewards" className="mt-0">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 gap-2">
              <div className="relative flex-1 w-full">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <Input
                  placeholder="Cari reward..."
                  className="pl-10"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              <Sheet>
                <SheetTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-1"
                  >
                    <Filter className="h-4 w-4" />
                    Filter
                    {rewardCategoryFilter !== 'all' && (
                      <Badge className="ml-1 bg-blue-500">
                        {rewardCategoryFilter}
                      </Badge>
                    )}
                  </Button>
                </SheetTrigger>
                <SheetContent>
                  <SheetHeader>
                    <SheetTitle>Filter Rewards</SheetTitle>
                    <SheetDescription>
                      Filter rewards berdasarkan kategori
                    </SheetDescription>
                  </SheetHeader>
                  <div className="py-4 space-y-4">
                    <div className="space-y-2">
                      <Label>Kategori</Label>
                      <Select
                        value={rewardCategoryFilter}
                        onValueChange={setRewardCategoryFilter}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Pilih kategori" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">Semua Kategori</SelectItem>
                          <SelectItem value="diskon">Diskon</SelectItem>
                          <SelectItem value="layanan">Layanan</SelectItem>
                          <SelectItem value="pengiriman">Pengiriman</SelectItem>
                          <SelectItem value="voucher">Voucher</SelectItem>
                          <SelectItem value="lainnya">Lainnya</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="flex justify-end gap-2 mt-6">
                      <Button
                        variant="outline"
                        onClick={() => setRewardCategoryFilter('all')}
                      >
                        Reset
                      </Button>
                      <Button>Terapkan</Button>
                    </div>
                  </div>
                </SheetContent>
              </Sheet>
              <Button
                onClick={() => router.push('/akun/loyalty/rewards/add')}
                className="bg-blue-500 hover:bg-blue-600"
                size="sm"
              >
                <Plus className="h-4 w-4 mr-1" /> Tambah Reward
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-20">
              {filteredRewards.map((reward) => (
                <Card
                  key={reward.id}
                  className={`p-4 ${!reward.isAvailable ? 'opacity-60' : ''}`}
                >
                  <div className="flex gap-3">
                    <div className="w-16 h-16 bg-purple-100 text-purple-600 rounded-md flex items-center justify-center">
                      {reward.icon}
                    </div>
                    <div className="flex-1">
                      <div className="flex justify-between items-start">
                        <h3 className="font-medium">{reward.name}</h3>
                        <Badge
                          variant={reward.isAvailable ? 'default' : 'secondary'}
                        >
                          {reward.isAvailable ? 'Tersedia' : 'Tidak Tersedia'}
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-500 mt-1">
                        {reward.description}
                      </p>
                      <div className="flex items-center justify-between mt-2">
                        <div className="flex items-center gap-1">
                          <Star className="h-4 w-4 text-yellow-500" />
                          <span className="font-medium">
                            {reward.pointsRequired} poin
                          </span>
                        </div>
                        <div className="text-sm text-gray-500 flex items-center">
                          <Users className="h-3 w-3 mr-1" />
                          {reward.redemptions} penukaran
                        </div>
                      </div>
                    </div>
                  </div>
                </Card>
              ))}
              <Card className="p-4 border-dashed flex items-center justify-center">
                <Button
                  variant="ghost"
                  className="w-full h-full py-8 flex flex-col gap-2"
                  onClick={() => router.push('/akun/loyalty/rewards/add')}
                >
                  <Plus className="h-6 w-6" />
                  <span>Tambah Reward Baru</span>
                </Button>
              </Card>
            </div>
          </TabsContent>

          {/* Tiers Tab */}
          <TabsContent value="tiers" className="mt-0">
            <div className="space-y-4 mb-20">
              {mockLoyaltyProgram.tiers.map((tier, index) => (
                <Card key={tier.name} className="p-4">
                  <div className="flex items-center gap-3 mb-3">
                    <div
                      className={`w-10 h-10 rounded-full ${tier.color} flex items-center justify-center text-white`}
                    >
                      {tier.icon}
                    </div>
                    <div className="flex-1">
                      <div className="flex justify-between items-center">
                        <h3 className="font-medium">{tier.name}</h3>
                        <Badge
                          variant="outline"
                          className="flex items-center gap-1"
                        >
                          <Users className="h-3 w-3" />
                          {tier.customers} pelanggan
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-500">
                        {tier.pointsRequired > 0
                          ? `Minimal ${tier.pointsRequired} poin`
                          : 'Level awal'}
                      </p>
                    </div>
                  </div>
                  {index < mockLoyaltyProgram.tiers.length - 1 && (
                    <div className="mb-3">
                      <div className="flex justify-between text-xs text-gray-500 mb-1">
                        <span>{tier.pointsRequired} poin</span>
                        <span>
                          {mockLoyaltyProgram.tiers[index + 1].pointsRequired}{' '}
                          poin
                        </span>
                      </div>
                      <Progress value={0} className="h-2" />
                    </div>
                  )}
                  <div className="space-y-1 mt-3">
                    <h4 className="text-sm font-medium">Keuntungan:</h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      {tier.benefits.map((benefit, i) => (
                        <li key={i} className="flex items-start gap-2">
                          <div className="min-w-4 mt-1">•</div>
                          <span>{benefit}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                  <div className="mt-3 flex justify-end">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-blue-500"
                      onClick={() =>
                        router.push(
                          `/akun/loyalty/tiers/edit/${tier.name.toLowerCase()}`
                        )
                      }
                    >
                      Edit <ChevronRight className="h-4 w-4 ml-1" />
                    </Button>
                  </div>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Campaigns Tab */}
          <TabsContent value="campaigns" className="mt-0">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 gap-2">
              <div className="relative flex-1 w-full">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <Input
                  placeholder="Cari kampanye..."
                  className="pl-10"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              <Sheet>
                <SheetTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-1"
                  >
                    <Filter className="h-4 w-4" />
                    Filter
                    {campaignStatusFilter !== 'all' && (
                      <Badge className="ml-1 bg-blue-500">
                        {campaignStatusFilter}
                      </Badge>
                    )}
                  </Button>
                </SheetTrigger>
                <SheetContent>
                  <SheetHeader>
                    <SheetTitle>Filter Kampanye</SheetTitle>
                    <SheetDescription>
                      Filter kampanye berdasarkan status
                    </SheetDescription>
                  </SheetHeader>
                  <div className="py-4 space-y-4">
                    <div className="space-y-2">
                      <Label>Status</Label>
                      <Select
                        value={campaignStatusFilter}
                        onValueChange={setCampaignStatusFilter}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Pilih status" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">Semua Status</SelectItem>
                          <SelectItem value="active">Aktif</SelectItem>
                          <SelectItem value="scheduled">Terjadwal</SelectItem>
                          <SelectItem value="ended">Berakhir</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="flex justify-end gap-2 mt-6">
                      <Button
                        variant="outline"
                        onClick={() => setCampaignStatusFilter('all')}
                      >
                        Reset
                      </Button>
                      <Button>Terapkan</Button>
                    </div>
                  </div>
                </SheetContent>
              </Sheet>
              <Button
                onClick={() => setShowCampaignDialog(true)}
                className="bg-blue-500 hover:bg-blue-600"
                size="sm"
              >
                <Plus className="h-4 w-4 mr-1" /> Tambah Kampanye
              </Button>
            </div>

            <div className="space-y-4 mb-20">
              {filteredCampaigns.map((campaign) => (
                <Card key={campaign.id} className="p-4">
                  <div className="flex justify-between items-start">
                    <div>
                      <div className="flex items-center gap-2">
                        <h3 className="font-medium">{campaign.name}</h3>
                        <Badge
                          className={
                            campaign.status === 'active'
                              ? 'bg-green-500'
                              : campaign.status === 'scheduled'
                              ? 'bg-blue-500'
                              : 'bg-gray-500'
                          }
                        >
                          {campaign.status === 'active'
                            ? 'Aktif'
                            : campaign.status === 'scheduled'
                            ? 'Terjadwal'
                            : 'Berakhir'}
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-500 mt-1">
                        {campaign.description}
                      </p>
                      <div className="flex items-center gap-3 mt-2">
                        <div className="flex items-center gap-1 text-sm text-gray-500">
                          <Calendar className="h-3 w-3" />
                          <span>
                            {campaign.startDate} - {campaign.endDate}
                          </span>
                        </div>
                        <div className="flex items-center gap-1 text-sm text-gray-500">
                          <Users className="h-3 w-3" />
                          <span>
                            {campaign.targetTier === 'all'
                              ? 'Semua level'
                              : campaign.targetTier
                                  .split(',')
                                  .map(
                                    (t) =>
                                      t.charAt(0).toUpperCase() + t.slice(1)
                                  )
                                  .join(' & ')}
                          </span>
                        </div>
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-blue-500"
                      onClick={() =>
                        router.push(`/akun/loyalty/campaigns/${campaign.id}`)
                      }
                    >
                      Detail <ChevronRight className="h-4 w-4 ml-1" />
                    </Button>
                  </div>
                </Card>
              ))}
              <Card className="p-4 border-dashed flex items-center justify-center">
                <Button
                  variant="ghost"
                  className="w-full h-full py-8 flex flex-col gap-2"
                  onClick={() => setShowCampaignDialog(true)}
                >
                  <Plus className="h-6 w-6" />
                  <span>Tambah Kampanye Baru</span>
                </Button>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>

      {/* Points Rule Dialog */}
      <Dialog
        open={showPointsRuleDialog}
        onOpenChange={setShowPointsRuleDialog}
      >
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Aturan Poin Loyalitas</DialogTitle>
            <DialogDescription>
              Atur bagaimana pelanggan mendapatkan dan menukarkan poin
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-2">
            <div className="space-y-2">
              <Label htmlFor="points-per-transaction">
                Poin per Rp 1.000 transaksi
              </Label>
              <Input
                id="points-per-transaction"
                type="number"
                defaultValue="10"
              />
              <p className="text-xs text-gray-500">
                Jumlah poin yang didapatkan untuk setiap Rp 1.000 transaksi
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="points-expiry">Masa berlaku poin (bulan)</Label>
              <Input id="points-expiry" type="number" defaultValue="12" />
              <p className="text-xs text-gray-500">
                Poin akan kedaluwarsa setelah periode ini
              </p>
            </div>

            <Separator />

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="enable-birthday-bonus">Bonus ulang tahun</Label>
                <Switch id="enable-birthday-bonus" defaultChecked />
              </div>
              <p className="text-xs text-gray-500">
                Berikan bonus poin pada bulan ulang tahun pelanggan
              </p>
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="enable-referral-bonus">Bonus referral</Label>
                <Switch id="enable-referral-bonus" defaultChecked />
              </div>
              <p className="text-xs text-gray-500">
                Berikan bonus poin untuk pelanggan yang mereferensikan pelanggan
                baru
              </p>
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="enable-first-order-bonus">
                  Bonus order pertama
                </Label>
                <Switch id="enable-first-order-bonus" defaultChecked />
              </div>
              <p className="text-xs text-gray-500">
                Berikan bonus poin untuk order pertama pelanggan baru
              </p>
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowPointsRuleDialog(false)}
            >
              Batal
            </Button>
            <Button onClick={() => setShowPointsRuleDialog(false)}>
              Simpan
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Campaign Dialog */}
      <Dialog open={showCampaignDialog} onOpenChange={setShowCampaignDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Tambah Kampanye Baru</DialogTitle>
            <DialogDescription>
              Buat kampanye loyalitas baru untuk pelanggan Anda
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-2">
            <div className="space-y-2">
              <Label htmlFor="campaign-name">Nama Kampanye</Label>
              <Input id="campaign-name" placeholder="Masukkan nama kampanye" />
            </div>

            <div className="space-y-2">
              <Label htmlFor="campaign-description">Deskripsi</Label>
              <Textarea
                id="campaign-description"
                placeholder="Masukkan deskripsi kampanye"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="start-date">Tanggal Mulai</Label>
                <Input id="start-date" type="date" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="end-date">Tanggal Selesai</Label>
                <Input id="end-date" type="date" />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="target-tier">Target Level</Label>
              <Select defaultValue="all">
                <SelectTrigger id="target-tier">
                  <SelectValue placeholder="Pilih target level" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Level</SelectItem>
                  <SelectItem value="bronze">Bronze</SelectItem>
                  <SelectItem value="silver">Silver</SelectItem>
                  <SelectItem value="gold">Gold</SelectItem>
                  <SelectItem value="platinum">Platinum</SelectItem>
                  <SelectItem value="gold,platinum">Gold & Platinum</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="campaign-type">Jenis Kampanye</Label>
              <Select defaultValue="multiplier">
                <SelectTrigger id="campaign-type">
                  <SelectValue placeholder="Pilih jenis kampanye" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="multiplier">Pengganda Poin</SelectItem>
                  <SelectItem value="bonus">Bonus Poin</SelectItem>
                  <SelectItem value="discount">Diskon Reward</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="multiplier">Pengganda Poin</Label>
              <Select defaultValue="2">
                <SelectTrigger id="multiplier">
                  <SelectValue placeholder="Pilih pengganda" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1.5">1.5x</SelectItem>
                  <SelectItem value="2">2x</SelectItem>
                  <SelectItem value="3">3x</SelectItem>
                  <SelectItem value="5">5x</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowCampaignDialog(false)}
            >
              Batal
            </Button>
            <Button onClick={() => setShowCampaignDialog(false)}>Simpan</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Analytics Dialog */}
      <Dialog open={showAnalyticsDialog} onOpenChange={setShowAnalyticsDialog}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Analitik Program Loyalitas</DialogTitle>
            <DialogDescription>
              Lihat statistik dan performa program loyalitas Anda
            </DialogDescription>
          </DialogHeader>

          <div className="py-2">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <Card className="p-4">
                <div className="flex flex-col items-center text-center">
                  <Star className="h-8 w-8 text-yellow-500 mb-2" />
                  <h3 className="text-2xl font-bold">
                    {mockLoyaltyProgram.analytics.totalPoints.toLocaleString()}
                  </h3>
                  <p className="text-sm text-gray-500">Total Poin Diberikan</p>
                </div>
              </Card>
              <Card className="p-4">
                <div className="flex flex-col items-center text-center">
                  <Gift className="h-8 w-8 text-purple-500 mb-2" />
                  <h3 className="text-2xl font-bold">
                    {mockLoyaltyProgram.analytics.pointsRedeemed.toLocaleString()}
                  </h3>
                  <p className="text-sm text-gray-500">Total Poin Ditukarkan</p>
                </div>
              </Card>
              <Card className="p-4">
                <div className="flex flex-col items-center text-center">
                  <TrendingUp className="h-8 w-8 text-green-500 mb-2" />
                  <h3 className="text-2xl font-bold">
                    {mockLoyaltyProgram.analytics.redemptionRate}%
                  </h3>
                  <p className="text-sm text-gray-500">Tingkat Penukaran</p>
                </div>
              </Card>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card className="p-4">
                <h3 className="font-medium mb-3">Distribusi Level</h3>
                <div className="space-y-3">
                  {mockLoyaltyProgram.analytics.tierDistribution.map((tier) => (
                    <div key={tier.tier} className="space-y-1">
                      <div className="flex justify-between text-sm">
                        <span>{tier.tier}</span>
                        <span>{tier.percentage}%</span>
                      </div>
                      <Progress value={tier.percentage} className="h-2" />
                    </div>
                  ))}
                </div>
              </Card>

              <Card className="p-4">
                <h3 className="font-medium mb-3">Reward Terpopuler</h3>
                <div className="space-y-3">
                  {mockLoyaltyProgram.analytics.popularRewards.map(
                    (reward, index) => (
                      <div key={reward.name} className="flex items-center">
                        <div className="w-6 text-center font-medium">
                          {index + 1}
                        </div>
                        <div className="flex-1 ml-2">{reward.name}</div>
                        <div className="text-sm text-gray-500">
                          {reward.redemptions} penukaran
                        </div>
                      </div>
                    )
                  )}
                </div>
              </Card>
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowAnalyticsDialog(false)}
            >
              Tutup
            </Button>
            <Button onClick={() => router.push('/akun/loyalty/analytics')}>
              Lihat Laporan Lengkap
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Referral Dialog */}
      <Dialog open={showReferralDialog} onOpenChange={setShowReferralDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Bagikan Kode Referral</DialogTitle>
            <DialogDescription>
              Ajak teman Anda bergabung dan dapatkan poin!
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-2">
            <div className="bg-gray-100 p-3 rounded-md text-center">
              <p className="text-sm text-gray-500 mb-1">Kode Referral Anda</p>
              <p className="text-xl font-bold tracking-wider">FELIS2024</p>
            </div>

            <div className="space-y-2">
              <Label>Bagikan melalui</Label>
              <div className="flex flex-col space-y-2">
                <div className="flex items-center space-x-2">
                  <input
                    type="radio"
                    id="whatsapp"
                    name="referralMethod"
                    value="whatsapp"
                    checked={referralMethod === 'whatsapp'}
                    onChange={() => setReferralMethod('whatsapp')}
                    className="h-4 w-4"
                  />
                  <Label htmlFor="whatsapp" className="flex items-center">
                    <MessageSquare className="h-4 w-4 mr-2 text-green-600" />{' '}
                    WhatsApp
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <input
                    type="radio"
                    id="email"
                    name="referralMethod"
                    value="email"
                    checked={referralMethod === 'email'}
                    onChange={() => setReferralMethod('email')}
                    className="h-4 w-4"
                  />
                  <Label htmlFor="email" className="flex items-center">
                    <Mail className="h-4 w-4 mr-2 text-blue-600" /> Email
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <input
                    type="radio"
                    id="sms"
                    name="referralMethod"
                    value="sms"
                    checked={referralMethod === 'sms'}
                    onChange={() => setReferralMethod('sms')}
                    className="h-4 w-4"
                  />
                  <Label htmlFor="sms" className="flex items-center">
                    <Smartphone className="h-4 w-4 mr-2 text-purple-600" /> SMS
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <input
                    type="radio"
                    id="copy"
                    name="referralMethod"
                    value="copy"
                    checked={referralMethod === 'copy'}
                    onChange={() => setReferralMethod('copy')}
                    className="h-4 w-4"
                  />
                  <Label htmlFor="copy" className="flex items-center">
                    <Copy className="h-4 w-4 mr-2 text-gray-600" /> Salin Tautan
                  </Label>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="referral-message">Pesan (opsional)</Label>
              <Textarea
                id="referral-message"
                placeholder="Tambahkan pesan personal..."
                defaultValue="Halo! Saya menggunakan layanan Felis Laundry dan sangat puas. Gunakan kode FELIS2024 untuk mendapatkan diskon 10% untuk order pertama Anda!"
              />
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowReferralDialog(false)}
            >
              Batal
            </Button>
            <Button onClick={() => setShowReferralDialog(false)}>
              {referralMethod === 'copy' ? 'Salin Tautan' : 'Bagikan'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
