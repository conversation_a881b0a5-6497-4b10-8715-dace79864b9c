'use client';

import Image from 'next/image';
import Link from 'next/link';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { ArrowLeft, Mail } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  ForgotPasswordFormValues,
  forgotPasswordSchema,
} from '@/lib/form-schema';
import { useAuth } from '@/lib/auth-context';

export default function ForgotPasswordPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [emailSent, setEmailSent] = useState(false);
  const router = useRouter();
  const { forgotPassword, isAuthenticated, isLoading: authLoading } = useAuth();

  const form = useForm<ForgotPasswordFormValues>({
    resolver: zodResolver(forgotPasswordSchema),
    defaultValues: {
      email: '',
    },
  });

  // Redirect if user is already authenticated
  useEffect(() => {
    if (isAuthenticated && !authLoading) {
      router.push('/');
    }
  }, [isAuthenticated, authLoading, router]);

  // Loading state saat auth sedang dicek
  if (authLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <p>Loading...</p>
      </div>
    );
  }

  // Early return jika user sudah authenticated
  if (isAuthenticated) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <p>Redirecting...</p>
      </div>
    );
  }

  async function onSubmit(values: ForgotPasswordFormValues) {
    setIsLoading(true);

    try {
      await forgotPassword(values.email);
      setEmailSent(true);
      toast.success(
        'Email reset password telah dikirim! Silakan cek inbox Anda.'
      );
    } catch (error: any) {
      console.error('Error sending reset email:', error);
      toast.error(error.message || 'Gagal mengirim email reset password');
    } finally {
      setIsLoading(false);
    }
  }

  if (emailSent) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="w-full max-w-md px-6">
          <div className="text-center mb-6">
            <Image
              src="/logo.png"
              alt="SuperLaundry Logo"
              width={200}
              height={80}
              className="mx-auto mb-4"
            />
          </div>

          <div className="text-center">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Mail className="w-8 h-8 text-green-600" />
            </div>

            <h1 className="text-2xl font-bold text-center mb-2">
              Email Terkirim!
            </h1>

            <p className="text-center text-gray-600 mb-6">
              Kami telah mengirim link reset password ke email Anda. Silakan cek
              inbox dan ikuti instruksi untuk mengatur ulang password.
            </p>

            <div className="space-y-4">
              <p className="text-sm text-gray-500">
                Tidak menerima email? Cek folder spam atau
              </p>

              <Button
                variant="outline"
                onClick={() => {
                  setEmailSent(false);
                  form.reset();
                }}
                className="w-full"
              >
                Kirim Ulang Email
              </Button>

              <div className="text-center">
                <Link
                  href="/auth/login"
                  className="text-blue-600 hover:text-blue-500 text-sm"
                >
                  Kembali ke Login
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="w-full max-w-md px-6">
        <div className="text-center mb-6">
          <Image
            src="/logo.png"
            alt="SuperLaundry Logo"
            width={200}
            height={80}
            className="mx-auto mb-4"
          />
        </div>

        <div className="mb-6">
          <Link
            href="/auth/login"
            className="inline-flex items-center text-blue-600 hover:text-blue-500 text-sm font-medium"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Kembali ke Login
          </Link>
        </div>

        <h1 className="text-2xl font-bold text-center mb-2">Lupa Password?</h1>

        <p className="text-center text-gray-600 mb-6">
          Masukkan alamat email Anda dan kami akan mengirimkan link untuk
          mengatur ulang password.
        </p>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input
                      type="email"
                      placeholder="Masukkan alamat email"
                      disabled={isLoading}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? 'Mengirim...' : 'Kirim Link Reset Password'}
            </Button>
          </form>
        </Form>

        <div className="mt-6 text-center">
          <p className="text-gray-600">
            Ingat password Anda?{' '}
            <Link
              href="/auth/login"
              className="text-blue-600 hover:text-blue-500 font-medium"
            >
              Login di sini
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}
