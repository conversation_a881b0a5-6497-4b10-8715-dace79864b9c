import { api } from '../auth-context';

export interface Perfume {
  id: number;
  name: string;
  description?: string;
  brand?: string;
  scent?: string;
  isActive: boolean;
  outletId: number;
  isPopular: boolean;
  isNew: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CreatePerfumeRequest {
  name: string;
  description?: string;
  brand?: string;
  scent?: string;
  isActive?: boolean;
  outletId: number;
  isPopular?: boolean;
  isNew?: boolean;
}

export interface UpdatePerfumeRequest {
  name?: string;
  description?: string;
  brand?: string;
  scent?: string;
  isActive?: boolean;
  outletId: number;
  isPopular?: boolean;
  isNew?: boolean;
}

export interface GetPerfumesParams {
  search?: string;
  name?: string;
  brand?: string;
  scent?: string;
  isActive?: boolean;
  sortBy?: string;
  limit?: number;
  page?: number;
  outletId?: number;
  isPopular?: boolean;
  isNew?: boolean;
}

export interface GetPerfumesResponse {
  results: Perfume[];
  page: number;
  limit: number;
  totalPages: number;
  totalResults: number;
}

export const perfumeAPI = {
  // Get all perfumes with pagination and filtering
  async getPerfumes(
    params?: GetPerfumesParams & { outletId: number }
  ): Promise<GetPerfumesResponse> {
    const response = await api.get('/perfumes', { params });
    return response.data;
  },

  // Get single perfume by ID
  async getPerfume(id: number, outletId: number): Promise<Perfume> {
    const response = await api.get(`/perfumes/${id}`, {
      params: { outletId },
    });
    return response.data;
  },

  // Create new perfume
  async createPerfume(
    data: CreatePerfumeRequest & { outletId: number }
  ): Promise<Perfume> {
    const response = await api.post('/perfumes', data);
    return response.data;
  },

  // Update perfume
  async updatePerfume(
    id: number,
    data: UpdatePerfumeRequest & { outletId: number }
  ): Promise<Perfume> {
    const response = await api.patch(`/perfumes/${id}`, data);
    return response.data;
  },

  // Delete perfume
  async deletePerfume(id: number, outletId: number): Promise<void> {
    await api.delete(`/perfumes/${id}`, {
      data: { outletId },
    });
  },
};
