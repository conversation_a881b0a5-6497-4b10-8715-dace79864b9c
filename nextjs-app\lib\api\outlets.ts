import { api } from '../auth-context';

export interface Outlet {
  id: number;
  name: string;
  address: string;
  province: string;
  city: string;
  timezone: string;
  phone: string;
  latitude?: number;
  longitude?: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  owner: {
    id: number;
    name: string;
    email: string;
  };
  copiedFrom?: {
    id: number;
    name: string;
  };
  _count?: {
    employees: number;
    orders: number;
    customers: number;
  };
}

export interface CreateOutletRequest {
  name: string;
  address: string;
  province: string;
  city: string;
  timezone?: string;
  phone: string;
  latitude?: number;
  longitude?: number;
  copiedFromId?: number;
}

export interface UpdateOutletRequest {
  name?: string;
  address?: string;
  province?: string;
  city?: string;
  timezone?: string;
  phone?: string;
  latitude?: number;
  longitude?: number;
  isActive?: boolean;
}

export interface OutletStats {
  totalOrders: number;
  totalCustomers: number;
  totalRevenue: number;
  pendingOrders: number;
  completedOrders: number;
  employeeCount: number;
}

export interface OutletService {
  id: number;
  price: number;
  isAvailable: boolean;
  estimationHours?: number;
  service: {
    id: number;
    name: string;
    description?: string;
    unit: string;
  };
}

export interface OutletsResponse {
  results: Outlet[];
  page: number;
  limit: number;
  totalPages: number;
  totalResults: number;
}

class OutletAPI {
  async getOutlets(params?: {
    name?: string;
    city?: string;
    province?: string;
    isActive?: boolean;
    page?: number;
    limit?: number;
    sortBy?: string;
    sortType?: 'asc' | 'desc';
  }): Promise<OutletsResponse> {
    const response = await api.get('/outlets', { params });
    return response.data;
  }

  async getOutlet(id: number): Promise<Outlet> {
    const response = await api.get(`/outlets/${id}`);
    return response.data;
  }

  async createOutlet(data: CreateOutletRequest): Promise<Outlet> {
    const response = await api.post('/outlets', data);
    return response.data;
  }

  async updateOutlet(id: number, data: UpdateOutletRequest): Promise<Outlet> {
    const response = await api.patch(`/outlets/${id}`, data);
    return response.data;
  }

  async deleteOutlet(id: number): Promise<void> {
    await api.delete(`/outlets/${id}`);
  }

  async getOutletServices(id: number): Promise<OutletService[]> {
    const response = await api.get(`/outlets/${id}/services`);
    return response.data;
  }

  async copyServicesFromOutlet(
    targetId: number,
    sourceId: number
  ): Promise<any> {
    const response = await api.post(`/outlets/${targetId}/copy-services`, {
      sourceOutletId: sourceId,
    });
    return response.data;
  }

  async getOutletStats(id: number): Promise<OutletStats> {
    const response = await api.get(`/outlets/${id}/stats`);
    return response.data;
  }
}

export const outletAPI = new OutletAPI();
