import httpStatus from 'http-status';
import pick from '../utils/pick';
import ApiError from '../utils/ApiError';
import catchAsync from '../utils/catchAsync';
import serviceCategoryService from '../services/service-category.service';
import { User } from '@prisma/client';

const createServiceCategory = catchAsync(async (req, res) => {
  if (!req.user) {
    throw new ApiError(httpStatus.UNAUTHORIZED, 'User not authenticated');
  }

  const serviceCategory = await serviceCategoryService.createServiceCategory(
    req.body,
    req.user as User
  );
  res.status(httpStatus.CREATED).send(serviceCategory);
});

const getServiceCategories = catchAsync(async (req, res) => {
  if (!req.user) {
    throw new ApiError(httpStatus.UNAUTHORIZED, 'User not authenticated');
  }

  const filter = pick(req.query, ['outletId', 'search', 'estimationHours']);
  const options = pick(req.query, ['sortBy', 'limit', 'page']);

  const result = await serviceCategoryService.queryServiceCategories(
    filter,
    options,
    req.user as User
  );
  res.send(result);
});

const getServiceCategory = catchAsync(async (req, res) => {
  if (!req.user) {
    throw new ApiError(httpStatus.UNAUTHORIZED, 'User not authenticated');
  }

  const serviceCategoryId = parseInt(req.params.id);
  const outletId = parseInt(req.query.outletId as string);

  const serviceCategory = await serviceCategoryService.getServiceCategoryById(
    serviceCategoryId,
    req.user as User,
    outletId
  );
  if (!serviceCategory) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Service category not found');
  }
  res.send(serviceCategory);
});

const updateServiceCategory = catchAsync(async (req, res) => {
  if (!req.user) {
    throw new ApiError(httpStatus.UNAUTHORIZED, 'User not authenticated');
  }

  const serviceCategoryId = parseInt(req.params.id);
  const serviceCategory = await serviceCategoryService.updateServiceCategoryById(
    serviceCategoryId,
    req.body,
    req.user as User
  );
  res.send(serviceCategory);
});

const deleteServiceCategory = catchAsync(async (req, res) => {
  if (!req.user) {
    throw new ApiError(httpStatus.UNAUTHORIZED, 'User not authenticated');
  }

  const serviceCategoryId = parseInt(req.params.id);
  const outletId = parseInt(req.query.outletId as string);

  await serviceCategoryService.deleteServiceCategoryById(
    serviceCategoryId,
    req.user as User,
    outletId
  );
  res.status(httpStatus.NO_CONTENT).send();
});

const getServiceCategoriesForSelect = catchAsync(async (req, res) => {
  if (!req.user) {
    throw new ApiError(httpStatus.UNAUTHORIZED, 'User not authenticated');
  }

  const outletId = parseInt(req.query.outletId as string);
  const categories = await serviceCategoryService.getServiceCategoriesForSelect(
    req.user as User,
    outletId
  );
  res.send(categories);
});

const queryCount = catchAsync(async (req, res) => {
  if (!req.user) {
    throw new ApiError(httpStatus.UNAUTHORIZED, 'User not authenticated');
  }

  const count = await serviceCategoryService.queryCount(
    req.user as User,
    parseInt(req.query.outletId as string)
  );
  res.send(count);
});
export default {
  createServiceCategory,
  getServiceCategories,
  getServiceCategory,
  updateServiceCategory,
  deleteServiceCategory,
  getServiceCategoriesForSelect,
  queryCount
};
