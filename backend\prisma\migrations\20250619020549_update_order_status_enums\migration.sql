/*
  Warnings:

  - The values [PROCESSING,READY] on the enum `OrderItemStatus` will be removed. If these variants are still used in the database, this will fail.
  - The values [WASHING,DRYING,IRONING,DELIVERED] on the enum `OrderStatus` will be removed. If these variants are still used in the database, this will fail.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "OrderItemStatus_new" AS ENUM ('PENDING', 'WASHING', 'DRYING', 'IRONING', 'PACKING', 'COMPLETED', 'CANCELLED');
ALTER TABLE "OrderItem" ALTER COLUMN "status" DROP DEFAULT;
ALTER TABLE "OrderItem" ALTER COLUMN "status" TYPE "OrderItemStatus_new" USING ("status"::text::"OrderItemStatus_new");
ALTER TABLE "OrderItemStatusHistory" ALTER COLUMN "previousStatus" TYPE "OrderItemStatus_new" USING ("previousStatus"::text::"OrderItemStatus_new");
ALTER TABLE "OrderItemStatusHistory" ALTER COLUMN "newStatus" TYPE "OrderItemStatus_new" USING ("newStatus"::text::"OrderItemStatus_new");
ALTER TYPE "OrderItemStatus" RENAME TO "OrderItemStatus_old";
ALTER TYPE "OrderItemStatus_new" RENAME TO "OrderItemStatus";
DROP TYPE "OrderItemStatus_old";
ALTER TABLE "OrderItem" ALTER COLUMN "status" SET DEFAULT 'PENDING';
COMMIT;

-- AlterEnum
BEGIN;
CREATE TYPE "OrderStatus_new" AS ENUM ('KONFIRMASI', 'PICKUP', 'PENDING', 'PROCESSING', 'READY', 'READY_FOR_PICKUP', 'COMPLETED', 'CANCELLED');
ALTER TABLE "Order" ALTER COLUMN "status" DROP DEFAULT;
ALTER TABLE "Order" ALTER COLUMN "status" TYPE "OrderStatus_new" USING ("status"::text::"OrderStatus_new");
ALTER TABLE "OrderStatusHistory" ALTER COLUMN "previousStatus" TYPE "OrderStatus_new" USING ("previousStatus"::text::"OrderStatus_new");
ALTER TABLE "OrderStatusHistory" ALTER COLUMN "newStatus" TYPE "OrderStatus_new" USING ("newStatus"::text::"OrderStatus_new");
ALTER TYPE "OrderStatus" RENAME TO "OrderStatus_old";
ALTER TYPE "OrderStatus_new" RENAME TO "OrderStatus";
DROP TYPE "OrderStatus_old";
ALTER TABLE "Order" ALTER COLUMN "status" SET DEFAULT 'PENDING';
COMMIT;
