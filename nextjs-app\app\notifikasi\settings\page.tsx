"use client"

import { useState } from "react"
import Link from "next/link"
import { ArrowLeft, Save } from "lucide-react"
import { useRouter } from "next/navigation"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"

export default function NotificationSettingsPage() {
  const router = useRouter()
  const [settings, setSettings] = useState({
    orderCreated: true,
    orderStatusChanged: true,
    orderCompleted: true,
    paymentReceived: true,
    newCustomer: true,
    inventoryAlert: true,
    dailySummary: false,
    weeklySummary: true,
    monthlySummary: true,
    marketingUpdates: false,
    pushNotifications: true,
    emailNotifications: true,
    smsNotifications: false,
  })

  const handleToggle = (setting: string, checked: boolean) => {
    setSettings((prev) => ({ ...prev, [setting]: checked }))
  }

  const handleSave = () => {
    // In a real app, you would save the settings to your backend
    alert("Pengaturan notifikasi berhasil disimpan!")
    router.push("/notifikasi")
  }

  return (
    <div className="flex flex-col min-h-screen bg-slate-50">
      <header className="p-4 flex justify-between items-center bg-white sticky top-0 z-10 shadow-sm">
        <div className="flex items-center">
          <Link href="/notifikasi" className="mr-3">
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <h1 className="text-lg font-semibold">Pengaturan Notifikasi</h1>
        </div>
        <Button onClick={handleSave} className="bg-blue-500 hover:bg-blue-600">
          <Save className="h-4 w-4 mr-2" /> Simpan
        </Button>
      </header>

      <main className="flex-1 p-4 pb-20">
        <Card className="p-4 mb-4">
          <h2 className="font-medium mb-3">Notifikasi Order</h2>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label htmlFor="orderCreated" className="cursor-pointer">
                Order Baru
                <p className="text-sm font-normal text-gray-500">Dapatkan notifikasi saat order baru dibuat</p>
              </Label>
              <Switch
                id="orderCreated"
                checked={settings.orderCreated}
                onCheckedChange={(checked) => handleToggle("orderCreated", checked)}
              />
            </div>
            <Separator />
            <div className="flex items-center justify-between">
              <Label htmlFor="orderStatusChanged" className="cursor-pointer">
                Perubahan Status Order
                <p className="text-sm font-normal text-gray-500">Dapatkan notifikasi saat status order berubah</p>
              </Label>
              <Switch
                id="orderStatusChanged"
                checked={settings.orderStatusChanged}
                onCheckedChange={(checked) => handleToggle("orderStatusChanged", checked)}
              />
            </div>
            <Separator />
            <div className="flex items-center justify-between">
              <Label htmlFor="orderCompleted" className="cursor-pointer">
                Order Selesai
                <p className="text-sm font-normal text-gray-500">
                  Dapatkan notifikasi saat order selesai dan siap diambil/diantar
                </p>
              </Label>
              <Switch
                id="orderCompleted"
                checked={settings.orderCompleted}
                onCheckedChange={(checked) => handleToggle("orderCompleted", checked)}
              />
            </div>
          </div>
        </Card>

        <Card className="p-4 mb-4">
          <h2 className="font-medium mb-3">Notifikasi Keuangan & Pelanggan</h2>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label htmlFor="paymentReceived" className="cursor-pointer">
                Pembayaran Diterima
                <p className="text-sm font-normal text-gray-500">Dapatkan notifikasi saat pembayaran diterima</p>
              </Label>
              <Switch
                id="paymentReceived"
                checked={settings.paymentReceived}
                onCheckedChange={(checked) => handleToggle("paymentReceived", checked)}
              />
            </div>
            <Separator />
            <div className="flex items-center justify-between">
              <Label htmlFor="newCustomer" className="cursor-pointer">
                Pelanggan Baru
                <p className="text-sm font-normal text-gray-500">Dapatkan notifikasi saat pelanggan baru mendaftar</p>
              </Label>
              <Switch
                id="newCustomer"
                checked={settings.newCustomer}
                onCheckedChange={(checked) => handleToggle("newCustomer", checked)}
              />
            </div>
            <Separator />
            <div className="flex items-center justify-between">
              <Label htmlFor="inventoryAlert" className="cursor-pointer">
                Peringatan Inventaris
                <p className="text-sm font-normal text-gray-500">Dapatkan notifikasi saat stok barang menipis</p>
              </Label>
              <Switch
                id="inventoryAlert"
                checked={settings.inventoryAlert}
                onCheckedChange={(checked) => handleToggle("inventoryAlert", checked)}
              />
            </div>
          </div>
        </Card>

        <Card className="p-4 mb-4">
          <h2 className="font-medium mb-3">Laporan & Ringkasan</h2>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label htmlFor="dailySummary" className="cursor-pointer">
                Ringkasan Harian
                <p className="text-sm font-normal text-gray-500">
                  Dapatkan ringkasan harian tentang aktivitas bisnis Anda
                </p>
              </Label>
              <Switch
                id="dailySummary"
                checked={settings.dailySummary}
                onCheckedChange={(checked) => handleToggle("dailySummary", checked)}
              />
            </div>
            <Separator />
            <div className="flex items-center justify-between">
              <Label htmlFor="weeklySummary" className="cursor-pointer">
                Ringkasan Mingguan
                <p className="text-sm font-normal text-gray-500">
                  Dapatkan ringkasan mingguan tentang aktivitas bisnis Anda
                </p>
              </Label>
              <Switch
                id="weeklySummary"
                checked={settings.weeklySummary}
                onCheckedChange={(checked) => handleToggle("weeklySummary", checked)}
              />
            </div>
            <Separator />
            <div className="flex items-center justify-between">
              <Label htmlFor="monthlySummary" className="cursor-pointer">
                Ringkasan Bulanan
                <p className="text-sm font-normal text-gray-500">
                  Dapatkan ringkasan bulanan tentang aktivitas bisnis Anda
                </p>
              </Label>
              <Switch
                id="monthlySummary"
                checked={settings.monthlySummary}
                onCheckedChange={(checked) => handleToggle("monthlySummary", checked)}
              />
            </div>
            <Separator />
            <div className="flex items-center justify-between">
              <Label htmlFor="marketingUpdates" className="cursor-pointer">
                Update Marketing
                <p className="text-sm font-normal text-gray-500">
                  Dapatkan tips dan strategi untuk meningkatkan bisnis Anda
                </p>
              </Label>
              <Switch
                id="marketingUpdates"
                checked={settings.marketingUpdates}
                onCheckedChange={(checked) => handleToggle("marketingUpdates", checked)}
              />
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <h2 className="font-medium mb-3">Metode Penerimaan Notifikasi</h2>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label htmlFor="pushNotifications" className="cursor-pointer">
                Notifikasi Push
                <p className="text-sm font-normal text-gray-500">Terima notifikasi langsung di perangkat Anda</p>
              </Label>
              <Switch
                id="pushNotifications"
                checked={settings.pushNotifications}
                onCheckedChange={(checked) => handleToggle("pushNotifications", checked)}
              />
            </div>
            <Separator />
            <div className="flex items-center justify-between">
              <Label htmlFor="emailNotifications" className="cursor-pointer">
                Email
                <p className="text-sm font-normal text-gray-500">Terima notifikasi melalui email</p>
              </Label>
              <Switch
                id="emailNotifications"
                checked={settings.emailNotifications}
                onCheckedChange={(checked) => handleToggle("emailNotifications", checked)}
              />
            </div>
            <Separator />
            <div className="flex items-center justify-between">
              <Label htmlFor="smsNotifications" className="cursor-pointer">
                SMS
                <p className="text-sm font-normal text-gray-500">Terima notifikasi melalui SMS</p>
              </Label>
              <Switch
                id="smsNotifications"
                checked={settings.smsNotifications}
                onCheckedChange={(checked) => handleToggle("smsNotifications", checked)}
              />
            </div>
          </div>
        </Card>
      </main>
    </div>
  )
}
