import httpStatus from 'http-status';
import catchAsync from '../utils/catchAsync';
import { authService, userService, tokenService, emailService } from '../services';
import phoneVerificationService from '../services/phoneVerification.service';
import emailVerificationService from '../services/emailVerification.service';
import exclude from '../utils/exclude';
import { Role, User } from '@prisma/client';
import logger from '../config/logger';
import ApiError from '../utils/ApiError';

const register = catchAsync(async (req, res) => {
  const { email, password, name, phone } = req.body;
  const clientIP = req.ip || req.connection.remoteAddress || req.headers['x-forwarded-for'];
  const userAgent = req.headers['user-agent'];

  logger.info(
    `[AUTH] Registration attempt - Email: ${email}, Name: ${name}, Phone: ${phone}, IP: ${clientIP}, UserAgent: ${userAgent}`
  );

  try {
    const user = await userService.createUser(email, password, name, Role.OWNER, phone);
    const userWithoutPassword = exclude(user, ['password', 'createdAt', 'updatedAt']);
    const tokens = await tokenService.generateAuthTokens(user);

    logger.info(
      `[AUTH] Registration successful - UserID: ${user.id}, Email: ${email}, Role: ${Role.OWNER}, IP: ${clientIP}`
    );
    logger.debug(
      `[AUTH] Auth tokens generated for new user - UserID: ${user.id}, AccessToken expires: ${
        tokens.access.expires
      }, RefreshToken expires: ${tokens.refresh?.expires || 'N/A'}`
    );

    res.status(httpStatus.CREATED).send({ user: userWithoutPassword, tokens });
  } catch (error: any) {
    logger.error(
      `[AUTH] Registration failed - Email: ${email}, Error: ${error.message}, IP: ${clientIP}`
    );
    throw error;
  }
});

const login = catchAsync(async (req, res) => {
  const { email, password } = req.body;
  const clientIP = req.ip || req.connection.remoteAddress || req.headers['x-forwarded-for'];
  const userAgent = req.headers['user-agent'];

  logger.info(`[AUTH] Login attempt - Email: ${email}, IP: ${clientIP}, UserAgent: ${userAgent}`);

  try {
    const user = await authService.loginUserWithEmailAndPassword(email, password);
    const tokens = await tokenService.generateAuthTokens(user);

    logger.info(
      `[AUTH] Login successful - UserID: ${user.id}, Email: ${email}, Role: ${user.role}, IP: ${clientIP}`
    );
    logger.debug(
      `[AUTH] Auth tokens generated for login - UserID: ${user.id}, AccessToken expires: ${
        tokens.access.expires
      }, RefreshToken expires: ${tokens.refresh?.expires || 'N/A'}`
    );

    // Log additional security info
    if (!user.isEmailVerified) {
      logger.warn(
        `[AUTH] User logged in with unverified email - UserID: ${user.id}, Email: ${email}, IP: ${clientIP}`
      );
    }
    if (!user.isPhoneVerified) {
      logger.warn(
        `[AUTH] User logged in with unverified phone - UserID: ${user.id}, Phone: ${user.phone}, IP: ${clientIP}`
      );
    }

    res.send({ user, tokens });
  } catch (error: any) {
    logger.warn(`[AUTH] Login failed - Email: ${email}, Error: ${error.message}, IP: ${clientIP}`);
    throw error;
  }
});

const logout = catchAsync(async (req, res) => {
  const refreshToken = req.body.refreshToken;
  const clientIP = req.ip || req.connection.remoteAddress || req.headers['x-forwarded-for'];
  const userAgent = req.headers['user-agent'];
  const user = req.user as User;

  logger.info(
    `[AUTH] Logout attempt - UserID: ${
      user?.id || 'unknown'
    }, IP: ${clientIP}, UserAgent: ${userAgent}`
  );

  try {
    await authService.logout(refreshToken);
    logger.info(`[AUTH] Logout successful - UserID: ${user?.id || 'unknown'}, IP: ${clientIP}`);
    res.status(httpStatus.NO_CONTENT).send();
  } catch (error: any) {
    logger.error(
      `[AUTH] Logout failed - UserID: ${user?.id || 'unknown'}, Error: ${
        error.message
      }, IP: ${clientIP}`
    );
    throw error;
  }
});

const refreshTokens = catchAsync(async (req, res) => {
  const refreshToken = req.body.refreshToken;
  const clientIP = req.ip || req.connection.remoteAddress || req.headers['x-forwarded-for'];
  const userAgent = req.headers['user-agent'];

  logger.info(`[AUTH] Token refresh attempt - IP: ${clientIP}, UserAgent: ${userAgent}`);

  try {
    const tokens = await authService.refreshAuth(refreshToken);
    logger.info(
      `[AUTH] Token refresh successful - New AccessToken expires: ${
        tokens.access.expires
      }, New RefreshToken expires: ${tokens.refresh?.expires || 'N/A'}, IP: ${clientIP}`
    );
    res.send({ ...tokens });
  } catch (error: any) {
    logger.warn(`[AUTH] Token refresh failed - Error: ${error.message}, IP: ${clientIP}`);
    throw error;
  }
});

const forgotPassword = catchAsync(async (req, res) => {
  const email = req.body.email;
  const clientIP = req.ip || req.connection.remoteAddress || req.headers['x-forwarded-for'];
  const userAgent = req.headers['user-agent'];

  logger.info(
    `[AUTH] Forgot password request - Email: ${email}, IP: ${clientIP}, UserAgent: ${userAgent}`
  );

  try {
    const resetPasswordToken = await tokenService.generateResetPasswordToken(email);
    await emailService.sendResetPasswordEmail(email, resetPasswordToken);

    logger.info(`[AUTH] Password reset email sent - Email: ${email}, IP: ${clientIP}`);
    logger.debug(
      `[AUTH] Reset password token generated - Email: ${email}, Token expires: ${
        resetPasswordToken.split('.').length > 1 ? 'JWT format' : 'Unknown format'
      }, IP: ${clientIP}`
    );

    res.status(httpStatus.NO_CONTENT).send();
  } catch (error: any) {
    logger.error(
      `[AUTH] Forgot password failed - Email: ${email}, Error: ${error.message}, IP: ${clientIP}`
    );
    throw error;
  }
});

const resetPassword = catchAsync(async (req, res) => {
  const token = req.query.token as string;
  const newPassword = req.body.password;
  const clientIP = req.ip || req.connection.remoteAddress || req.headers['x-forwarded-for'];
  const userAgent = req.headers['user-agent'];

  logger.info(`[AUTH] Password reset attempt - IP: ${clientIP}, UserAgent: ${userAgent}`);

  try {
    await authService.resetPassword(token, newPassword);
    logger.info(`[AUTH] Password reset successful - IP: ${clientIP}`);
    res.status(httpStatus.NO_CONTENT).send();
  } catch (error: any) {
    logger.warn(`[AUTH] Password reset failed - Error: ${error.message}, IP: ${clientIP}`);
    throw error;
  }
});

const sendVerificationEmail = catchAsync(async (req, res) => {
  const user = req.user as User;
  const { method } = req.query;
  const clientIP = req.ip || req.connection.remoteAddress || req.headers['x-forwarded-for'];

  logger.info(
    `[AUTH] Send verification email request - UserID: ${user.id}, Email: ${user.email}, Method: ${
      method || 'token'
    }, IP: ${clientIP}`
  );

  try {
    if (method === 'otp') {
      // Send OTP
      const otp = await emailVerificationService.sendEmailVerificationOTP(user.email);
      await emailService.sendVerificationOTPEmail(user.email, otp);
      logger.info(
        `[AUTH] Verification OTP sent - UserID: ${user.id}, Email: ${user.email}, IP: ${clientIP}`
      );

      res.status(httpStatus.OK).json({
        message: 'Verification code sent successfully',
        method: 'otp'
      });
    } else {
      // Send JWT token (existing behavior)
      const verifyEmailToken = await tokenService.generateVerifyEmailToken(user);
      await emailService.sendVerificationEmail(user.email, verifyEmailToken);
      logger.info(
        `[AUTH] Verification email sent - UserID: ${user.id}, Email: ${user.email}, IP: ${clientIP}`
      );

      res.status(httpStatus.OK).json({
        message: 'Verification email sent successfully',
        method: 'token'
      });
    }
  } catch (error: any) {
    logger.error(
      `[AUTH] Send verification email failed - UserID: ${user.id}, Email: ${user.email}, Method: ${
        method || 'token'
      }, Error: ${error.message}, IP: ${clientIP}`
    );
    throw error;
  }
});

const verifyEmail = catchAsync(async (req, res) => {
  const { token, code } = req.body;
  const clientIP = req.ip || req.connection.remoteAddress || req.headers['x-forwarded-for'];
  const method = token ? 'token' : 'otp';

  logger.info(`[AUTH] Email verification attempt - Method: ${method}, IP: ${clientIP}`);

  if (token) {
    // Verify with JWT token
    try {
      await authService.verifyEmail(token);
      logger.info(`[AUTH] Email verification successful - Method: token, IP: ${clientIP}`);
      res.status(httpStatus.OK).json({
        message: 'Email verified successfully',
        method: 'token'
      });
    } catch (error: any) {
      logger.warn(
        `[AUTH] Email verification failed - Method: token, Error: ${error.message}, IP: ${clientIP}`
      );
      throw new ApiError(httpStatus.UNAUTHORIZED, 'Invalid or expired verification link');
    }
  } else if (code) {
    // Verify with OTP
    try {
      await emailVerificationService.verifyEmailOTPByCode(code);
      logger.info(
        `[AUTH] Email verification successful - Method: otp, Code: ${code.substring(
          0,
          2
        )}***, IP: ${clientIP}`
      );
      res.status(httpStatus.OK).json({
        message: 'Email verified successfully',
        method: 'otp'
      });
    } catch (error: any) {
      logger.warn(
        `[AUTH] Email verification failed - Method: otp, Code: ${code.substring(0, 2)}***, Error: ${
          error.message
        }, IP: ${clientIP}`
      );
      // Provide user-friendly error messages
      if (error.message === 'Invalid or expired verification code') {
        throw new ApiError(httpStatus.BAD_REQUEST, 'Invalid verification code');
      } else if (error.message === 'Verification code has expired') {
        throw new ApiError(httpStatus.BAD_REQUEST, 'Verification code has expired');
      } else if (error.message === 'Verification code has been used') {
        throw new ApiError(httpStatus.BAD_REQUEST, 'Verification code has been used');
      } else {
        throw new ApiError(httpStatus.BAD_REQUEST, 'Invalid verification code');
      }
    }
  }
});

const sendPhoneVerification = catchAsync(async (req, res) => {
  const user = req.user as User;
  const { phone } = req.body;
  const clientIP = req.ip || req.connection.remoteAddress || req.headers['x-forwarded-for'];

  // Use phone from request body or user's phone
  const phoneToVerify = phone || user.phone;

  logger.info(
    `[AUTH] Send phone verification request - UserID: ${user.id}, Phone: ${
      phoneToVerify ? phoneToVerify.substring(0, 4) + '***' : 'none'
    }, IP: ${clientIP}`
  );

  if (!phoneToVerify) {
    logger.warn(
      `[AUTH] Send phone verification failed - UserID: ${user.id}, Error: Phone number is required, IP: ${clientIP}`
    );
    return res.status(httpStatus.BAD_REQUEST).send({
      code: httpStatus.BAD_REQUEST,
      message: 'Phone number is required'
    });
  }

  try {
    await phoneVerificationService.sendPhoneVerificationCode(phoneToVerify);
    logger.info(
      `[AUTH] Phone verification code sent - UserID: ${user.id}, Phone: ${phoneToVerify.substring(
        0,
        4
      )}***, IP: ${clientIP}`
    );
    res.status(httpStatus.NO_CONTENT).send();
  } catch (error: any) {
    logger.error(
      `[AUTH] Send phone verification failed - UserID: ${user.id}, Phone: ${phoneToVerify.substring(
        0,
        4
      )}***, Error: ${error.message}, IP: ${clientIP}`
    );
    throw error;
  }
});

const verifyPhone = catchAsync(async (req, res) => {
  const user = req.user as User;
  const { phone, code } = req.body;
  const clientIP = req.ip || req.connection.remoteAddress || req.headers['x-forwarded-for'];

  // Use phone from request body or user's phone
  const phoneToVerify = phone || user.phone;

  logger.info(
    `[AUTH] Phone verification attempt - UserID: ${user.id}, Phone: ${
      phoneToVerify ? phoneToVerify.substring(0, 4) + '***' : 'none'
    }, Code: ${code ? code.substring(0, 2) + '***' : 'none'}, IP: ${clientIP}`
  );

  if (!phoneToVerify || !code) {
    logger.warn(
      `[AUTH] Phone verification failed - UserID: ${user.id}, Error: Phone number and verification code are required, IP: ${clientIP}`
    );
    return res.status(httpStatus.BAD_REQUEST).send({
      code: httpStatus.BAD_REQUEST,
      message: 'Phone number and verification code are required'
    });
  }

  try {
    await phoneVerificationService.verifyPhoneCode(phoneToVerify, code, user.id);
    logger.info(
      `[AUTH] Phone verification successful - UserID: ${user.id}, Phone: ${phoneToVerify.substring(
        0,
        4
      )}***, IP: ${clientIP}`
    );
    res.status(httpStatus.NO_CONTENT).send();
  } catch (error: any) {
    logger.warn(
      `[AUTH] Phone verification failed - UserID: ${user.id}, Phone: ${phoneToVerify.substring(
        0,
        4
      )}***, Code: ${code.substring(0, 2)}***, Error: ${error.message}, IP: ${clientIP}`
    );
    throw error;
  }
});

export default {
  register,
  login,
  logout,
  refreshTokens,
  forgotPassword,
  resetPassword,
  sendVerificationEmail,
  verifyEmail,
  sendPhoneVerification,
  verifyPhone
};
