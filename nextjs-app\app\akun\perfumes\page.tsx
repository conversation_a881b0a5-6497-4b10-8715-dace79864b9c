'use client';

import { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import {
  ArrowLeft,
  Plus,
  Search,
  Edit,
  Trash2,
  AlertCircle,
} from 'lucide-react';
import { useRouter } from 'next/navigation';

import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { usePerfumes, useDeletePerfume } from '@/hooks/usePerfumes';

export default function PerfumesPage() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');
  const [debouncedSearch, setDebouncedSearch] = useState('');
  const [showInactive, setShowInactive] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedPerfume, setSelectedPerfume] = useState<any>(null);
  const debounceRef = useRef<NodeJS.Timeout | null>(null);

  // Debounce searchQuery
  useEffect(() => {
    if (debounceRef.current) clearTimeout(debounceRef.current);
    debounceRef.current = setTimeout(() => {
      setDebouncedSearch(searchQuery);
    }, 400);
    return () => {
      if (debounceRef.current) clearTimeout(debounceRef.current);
    };
  }, [searchQuery]);

  // Ambil data parfum dari API
  const { data, isLoading, isError, refetch } = usePerfumes({
    search: debouncedSearch || undefined,
    isActive: showInactive ? false : true,
  });
  const deletePerfume = useDeletePerfume();

  const perfumes = data?.results || [];

  const handleDeleteClick = (perfume: any) => {
    setSelectedPerfume(perfume);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!selectedPerfume) return;
    await deletePerfume.mutateAsync(selectedPerfume.id);
    setDeleteDialogOpen(false);
    setSelectedPerfume(null);
    refetch();
  };

  return (
    <div className="flex flex-col min-h-screen bg-slate-50">
      <header className="p-4 flex justify-between items-center bg-white sticky top-0 z-10 shadow-sm">
        <div className="flex items-center">
          <Link href="/akun" className="mr-3">
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <h1 className="text-lg font-semibold">Kelola Parfum</h1>
        </div>
        <Button
          onClick={() => router.push('/akun/perfumes/add')}
          className="bg-blue-500 hover:bg-blue-600"
          size="sm"
        >
          <Plus className="h-4 w-4 mr-1" /> Tambah Parfum
        </Button>
      </header>

      <main className="flex-1 p-4 pb-20">
        <Card className="mb-4">
          <div className="p-4 space-y-4">
            <div className="flex flex-col sm:flex-row gap-2">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <Input
                  placeholder="Cari parfum..."
                  className="pl-10"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="show-inactive"
                  checked={showInactive}
                  onCheckedChange={setShowInactive}
                />
                <Label htmlFor="show-inactive">Tampilkan tidak aktif</Label>
              </div>
            </div>

            {isLoading ? (
              <div className="text-center py-8 text-gray-500">
                <p>Loading...</p>
              </div>
            ) : isError ? (
              <div className="text-center py-8 text-red-500">
                <p>
                  Error:{' '}
                  {typeof isError === 'object' &&
                  isError !== null &&
                  'message' in isError
                    ? (isError as any).message
                    : 'Gagal memuat data'}
                </p>
              </div>
            ) : perfumes.length > 0 ? (
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Nama Parfum</TableHead>
                      <TableHead className="hidden md:table-cell">
                        Deskripsi
                      </TableHead>
                      <TableHead className="hidden md:table-cell">
                        Status
                      </TableHead>
                      <TableHead className="text-right">Aksi</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {perfumes.map((perfume) => (
                      <TableRow key={perfume.id}>
                        <TableCell className="font-medium">
                          <div className="flex flex-col">
                            {perfume.name}
                            {perfume.isPopular && (
                              <Badge className="mt-1 w-fit bg-orange-500">
                                Populer
                              </Badge>
                            )}
                          </div>
                        </TableCell>
                        <TableCell className="hidden md:table-cell">
                          {perfume.description}
                        </TableCell>
                        <TableCell>
                          <Badge
                            variant={perfume.isActive ? 'default' : 'secondary'}
                            className={
                              perfume.isActive ? 'bg-green-500' : 'bg-gray-500'
                            }
                          >
                            {perfume.isActive ? 'Aktif' : 'Tidak Aktif'}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() =>
                                router.push(`/akun/perfumes/edit/${perfume.id}`)
                              }
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="text-red-500"
                              onClick={() => handleDeleteClick(perfume)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <AlertCircle className="mx-auto h-12 w-12 text-gray-400 mb-3" />
                <p>Tidak ada parfum yang ditemukan</p>
                <p className="text-sm">
                  Coba ubah filter atau tambahkan parfum baru
                </p>
              </div>
            )}
          </div>
        </Card>
      </main>

      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Hapus Parfum</DialogTitle>
            <DialogDescription>
              Apakah Anda yakin ingin menghapus parfum &quot;
              {selectedPerfume?.name}&quot;? Tindakan ini tidak dapat
              dibatalkan.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setDeleteDialogOpen(false)}
            >
              Batal
            </Button>
            <Button variant="destructive" onClick={handleDeleteConfirm}>
              Hapus
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
