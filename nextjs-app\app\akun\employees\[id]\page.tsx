'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import {
  ArrowLeft,
  Edit,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Briefcase,
  Building,
  Loader2,
} from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

import { useEmployee } from '@/hooks/useEmployees';

export default function EmployeeDetailPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('info');
  const [employeeId, setEmployeeId] = useState<string | null>(null);

  // Handle Next.js 15 params
  useEffect(() => {
    const getParams = async () => {
      const resolvedParams = await params;
      setEmployeeId(resolvedParams.id);
    };
    getParams();
  }, [params]);

  const {
    data: employee,
    isLoading,
    error,
  } = useEmployee(employeeId ? parseInt(employeeId) : null);

  // Loading state
  if (!employeeId || isLoading) {
    return (
      <div className="flex flex-col min-h-screen bg-slate-50">
        <header className="p-4 flex justify-between items-center bg-white sticky top-0 z-10 shadow-sm">
          <div className="flex items-center">
            <Link href="/akun/employees" className="mr-3">
              <ArrowLeft className="h-5 w-5" />
            </Link>
            <h1 className="text-lg font-semibold">Detail Pegawai</h1>
          </div>
        </header>
        <main className="flex-1 p-4 flex items-center justify-center">
          <div className="flex items-center gap-2">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span>Memuat data pegawai...</span>
          </div>
        </main>
      </div>
    );
  }

  // Error state
  if (error || !employee) {
    return (
      <div className="flex flex-col min-h-screen bg-slate-50">
        <header className="p-4 flex justify-between items-center bg-white sticky top-0 z-10 shadow-sm">
          <div className="flex items-center">
            <Link href="/akun/employees" className="mr-3">
              <ArrowLeft className="h-5 w-5" />
            </Link>
            <h1 className="text-lg font-semibold">Detail Pegawai</h1>
          </div>
        </header>
        <main className="flex-1 p-4 flex items-center justify-center">
          <div className="text-center">
            <h2 className="text-xl font-semibold mb-2">
              Pegawai tidak ditemukan
            </h2>
            <p className="text-gray-600 mb-4">
              Pegawai yang Anda cari tidak ditemukan atau telah dihapus.
            </p>
            <Link href="/akun/employees">
              <Button>Kembali ke Daftar Pegawai</Button>
            </Link>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen bg-slate-50">
      <header className="p-4 flex justify-between items-center bg-white sticky top-0 z-10 shadow-sm">
        <div className="flex items-center">
          <Link href="/akun/employees" className="mr-3">
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <h1 className="text-lg font-semibold">Detail Pegawai</h1>
        </div>
        <Link href={`/akun/employees/edit/${employeeId}`}>
          <Button size="sm" className="bg-blue-500 hover:bg-blue-600">
            <Edit className="h-4 w-4 mr-1" /> Edit
          </Button>
        </Link>
      </header>

      <main className="flex-1 p-4 pb-20">
        <Card className="mb-4">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row items-center md:items-start gap-4">
              <Avatar className="h-24 w-24">
                <AvatarImage src="/placeholder.svg" alt={employee.name} />
                <AvatarFallback>
                  {employee.name.substring(0, 2).toUpperCase()}
                </AvatarFallback>
              </Avatar>
              <div className="text-center md:text-left">
                <h2 className="text-xl font-bold">{employee.name}</h2>
                <div className="flex flex-wrap justify-center md:justify-start gap-2 mt-2">
                  <Badge variant="secondary">{employee.role}</Badge>
                  <Badge
                    variant="outline"
                    className={
                      employee.isActive
                        ? 'border-green-500 text-green-500'
                        : 'border-red-500 text-red-500'
                    }
                  >
                    {employee.isActive ? 'Aktif' : 'Tidak Aktif'}
                  </Badge>
                </div>
                <div className="mt-4 space-y-2">
                  <div className="flex items-center gap-2">
                    <Mail className="h-4 w-4 text-gray-500" />
                    <span>{employee.email}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Phone className="h-4 w-4 text-gray-500" />
                    <span>{employee.phone}</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Tabs
          value={activeTab}
          onValueChange={setActiveTab}
          className="space-y-4"
        >
          <TabsList className="grid grid-cols-2 w-full">
            <TabsTrigger value="info">Informasi</TabsTrigger>
            <TabsTrigger value="history">Riwayat</TabsTrigger>
          </TabsList>

          <TabsContent value="info" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Informasi Dasar</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-start gap-2">
                    <Briefcase className="h-5 w-5 text-gray-500 mt-0.5" />
                    <div>
                      <p className="text-sm text-gray-500">Role</p>
                      <p className="font-medium">{employee.role}</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-2">
                    <Building className="h-5 w-5 text-gray-500 mt-0.5" />
                    <div>
                      <p className="text-sm text-gray-500">Outlet</p>
                      <p className="font-medium">
                        {employee.employeeAt?.name || 'Outlet tidak ditemukan'}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-2">
                    <Calendar className="h-5 w-5 text-gray-500 mt-0.5" />
                    <div>
                      <p className="text-sm text-gray-500">Tanggal Bergabung</p>
                      <p className="font-medium">
                        {new Date(employee.createdAt).toLocaleDateString(
                          'id-ID'
                        )}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-2">
                    <Calendar className="h-5 w-5 text-gray-500 mt-0.5" />
                    <div>
                      <p className="text-sm text-gray-500">Terakhir Diupdate</p>
                      <p className="font-medium">
                        {new Date(employee.updatedAt).toLocaleDateString(
                          'id-ID'
                        )}
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Detail Kontak</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 gap-4">
                <div className="flex items-start gap-2">
                    <Mail className="h-5 w-5 text-gray-500 mt-0.5" />
                      <div>
                      <p className="text-sm text-gray-500">Email</p>
                      <p className="font-medium">{employee.email}</p>
                        </div>
                      </div>
                  <div className="flex items-start gap-2">
                    <Phone className="h-5 w-5 text-gray-500 mt-0.5" />
                    <div>
                      <p className="text-sm text-gray-500">Nomor Telepon</p>
                      <p className="font-medium">{employee.phone}</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="history" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Riwayat Aktivitas</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 text-gray-500">
                  <p>Fitur riwayat aktivitas akan segera hadir.</p>
                  <p className="text-sm mt-2">
                    Mencakup kehadiran, performa, dan aktivitas lainnya.
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </main>
    </div>
  );
}
