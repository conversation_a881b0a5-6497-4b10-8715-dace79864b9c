'use client';

import type React from 'react';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { ArrowLeft, Calendar, Percent, DollarSign, Save } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/components/ui/use-toast';
import { useCreatePromotion } from '@/hooks/usePromotions';

export default function AddPromotionPage() {
  const router = useRouter();
  const { toast } = useToast();
  const createPromotionMutation = useCreatePromotion();

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    code: '',
    description: '',
    discountType: 'PERCENTAGE' as 'PERCENTAGE' | 'FIXED',
    discountValue: '',
    minOrderValue: '',
    maxDiscountAmount: '',
    validFrom: '',
    validUntil: '',
    isActive: true,
    usageLimit: '',
    isFirstTimeOnly: false,
    applicableServices: [] as string[],
  });

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleDiscountTypeChange = (value: string) => {
    setFormData((prev) => ({
      ...prev,
      discountType: value as 'PERCENTAGE' | 'FIXED',
      maxDiscountAmount: value === 'FIXED' ? '' : prev.maxDiscountAmount,
    }));
  };

  const handleStatusChange = (checked: boolean) => {
    setFormData((prev) => ({ ...prev, isActive: checked }));
  };

  const handleFirstTimeOnlyChange = (checked: boolean) => {
    setFormData((prev) => ({ ...prev, isFirstTimeOnly: checked }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    if (
      !formData.name ||
      !formData.code ||
      !formData.discountValue ||
      !formData.validFrom ||
      !formData.validUntil
    ) {
      toast({
        title: 'Error',
        description: 'Mohon lengkapi semua field yang wajib diisi',
        variant: 'destructive',
      });
      return;
    }

    // Validate discount value
    const discountValue = parseFloat(formData.discountValue);
    if (isNaN(discountValue) || discountValue <= 0) {
      toast({
        title: 'Error',
        description: 'Nilai diskon harus berupa angka positif',
        variant: 'destructive',
      });
      return;
    }

    // Validate percentage discount
    if (formData.discountType === 'PERCENTAGE' && discountValue > 100) {
      toast({
        title: 'Error',
        description: 'Nilai diskon persentase tidak boleh lebih dari 100%',
        variant: 'destructive',
      });
      return;
    }

    // Validate dates
    const validFrom = new Date(formData.validFrom);
    const validUntil = new Date(formData.validUntil);
    if (validUntil <= validFrom) {
      toast({
        title: 'Error',
        description: 'Tanggal berakhir harus setelah tanggal mulai',
        variant: 'destructive',
      });
      return;
    }

    try {
      const promotionData = {
        name: formData.name,
        code: formData.code.toUpperCase(),
        description: formData.description || undefined,
        discountType: formData.discountType,
        discountValue: parseFloat(formData.discountValue),
        minOrderValue: formData.minOrderValue
          ? parseFloat(formData.minOrderValue)
          : 0,
        maxDiscountAmount:
          formData.maxDiscountAmount && formData.discountType === 'PERCENTAGE'
            ? parseFloat(formData.maxDiscountAmount)
            : undefined,
        validFrom: formData.validFrom,
        validUntil: formData.validUntil,
        isActive: formData.isActive,
        usageLimit: formData.usageLimit
          ? parseInt(formData.usageLimit)
          : undefined,
        isFirstTimeOnly: formData.isFirstTimeOnly,
        applicableServices: formData.applicableServices,
      };

      await createPromotionMutation.mutateAsync(promotionData);

      toast({
        title: 'Berhasil',
        description: 'Promosi baru telah berhasil ditambahkan',
      });

      router.push('/akun/promotions');
    } catch (error: any) {
      console.error('Error creating promotion:', error);
      toast({
        title: 'Error',
        description:
          error?.response?.data?.message || 'Gagal menambahkan promosi',
        variant: 'destructive',
      });
    }
  };

  return (
    <div className="flex flex-col min-h-screen bg-slate-50">
      <header className="p-4 flex items-center bg-white sticky top-0 z-10 shadow-sm">
        <Link href="/akun/promotions" className="mr-3">
          <ArrowLeft className="h-5 w-5" />
        </Link>
        <h1 className="text-xl font-semibold">Tambah Promosi Baru</h1>
      </header>

      <main className="flex-1 p-4 pb-20">
        <form onSubmit={handleSubmit}>
          <Card className="mb-6">
            <CardContent className="p-6">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="name">
                    Nama Promosi <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="name"
                    name="name"
                    placeholder="Masukkan nama promosi"
                    value={formData.name}
                    onChange={handleChange}
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="code">
                    Kode Promosi <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="code"
                    name="code"
                    placeholder="Contoh: SUMMER25"
                    value={formData.code}
                    onChange={handleChange}
                    required
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Kode promosi harus unik dan akan digunakan pelanggan saat
                    checkout
                  </p>
                </div>

                <div>
                  <Label htmlFor="description">Deskripsi</Label>
                  <Textarea
                    id="description"
                    name="description"
                    placeholder="Deskripsi singkat tentang promosi ini"
                    value={formData.description}
                    onChange={handleChange}
                    rows={3}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="mb-6">
            <CardContent className="p-6">
              <h2 className="text-lg font-medium mb-4">Detail Diskon</h2>

              <div className="space-y-4">
                <div>
                  <Label>
                    Tipe Diskon <span className="text-red-500">*</span>
                  </Label>
                  <RadioGroup
                    value={formData.discountType}
                    onValueChange={handleDiscountTypeChange}
                    className="flex flex-col space-y-1 mt-2"
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="PERCENTAGE" id="percentage" />
                      <Label htmlFor="percentage" className="flex items-center">
                        <Percent className="h-4 w-4 mr-2" />
                        Persentase (%)
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="FIXED" id="fixed" />
                      <Label htmlFor="fixed" className="flex items-center">
                        <DollarSign className="h-4 w-4 mr-2" />
                        Nominal Tetap (Rp)
                      </Label>
                    </div>
                  </RadioGroup>
                </div>

                <div>
                  <Label htmlFor="discountValue">
                    Nilai Diskon <span className="text-red-500">*</span>
                  </Label>
                  <div className="relative">
                    <Input
                      id="discountValue"
                      name="discountValue"
                      type="number"
                      placeholder={
                        formData.discountType === 'PERCENTAGE'
                          ? 'Contoh: 25'
                          : 'Contoh: 50000'
                      }
                      value={formData.discountValue}
                      onChange={handleChange}
                      className={
                        formData.discountType === 'PERCENTAGE' ? 'pr-8' : ''
                      }
                      required
                    />
                    {formData.discountType === 'PERCENTAGE' && (
                      <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                        <Percent className="h-4 w-4 text-gray-400" />
                      </div>
                    )}
                  </div>
                  {formData.discountType === 'PERCENTAGE' && (
                    <p className="text-xs text-gray-500 mt-1">
                      Masukkan nilai persentase tanpa simbol % (1-100)
                    </p>
                  )}
                </div>

                {formData.discountType === 'PERCENTAGE' && (
                  <div>
                    <Label htmlFor="maxDiscountAmount">
                      Maksimal Diskon (Rp)
                    </Label>
                    <Input
                      id="maxDiscountAmount"
                      name="maxDiscountAmount"
                      type="number"
                      placeholder="Contoh: 100000"
                      value={formData.maxDiscountAmount}
                      onChange={handleChange}
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Batas maksimal nominal diskon untuk persentase (opsional)
                    </p>
                  </div>
                )}

                <div>
                  <Label htmlFor="minOrderValue">
                    Minimum Nilai Order (Rp)
                  </Label>
                  <Input
                    id="minOrderValue"
                    name="minOrderValue"
                    type="number"
                    placeholder="Contoh: 100000"
                    value={formData.minOrderValue}
                    onChange={handleChange}
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Kosongkan jika tidak ada minimum order
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="mb-6">
            <CardContent className="p-6">
              <h2 className="text-lg font-medium mb-4">Periode & Batasan</h2>

              <div className="space-y-4">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="validFrom">
                      Tanggal Mulai <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="validFrom"
                      name="validFrom"
                      type="date"
                      value={formData.validFrom}
                      onChange={handleChange}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="validUntil">
                      Tanggal Berakhir <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="validUntil"
                      name="validUntil"
                      type="date"
                      value={formData.validUntil}
                      onChange={handleChange}
                      required
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="usageLimit">Batas Penggunaan</Label>
                  <Input
                    id="usageLimit"
                    name="usageLimit"
                    type="number"
                    placeholder="Contoh: 100"
                    value={formData.usageLimit}
                    onChange={handleChange}
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Kosongkan untuk penggunaan tanpa batas
                  </p>
                </div>

                <Separator />

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="isActive">Status Promosi</Label>
                      <p className="text-sm text-gray-500">
                        Aktifkan promosi setelah dibuat
                      </p>
                    </div>
                    <Switch
                      id="isActive"
                      checked={formData.isActive}
                      onCheckedChange={handleStatusChange}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="isFirstTimeOnly">
                        Khusus Pelanggan Baru
                      </Label>
                      <p className="text-sm text-gray-500">
                        Hanya untuk pelanggan yang pertama kali memesan
                      </p>
                    </div>
                    <Switch
                      id="isFirstTimeOnly"
                      checked={formData.isFirstTimeOnly}
                      onCheckedChange={handleFirstTimeOnlyChange}
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="flex gap-4">
            <Button
              type="button"
              variant="outline"
              className="flex-1"
              onClick={() => router.back()}
              disabled={createPromotionMutation.isPending}
            >
              Batal
            </Button>
            <Button
              type="submit"
              className="flex-1"
              disabled={createPromotionMutation.isPending}
            >
              <Save className="h-4 w-4 mr-2" />
              {createPromotionMutation.isPending
                ? 'Menyimpan...'
                : 'Simpan Promosi'}
            </Button>
          </div>
        </form>
      </main>
    </div>
  );
}
