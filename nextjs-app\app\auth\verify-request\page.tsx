'use client';

import Image from 'next/image';
import Link from 'next/link';
import { Mail, CheckCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { useSearchParams, useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  ResendVerificationFormValues,
  resendVerificationSchema,
} from '@/lib/form-schema';
import * as z from 'zod';
import { useAuth } from '@/lib/auth-context';
import { useMutation } from '@tanstack/react-query';

// Schema untuk validasi OTP
const otpSchema = z.object({
  code: z
    .string()
    .length(6, 'Kode OTP harus 6 digit')
    .regex(/^\d{6}$/, 'Kode OTP hanya boleh berisi angka'),
});

type OtpFormValues = z.infer<typeof otpSchema>;

export default function VerifyRequestPage() {
  const [isResending, setIsResending] = useState(false);
  const [cooldownTime, setCooldownTime] = useState(0);
  const [isVerified, setIsVerified] = useState(false);
  const searchParams = useSearchParams();
  const router = useRouter();
  const { sendVerificationEmail, user } = useAuth();

  const otpForm = useForm<OtpFormValues>({
    resolver: zodResolver(otpSchema),
    defaultValues: {
      code: '',
    },
  });

  // Mutation untuk verifikasi OTP
  const verifyOtpMutation = useMutation({
    mutationFn: async (code: string) => {
      const response = await fetch(
        `${
          process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/v1'
        }/auth/verify-email`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ code }),
        }
      );

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || data.error || 'Verifikasi gagal');
      }

      return data;
    },
    onSuccess: () => {
      // Verifikasi berhasil
      setIsVerified(true);
      toast.success(
        'Email berhasil diverifikasi! Anda akan diarahkan ke halaman login.'
      );

      // Redirect ke login setelah 2 detik
      setTimeout(() => {
        router.push('/dashboard');
      }, 2000);
    },
    onError: (error: any) => {
      console.error('Error verifying OTP:', error);
      toast.error(
        error.message || 'Kode verifikasi tidak valid atau sudah kadaluarsa'
      );
      // Reset form jika error
      otpForm.reset();
    },
  });

  // Cooldown timer effect
  useEffect(() => {
    if (cooldownTime > 0) {
      const timer = setTimeout(() => {
        setCooldownTime(cooldownTime - 1);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [cooldownTime]);

  async function handleResendVerification() {
    if (cooldownTime > 0) {
      toast.error(`Tunggu ${cooldownTime} detik sebelum mengirim ulang`);
      return;
    }

    setIsResending(true);
    try {
      // Karena endpoint /auth/send-verification-email memerlukan authentication,
      // kita perlu membuat endpoint terpisah atau memberitahu user untuk login dulu
      const response = await sendVerificationEmail('otp');
      toast.success('Email verifikasi berhasil dikirim ulang');
      // Set cooldown timer 1 menit (60 detik)
      setCooldownTime(60);
    } catch (error: any) {
      console.error('Error resending verification:', error);
      if (error.message.includes('login')) {
        toast.error(error.message);
      } else {
        toast.error(
          'Untuk mengirim ulang email verifikasi, silakan login terlebih dahulu'
        );
      }
    } finally {
      setIsResending(false);
    }
  }

  function handleVerifyOtp(values: OtpFormValues) {
    verifyOtpMutation.mutate(values.code);
  }

  const formatCooldownTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    if (minutes > 0) {
      return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
    return `${remainingSeconds}`;
  };

  if (isVerified) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center">
        <div className="w-full max-w-md px-6 text-center">
          <div className="mb-6">
            <Image
              src="/logo.png"
              alt="SuperLaundry Logo"
              width={200}
              height={80}
              className="mx-auto mb-4"
            />
            <p className="text-sm mb-4">Manajemen Usaha Laundry</p>
          </div>

          <div className="bg-green-50 border border-green-200 rounded-lg p-8 mb-6">
            <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-center mb-4 text-green-700">
              Email Terverifikasi!
            </h1>
            <p className="text-center text-muted-foreground mb-4">
              Email Anda telah berhasil diverifikasi. Anda akan diarahkan ke
              halaman login dalam beberapa detik.
            </p>
            <Button
              onClick={() => router.push('/auth/login')}
              className="w-full"
            >
              Lanjut ke Login
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen flex-col items-center justify-center">
      <div className="w-full max-w-md px-6 text-center">
        <div className="mb-6">
          <Image
            src="/logo.png"
            alt="SuperLaundry Logo"
            width={200}
            height={80}
            className="mx-auto mb-4"
          />
          <p className="text-sm mb-4">Manajemen Usaha Laundry</p>
        </div>

        <div className="bg-blue-50 border border-blue-200 rounded-lg p-8 mb-6">
          <Mail className="h-16 w-16 text-blue-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-center mb-4">
            Cek Email Anda
          </h1>
          <p className="text-center text-muted-foreground mb-6">
            Kami telah mengirim email verifikasi ke{' '}
            <span className="font-medium">{user?.email}</span>. Silakan periksa
            kotak masuk atau folder spam Anda dan masukkan kode 6 digit yang
            diberikan.
          </p>

          {/* Form OTP */}
          <div className="mb-6">
            <Form {...otpForm}>
              <form
                onSubmit={otpForm.handleSubmit(handleVerifyOtp)}
                className="space-y-4"
              >
                <FormField
                  control={otpForm.control}
                  name="code"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Kode Verifikasi (6 digit)</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder="123456"
                          className="text-center text-2xl tracking-widest"
                          maxLength={6}
                          disabled={verifyOtpMutation.isPending}
                          onChange={(e) => {
                            // Hanya allow angka
                            const value = e.target.value.replace(/\D/g, '');
                            field.onChange(value);
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                {verifyOtpMutation.error && (
                  <div className="bg-red-50 border border-red-200 rounded-md p-3 mb-4">
                    <p className="text-red-600 text-sm">
                      {verifyOtpMutation.error.message}
                    </p>
                  </div>
                )}

                <Button
                  type="submit"
                  className="w-full"
                  disabled={
                    verifyOtpMutation.isPending ||
                    otpForm.watch('code').length !== 6
                  }
                >
                  {verifyOtpMutation.isPending
                    ? 'Memverifikasi...'
                    : 'Verifikasi'}
                </Button>
              </form>
            </Form>
          </div>

          <div className="border-t pt-6">
            <p className="text-center text-muted-foreground mb-6">
              Belum menerima email? Klik tombol di bawah untuk mengirim ulang.
            </p>

            <p className="text-center text-muted-foreground mb-4 text-xs">
              <strong>Catatan:</strong> Jika gagal mengirim ulang, silakan{' '}
              <Link href="/auth/login" className="text-primary hover:underline">
                login terlebih dahulu
              </Link>{' '}
              untuk mengirim ulang email verifikasi.
            </p>

            <Form {...otpForm}>
              <Button
                onClick={handleResendVerification}
                disabled={isResending || cooldownTime > 0}
                className="w-full"
                type="button"
                variant="outline"
              >
                {isResending
                  ? 'Mengirim...'
                  : cooldownTime > 0
                  ? `Tunggu ${formatCooldownTime(cooldownTime)} detik`
                  : 'Kirim Ulang Email Verifikasi'}
              </Button>
            </Form>

            {cooldownTime > 0 && (
              <p className="text-sm text-muted-foreground mt-2">
                Anda dapat mengirim ulang email dalam{' '}
                {formatCooldownTime(cooldownTime)} detik
              </p>
            )}
          </div>
        </div>

        <p className="text-sm text-muted-foreground">
          Kembali ke{' '}
          <Link href="/auth/login" className="text-primary hover:underline">
            halaman login
          </Link>
        </p>
      </div>
    </div>
  );
}
