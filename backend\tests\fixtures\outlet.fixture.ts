import { faker } from '@faker-js/faker';
import prisma from '../../src/client';
import { Prisma } from '@prisma/client';

export const outletOne = {
  id: 1,
  name: faker.company.name(),
  address: faker.address.streetAddress(),
  province: faker.address.state(),
  city: faker.address.city(),
  timezone: 'Asia/Jakarta',
  phone: faker.phone.number('08##########'),
  latitude: parseFloat(faker.address.latitude()),
  longitude: parseFloat(faker.address.longitude()),
  isActive: true
};

export const outletTwo = {
  id: 2,
  name: faker.company.name(),
  address: faker.address.streetAddress(),
  province: faker.address.state(),
  city: faker.address.city(),
  timezone: 'Asia/Jakarta',
  phone: faker.phone.number('08##########'),
  latitude: parseFloat(faker.address.latitude()),
  longitude: parseFloat(faker.address.longitude()),
  isActive: true
};

export const outletThree = {
  id: 3,
  name: faker.company.name(),
  address: faker.address.streetAddress(),
  province: faker.address.state(),
  city: faker.address.city(),
  timezone: 'Asia/Jakarta',
  phone: faker.phone.number('08##########'),
  latitude: parseFloat(faker.address.latitude()),
  longitude: parseFloat(faker.address.longitude()),
  isActive: false
};

export const insertOutlets = async (outlets: any[], ownerId?: number) => {
  const createdOutlets = [];

  for (const outlet of outlets) {
    const { id, ...outletData } = outlet;

    let finalOwnerId = ownerId;
    if (!finalOwnerId) {
      const firstUser = await prisma.user.findFirst({
        orderBy: { id: 'asc' }
      });
      if (!firstUser) {
        throw new Error('No users found for outlet creation');
      }
      finalOwnerId = firstUser.id;
    }

    const created = await prisma.outlet.create({
      data: {
        ...outletData,
        ownerId: finalOwnerId
      }
    });
    createdOutlets.push(created);
  }

  return createdOutlets;
};
