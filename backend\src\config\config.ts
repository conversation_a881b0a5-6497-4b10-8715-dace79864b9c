import dotenv from 'dotenv';
import path from 'path';
import <PERSON><PERSON> from 'joi';

dotenv.config({ path: path.join(process.cwd(), '.env') });

const envVarsSchema = Joi.object()
  .keys({
    NODE_ENV: Joi.string().valid('production', 'development', 'test').required(),
    PORT: Joi.number().default(3000),
    JWT_SECRET: Joi.string().required().description('JWT secret key'),
    JWT_ACCESS_EXPIRATION_MINUTES: Joi.number()
      .default(30)
      .description('minutes after which access tokens expire'),
    JWT_REFRESH_EXPIRATION_DAYS: Joi.number()
      .default(30)
      .description('days after which refresh tokens expire'),
    JWT_RESET_PASSWORD_EXPIRATION_MINUTES: Joi.number()
      .default(10)
      .description('minutes after which reset password token expires'),
    JWT_VERIFY_EMAIL_EXPIRATION_MINUTES: Joi.number()
      .default(10)
      .description('minutes after which verify email token expires'),
    SMTP_HOST: Joi.string().description('server that will send the emails'),
    SMTP_PORT: Joi.number().description('port to connect to the email server'),
    SMTP_USERNAME: Joi.string().description('username for email server'),
    SMTP_PASSWORD: Joi.string().description('password for email server'),
    EMAIL_FROM: Joi.string().description('the from field in the emails sent by the app'),
    EMAIL_PROVIDER: Joi.string().valid('smtp', 'sendgrid').default('smtp'),
    SENDGRID_API_KEY: Joi.string().description('SendGrid API key'),
    SENDGRID_FROM_EMAIL: Joi.string().description('SendGrid from email'),
    SENDGRID_FROM_NAME: Joi.string().description('SendGrid from name'),
    VERIFICATION_ENABLED: Joi.boolean().default(true),
    VERIFICATION_REQUIRE_EMAIL_VERIFICATION: Joi.boolean().default(true),
    VERIFICATION_REQUIRE_PHONE_VERIFICATION: Joi.boolean().default(false),
    VERIFICATION_AUTO_SEND_EMAIL_ON_REGISTER: Joi.boolean().default(false),
    VERIFICATION_PHONE_VERIFICATION_EXPIRATION_MINUTES: Joi.number().default(10),
    VERIFICATION_EMAIL_OTP_EXPIRATION_MINUTES: Joi.number().default(10)
  })
  .unknown();

const { value: envVars, error } = envVarsSchema
  .prefs({ errors: { label: 'key' } })
  .validate(process.env);

if (error) {
  throw new Error(`Config validation error: ${error.message}`);
}

export default {
  env: envVars.NODE_ENV,
  port: envVars.PORT,
  jwt: {
    secret: envVars.JWT_SECRET,
    accessExpirationMinutes: envVars.JWT_ACCESS_EXPIRATION_MINUTES,
    refreshExpirationDays: envVars.JWT_REFRESH_EXPIRATION_DAYS,
    resetPasswordExpirationMinutes: envVars.JWT_RESET_PASSWORD_EXPIRATION_MINUTES,
    verifyEmailExpirationMinutes: envVars.JWT_VERIFY_EMAIL_EXPIRATION_MINUTES
  },
  email: {
    provider: envVars.EMAIL_PROVIDER,
    smtp: {
      host: envVars.SMTP_HOST,
      port: envVars.SMTP_PORT,
      auth: {
        user: envVars.SMTP_USERNAME,
        pass: envVars.SMTP_PASSWORD
      }
    },
    from: envVars.EMAIL_FROM
  },
  sendgrid: {
    apiKey: envVars.SENDGRID_API_KEY,
    fromEmail: envVars.SENDGRID_FROM_EMAIL,
    fromName: envVars.SENDGRID_FROM_NAME
  },
  verification: {
    requireEmailVerification: envVars.VERIFICATION_REQUIRE_EMAIL_VERIFICATION,
    requirePhoneVerification: envVars.VERIFICATION_REQUIRE_PHONE_VERIFICATION,
    enabled: envVars.VERIFICATION_ENABLED,
    autoSendEmailOnRegister: envVars.VERIFICATION_AUTO_SEND_EMAIL_ON_REGISTER,
    phoneVerificationExpirationMinutes: envVars.VERIFICATION_PHONE_VERIFICATION_EXPIRATION_MINUTES,
    emailOtpExpirationMinutes: envVars.VERIFICATION_EMAIL_OTP_EXPIRATION_MINUTES
  }
};
