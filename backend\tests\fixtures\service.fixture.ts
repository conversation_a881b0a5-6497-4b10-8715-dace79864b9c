import { Service } from '@prisma/client';
import { userOne } from './user.fixture';
import { outletOne, outletTwo } from './outlet.fixture';
import prisma from '../../src/client';

const serviceOne: Partial<Service> = {
  id: 1, // Will be overridden with actual ID from database
  name: '<PERSON><PERSON><PERSON>',
  description: '<PERSON>ndry cuci kering setrika per kilogram',
  price: 10000,
  unit: 'kg',
  estimationHours: 24,
  isActive: true,
  outletId: 1 // Will be overridden with actual outletId
};

const serviceTwo: Partial<Service> = {
  id: 2, // Will be overridden with actual ID from database
  name: 'Dry Clean',
  description: 'Dry cleaning untuk pakaian formal',
  price: 15000,
  unit: 'pcs',
  estimationHours: 48,
  isActive: true,
  outletId: 1 // Will be overridden with actual outletId
};

const insertServices = async (services: Partial<Service>[], outletId?: number) => {
  const createdServices = [];
  for (const service of services) {
    const { id, ...serviceData } = service; // Remove id from data to let DB auto-generate

    // Ensure outlet exists before creating service
    const targetOutletId = outletId || serviceData.outletId!;
    const outlet = await prisma.outlet.findUnique({
      where: { id: targetOutletId }
    });

    if (!outlet) {
      throw new Error(`Outlet with ID ${targetOutletId} does not exist`);
    }

    const createdService = await prisma.service.create({
      data: {
        name: serviceData.name!,
        description: serviceData.description!,
        price: serviceData.price!,
        unit: serviceData.unit!,
        estimationHours: serviceData.estimationHours,
        isActive: serviceData.isActive!,
        outletId: targetOutletId
      }
    });
    createdServices.push(createdService);
  }
  return createdServices;
};

// Legacy function for backward compatibility - now just creates services with outletId
const insertOutletServices = async (outletIds: number[], serviceIds: number[]) => {
  if (outletIds.length === 0 || serviceIds.length === 0) {
    throw new Error('Outlet IDs and Service IDs are required');
  }

  // This function is now just for compatibility
  // In the new model, services are created directly with outletId
  // We don't need to do anything here as services are already created with outletId
  return;
};

export { serviceOne, serviceTwo, insertServices, insertOutletServices };
