/// <reference types="cypress" />

// ***********************************************
// Commands.ts - Custom Cypress Commands
// ***********************************************

// Types
interface TestUser {
  email: string;
  password: string;
  name?: string;
  phone?: string;
}

// Declare global namespace untuk custom commands
declare global {
  namespace Cypress {
    interface Chainable {
      // Authentication commands
      loginAs(email: string, password: string): Chainable<void>;
      clearAuthData(): Chainable<void>;

      // User management commands
      createTestUser(user: TestUser): Chainable<any>;
      verifyUserInDatabase(userId: string): Chainable<void>;

      // Wait helpers
      waitForPageLoad(): Chainable<void>;
      waitForElement(selector: string): Chainable<JQuery<HTMLElement>>;

      // Form helpers
      fillForm(data: Record<string, string>): Chainable<void>;
    }
  }
}

// Helper functions
export const generateTestUser = (
  overrides: Partial<TestUser> = {}
): TestUser => {
  const timestamp = Date.now();
  return {
    email: `test${timestamp}@example.com`,
    password: 'password123',
    name: `Test User ${timestamp}`,
    phone: `0812345${timestamp.toString().slice(-5)}`,
    ...overrides,
  };
};

export const apiHelpers = {
  // API helper functions
  createUser: (userData: TestUser) => {
    return cy.request('POST', `${Cypress.env('apiUrl')}/users`, userData);
  },

  deleteUser: (userId: string) => {
    return cy.request('DELETE', `${Cypress.env('apiUrl')}/users/${userId}`);
  },
};

export const waitHelpers = {
  forPageLoad: () => {
    cy.get('body').should('be.visible');
    cy.document().should('have.property', 'readyState', 'complete');
  },

  forApiCall: (alias: string) => {
    cy.wait(alias);
  },
};

// Custom Commands Implementation

// Authentication Commands
Cypress.Commands.add('loginAs', (email: string, password: string) => {
  cy.session([email, password], () => {
    cy.visit('/auth/login');
    cy.get('[data-testid="email-input"]').type(email);
    cy.get('[data-testid="password-input"]').type(password);
    cy.get('[data-testid="login-button"]').click();
    cy.url().should('not.include', '/auth/login');
  });
});

Cypress.Commands.add('clearAuthData', () => {
  cy.clearCookies();
  cy.clearLocalStorage();
  cy.clearAllSessionStorage();
  // Clear any application-specific auth data
  cy.window().then((win) => {
    win.localStorage.removeItem('token');
    win.localStorage.removeItem('user');
  });
});

// User Management Commands
Cypress.Commands.add('createTestUser', (user: TestUser) => {
  return cy
    .request({
      method: 'POST',
      url: `${Cypress.env('apiUrl')}/auth/register`,
      body: user,
      failOnStatusCode: false,
    })
    .then((response) => {
      expect(response.status).to.be.oneOf([200, 201]);
      return response.body.user || response.body;
    });
});

Cypress.Commands.add('verifyUserInDatabase', (userId: string) => {
  // This would typically make a database call or API call to verify user
  cy.request({
    method: 'PUT',
    url: `${Cypress.env('apiUrl')}/users/${userId}/verify`,
    failOnStatusCode: false,
  });
});

// Wait Helpers
Cypress.Commands.add('waitForPageLoad', () => {
  waitHelpers.forPageLoad();
});

Cypress.Commands.add('waitForElement', (selector: string) => {
  return cy.get(selector, { timeout: 10000 }).should('be.visible');
});

// Form Helpers
Cypress.Commands.add('fillForm', (data: Record<string, string>) => {
  Object.entries(data).forEach(([key, value]) => {
    cy.get(`[data-testid="${key}-input"]`).clear().type(value);
  });
});

// Global error handling
// eslint-disable-next-line @typescript-eslint/no-unused-vars
Cypress.on('uncaught:exception', (err, _runnable) => {
  // returning false here prevents Cypress from failing the test
  if (err.message.includes('ResizeObserver loop limit exceeded')) {
    return false;
  }
  if (err.message.includes('Non-Error promise rejection captured')) {
    return false;
  }
  return true;
});

export {};
