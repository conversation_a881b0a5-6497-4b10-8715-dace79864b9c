'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Search, Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { useCustomers } from '@/hooks/useCustomers';

export default function SelectCustomerPage() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState('semua');
  const [isNewCustomerDialogOpen, setIsNewCustomerDialogOpen] = useState(false);
  const [newCustomer, setNewCustomer] = useState({
    name: '',
    phone: '',
    address: '',
    labels: [],
  });

  // Ambil data customer dari API
  const { data, isLoading, isError } = useCustomers();
  const customers = data?.results || [];

  // Handle memilih customer
  const handleSelectCustomer = (customer: { id: number }) => {
    router.push(`/orders/create?customerId=${customer.id}`);
  };

  // Filter customers berdasarkan search dan tab
  const filteredCustomers = customers.filter((customer) => {
    const matchesSearch =
      customer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (customer.phone || '').includes(searchQuery);
    // Tab
    if (activeTab === 'semua') return matchesSearch;
    if (activeTab === 'baru') return matchesSearch && customer.status === 'NEW';
    if (activeTab === 'vip')
      return matchesSearch && customer.labels?.includes('VIP');
    if (activeTab === 'langganan')
      return matchesSearch && customer.labels?.includes('Langganan');
    if (activeTab === 'aktif')
      return matchesSearch && customer.status === 'ACTIVE';
    if (activeTab === 'nonaktif')
      return matchesSearch && customer.status === 'INACTIVE';
    return matchesSearch;
  });

  return (
    <div className=" mx-auto bg-background min-h-screen pb-16">
      {/* Header */}
      <div className="sticky top-0 z-10 bg-background border-b">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-3">
            <Button
              variant="ghost"
              size="icon"
              className="h-9 w-9"
              onClick={() => router.push('/dashboard')}
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <h1 className="text-xl font-semibold">Pilih Pelanggan</h1>
          </div>
          <Button
            size="sm"
            className="h-9"
            onClick={() =>
              router.push('/akun/customers/add?redirect=select-customer')
            }
          >
            <Plus className="h-4 w-4 mr-1" />
            Pelanggan Baru
          </Button>
        </div>
      </div>

      {/* Search */}
      <div className="p-4">
        <div className="relative">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Cari pelanggan..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        <div className="flex flex-wrap gap-2 mt-4">
          <Badge
            variant={activeTab === 'semua' ? 'default' : 'outline'}
            className="cursor-pointer"
            onClick={() => setActiveTab('semua')}
          >
            Semua ({customers.length})
          </Badge>
          <Badge
            variant={activeTab === 'baru' ? 'default' : 'outline'}
            className="cursor-pointer"
            onClick={() => setActiveTab('baru')}
          >
            Baru ({customers.filter((c) => c.status === 'NEW').length})
          </Badge>
          <Badge
            variant={activeTab === 'vip' ? 'default' : 'outline'}
            className="cursor-pointer"
            onClick={() => setActiveTab('vip')}
          >
            VIP ({customers.filter((c) => c.labels?.includes('VIP')).length})
          </Badge>
          <Badge
            variant={activeTab === 'langganan' ? 'default' : 'outline'}
            className="cursor-pointer"
            onClick={() => setActiveTab('langganan')}
          >
            Langganan (
            {customers.filter((c) => c.labels?.includes('Langganan')).length})
          </Badge>
          <Badge
            variant={activeTab === 'aktif' ? 'default' : 'outline'}
            className="cursor-pointer"
            onClick={() => setActiveTab('aktif')}
          >
            Aktif ({customers.filter((c) => c.status === 'ACTIVE').length})
          </Badge>
          <Badge
            variant={activeTab === 'nonaktif' ? 'default' : 'outline'}
            className="cursor-pointer"
            onClick={() => setActiveTab('nonaktif')}
          >
            Nonaktif ({customers.filter((c) => c.status === 'INACTIVE').length})
          </Badge>
        </div>
      </div>

      {/* Customer List */}
      <div className="p-4 space-y-3">
        {isLoading ? (
          <div className="text-center py-8 text-muted-foreground">
            Memuat data pelanggan...
          </div>
        ) : isError ? (
          <div className="text-center py-8 text-destructive">
            Gagal memuat data pelanggan
          </div>
        ) : filteredCustomers.length > 0 ? (
          filteredCustomers.map(
            (customer: {
              id: number;
              name: string;
              phone?: string | null | undefined;
              status: string;
              labels?: string[];
            }) => (
              <Card
                key={customer.id}
                className="p-3 cursor-pointer"
                onClick={() => handleSelectCustomer(customer)}
              >
                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-3">
                    <Avatar>
                      <AvatarFallback>
                        {customer.name?.charAt(0)}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="flex items-center gap-2">
                        <h3 className="font-medium">{customer.name}</h3>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {customer.status === 'NEW' && (
                            <Badge variant="default">Baru</Badge>
                          )}
                          {customer.labels &&
                            customer.labels.map(
                              (label: string, index: number) => (
                                <Badge
                                  key={index}
                                  variant="secondary"
                                  className="text-xs"
                                >
                                  {label}
                                </Badge>
                              )
                            )}
                        </div>
                      </div>
                      <p className="text-sm text-gray-500">{customer.phone}</p>
                    </div>
                  </div>
                </div>
              </Card>
            )
          )
        ) : (
          <div className="text-center py-8 text-muted-foreground">
            {searchQuery
              ? 'Tidak ada pelanggan yang ditemukan'
              : 'Belum ada pelanggan'}
          </div>
        )}
      </div>

      {/* New Customer Dialog */}
      <Dialog
        open={isNewCustomerDialogOpen}
        onOpenChange={setIsNewCustomerDialogOpen}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Tambah Pelanggan Baru</DialogTitle>
            <DialogDescription>
              Masukkan informasi pelanggan baru untuk ditambahkan ke sistem.
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-2">
            <div className="grid gap-2">
              <Label htmlFor="customer-name">Nama</Label>
              <Input
                id="customer-name"
                placeholder="Nama pelanggan"
                value={newCustomer.name}
                onChange={(e) =>
                  setNewCustomer({ ...newCustomer, name: e.target.value })
                }
              />
            </div>

            <div className="grid gap-2">
              <Label htmlFor="customer-phone">Nomor Telepon</Label>
              <Input
                id="customer-phone"
                placeholder="08xxxxxxxxxx"
                value={newCustomer.phone}
                onChange={(e) =>
                  setNewCustomer({ ...newCustomer, phone: e.target.value })
                }
              />
            </div>

            <div className="grid gap-2">
              <Label htmlFor="customer-address">Alamat</Label>
              <Textarea
                id="customer-address"
                placeholder="Alamat pelanggan"
                value={newCustomer.address}
                onChange={(e) =>
                  setNewCustomer({ ...newCustomer, address: e.target.value })
                }
                rows={3}
              />
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsNewCustomerDialogOpen(false)}
            >
              Batal
            </Button>
            <Button onClick={() => router.push('/users/add')}>Tambah</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
