describe('🔐 Authentication Integration (TDD)', () => {
  const testUser = {
    name: 'Test User Integration',
    email: '<EMAIL>',
    phone: '08123456789',
    password: 'password123',
    role: 'ADMIN',
  };

  const invalidUser = {
    email: '<EMAIL>',
    password: 'wrongpassword',
  };

  beforeEach(() => {
    // Clear any existing authentication data
    cy.clearAuthData();

    // Ensure we start on login page
    cy.visit('/auth/login');
  });

  afterEach(() => {
    // Clean up test data
    cy.clearAuthData();
  });

  describe('🧪 TDD: Registration Flow', () => {
    it('should register new user successfully', () => {
      // Test case: Registration with valid data
      cy.get('[data-testid="register-link"]').click();
      cy.url().should('include', '/auth/register');

      // Fill registration form
      cy.get('[data-testid="name-input"]').type(testUser.name);
      cy.get('[data-testid="email-input"]').type(testUser.email);
      cy.get('[data-testid="phone-input"]').type(testUser.phone);
      cy.get('[data-testid="password-input"]').type(testUser.password);
      cy.get('[data-testid="confirm-password-input"]').type(testUser.password);

      // Submit form
      cy.get('[data-testid="register-submit"]').click();

      // Should redirect to dashboard after successful registration
      cy.url().should('include', '/dashboard');

      // User should be authenticated
      cy.window().its('localStorage').should('contain.key', 'user');
      cy.window().its('localStorage').should('contain.key', 'tokens');

      // Verify user data is stored correctly
      cy.window()
        .its('localStorage.user')
        .then((userString) => {
          const user = JSON.parse(userString);
          expect(user.email).to.equal(testUser.email);
          expect(user.name).to.equal(testUser.name);
        });
    });

    it('should show validation errors for invalid registration data', () => {
      cy.get('[data-testid="register-link"]').click();

      // Submit empty form
      cy.get('[data-testid="register-submit"]').click();

      // Should show validation errors
      cy.get('[data-testid="name-error"]').should('be.visible');
      cy.get('[data-testid="email-error"]').should('be.visible');
      cy.get('[data-testid="password-error"]').should('be.visible');

      // Try with invalid email
      cy.get('[data-testid="email-input"]').type('invalid-email');
      cy.get('[data-testid="email-error"]').should('contain', 'email');

      // Try with weak password
      cy.get('[data-testid="password-input"]').type('123');
      cy.get('[data-testid="password-error"]').should('contain', 'password');
    });

    it('should prevent registration with existing email', () => {
      // First create a user
      cy.createTestUser(testUser);

      // Try to register with same email
      cy.get('[data-testid="register-link"]').click();
      cy.get('[data-testid="name-input"]').type('Another User');
      cy.get('[data-testid="email-input"]').type(testUser.email);
      cy.get('[data-testid="phone-input"]').type('08987654321');
      cy.get('[data-testid="password-input"]').type('password456');
      cy.get('[data-testid="confirm-password-input"]').type('password456');

      cy.get('[data-testid="register-submit"]').click();

      // Should show error message
      cy.get('[data-testid="error-message"]')
        .should('be.visible')
        .and('contain', 'email');
    });
  });

  describe('🧪 TDD: Login Flow', () => {
    beforeEach(() => {
      // Create test user for login tests
      cy.createTestUser(testUser);
    });

    it('should login with valid credentials', () => {
      // Fill login form
      cy.get('[data-testid="email-input"]').type(testUser.email);
      cy.get('[data-testid="password-input"]').type(testUser.password);

      // Submit login
      cy.get('[data-testid="login-submit"]').click();

      // Should redirect to dashboard
      cy.url().should('include', '/dashboard');

      // Verify authentication state
      cy.window().its('localStorage').should('contain.key', 'user');
      cy.window().its('localStorage').should('contain.key', 'tokens');

      // Verify user data
      cy.window()
        .its('localStorage.user')
        .then((userString) => {
          const user = JSON.parse(userString);
          expect(user.email).to.equal(testUser.email);
          expect(user.isAuthenticated).to.be.undefined; // This is managed by context
        });

      // Verify tokens structure
      cy.window()
        .its('localStorage.tokens')
        .then((tokensString) => {
          const tokens = JSON.parse(tokensString);
          expect(tokens).to.have.property('access');
          expect(tokens).to.have.property('refresh');
          expect(tokens.access).to.have.property('token');
          expect(tokens.access).to.have.property('expires');
          expect(tokens.refresh).to.have.property('token');
          expect(tokens.refresh).to.have.property('expires');
        });
    });

    it('should show error for invalid credentials', () => {
      // Try login with invalid credentials
      cy.get('[data-testid="email-input"]').type(invalidUser.email);
      cy.get('[data-testid="password-input"]').type(invalidUser.password);

      cy.get('[data-testid="login-submit"]').click();

      // Should stay on login page
      cy.url().should('include', '/auth/login');

      // Should show error message
      cy.get('[data-testid="error-message"]')
        .should('be.visible')
        .and('contain', 'email or password');

      // Should not store any auth data
      cy.window().its('localStorage.user').should('be.null');
      cy.window().its('localStorage.tokens').should('be.null');
    });

    it('should show validation errors for empty fields', () => {
      // Submit empty form
      cy.get('[data-testid="login-submit"]').click();

      // Should show field errors
      cy.get('[data-testid="email-error"]').should('be.visible');
      cy.get('[data-testid="password-error"]').should('be.visible');
    });

    it('should remember user choice (Remember Me)', () => {
      // Login with remember me checked
      cy.get('[data-testid="email-input"]').type(testUser.email);
      cy.get('[data-testid="password-input"]').type(testUser.password);
      cy.get('[data-testid="remember-me"]').check();

      cy.get('[data-testid="login-submit"]').click();

      // Should set appropriate cookie expiration
      cy.getCookie('tokens').should('exist');
      cy.getCookie('user').should('exist');
    });
  });

  describe('🧪 TDD: Logout Flow', () => {
    beforeEach(() => {
      // Setup authenticated user
      cy.createTestUser(testUser);
      cy.loginAs(testUser.email, testUser.password);
    });

    it('should logout successfully', () => {
      // Navigate to dashboard to confirm we're logged in
      cy.visit('/dashboard');
      cy.url().should('include', '/dashboard');

      // Logout via UI
      cy.get('[data-testid="user-menu"]').click();
      cy.get('[data-testid="logout-button"]').click();

      // Should redirect to login page
      cy.url().should('include', '/auth/login');

      // Should clear all auth data
      cy.window().its('localStorage.user').should('be.null');
      cy.window().its('localStorage.tokens').should('be.null');
      cy.getCookie('user').should('not.exist');
      cy.getCookie('tokens').should('not.exist');
    });

    it('should logout when refresh token expires', () => {
      // Mock expired refresh token
      cy.window().then((win) => {
        const expiredTokens = {
          access: {
            token: 'expired-access-token',
            expires: new Date(Date.now() - 1000).toISOString(),
          },
          refresh: {
            token: 'expired-refresh-token',
            expires: new Date(Date.now() - 1000).toISOString(),
          },
        };
        win.localStorage.setItem('tokens', JSON.stringify(expiredTokens));
      });

      // Try to access protected route
      cy.visit('/dashboard');

      // Should redirect to login due to expired tokens
      cy.url().should('include', '/auth/login');
    });
  });

  describe('🧪 TDD: Token Management', () => {
    beforeEach(() => {
      cy.createTestUser(testUser);
    });

    it('should automatically refresh access token', () => {
      cy.loginAs(testUser.email, testUser.password);

      // Get initial tokens
      cy.window()
        .its('localStorage.tokens')
        .then((tokensString) => {
          const initialTokens = JSON.parse(tokensString);

          // Mock nearly expired access token
          const nearExpiredTokens = {
            ...initialTokens,
            access: {
              ...initialTokens.access,
              expires: new Date(Date.now() + 60000).toISOString(), // 1 minute from now
            },
          };

          cy.window().then((win) => {
            win.localStorage.setItem(
              'tokens',
              JSON.stringify(nearExpiredTokens)
            );
          });

          // Make API call that should trigger token refresh
          cy.request({
            method: 'GET',
            url: '/api/v1/users/me',
            headers: {
              Authorization: `Bearer ${nearExpiredTokens.access.token}`,
            },
            failOnStatusCode: false,
          }).then((response) => {
            // Token refresh should happen automatically
            cy.window()
              .its('localStorage.tokens')
              .then((newTokensString) => {
                const newTokens = JSON.parse(newTokensString);
                expect(newTokens.access.token).to.not.equal(
                  initialTokens.access.token
                );
              });
          });
        });
    });

    it('should handle API calls with valid token', () => {
      cy.loginAs(testUser.email, testUser.password);

      // Make authenticated API call
      cy.window()
        .its('localStorage.tokens')
        .then((tokensString) => {
          const tokens = JSON.parse(tokensString);

          cy.request({
            method: 'GET',
            url: '/api/v1/users/me',
            headers: {
              Authorization: `Bearer ${tokens.access.token}`,
            },
          }).then((response) => {
            expect(response.status).to.equal(200);
            expect(response.body).to.have.property('email', testUser.email);
          });
        });
    });
  });

  describe('🧪 TDD: Protected Routes', () => {
    it('should redirect unauthenticated user to login', () => {
      // Try to access protected route without authentication
      cy.visit('/dashboard');

      // Should redirect to login with callback URL
      cy.url().should('include', '/auth/login');
      cy.url().should('include', 'callbackUrl=%2Fdashboard');
    });

    it('should allow access to protected route when authenticated', () => {
      cy.createTestUser(testUser);
      cy.loginAs(testUser.email, testUser.password);

      // Should be able to access protected route
      cy.visit('/dashboard');
      cy.url().should('include', '/dashboard');

      // Should see user-specific content
      cy.get('[data-testid="user-greeting"]').should('contain', testUser.name);
    });

    it('should redirect from auth pages when already authenticated', () => {
      cy.createTestUser(testUser);
      cy.loginAs(testUser.email, testUser.password);

      // Try to visit login page when already authenticated
      cy.visit('/auth/login');

      // Should redirect to dashboard
      cy.url().should('include', '/dashboard');
    });
  });

  describe('🧪 TDD: Password Reset Flow', () => {
    beforeEach(() => {
      cy.createTestUser(testUser);
    });

    it('should initiate password reset successfully', () => {
      cy.get('[data-testid="forgot-password-link"]').click();
      cy.url().should('include', '/auth/forgot-password');

      // Enter email
      cy.get('[data-testid="email-input"]').type(testUser.email);
      cy.get('[data-testid="submit-button"]').click();

      // Should show success message
      cy.get('[data-testid="success-message"]')
        .should('be.visible')
        .and('contain', 'reset link');
    });

    it('should handle invalid email in password reset', () => {
      cy.get('[data-testid="forgot-password-link"]').click();

      // Enter non-existent email
      cy.get('[data-testid="email-input"]').type('<EMAIL>');
      cy.get('[data-testid="submit-button"]').click();

      // Should show error message
      cy.get('[data-testid="error-message"]')
        .should('be.visible')
        .and('contain', 'email');
    });
  });

  describe('🧪 TDD: Session Management', () => {
    beforeEach(() => {
      cy.createTestUser(testUser);
    });

    it('should maintain session across page refreshes', () => {
      cy.loginAs(testUser.email, testUser.password);
      cy.visit('/dashboard');

      // Refresh page
      cy.reload();

      // Should still be authenticated
      cy.url().should('include', '/dashboard');
      cy.get('[data-testid="user-greeting"]').should('be.visible');
    });

    it('should handle concurrent login sessions', () => {
      // Login in first session
      cy.loginAs(testUser.email, testUser.password);

      // Simulate login in another session (new tokens)
      cy.createTestUser({
        ...testUser,
        email: '<EMAIL>',
      });

      cy.clearAuthData();
      cy.loginAs('<EMAIL>', testUser.password);

      // Original session should still work independently
      cy.window().its('localStorage.tokens').should('exist');
    });
  });

  describe('🧪 TDD: API Integration', () => {
    beforeEach(() => {
      cy.createTestUser(testUser);
    });

    it('should authenticate API requests correctly', () => {
      cy.loginAs(testUser.email, testUser.password);

      // Test authenticated endpoint
      cy.window()
        .its('localStorage.tokens')
        .then((tokensString) => {
          const tokens = JSON.parse(tokensString);

          cy.request({
            method: 'GET',
            url: `${Cypress.env('apiUrl')}/users/me`,
            headers: {
              Authorization: `Bearer ${tokens.access.token}`,
            },
          }).then((response) => {
            expect(response.status).to.equal(200);
            expect(response.body.email).to.equal(testUser.email);
          });
        });
    });

    it('should handle unauthorized API responses', () => {
      // Make request with invalid token
      cy.request({
        method: 'GET',
        url: `${Cypress.env('apiUrl')}/users/me`,
        headers: {
          Authorization: 'Bearer invalid-token',
        },
        failOnStatusCode: false,
      }).then((response) => {
        expect(response.status).to.equal(401);
      });
    });
  });

  describe('🧪 TDD: User Interface Integration', () => {
    beforeEach(() => {
      cy.createTestUser(testUser);
    });

    it('should update UI state based on authentication', () => {
      // Unauthenticated state
      cy.visit('/');
      cy.get('[data-testid="login-button"]').should('be.visible');
      cy.get('[data-testid="user-menu"]').should('not.exist');

      // Login
      cy.loginAs(testUser.email, testUser.password);
      cy.visit('/');

      // Authenticated state
      cy.get('[data-testid="user-menu"]').should('be.visible');
      cy.get('[data-testid="login-button"]').should('not.exist');
    });

    it('should show proper loading states during authentication', () => {
      cy.visit('/auth/login');

      // Mock slow network
      cy.intercept('POST', '**/auth/login', {
        delay: 2000,
        statusCode: 200,
        body: {
          user: testUser,
          tokens: {
            access: { token: 'mock-access', expires: new Date() },
            refresh: { token: 'mock-refresh', expires: new Date() },
          },
        },
      }).as('slowLogin');

      cy.get('[data-testid="email-input"]').type(testUser.email);
      cy.get('[data-testid="password-input"]').type(testUser.password);
      cy.get('[data-testid="login-submit"]').click();

      // Should show loading state
      cy.get('[data-testid="login-submit"]').should('be.disabled');
      cy.get('[data-testid="loading-spinner"]').should('be.visible');

      cy.wait('@slowLogin');
    });
  });
});
