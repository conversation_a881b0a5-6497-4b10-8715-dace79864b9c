import { faker } from '@faker-js/faker';
import prisma from '../../src/client';
import { Prisma } from '@prisma/client';

// Data provinsi sample
export const provinceOne = {
  id: 1,
  name: 'DKI Jakarta',
  code: '31'
};

export const provinceTwo = {
  id: 2,
  name: 'Jawa Barat',
  code: '32'
};

export const provinceThree = {
  id: 3,
  name: 'Jawa Tengah',
  code: '33'
};

// Data kota/kabupaten sample
export const cityOne = {
  id: 1,
  name: 'Jakarta Pusat',
  code: '3171',
  provinceId: 1
};

export const cityTwo = {
  id: 2,
  name: 'Jakarta Selatan',
  code: '3174',
  provinceId: 1
};

export const cityThree = {
  id: 3,
  name: 'Bandung',
  code: '3273',
  provinceId: 2
};

export const cityFour = {
  id: 4,
  name: 'Bekasi',
  code: '3275',
  provinceId: 2
};

export const cityFive = {
  id: 5,
  name: 'Semarang',
  code: '3374',
  provinceId: 3
};

// Helper functions untuk insert data
export const insertProvinces = async (provinces: Prisma.ProvinceCreateManyInput[]) => {
  await prisma.province.createMany({
    data: provinces,
    skipDuplicates: true
  });
};

export const insertCities = async (cities: Prisma.CityCreateManyInput[]) => {
  await prisma.city.createMany({
    data: cities,
    skipDuplicates: true
  });
};

// Data lengkap untuk testing
export const allProvinces = [provinceOne, provinceTwo, provinceThree];
export const allCities = [cityOne, cityTwo, cityThree, cityFour, cityFive];
