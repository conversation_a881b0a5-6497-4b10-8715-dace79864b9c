import httpStatus from 'http-status';
import pick from '../utils/pick';
import catchAsync from '../utils/catchAsync';
import { locationService } from '../services';

const getProvinces = catchAsync(async (req, res) => {
  const options = pick(req.query, ['sortBy', 'sortOrder']);
  const result = await locationService.getProvinces(options);
  res.send(result);
});

const getCities = catchAsync(async (req, res) => {
  const filter = pick(req.query, ['provinceId']);
  const options = pick(req.query, ['includeProvince', 'sortBy', 'sortOrder']);

  // Convert string to boolean for includeProvince
  if (options.includeProvince !== undefined) {
    options.includeProvince =
      options.includeProvince === 'true' || options.includeProvince === true;
  }

  const result = await locationService.getCities(filter, options);
  res.send(result);
});

const getCitiesByProvince = catchAsync(async (req, res) => {
  const provinceId = parseInt(req.params.provinceId);
  const options = pick(req.query, ['sortBy', 'sortOrder']);
  const result = await locationService.getCitiesByProvinceId(provinceId, options);
  res.send(result);
});

const searchLocations = catchAsync(async (req, res) => {
  const { q: query } = req.query;
  const options = pick(req.query, ['limit']);
  const result = await locationService.searchLocations(query as string, options);
  res.send(result);
});

const getTimezones = catchAsync(async (req, res) => {
  const result = await locationService.getTimezones();
  res.send(result);
});

export default {
  getProvinces,
  getCities,
  getCitiesByProvince,
  searchLocations,
  getTimezones
};
