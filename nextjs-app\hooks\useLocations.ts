import { useQuery } from '@tanstack/react-query';
import {
  locationAPI,
  type Province,
  type City,
  type Timezone,
} from '@/lib/api/locations';

// Hook untuk mengambil semua provinsi
export function useProvinces(params?: {
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}) {
  return useQuery({
    queryKey: ['provinces', params],
    queryFn: () => locationAPI.getProvinces(params),
    staleTime: 30 * 60 * 1000, // 30 minutes (data provinsi jarang berubah)
  });
}

// Hook untuk mengambil kota berdasarkan provinsi
export function useCitiesByProvince(
  provinceId: number | null,
  params?: {
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }
) {
  return useQuery({
    queryKey: ['cities', 'province', provinceId, params],
    queryFn: () => locationAPI.getCitiesByProvince(provinceId!, params),
    enabled: !!provinceId,
    staleTime: 30 * 60 * 1000, // 30 minutes
  });
}

// Hook untuk mengambil semua kota
export function useCities(params?: {
  provinceId?: number;
  includeProvince?: boolean;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}) {
  return useQuery({
    queryKey: ['cities', params],
    queryFn: () => locationAPI.getCities(params),
    staleTime: 30 * 60 * 1000, // 30 minutes
  });
}

// Hook untuk search location
export function useSearchLocations(
  query: string,
  params?: {
    limit?: number;
  }
) {
  return useQuery({
    queryKey: ['locations', 'search', query, params],
    queryFn: () => locationAPI.searchLocations(query, params),
    enabled: query.length >= 2, // Minimal 2 karakter untuk search
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook untuk mengambil semua timezone Indonesia
export function useTimezones() {
  return useQuery({
    queryKey: ['timezones'],
    queryFn: () => locationAPI.getTimezones(),
    staleTime: 60 * 60 * 1000, // 1 hour (data timezone sangat jarang berubah)
  });
}
