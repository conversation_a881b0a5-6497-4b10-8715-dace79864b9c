import request from 'supertest';
import { faker } from '@faker-js/faker';
import httpStatus from 'http-status';
import { describe, beforeEach, test, expect, jest, afterAll } from '@jest/globals';
import app from '../../src/app';
import { clearDatabase, setupTestDBOptimized } from '../utils/setupTestDb';
import { generateUserOneAccessToken, generateUserTwoAccessToken } from '../fixtures/token.fixture';
import { userOne, userTwo, insertUsers } from '../fixtures/user.fixture';
import { outletOne, outletTwo, insertOutlets } from '../fixtures/outlet.fixture';
import { customerOne, customerTwo, insertCustomers } from '../fixtures/customer.fixture';
import {
  serviceOne,
  serviceTwo,
  insertServices,
  insertOutletServices
} from '../fixtures/service.fixture';
import prisma from '../../src/client';

describe('Order routes', () => {
  setupTestDBOptimized();

  let dbUserOne: any;
  let dbUserTwo: any;
  let dbOutletOne: any;
  let dbOutletTwo: any;
  let dbCustomerOne: any;
  let dbCustomerTwo: any;
  let dbServiceOne: any;
  let dbServiceTwo: any;
  let userOneToken: string;
  let userTwoToken: string;

  beforeEach(async () => {
    await clearDatabase();

    // Insert users and get actual IDs
    const insertedUsers = await insertUsers([userOne, userTwo]);
    dbUserOne = insertedUsers[0];
    dbUserTwo = insertedUsers[1];

    // Insert outlets with actual user IDs
    const insertedOutlets = await insertOutlets([outletOne, outletTwo], dbUserOne.id);
    dbOutletOne = insertedOutlets[0];
    dbOutletTwo = insertedOutlets[1];

    // Update users with outlet IDs
    await prisma.user.update({
      where: { id: dbUserOne.id },
      data: { outletId: dbOutletOne.id }
    });
    await prisma.user.update({
      where: { id: dbUserTwo.id },
      data: { outletId: dbOutletTwo.id }
    });

    // Refresh user data
    dbUserOne = await prisma.user.findUnique({ where: { id: dbUserOne.id } });
    dbUserTwo = await prisma.user.findUnique({ where: { id: dbUserTwo.id } });

    // Insert customers with actual outlet IDs
    const insertedCustomersOutlet1 = await insertCustomers([customerOne], dbOutletOne.id);
    const insertedCustomersOutlet2 = await insertCustomers([customerTwo], dbOutletTwo.id);
    dbCustomerOne = insertedCustomersOutlet1[0];
    dbCustomerTwo = insertedCustomersOutlet2[0];

    // Insert services and get actual IDs
    const insertedServices = await insertServices([serviceOne, serviceTwo], dbOutletOne.id);
    dbServiceOne = insertedServices[0];
    dbServiceTwo = insertedServices[1];

    // Services are now created directly with outletId, no need for outlet services

    // Generate tokens with actual user IDs
    userOneToken = generateUserOneAccessToken(dbUserOne.id);
    userTwoToken = generateUserTwoAccessToken(dbUserTwo.id);
  });

  describe('POST /v1/orders', () => {
    let createOrderData: any;

    beforeEach(() => {
      createOrderData = {
        customerId: dbCustomerOne.id,
        items: [
          {
            serviceId: dbServiceOne.id,
            quantity: 2.5,
            weight: 2.5,
            price: 10000,
            subtotal: 25000
          }
        ],
        notes: 'Tolong setrika rapi',
        pickupDate: new Date().toISOString(),
        estimatedFinish: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
      };
    });

    test('should return 201 and successfully create order if data is ok', async () => {
      const res = await request(app)
        .post('/v1/orders')
        .set('Authorization', `Bearer ${userOneToken}`)
        .send(createOrderData)
        .expect(httpStatus.CREATED);

      expect(res.body).toEqual({
        id: expect.any(Number),
        orderNumber: expect.stringMatching(/^ORD\/\d{6}\/\d{3}$/),
        status: 'PENDING',
        totalPrice: 25000,
        totalWeight: 2.5,
        paidAmount: 0,
        paymentStatus: 'UNPAID',
        paymentMethod: null,
        notes: createOrderData.notes,
        pickupDate: expect.any(String),
        deliveryDate: null,
        estimatedFinish: expect.any(String),
        actualFinish: null,
        outletId: dbUserOne.outletId,
        customerId: dbCustomerOne.id,
        items: expect.arrayContaining([
          expect.objectContaining({
            serviceId: dbServiceOne.id,
            quantity: 2.5,
            weight: 2.5,
            price: 10000,
            subtotal: 25000
          })
        ]),
        createdAt: expect.any(String),
        updatedAt: expect.any(String)
      });
    });

    test('should return 400 error if customerId is invalid', async () => {
      const invalidData = { ...createOrderData, customerId: 999999 };

      await request(app)
        .post('/v1/orders')
        .set('Authorization', `Bearer ${userOneToken}`)
        .send(invalidData)
        .expect(httpStatus.BAD_REQUEST);
    });

    test('should return 400 error if items array is empty', async () => {
      const invalidData = { ...createOrderData, items: [] };

      await request(app)
        .post('/v1/orders')
        .set('Authorization', `Bearer ${userOneToken}`)
        .send(invalidData)
        .expect(httpStatus.BAD_REQUEST);
    });

    test('should return 401 error if access token is missing', async () => {
      await request(app).post('/v1/orders').send(createOrderData).expect(httpStatus.UNAUTHORIZED);
    });

    test('should return 403 error if user tries to create order for different outlet customer', async () => {
      const differentOutletCustomerData = { ...createOrderData, customerId: dbCustomerTwo.id };

      await request(app)
        .post('/v1/orders')
        .set('Authorization', `Bearer ${userOneToken}`)
        .send(differentOutletCustomerData)
        .expect(httpStatus.FORBIDDEN);
    });
  });

  describe('GET /v1/orders', () => {
    beforeEach(async () => {
      // Insert some test orders
      await request(app)
        .post('/v1/orders')
        .set('Authorization', `Bearer ${userOneToken}`)
        .send({
          customerId: dbCustomerOne.id,
          items: [
            {
              serviceId: dbServiceOne.id,
              quantity: 1,
              weight: 1,
              price: 10000,
              subtotal: 10000
            }
          ],
          notes: 'Test order 1'
        });

      await request(app)
        .post('/v1/orders')
        .set('Authorization', `Bearer ${userOneToken}`)
        .send({
          customerId: dbCustomerOne.id,
          items: [
            {
              serviceId: dbServiceTwo.id,
              quantity: 2,
              weight: 2,
              price: 15000,
              subtotal: 30000
            }
          ],
          notes: 'Test order 2'
        });
    });

    test('should return 200 and apply the default query options', async () => {
      const res = await request(app)
        .get('/v1/orders')
        .set('Authorization', `Bearer ${userOneToken}`)
        .expect(httpStatus.OK);

      expect(res.body).toEqual({
        results: expect.any(Array),
        page: 1,
        limit: 10,
        totalPages: expect.any(Number),
        totalResults: expect.any(Number)
      });
      expect(res.body.results).toHaveLength(2);
      expect(res.body.results[0]).toMatchObject({
        id: expect.any(Number),
        orderNumber: expect.any(String),
        status: expect.any(String),
        totalPrice: expect.any(Number),
        totalWeight: expect.any(Number),
        paidAmount: expect.any(Number),
        paymentStatus: expect.any(String),
        outletId: expect.any(Number),
        customerId: expect.any(Number),
        customer: expect.objectContaining({
          id: expect.any(Number),
          name: expect.any(String)
        }),
        items: expect.any(Array),
        createdAt: expect.any(String),
        updatedAt: expect.any(String)
      });
    });

    test('should correctly apply filter on paymentStatus field', async () => {
      const res = await request(app)
        .get('/v1/orders')
        .set('Authorization', `Bearer ${userOneToken}`)
        .query({ paymentStatus: 'UNPAID' })
        .expect(httpStatus.OK);

      expect(res.body.results).toHaveLength(2);
      res.body.results.forEach((order: any) => {
        expect(order.paymentStatus).toBe('UNPAID');
      });
    });

    test('should correctly sort the returned array if descending sort param is specified', async () => {
      const res = await request(app)
        .get('/v1/orders')
        .set('Authorization', `Bearer ${userOneToken}`)
        .query({ sortBy: 'createdAt:desc' })
        .expect(httpStatus.OK);

      expect(res.body.results).toHaveLength(2);
      expect(new Date(res.body.results[0].createdAt).getTime()).toBeGreaterThan(
        new Date(res.body.results[1].createdAt).getTime()
      );
    });

    test('should correctly apply filter on search field', async () => {
      const res = await request(app)
        .get('/v1/orders')
        .set('Authorization', `Bearer ${userOneToken}`)
        .query({ search: 'Test order 1' })
        .expect(httpStatus.OK);

      expect(res.body.results).toHaveLength(1);
      expect(res.body.results[0].notes).toContain('Test order 1');
    });

    test('should limit returned array if limit param is specified', async () => {
      const res = await request(app)
        .get('/v1/orders')
        .set('Authorization', `Bearer ${userOneToken}`)
        .query({ limit: 1 })
        .expect(httpStatus.OK);

      expect(res.body.results).toHaveLength(1);
      expect(res.body.limit).toBe(1);
    });

    test('should return 401 error if access token is missing', async () => {
      await request(app).get('/v1/orders').expect(httpStatus.UNAUTHORIZED);
    });
  });

  describe('GET /v1/orders/:orderId', () => {
    let orderOne: any;

    beforeEach(async () => {
      const orderRes = await request(app)
        .post('/v1/orders')
        .set('Authorization', `Bearer ${userOneToken}`)
        .send({
          customerId: dbCustomerOne.id,
          items: [
            {
              serviceId: dbServiceOne.id,
              quantity: 1,
              weight: 1,
              price: 10000,
              subtotal: 10000
            }
          ],
          notes: 'Test order for get by id'
        });
      orderOne = orderRes.body;
    });

    test('should return 200 and the order object if data is ok', async () => {
      const res = await request(app)
        .get(`/v1/orders/${orderOne.id}`)
        .set('Authorization', `Bearer ${userOneToken}`)
        .expect(httpStatus.OK);

      expect(res.body).toEqual({
        id: orderOne.id,
        orderNumber: orderOne.orderNumber,
        status: 'PENDING',
        totalPrice: 10000,
        totalWeight: 1,
        paidAmount: 0,
        paymentStatus: 'UNPAID',
        paymentMethod: null,
        notes: 'Test order for get by id',
        pickupDate: null,
        deliveryDate: null,
        estimatedFinish: null,
        actualFinish: null,
        outletId: dbUserOne.outletId,
        customerId: dbCustomerOne.id,
        customer: expect.objectContaining({
          id: dbCustomerOne.id,
          name: dbCustomerOne.name
        }),
        items: expect.arrayContaining([
          expect.objectContaining({
            serviceId: dbServiceOne.id,
            quantity: 1,
            weight: 1,
            price: 10000,
            subtotal: 10000,
            service: expect.objectContaining({
              id: dbServiceOne.id,
              name: dbServiceOne.name
            })
          })
        ]),
        payments: [],
        createdAt: expect.any(String),
        updatedAt: expect.any(String)
      });
    });

    test('should return 401 error if access token is missing', async () => {
      await request(app).get(`/v1/orders/${orderOne.id}`).expect(httpStatus.UNAUTHORIZED);
    });

    test('should return 403 error if user tries to access order from different outlet', async () => {
      await request(app)
        .get(`/v1/orders/${orderOne.id}`)
        .set('Authorization', `Bearer ${userTwoToken}`)
        .expect(httpStatus.FORBIDDEN);
    });

    test('should return 404 error if order is not found', async () => {
      await request(app)
        .get('/v1/orders/999999')
        .set('Authorization', `Bearer ${userOneToken}`)
        .expect(httpStatus.NOT_FOUND);
    });
  });

  describe('PATCH /v1/orders/:orderId', () => {
    let orderOne: any;
    const updateBody = {
      notes: 'Updated notes',
      deliveryDate: new Date().toISOString()
    };

    beforeEach(async () => {
      const orderRes = await request(app)
        .post('/v1/orders')
        .set('Authorization', `Bearer ${userOneToken}`)
        .send({
          customerId: dbCustomerOne.id,
          items: [
            {
              serviceId: dbServiceOne.id,
              quantity: 1,
              weight: 1,
              price: 10000,
              subtotal: 10000
            }
          ],
          notes: 'Original notes'
        });
      orderOne = orderRes.body;
    });

    test('should return 200 and successfully update order if data is ok', async () => {
      const res = await request(app)
        .patch(`/v1/orders/${orderOne.id}`)
        .set('Authorization', `Bearer ${userOneToken}`)
        .send(updateBody)
        .expect(httpStatus.OK);

      expect(res.body).toMatchObject({
        id: orderOne.id,
        notes: updateBody.notes,
        deliveryDate: expect.any(String)
      });
    });

    test('should return 401 error if access token is missing', async () => {
      await request(app)
        .patch(`/v1/orders/${orderOne.id}`)
        .send(updateBody)
        .expect(httpStatus.UNAUTHORIZED);
    });

    test('should return 403 error if user tries to update order from different outlet', async () => {
      await request(app)
        .patch(`/v1/orders/${orderOne.id}`)
        .set('Authorization', `Bearer ${userTwoToken}`)
        .send(updateBody)
        .expect(httpStatus.FORBIDDEN);
    });

    test('should return 404 error if order is not found', async () => {
      await request(app)
        .patch('/v1/orders/999999')
        .set('Authorization', `Bearer ${userOneToken}`)
        .send(updateBody)
        .expect(httpStatus.NOT_FOUND);
    });
  });

  describe('PATCH /v1/orders/:orderId/status', () => {
    let orderOne: any;

    beforeEach(async () => {
      const orderRes = await request(app)
        .post('/v1/orders')
        .set('Authorization', `Bearer ${userOneToken}`)
        .send({
          customerId: dbCustomerOne.id,
          items: [
            {
              serviceId: dbServiceOne.id,
              quantity: 1,
              weight: 1,
              price: 10000,
              subtotal: 10000
            }
          ]
        });
      orderOne = orderRes.body;
    });

    test('should return 200 and successfully update order status', async () => {
      const res = await request(app)
        .patch(`/v1/orders/${orderOne.id}/status`)
        .set('Authorization', `Bearer ${userOneToken}`)
        .send({ status: 'PROCESSING' })
        .expect(httpStatus.OK);

      expect(res.body).toMatchObject({
        id: orderOne.id,
        status: 'PROCESSING'
      });
    });

    test('should return 400 error if status is invalid', async () => {
      await request(app)
        .patch(`/v1/orders/${orderOne.id}/status`)
        .set('Authorization', `Bearer ${userOneToken}`)
        .send({ status: 'INVALID_STATUS' })
        .expect(httpStatus.BAD_REQUEST);
    });

    test('should return 401 error if access token is missing', async () => {
      await request(app)
        .patch(`/v1/orders/${orderOne.id}/status`)
        .send({ status: 'PROCESSING' })
        .expect(httpStatus.UNAUTHORIZED);
    });

    test('should return 404 error if order is not found', async () => {
      await request(app)
        .patch('/v1/orders/999999/status')
        .set('Authorization', `Bearer ${userOneToken}`)
        .send({ status: 'PROCESSING' })
        .expect(httpStatus.NOT_FOUND);
    });
  });

  describe('POST /v1/orders/:orderId/payments', () => {
    let orderOne: any;
    const paymentData = {
      amount: 5000,
      method: 'CASH',
      reference: 'CASH-001',
      notes: 'Pembayaran sebagian'
    };

    beforeEach(async () => {
      const orderRes = await request(app)
        .post('/v1/orders')
        .set('Authorization', `Bearer ${userOneToken}`)
        .send({
          customerId: dbCustomerOne.id,
          items: [
            {
              serviceId: dbServiceOne.id,
              quantity: 1,
              weight: 1,
              price: 10000,
              subtotal: 10000
            }
          ]
        });
      orderOne = orderRes.body;
    });

    test('should return 201 and successfully add payment to order', async () => {
      const res = await request(app)
        .post(`/v1/orders/${orderOne.id}/payments`)
        .set('Authorization', `Bearer ${userOneToken}`)
        .send(paymentData)
        .expect(httpStatus.CREATED);

      expect(res.body).toEqual({
        id: expect.any(Number),
        amount: paymentData.amount,
        method: paymentData.method,
        reference: paymentData.reference,
        notes: paymentData.notes,
        orderId: orderOne.id,
        createdAt: expect.any(String),
        updatedAt: expect.any(String)
      });

      // Verify order payment status is updated
      const orderRes = await request(app)
        .get(`/v1/orders/${orderOne.id}`)
        .set('Authorization', `Bearer ${userOneToken}`)
        .expect(httpStatus.OK);

      expect(orderRes.body.paidAmount).toBe(5000);
      expect(orderRes.body.paymentStatus).toBe('PARTIAL');
    });

    test('should update payment status to PAID when full payment is made', async () => {
      const fullPaymentData = { ...paymentData, amount: 10000 };

      await request(app)
        .post(`/v1/orders/${orderOne.id}/payments`)
        .set('Authorization', `Bearer ${userOneToken}`)
        .send(fullPaymentData)
        .expect(httpStatus.CREATED);

      const orderRes = await request(app)
        .get(`/v1/orders/${orderOne.id}`)
        .set('Authorization', `Bearer ${userOneToken}`)
        .expect(httpStatus.OK);

      expect(orderRes.body.paidAmount).toBe(10000);
      expect(orderRes.body.paymentStatus).toBe('PAID');
    });

    test('should return 400 error if payment amount exceeds remaining balance', async () => {
      const excessivePaymentData = { ...paymentData, amount: 15000 };

      await request(app)
        .post(`/v1/orders/${orderOne.id}/payments`)
        .set('Authorization', `Bearer ${userOneToken}`)
        .send(excessivePaymentData)
        .expect(httpStatus.BAD_REQUEST);
    });

    test('should return 401 error if access token is missing', async () => {
      await request(app)
        .post(`/v1/orders/${orderOne.id}/payments`)
        .send(paymentData)
        .expect(httpStatus.UNAUTHORIZED);
    });

    test('should return 404 error if order is not found', async () => {
      await request(app)
        .post('/v1/orders/999999/payments')
        .set('Authorization', `Bearer ${userOneToken}`)
        .send(paymentData)
        .expect(httpStatus.NOT_FOUND);
    });
  });

  describe('DELETE /v1/orders/:orderId', () => {
    let orderOne: any;

    beforeEach(async () => {
      const orderRes = await request(app)
        .post('/v1/orders')
        .set('Authorization', `Bearer ${userOneToken}`)
        .send({
          customerId: dbCustomerOne.id,
          items: [
            {
              serviceId: dbServiceOne.id,
              quantity: 1,
              weight: 1,
              price: 10000,
              subtotal: 10000
            }
          ]
        });
      orderOne = orderRes.body;
    });

    test('should return 204 and successfully delete order', async () => {
      await request(app)
        .delete(`/v1/orders/${orderOne.id}`)
        .set('Authorization', `Bearer ${userOneToken}`)
        .expect(httpStatus.NO_CONTENT);

      // Verify order is not found after deletion
      await request(app)
        .get(`/v1/orders/${orderOne.id}`)
        .set('Authorization', `Bearer ${userOneToken}`)
        .expect(httpStatus.NOT_FOUND);
    });

    test('should return 401 error if access token is missing', async () => {
      await request(app).delete(`/v1/orders/${orderOne.id}`).expect(httpStatus.UNAUTHORIZED);
    });

    test('should return 403 error if user tries to delete order from different outlet', async () => {
      await request(app)
        .delete(`/v1/orders/${orderOne.id}`)
        .set('Authorization', `Bearer ${userTwoToken}`)
        .expect(httpStatus.FORBIDDEN);
    });

    test('should return 404 error if order is not found', async () => {
      await request(app)
        .delete('/v1/orders/999999')
        .set('Authorization', `Bearer ${userOneToken}`)
        .expect(httpStatus.NOT_FOUND);
    });
  });
});
