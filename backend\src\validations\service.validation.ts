import Joi from 'joi';

const createService = {
  body: Joi.object().keys({
    name: Joi.string().required().trim().min(1).max(100),
    description: Joi.string().optional().max(500).allow(null),
    price: Joi.number().required().min(0),
    unit: Joi.string().required().valid('kg', 'pcs', 'm²', 'pair', 'lot'),
    estimationHours: Joi.number().integer().optional().min(1),
    isActive: Joi.boolean().optional(),
    outletId: Joi.number().integer().positive().required(),
    categoryId: Joi.number().integer().positive().optional()
  })
};

const getServices = {
  query: Joi.object().keys({
    search: Joi.string().optional().trim().allow(''),
    isActive: Joi.boolean().optional(),
    unit: Joi.string().optional().valid('kg', 'pcs', 'm²', 'pair', 'lot'),
    sortBy: Joi.string().optional(),
    limit: Joi.number().optional().integer().min(1).max(100),
    page: Joi.number().optional().integer().min(1),
    outletId: Joi.number().integer().positive().required()
  })
};

const getService = {
  params: Joi.object().keys({
    serviceId: Joi.number().integer().positive().required()
  }),
  query: Joi.object().keys({
    outletId: Joi.number().integer().positive().required()
  })
};

const updateService = {
  params: Joi.object().keys({
    serviceId: Joi.number().integer().positive().required()
  }),
  body: Joi.object()
    .keys({
      name: Joi.string().optional().trim().min(1).max(100),
      description: Joi.string().optional().max(500).allow(null),
      price: Joi.number().optional().min(0),
      unit: Joi.string().optional().valid('kg', 'pcs', 'm²', 'pair', 'lot'),
      estimationHours: Joi.number().integer().optional().min(1),
      isActive: Joi.boolean().optional(),
      outletId: Joi.number().integer().positive().required(),
      categoryId: Joi.number().integer().positive().optional()
    })
    .min(1)
};

const deleteService = {
  params: Joi.object().keys({
    serviceId: Joi.number().integer().positive().required()
  }),
  query: Joi.object().keys({
    outletId: Joi.number().integer().positive().required()
  })
};

export default {
  createService,
  getServices,
  getService,
  updateService,
  deleteService
};
