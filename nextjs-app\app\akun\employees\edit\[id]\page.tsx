'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Save, Upload, Loader2, AlertCircle } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Alert, AlertDescription } from '@/components/ui/alert';

import {
  useEmployee,
  useUpdateEmployee,
  getErrorMessage,
  getFieldErrors,
} from '@/hooks/useEmployees';
import { useAuth } from '@/lib/auth-context';
import type { UpdateEmployeeRequest } from '@/lib/api/employees';

export default function EditEmployeePage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const router = useRouter();
  const { activeOutlet } = useAuth();
  const [employeeId, setEmployeeId] = useState<string | null>(null);
  const [formData, setFormData] = useState<
    Omit<UpdateEmployeeRequest, 'outletId'>
  >({
    name: '',
    email: '',
    phone: '',
    isActive: true,
  });
  const [clientErrors, setClientErrors] = useState<Record<string, string>>({});

  // Handle Next.js 15 params
  useEffect(() => {
    const getParams = async () => {
      const resolvedParams = await params;
      setEmployeeId(resolvedParams.id);
    };
    getParams();
  }, [params]);

  const {
    data: employee,
    isLoading: employeeLoading,
    error: employeeError,
  } = useEmployee(employeeId ? parseInt(employeeId) : null);

  const updateEmployeeMutation = useUpdateEmployee();

  // Extract error details when mutation fails
  const apiError = updateEmployeeMutation.error;
  const apiErrorMessage = apiError ? getErrorMessage(apiError) : '';
  const fieldErrors = apiError ? getFieldErrors(apiError) : {};

  // Reset API errors when form data changes
  useEffect(() => {
    if (updateEmployeeMutation.error) {
      updateEmployeeMutation.reset();
    }
  }, [formData]);

  // Populate form when employee data is loaded
  useEffect(() => {
    if (employee) {
      setFormData({
        name: employee.name,
        email: employee.email,
        phone: employee.phone,
        isActive: employee.isActive,
      });
    }
  }, [employee]);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));

    // Clear client error for this field
    if (clientErrors[name]) {
      setClientErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  const handleSwitchChange = (checked: boolean) => {
    setFormData((prev) => ({ ...prev, isActive: checked }));
  };

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!formData.name?.trim()) {
      errors.name = 'Nama lengkap wajib diisi';
    }

    if (!formData.email?.trim()) {
      errors.email = 'Email wajib diisi';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = 'Format email tidak valid';
    }

    if (!formData.phone?.trim()) {
      errors.phone = 'Nomor telepon wajib diisi';
    } else if (!/^[0-9+]{10,15}$/.test(formData.phone)) {
      errors.phone = 'Nomor telepon harus 10-15 digit';
    }

    if (!activeOutlet?.id) {
      errors.outlet = 'Outlet aktif tidak ditemukan';
    }

    setClientErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!employeeId) return;

    if (!validateForm()) {
      return;
    }

    if (!activeOutlet?.id) {
      console.error('Active outlet not found');
      return;
    }

    try {
      await updateEmployeeMutation.mutateAsync({
        id: parseInt(employeeId),
        data: {
          ...formData,
          outletId: activeOutlet.id,
        },
      });
      router.push(`/akun/employees/${employeeId}`);
    } catch {
      // Error akan ditampilkan melalui fieldErrors dan apiErrorMessage
    }
  };

  const getFieldError = (fieldName: string): string => {
    return clientErrors[fieldName] || fieldErrors[fieldName] || '';
  };

  // Loading state
  if (!employeeId || employeeLoading) {
    return (
      <div className="flex flex-col min-h-screen bg-slate-50">
        <header className="p-4 flex justify-between items-center bg-white sticky top-0 z-10 shadow-sm">
          <div className="flex items-center">
            <Link href="/akun/employees" className="mr-3">
              <ArrowLeft className="h-5 w-5" />
            </Link>
            <h1 className="text-lg font-semibold">Edit Pegawai</h1>
          </div>
        </header>
        <main className="flex-1 p-4 flex items-center justify-center">
          <div className="flex items-center gap-2">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span>Memuat data pegawai...</span>
          </div>
        </main>
      </div>
    );
  }

  // Error state
  if (employeeError || !employee) {
    return (
      <div className="flex flex-col min-h-screen bg-slate-50">
        <header className="p-4 flex justify-between items-center bg-white sticky top-0 z-10 shadow-sm">
          <div className="flex items-center">
            <Link href="/akun/employees" className="mr-3">
              <ArrowLeft className="h-5 w-5" />
            </Link>
            <h1 className="text-lg font-semibold">Edit Pegawai</h1>
          </div>
        </header>
        <main className="flex-1 p-4 flex items-center justify-center">
          <div className="text-center">
            <h2 className="text-xl font-semibold mb-2">
              Pegawai tidak ditemukan
            </h2>
            <p className="text-gray-600 mb-4">
              Pegawai yang Anda cari tidak ditemukan atau telah dihapus.
            </p>
            <Link href="/akun/employees">
              <Button>Kembali ke Daftar Pegawai</Button>
            </Link>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen bg-slate-50">
      <header className="p-4 flex justify-between items-center bg-white sticky top-0 z-10 shadow-sm">
        <div className="flex items-center">
          <Link href={`/akun/employees/${employeeId}`} className="mr-3">
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <h1 className="text-lg font-semibold">Edit Pegawai</h1>
        </div>
        <Button
          type="submit"
          form="edit-employee-form"
          className="bg-blue-500 hover:bg-blue-600"
          disabled={updateEmployeeMutation.isPending}
        >
          <Save className="h-4 w-4 mr-2" />
          {updateEmployeeMutation.isPending ? 'Menyimpan...' : 'Simpan'}
        </Button>
      </header>

      <main className="flex-1 p-4 pb-20">
        <form id="edit-employee-form" onSubmit={handleSubmit}>
          {/* General Error Alert */}
          {apiErrorMessage && !Object.keys(fieldErrors).length && (
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{apiErrorMessage}</AlertDescription>
            </Alert>
          )}

          {/* Outlet Error Alert */}
          {clientErrors.outlet && (
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{clientErrors.outlet}</AlertDescription>
            </Alert>
          )}

          <Card className="p-4 space-y-4 mb-4">
            <div className="flex flex-col items-center justify-center">
              <Avatar className="h-24 w-24 mb-2">
                <AvatarImage src="/placeholder.svg" alt="Avatar" />
                <AvatarFallback>
                  {employee.name.substring(0, 2).toUpperCase()}
                </AvatarFallback>
              </Avatar>
              <Button
                type="button"
                variant="outline"
                size="sm"
                className="mt-2"
                disabled
              >
                <Upload className="h-4 w-4 mr-2" /> Ganti Foto (Coming Soon)
              </Button>
            </div>
          </Card>

          <Card className="p-4 space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">Nama Lengkap *</Label>
              <Input
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                placeholder="Masukkan nama lengkap"
                className={getFieldError('name') ? 'border-red-500' : ''}
                required
              />
              {getFieldError('name') && (
                <p className="text-sm text-red-500">{getFieldError('name')}</p>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="email">Email *</Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={handleChange}
                  placeholder="<EMAIL>"
                  className={getFieldError('email') ? 'border-red-500' : ''}
                  required
                />
                {getFieldError('email') && (
                  <p className="text-sm text-red-500">
                    {getFieldError('email')}
                  </p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="phone">Nomor Telepon *</Label>
                <Input
                  id="phone"
                  name="phone"
                  value={formData.phone}
                  onChange={handleChange}
                  placeholder="08xxxxxxxxxx"
                  className={getFieldError('phone') ? 'border-red-500' : ''}
                  required
                />
                {getFieldError('phone') && (
                  <p className="text-sm text-red-500">
                    {getFieldError('phone')}
                  </p>
                )}
              </div>
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="isActive" className="cursor-pointer">
                Status Pegawai Aktif
              </Label>
              <Switch
                id="isActive"
                checked={formData.isActive}
                onCheckedChange={handleSwitchChange}
              />
            </div>
          </Card>
        </form>
      </main>
    </div>
  );
}
