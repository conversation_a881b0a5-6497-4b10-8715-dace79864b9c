import httpStatus from 'http-status';
import catchAsync from '../utils/catchAsync';
import { promotionService } from '../services';
import pick from '../utils/pick';
import ApiError from '../utils/ApiError';
import logger from '../config/logger';
import { Request, Response } from 'express';

const createPromotion = catchAsync(async (req: Request, res: Response) => {
  const promotionData = { ...req.body };

  logger.info('Creating promotion', {
    userId: (req as any).user?.id,
    outletId: promotionData.outletId,
    promotionName: req.body.name,
    code: req.body.code
  });

  const promotion = await promotionService.createPromotion(promotionData);

  logger.info('Promotion created successfully', {
    userId: (req as any).user?.id,
    promotionId: promotion.id,
    promotionName: promotion.name,
    code: promotion.code
  });

  res.status(httpStatus.CREATED).send(promotion);
});

const getPromotions = catchAsync(async (req, res) => {
  const filter = pick(req.query, [
    'name',
    'code',
    'discountType',
    'isActive',
    'isExpired',
    'outletId'
  ]);
  const options = pick(req.query, ['sortBy', 'limit', 'page']);

  logger.debug('Fetching promotions', {
    userId: (req as any).user?.id,
    outletId: filter.outletId,
    filter,
    options
  });

  const result = await promotionService.queryPromotions(filter, options);

  logger.debug('Promotions fetched successfully', {
    userId: (req as any).user?.id,
    outletId: filter.outletId,
    count: result.results.length,
    totalResults: result.totalResults
  });

  res.send(result);
});

const getPromotion = catchAsync(async (req, res) => {
  const promotionId = parseInt(req.params.promotionId);

  logger.debug('Fetching promotion by ID', {
    userId: (req as any).user?.id,
    promotionId
  });

  const promotion = await promotionService.getPromotionById(promotionId);
  if (!promotion) {
    logger.warn('Promotion not found', {
      userId: (req as any).user?.id,
      promotionId
    });
    throw new ApiError(httpStatus.NOT_FOUND, 'Promotion not found');
  }

  logger.debug('Promotion found', {
    userId: (req as any).user?.id,
    promotionId: promotion.id,
    promotionName: promotion.name
  });

  res.send(promotion);
});

const updatePromotion = catchAsync(async (req, res) => {
  const promotionId = parseInt(req.params.promotionId);

  logger.info('Updating promotion', {
    userId: (req as any).user?.id,
    promotionId,
    updateData: req.body
  });

  const promotion = await promotionService.updatePromotionById(
    promotionId,
    req.body,
    req.body.outletId
  );

  logger.info('Promotion updated successfully', {
    userId: (req as any).user?.id,
    promotionId: promotion.id,
    promotionName: promotion.name
  });

  res.send(promotion);
});

const deletePromotion = catchAsync(async (req, res) => {
  const promotionId = parseInt(req.params.promotionId);

  logger.info('Deleting promotion', {
    userId: (req as any).user?.id,
    promotionId
  });

  await promotionService.deletePromotionById(promotionId, req.body.outletId);

  logger.info('Promotion deleted successfully', {
    userId: (req as any).user?.id,
    promotionId
  });

  res.status(httpStatus.NO_CONTENT).send();
});

const validatePromotion = catchAsync(async (req, res) => {
  const { code, orderTotal, customerId, outletId } = req.body;

  logger.info('Validating promotion', {
    userId: (req as any).user?.id,
    code,
    orderTotal,
    customerId,
    outletId
  });

  const result = await promotionService.validatePromotion(code, orderTotal, customerId, outletId);

  logger.info('Promotion validation successful', {
    userId: (req as any).user?.id,
    promotionId: result.promotion.id,
    discountAmount: result.discountAmount
  });

  res.send({
    promotion: result.promotion,
    discountAmount: result.discountAmount,
    isValid: true
  });
});

export default {
  createPromotion,
  getPromotions,
  getPromotion,
  updatePromotion,
  deletePromotion,
  validatePromotion
};
