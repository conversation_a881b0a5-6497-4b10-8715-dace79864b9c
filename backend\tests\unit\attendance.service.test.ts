import { AttendanceStatus } from '@prisma/client';
import { attendanceService } from '../../src/services';
import { describe, beforeEach, test, expect, jest, beforeAll, afterAll } from '@jest/globals';
import { insertUsers, userOne, userTwo, createOutletForOwner } from '../fixtures/user.fixture';
import {
  attendanceOne,
  attendanceTwo,
  scheduleOne,
  scheduleTwo,
  settingsOne,
  insertAttendances,
  insertWorkSchedules,
  insertAttendanceSettings
} from '../fixtures/attendance.fixture';
import moment from 'moment';
import prisma from '../../src/client';

// Simple setup for attendance tests only
beforeAll(async () => {
  await prisma.$connect();
}, 30000);

afterAll(async () => {
  await prisma.$disconnect();
}, 15000);

describe('AttendanceService', () => {
  let outletOne: any;
  let dbUserOne: any;
  let dbUserTwo: any;

  beforeEach(async () => {
    // Simple cleanup for attendance tables only - proper order to avoid foreign key constraints
    await prisma.attendance.deleteMany();
    await prisma.workSchedule.deleteMany();
    await prisma.attendanceSettings.deleteMany();
    await prisma.outlet.deleteMany();
    await prisma.token.deleteMany(); // Delete tokens before users
    await prisma.user.deleteMany();

    await insertUsers([userOne, userTwo]);

    // Get users from database with IDs
    dbUserOne = await prisma.user.findUnique({ where: { email: userOne.email } });
    dbUserTwo = await prisma.user.findUnique({ where: { email: userTwo.email } });

    outletOne = await createOutletForOwner(dbUserOne.id);

    // Insert default attendance settings
    await insertAttendanceSettings([{ ...settingsOne, outletId: outletOne.id }]);
  }, 30000);

  describe('checkIn', () => {
    test('should create attendance record for valid check-in', async () => {
      // Insert schedule for user
      await insertWorkSchedules([{ ...scheduleOne, userId: dbUserOne.id, outletId: outletOne.id }]);

      const checkInData = {
        userId: dbUserOne.id,
        outletId: outletOne.id,
        location: '-6.2088,106.8456',
        pin: '1234'
      };

      const result = await attendanceService.checkIn(checkInData);

      expect(result).toMatchObject({
        userId: dbUserOne.id,
        outletId: outletOne.id,
        status: AttendanceStatus.PRESENT,
        checkIn: expect.any(Date),
        location: '-6.2088,106.8456'
      });
    });

    test('should detect late check-in based on schedule', async () => {
      // Insert morning schedule (08:00 start) for Monday (dayOfWeek: 1)
      await insertWorkSchedules([{ ...scheduleOne, userId: dbUserOne.id, outletId: outletOne.id }]);

      // Create a Monday at 08:30 (30 minutes late)
      const lateTime = moment().startOf('week').add(1, 'day').hour(8).minute(30).second(0).toDate();

      const checkInData = {
        userId: dbUserOne.id,
        outletId: outletOne.id,
        location: '-6.2088,106.8456'
      };

      const result = await attendanceService.checkIn(checkInData, lateTime);

      expect(result).toMatchObject({
        status: AttendanceStatus.LATE,
        isLate: true,
        lateByMinutes: 30
      });
    });

    test('should prevent duplicate check-in for same day', async () => {
      // Insert existing attendance for today
      const today = moment().startOf('day').toDate();
      await insertAttendances([
        { ...attendanceOne, userId: dbUserOne.id, outletId: outletOne.id, date: today }
      ]);

      const checkInData = {
        userId: dbUserOne.id,
        outletId: outletOne.id,
        location: '-6.2088,106.8456'
      };

      await expect(attendanceService.checkIn(checkInData)).rejects.toThrow(
        'Anda sudah melakukan check-in hari ini'
      );
    });

    test('should validate PIN when required', async () => {
      // Update settings to require PIN
      await prisma.attendanceSettings.update({
        where: { outletId: outletOne.id },
        data: { requirePin: true }
      });

      const checkInData = {
        userId: dbUserOne.id,
        outletId: outletOne.id,
        location: '-6.2088,106.8456'
        // No PIN provided
      };

      await expect(attendanceService.checkIn(checkInData)).rejects.toThrow('PIN wajib diisi');
    });

    test('should validate photo when required', async () => {
      // Update settings to require photo
      await prisma.attendanceSettings.update({
        where: { outletId: outletOne.id },
        data: { requirePhoto: true }
      });

      const checkInData = {
        userId: dbUserOne.id,
        outletId: outletOne.id,
        location: '-6.2088,106.8456'
        // No photo provided
      };

      await expect(attendanceService.checkIn(checkInData)).rejects.toThrow('Foto wajib diupload');
    });
  });

  describe('checkOut', () => {
    beforeEach(async () => {
      // Create check-in record first with exact 08:00 time
      const today = moment().startOf('day').toDate();
      const checkInTime = moment().hour(8).minute(0).second(0).millisecond(0).toDate();
      await insertAttendances([
        {
          ...attendanceOne,
          userId: dbUserOne.id,
          outletId: outletOne.id,
          date: today,
          checkIn: checkInTime,
          checkOut: null
        }
      ]);
    });

    test('should update attendance record with check-out time', async () => {
      const checkOutData = {
        userId: dbUserOne.id,
        outletId: outletOne.id,
        notes: 'Selesai kerja'
      };

      const result = await attendanceService.checkOut(checkOutData);

      expect(result).toMatchObject({
        userId: dbUserOne.id,
        outletId: outletOne.id,
        checkOut: expect.any(Date),
        workingHours: expect.any(Number),
        notes: 'Selesai kerja'
      });
    });

    test('should calculate working hours correctly', async () => {
      // Create check-out time at exactly 17:00 (9 hours after 08:00 check-in)
      const checkOutTime = moment().hour(17).minute(0).second(0).millisecond(0).toDate();

      const checkOutData = {
        userId: dbUserOne.id,
        outletId: outletOne.id
      };

      const result = await attendanceService.checkOut(checkOutData, checkOutTime);

      expect(result.workingHours).toBe(9);
    });

    test('should calculate overtime hours when exceeding threshold', async () => {
      // Create check-out time at exactly 19:00 (11 hours after 08:00 check-in)
      const checkOutTime = moment().hour(19).minute(0).second(0).millisecond(0).toDate();

      const checkOutData = {
        userId: dbUserOne.id,
        outletId: outletOne.id
      };

      const result = await attendanceService.checkOut(checkOutData, checkOutTime);

      expect(result.workingHours).toBe(11);
      expect(result.overtimeHours).toBe(3); // 11 - 8 (threshold)
    });

    test('should prevent check-out without check-in', async () => {
      const checkOutData = {
        userId: dbUserTwo.id, // Different user who hasn't checked in
        outletId: outletOne.id
      };

      await expect(attendanceService.checkOut(checkOutData)).rejects.toThrow(
        'Anda belum melakukan check-in hari ini'
      );
    });

    test('should prevent duplicate check-out', async () => {
      // First check-out
      await attendanceService.checkOut({
        userId: dbUserOne.id,
        outletId: outletOne.id
      });

      // Second check-out attempt
      await expect(
        attendanceService.checkOut({
          userId: dbUserOne.id,
          outletId: outletOne.id
        })
      ).rejects.toThrow('Anda sudah melakukan check-out hari ini');
    });
  });

  describe('getTodayStatus', () => {
    test('should return attendance status for today', async () => {
      const today = moment().startOf('day').toDate();
      await insertAttendances([
        {
          ...attendanceOne,
          userId: dbUserOne.id,
          outletId: outletOne.id,
          date: today
        }
      ]);

      const result = await attendanceService.getTodayStatus(dbUserOne.id, outletOne.id);

      expect(result).toMatchObject({
        userId: dbUserOne.id,
        outletId: outletOne.id,
        status: AttendanceStatus.PRESENT,
        checkIn: expect.any(Date)
      });
    });

    test('should return null when no attendance record exists', async () => {
      const result = await attendanceService.getTodayStatus(dbUserOne.id, outletOne.id);

      expect(result).toBeNull();
    });
  });

  describe('findActiveSchedule', () => {
    test('should find active schedule for current day and time', async () => {
      // Insert schedule for Monday (dayOfWeek: 1)
      await insertWorkSchedules([{ ...scheduleOne, userId: dbUserOne.id, outletId: outletOne.id }]);

      // Create Monday morning time
      const mondayMorning = moment().day(1).hour(8).minute(0).toDate();

      const result = await attendanceService.findActiveSchedule(
        dbUserOne.id,
        outletOne.id,
        mondayMorning
      );

      expect(result).toMatchObject({
        userId: dbUserOne.id,
        outletId: outletOne.id,
        dayOfWeek: 1,
        startTime: '08:00',
        endTime: '16:00'
      });
    });

    test('should return null when no schedule exists for current day', async () => {
      // Insert schedule for Monday, but test with Sunday
      await insertWorkSchedules([
        { ...scheduleOne, userId: dbUserOne.id, outletId: outletOne.id, dayOfWeek: 1 }
      ]);

      const sundayMorning = moment().day(0).hour(8).minute(0).toDate();

      const result = await attendanceService.findActiveSchedule(
        dbUserOne.id,
        outletOne.id,
        sundayMorning
      );

      expect(result).toBeNull();
    });
  });

  describe('calculateLateMinutes', () => {
    test('should calculate late minutes correctly', async () => {
      const schedule = {
        startTime: '08:00',
        endTime: '16:00'
      };

      // Check-in at 08:30 (30 minutes late)
      const checkInTime = moment().hour(8).minute(30).toDate();

      const result = attendanceService.calculateLateMinutes(schedule, checkInTime);

      expect(result).toBe(30);
    });

    test('should return 0 for on-time check-in', async () => {
      const schedule = {
        startTime: '08:00',
        endTime: '16:00'
      };

      // Check-in at 08:00 (on time)
      const checkInTime = moment().hour(8).minute(0).toDate();

      const result = attendanceService.calculateLateMinutes(schedule, checkInTime);

      expect(result).toBe(0);
    });

    test('should return 0 for early check-in', async () => {
      const schedule = {
        startTime: '08:00',
        endTime: '16:00'
      };

      // Check-in at 07:30 (early)
      const checkInTime = moment().hour(7).minute(30).toDate();

      const result = attendanceService.calculateLateMinutes(schedule, checkInTime);

      expect(result).toBe(0);
    });
  });
});
