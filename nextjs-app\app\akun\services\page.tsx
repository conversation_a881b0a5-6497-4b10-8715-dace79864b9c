'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  ArrowLeft,
  Plus,
  Edit,
  Trash2,
  Search,
  Filter,
  MoreVertical,
} from 'lucide-react';
import { toast } from 'sonner';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  useServiceCategories,
  useServiceCategoriesCount,
  useCreateServiceCategory,
  useUpdateServiceCategory,
  useDeleteServiceCategory,
} from '@/hooks/useServiceCategories';
import {
  useServices,
  useCreateService,
  useUpdateService,
  useDeleteService,
} from '@/hooks/useServices';
import { useAuth } from '@/lib/auth-context';

interface ServiceVariant {
  id: number;
  name: string;
  price: number;
  duration: number; // in hours
  unit: string;
}

interface ProcessStep {
  id: string;
  label: string;
}

interface LocalServiceCategory {
  id: number;
  name: string;
  processes: string[]; // IDs of selected processes
  variants: ServiceVariant[];
}

// Zod schemas
const categoryFormSchema = z.object({
  name: z
    .string()
    .min(1, 'Nama kategori harus diisi')
    .min(2, 'Nama kategori minimal 2 karakter'),
  processes: z.array(z.string()).min(1, 'Pilih minimal 1 proses'),
});

const variantFormSchema = z.object({
  name: z
    .string()
    .min(1, 'Nama service harus diisi')
    .min(2, 'Nama service minimal 2 karakter'),
  price: z
    .string()
    .min(1, 'Harga harus diisi')
    .refine(
      (val) => !isNaN(Number(val)) && Number(val) > 0,
      'Harga harus berupa angka dan lebih dari 0'
    ),
  duration: z
    .string()
    .min(1, 'Durasi harus diisi')
    .refine(
      (val) => !isNaN(Number(val)) && Number(val) > 0,
      'Durasi harus berupa angka dan lebih dari 0'
    ),
  unit: z.string().min(1, 'Satuan harus dipilih'),
});

type CategoryFormData = z.infer<typeof categoryFormSchema>;
type VariantFormData = z.infer<typeof variantFormSchema>;

export default function ServicesPage() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState('');

  const { activeOutlet } = useAuth();

  // Debounce search query - API akan dipanggil setelah user berhenti mengetik 0.5 detik
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
    }, 500);

    return () => clearTimeout(timer);
  }, [searchQuery]);
  // Available process steps
  const processSteps: ProcessStep[] = [
    { id: 'cuci', label: 'Cuci' },
    { id: 'kering', label: 'Kering' },
    { id: 'gosok', label: 'Gosok' },
    { id: 'packing', label: 'Packing' },
  ];

  // API hooks
  const { data: serviceCategoriesData, isLoading: isLoadingCategories } =
    useServiceCategories({
      outletId: activeOutlet?.id,
      search: debouncedSearchQuery,
      estimationHours: activeTab !== 'all' ? parseInt(activeTab) : undefined,
    });
  console.log(
    '🚀 ~ ServicesPage ~ serviceCategoriesData:',
    serviceCategoriesData
  );

  const { data: countData, isLoading: isLoadingCount } =
    useServiceCategoriesCount({
      outletId: activeOutlet?.id,
    });

  const createCategoryMutation = useCreateServiceCategory();
  const updateCategoryMutation = useUpdateServiceCategory();
  const deleteCategoryMutation = useDeleteServiceCategory();

  // Service/Variant mutations
  const createServiceMutation = useCreateService();
  const updateServiceMutation = useUpdateService();
  const deleteServiceMutation = useDeleteService();

  // Dialog states
  const [isCategoryDialogOpen, setIsCategoryDialogOpen] = useState(false);
  const [isVariantDialogOpen, setIsVariantDialogOpen] = useState(false);
  const [isDeleteCategoryDialogOpen, setIsDeleteCategoryDialogOpen] =
    useState(false);
  const [isDeleteVariantDialogOpen, setIsDeleteVariantDialogOpen] =
    useState(false);

  // Selected items for edit/delete
  const [selectedCategory, setSelectedCategory] = useState<any | null>(null);
  const [selectedVariant, setSelectedVariant] = useState<{
    variant: any;
    categoryId: number;
  } | null>(null);

  // Edit mode flags
  const [categoryEditMode, setCategoryEditMode] = useState(false);
  const [variantEditMode, setVariantEditMode] = useState(false);

  // React Hook Form for Category
  const categoryForm = useForm<CategoryFormData>({
    resolver: zodResolver(categoryFormSchema),
    defaultValues: {
      name: '',
      processes: [],
    },
  });

  // React Hook Form for Variant
  const variantForm = useForm<VariantFormData>({
    resolver: zodResolver(variantFormSchema),
    defaultValues: {
      name: '',
      price: '',
      duration: '',
      unit: 'kg',
    },
  });

  // Generate dynamic tabs based on service count data
  const availableDurations = countData || [
    { estimateHour: 6, count: 0 },
    { estimateHour: 24, count: 0 },
    { estimateHour: 48, count: 0 },
    { estimateHour: 72, count: 0 },
  ]; // Fallback to default durations

  // Process checkbox handlers
  const handleProcessCheckboxChange = (processId: string, checked: boolean) => {
    const currentProcesses = categoryForm.getValues('processes');
    if (checked) {
      categoryForm.setValue('processes', [...currentProcesses, processId]);
    } else {
      categoryForm.setValue(
        'processes',
        currentProcesses.filter((id) => id !== processId)
      );
    }
  };

  // Category CRUD operations
  const handleAddCategory = async (data: CategoryFormData) => {
    try {
      if (categoryEditMode && selectedCategory) {
        await updateCategoryMutation.mutateAsync({
          id: selectedCategory.id,
          data: {
            name: data.name,
            productionProcess: data.processes,
            outletId: activeOutlet?.id,
          },
        });
      } else {
        await createCategoryMutation.mutateAsync({
          name: data.name,
          productionProcess: data.processes,
          outletId: activeOutlet?.id,
        });
      }

      resetCategoryForm();
      setIsCategoryDialogOpen(false);
    } catch (error) {
      console.error('Error saving category:', error);
    }
  };

  const handleDeleteCategory = async () => {
    if (selectedCategory) {
      try {
        await deleteCategoryMutation.mutateAsync(selectedCategory.id);
        setIsDeleteCategoryDialogOpen(false);
        setSelectedCategory(null);
      } catch (error) {
        console.error('Error deleting category:', error);
      }
    }
  };

  const handleEditCategoryClick = (category: any) => {
    setSelectedCategory(category);
    categoryForm.reset({
      name: category.name,
      processes: category.productionProcess || [],
    });
    setCategoryEditMode(true);
    setIsCategoryDialogOpen(true);
  };

  const handleDeleteCategoryClick = (category: any) => {
    setSelectedCategory(category);
    setIsDeleteCategoryDialogOpen(true);
  };

  const resetCategoryForm = () => {
    categoryForm.reset({
      name: '',
      processes: [],
    });
    setCategoryEditMode(false);
    setSelectedCategory(null);
  };

  // Variant CRUD operations - Services within categories
  const handleAddVariant = async (data: VariantFormData) => {
    // Validation
    if (!selectedCategory) {
      toast.error('Kategori tidak dipilih');
      return;
    }

    try {
      const serviceData = {
        name: data.name.trim(),
        price: Number.parseInt(data.price),
        unit: data.unit,
        estimationHours: Number.parseInt(data.duration),
        categoryId: selectedCategory.id,
        outletId: activeOutlet?.id || 0,
        isActive: true,
      };

      if (variantEditMode && selectedVariant) {
        await updateServiceMutation.mutateAsync({
          id: selectedVariant.variant.id,
          data: serviceData,
        });
      } else {
        await createServiceMutation.mutateAsync(serviceData);
      }

      // Refresh service categories data to show updated services
      // This will be handled by the hooks automatically

      resetVariantForm();
      setIsVariantDialogOpen(false);
    } catch (error) {
      console.error('Error saving service:', error);
    }
  };

  const handleDeleteVariant = async () => {
    if (selectedVariant) {
      try {
        await deleteServiceMutation.mutateAsync(selectedVariant.variant.id);
        setIsDeleteVariantDialogOpen(false);
        setSelectedVariant(null);
      } catch (error) {
        console.error('Error deleting service:', error);
      }
    }
  };

  const handleAddVariantClick = (category: any) => {
    setSelectedCategory(category);
    resetVariantForm();
    setVariantEditMode(false);
    setIsVariantDialogOpen(true);
  };

  const handleEditVariantClick = (variant: any, categoryId: number) => {
    setSelectedVariant({ variant, categoryId });
    variantForm.reset({
      name: variant.name,
      price: variant.price.toString(),
      duration:
        variant.estimationHours?.toString() ||
        variant.duration?.toString() ||
        '',
      unit: variant.unit || 'pcs',
    });
    setVariantEditMode(true);
    setIsVariantDialogOpen(true);
  };

  const handleDeleteVariantClick = (variant: any, categoryId: number) => {
    setSelectedVariant({ variant, categoryId });
    setIsDeleteVariantDialogOpen(true);
  };

  const resetVariantForm = () => {
    variantForm.reset({
      name: '',
      price: '',
      duration: '',
      unit: 'kg',
    });
    setVariantEditMode(false);
    setSelectedVariant(null);
  };

  // // Filter services based on tab and search query
  // const filteredCategories = mappedCategories.filter((category) => {
  //   // Filter by search query
  //   const matchesSearch =
  //     searchQuery === '' ||
  //     category.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
  //     category.variants.some((variant) =>
  //       variant.name.toLowerCase().includes(searchQuery.toLowerCase())
  //     );

  //   // Filter by active tab (duration)
  //   if (activeTab === 'all') {
  //     return matchesSearch;
  //   }

  //   const tabDuration = Number.parseInt(activeTab);
  //   const hasMatchingDuration = category.variants.some(
  //     (variant) => variant.duration === tabDuration
  //   );

  //   return matchesSearch && hasMatchingDuration;
  // });

  return (
    <div className="max-w-md mx-auto bg-background min-h-screen pb-16">
      {/* Header */}
      <div className="sticky top-0 z-10 bg-background border-b">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-3">
            <Button
              variant="ghost"
              size="icon"
              className="h-9 w-9"
              onClick={() => router.back()}
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <h1 className="text-xl font-semibold">Layanan Laundry</h1>
          </div>
          <Button
            variant="default"
            size="sm"
            className="h-9"
            onClick={() => {
              resetCategoryForm();
              setIsCategoryDialogOpen(true);
            }}
            disabled={createCategoryMutation.isPending}
          >
            <Plus className="h-4 w-4 mr-1" />
            Tambah Kategori
          </Button>
        </div>
      </div>

      {/* Search */}
      <div className="p-4">
        <div className="flex gap-2">
          <div className="relative flex-1">
            {searchQuery && searchQuery !== debouncedSearchQuery ? (
              <div className="absolute left-2.5 top-2.5 h-4 w-4">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
              </div>
            ) : (
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            )}
            <Input
              placeholder="Cari layanan..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <Button variant="outline" size="icon" className="h-10 w-10">
            <Filter className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Tabs */}
      <div className="px-4">
        <Tabs
          defaultValue="all"
          className="w-full"
          onValueChange={setActiveTab}
        >
          <div className="overflow-x-auto pb-2">
            <TabsList className="bg-muted/50 h-9 w-auto inline-flex whitespace-nowrap">
              <TabsTrigger value="all" className="px-4">
                Semua
              </TabsTrigger>
              {availableDurations.map(({ estimateHour, count }) => (
                <TabsTrigger
                  key={estimateHour}
                  value={estimateHour.toString()}
                  className="px-4"
                >
                  {estimateHour} Jam
                </TabsTrigger>
              ))}
            </TabsList>
          </div>

          <TabsContent value={activeTab} className="mt-4 space-y-6">
            {serviceCategoriesData?.results?.length &&
            serviceCategoriesData?.results?.length > 0 ? (
              serviceCategoriesData?.results?.map((category) => (
                <div key={category.id} className="space-y-2">
                  <div className="flex items-center justify-between px-1">
                    <div>
                      <h3 className="font-medium text-sm">{category.name}</h3>
                      <div className="flex items-center gap-1 mt-1">
                        {category.productionProcess.map((process) => (
                          <Badge
                            key={process}
                            variant="outline"
                            className="text-xs px-1.5 h-5"
                          >
                            {process}
                          </Badge>
                        ))}
                        {/* {category.productionProcess.length > 2 && (
                          <Badge
                            variant="outline"
                            className="text-xs px-1.5 h-5"
                          >
                            +{category.productionProcess.length - 2} lagi
                          </Badge>
                        )} */}
                      </div>
                    </div>
                    <div className="flex items-center gap-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-7 px-2 text-xs"
                        onClick={() => handleAddVariantClick(category as any)}
                      >
                        <Plus className="h-3 w-3 mr-1" />
                        Tambah Variant
                      </Button>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-7 w-7"
                          >
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-40">
                          <DropdownMenuItem
                            onClick={() => handleEditCategoryClick(category)}
                            className="cursor-pointer"
                          >
                            <Edit className="h-4 w-4 mr-2" />
                            Edit Kategori
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => handleDeleteCategoryClick(category)}
                            className="cursor-pointer text-destructive"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Hapus Kategori
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>

                  {category.services?.length &&
                  category.services?.length > 0 ? (
                    <Card>
                      <CardContent className="p-0">
                        <div className="divide-y">
                          {category.services?.map((service) => (
                            <div key={service.id} className="p-3">
                              <div className="flex justify-between items-start">
                                <div>
                                  <div className="font-medium">
                                    {service.name}
                                  </div>
                                  <div className="text-sm text-muted-foreground mt-0.5">
                                    Rp {service.price.toLocaleString()} /{' '}
                                    {service.unit}
                                  </div>
                                </div>
                                <div className="flex items-center gap-1">
                                  <Badge variant="outline" className="text-xs">
                                    {service.estimationHours} jam
                                  </Badge>
                                  <DropdownMenu>
                                    <DropdownMenuTrigger asChild>
                                      <Button
                                        variant="ghost"
                                        size="icon"
                                        className="h-8 w-8"
                                      >
                                        <MoreVertical className="h-4 w-4" />
                                      </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent
                                      align="end"
                                      className="w-32"
                                    >
                                      <DropdownMenuItem
                                        onClick={() =>
                                          handleEditVariantClick(
                                            service,
                                            category.id
                                          )
                                        }
                                        className="cursor-pointer"
                                      >
                                        <Edit className="h-4 w-4 mr-2" />
                                        Edit
                                      </DropdownMenuItem>
                                      <DropdownMenuItem
                                        onClick={() =>
                                          handleDeleteVariantClick(
                                            service,
                                            category.id
                                          )
                                        }
                                        className="cursor-pointer text-destructive"
                                      >
                                        <Trash2 className="h-4 w-4 mr-2" />
                                        Hapus
                                      </DropdownMenuItem>
                                    </DropdownMenuContent>
                                  </DropdownMenu>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  ) : (
                    <Card>
                      <CardContent className="p-4 flex justify-center items-center">
                        <Button
                          variant="outline"
                          className="w-full"
                          onClick={() => handleAddVariantClick(category)}
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          Tambah Variant
                        </Button>
                      </CardContent>
                    </Card>
                  )}
                </div>
              ))
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                Tidak ada layanan yang ditemukan
              </div>
            )}
          </TabsContent>

          {/* {availableDurations.map((duration) => (
            <TabsContent
              key={duration.estimateHour}
              value={duration.toString()}
              className="mt-4 space-y-6"
            >
              {serviceCategoriesData?.results?.length &&
              serviceCategoriesData?.results?.length > 0 ? (
                serviceCategoriesData?.results?.map((category) => (
                  <div key={category.id} className="space-y-2">
                    <div className="flex items-center justify-between px-1">
                      <div>
                        <h3 className="font-medium text-sm">{category.name}</h3>
                        <div className="flex items-center gap-1 mt-1">
                          {category.productionProcess.map((process) => (
                            <Badge
                              key={process}
                              variant="outline"
                              className="text-xs px-1.5 h-5"
                            >
                              {
                                processSteps.find((step) => step.id === process)
                                  ?.label
                              }
                            </Badge>
                          ))}
                          {category.productionProcess.length > 2 && (
                            <Badge
                              variant="outline"
                              className="text-xs px-1.5 h-5"
                            >
                              +{category.productionProcess.length - 2} lagi
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                    <Card>
                      <CardContent className="p-0">
                        <div className="divide-y">
                          {category.services
                            ?.filter(
                              (service) =>
                                service.estimationHours ===
                                duration.estimateHour
                            )
                            ?.map((service) => (
                              <div key={service.id} className="p-3">
                                <div className="flex justify-between items-start">
                                  <div>
                                    <div className="font-medium">
                                      {service.name}
                                    </div>
                                    <div className="text-sm text-muted-foreground mt-0.5">
                                      Rp {service.price.toLocaleString()} /{' '}
                                      {service.unit}
                                    </div>
                                  </div>
                                  <div className="flex items-center gap-1">
                                    <Button
                                      variant="ghost"
                                      size="icon"
                                      className="h-8 w-8"
                                      onClick={() =>
                                        handleEditVariantClick(
                                          service,
                                          category.id
                                        )
                                      }
                                    >
                                      <Edit className="h-4 w-4" />
                                    </Button>
                                    <Button
                                      variant="ghost"
                                      size="icon"
                                      className="h-8 w-8 text-destructive"
                                      onClick={() =>
                                        handleDeleteVariantClick(
                                          service,
                                          category.id
                                        )
                                      }
                                    >
                                      <Trash2 className="h-4 w-4" />
                                    </Button>
                                  </div>
                                </div>
                              </div>
                            ))}
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                ))
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  Tidak ada layanan {duration.estimateHour} jam yang ditemukan
                </div>
              )}
            </TabsContent>
          ))} */}
        </Tabs>
      </div>

      {/* Add/Edit Category Dialog */}
      <Dialog
        open={isCategoryDialogOpen}
        onOpenChange={setIsCategoryDialogOpen}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {categoryEditMode ? 'Edit Kategori' : 'Tambah Kategori Baru'}
            </DialogTitle>
            <DialogDescription>
              {categoryEditMode
                ? 'Ubah detail kategori layanan'
                : 'Tambahkan kategori layanan baru'}
            </DialogDescription>
          </DialogHeader>

          <Form {...categoryForm}>
            <form
              onSubmit={categoryForm.handleSubmit(handleAddCategory)}
              className="space-y-4"
            >
              <FormField
                control={categoryForm.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nama Kategori</FormLabel>
                    <FormControl>
                      <Input placeholder="Contoh: Cuci Gosok" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={categoryForm.control}
                name="processes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Proses</FormLabel>
                    <FormControl>
                      <div className="border rounded-md p-4">
                        <div className="grid grid-cols-2 gap-4">
                          {processSteps.map((process) => (
                            <div
                              key={process.id}
                              className="flex items-center space-x-2"
                            >
                              <Checkbox
                                id={`process-${process.id}`}
                                checked={field.value.includes(process.id)}
                                onCheckedChange={(checked) =>
                                  handleProcessCheckboxChange(
                                    process.id,
                                    checked === true
                                  )
                                }
                              />
                              <Label
                                htmlFor={`process-${process.id}`}
                                className="text-sm font-normal"
                              >
                                {process.label}
                              </Label>
                            </div>
                          ))}
                        </div>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsCategoryDialogOpen(false)}
                  disabled={
                    createCategoryMutation.isPending ||
                    updateCategoryMutation.isPending
                  }
                >
                  Batal
                </Button>
                <Button
                  type="submit"
                  disabled={
                    createCategoryMutation.isPending ||
                    updateCategoryMutation.isPending
                  }
                >
                  {(createCategoryMutation.isPending ||
                    updateCategoryMutation.isPending) && (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  )}
                  {categoryEditMode ? 'Simpan' : 'Tambah'}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Add/Edit Variant Dialog */}
      <Dialog open={isVariantDialogOpen} onOpenChange={setIsVariantDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {variantEditMode
                ? 'Edit Variant'
                : `Tambah Variant untuk ${selectedCategory?.name || ''}`}
            </DialogTitle>
            <DialogDescription>
              {variantEditMode
                ? 'Ubah detail variant layanan'
                : 'Tambahkan variant layanan baru'}
            </DialogDescription>
          </DialogHeader>

          <Form {...variantForm}>
            <form
              onSubmit={variantForm.handleSubmit(handleAddVariant)}
              className="space-y-4"
            >
              <FormField
                control={variantForm.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nama Variant</FormLabel>
                    <FormControl>
                      <Input placeholder="Contoh: Baju" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={variantForm.control}
                name="price"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Harga (Rp)</FormLabel>
                    <FormControl>
                      <Input type="number" placeholder="15000" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={variantForm.control}
                  name="duration"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Durasi (Jam)</FormLabel>
                      <FormControl>
                        <Select
                          value={field.value}
                          onValueChange={field.onChange}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Pilih durasi" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="6">6 Jam</SelectItem>
                            <SelectItem value="24">24 Jam</SelectItem>
                            <SelectItem value="48">48 Jam</SelectItem>
                            <SelectItem value="72">72 Jam</SelectItem>
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={variantForm.control}
                  name="unit"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Satuan</FormLabel>
                      <FormControl>
                        <Select
                          value={field.value}
                          onValueChange={field.onChange}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Pilih satuan" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="kg">Kilogram (kg)</SelectItem>
                            <SelectItem value="pcs">Pieces (pcs)</SelectItem>
                            <SelectItem value="m²">
                              Meter persegi (m²)
                            </SelectItem>
                            <SelectItem value="pair">Pasang (pair)</SelectItem>
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsVariantDialogOpen(false)}
                  disabled={
                    createServiceMutation.isPending ||
                    updateServiceMutation.isPending
                  }
                >
                  Batal
                </Button>
                <Button
                  type="submit"
                  disabled={
                    createServiceMutation.isPending ||
                    updateServiceMutation.isPending
                  }
                >
                  {(createServiceMutation.isPending ||
                    updateServiceMutation.isPending) && (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  )}
                  {variantEditMode ? 'Simpan' : 'Tambah'}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Delete Category Confirmation Dialog */}
      <AlertDialog
        open={isDeleteCategoryDialogOpen}
        onOpenChange={setIsDeleteCategoryDialogOpen}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Hapus Kategori</AlertDialogTitle>
            <AlertDialogDescription>
              Apakah Anda yakin ingin menghapus kategori "
              {selectedCategory?.name}"? Semua variant dalam kategori ini juga
              akan dihapus. Tindakan ini tidak dapat dibatalkan.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={deleteCategoryMutation.isPending}>
              Batal
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteCategory}
              disabled={deleteCategoryMutation.isPending}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {deleteCategoryMutation.isPending && (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              )}
              Hapus
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Delete Variant Confirmation Dialog */}
      <AlertDialog
        open={isDeleteVariantDialogOpen}
        onOpenChange={setIsDeleteVariantDialogOpen}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Hapus Variant</AlertDialogTitle>
            <AlertDialogDescription>
              Apakah Anda yakin ingin menghapus variant "
              {selectedVariant?.variant.name}"? Tindakan ini tidak dapat
              dibatalkan.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={deleteServiceMutation.isPending}>
              Batal
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteVariant}
              disabled={deleteServiceMutation.isPending}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {deleteServiceMutation.isPending && (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              )}
              Hapus
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
