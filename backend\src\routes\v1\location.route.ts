import express from 'express';
import auth from '../../middlewares/auth';
import validate from '../../middlewares/validate';
import { locationValidation } from '../../validations';
import { locationController } from '../../controllers';

const router = express.Router();

router.route('/provinces').get(
  auth('getOutlets'), // Menggunakan permission yang sama dengan outlet
  validate(locationValidation.getProvinces),
  locationController.getProvinces
);

router
  .route('/cities')
  .get(auth('getOutlets'), validate(locationValidation.getCities), locationController.getCities);

router
  .route('/provinces/:provinceId/cities')
  .get(
    auth('getOutlets'),
    validate(locationValidation.getCitiesByProvince),
    locationController.getCitiesByProvince
  );

router
  .route('/search')
  .get(
    auth('getOutlets'),
    validate(locationValidation.searchLocations),
    locationController.searchLocations
  );

router
  .route('/timezones')
  .get(
    auth('getOutlets'),
    validate(locationValidation.getTimezones),
    locationController.getTimezones
  );

export default router;

/**
 * @swagger
 * tags:
 *   name: Locations
 *   description: API untuk mengelola data provinsi dan kota/kabupaten Indonesia
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     Province:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: ID provinsi
 *           example: 1
 *         name:
 *           type: string
 *           description: Nama provinsi
 *           example: "DKI Jakarta"
 *         code:
 *           type: string
 *           description: Kode provinsi
 *           example: "31"
 *       required:
 *         - id
 *         - name
 *         - code
 *
 *     City:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: ID kota/kabupaten
 *           example: 1
 *         name:
 *           type: string
 *           description: Nama kota/kabupaten
 *           example: "Jakarta Pusat"
 *         code:
 *           type: string
 *           description: Kode kota/kabupaten
 *           example: "3171"
 *         provinceId:
 *           type: integer
 *           description: ID provinsi
 *           example: 1
 *         province:
 *           $ref: '#/components/schemas/Province'
 *           description: Informasi provinsi (optional)
 *       required:
 *         - id
 *         - name
 *         - code
 *         - provinceId
 *
 *     Timezone:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: ID timezone (IANA timezone identifier)
 *           example: "Asia/Jakarta"
 *         name:
 *           type: string
 *           description: Nama timezone
 *           example: "WIB (UTC+7)"
 *         description:
 *           type: string
 *           description: Deskripsi timezone
 *           example: "Waktu Indonesia Barat"
 *         offset:
 *           type: string
 *           description: UTC offset
 *           example: "+07:00"
 *         regions:
 *           type: array
 *           items:
 *             type: string
 *           description: Daftar wilayah yang menggunakan timezone ini
 *           example: ["Sumatera", "Jawa", "Kalimantan Barat", "Kalimantan Tengah"]
 *       required:
 *         - id
 *         - name
 *         - description
 *         - offset
 *         - regions
 *
 *     SearchResult:
 *       type: object
 *       properties:
 *         provinces:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/Province'
 *           description: Daftar provinsi yang ditemukan
 *         cities:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/City'
 *           description: Daftar kota yang ditemukan
 *       required:
 *         - provinces
 *         - cities
 */

/**
 * @swagger
 * /locations/provinces:
 *   get:
 *     summary: Mendapatkan daftar semua provinsi
 *     description: Mengambil daftar semua provinsi di Indonesia dengan opsi sorting
 *     tags: [Locations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           enum: [name, code]
 *           default: name
 *         description: Field untuk sorting
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *           default: asc
 *         description: Urutan sorting
 *     responses:
 *       "200":
 *         description: Daftar provinsi berhasil diambil
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Province'
 *             example:
 *               - id: 1
 *                 name: "DKI Jakarta"
 *                 code: "31"
 *               - id: 2
 *                 name: "Jawa Barat"
 *                 code: "32"
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 */

/**
 * @swagger
 * /locations/cities:
 *   get:
 *     summary: Mendapatkan daftar kota/kabupaten
 *     description: Mengambil daftar kota/kabupaten dengan opsi filter provinsi dan include informasi provinsi
 *     tags: [Locations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: provinceId
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: ID provinsi untuk filter kota
 *         example: 1
 *       - in: query
 *         name: includeProvince
 *         schema:
 *           type: boolean
 *           default: false
 *         description: Sertakan informasi provinsi dalam response
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           enum: [name, code]
 *           default: name
 *         description: Field untuk sorting
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *           default: asc
 *         description: Urutan sorting
 *     responses:
 *       "200":
 *         description: Daftar kota berhasil diambil
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/City'
 *             example:
 *               - id: 1
 *                 name: "Jakarta Pusat"
 *                 code: "3171"
 *                 provinceId: 1
 *                 province:
 *                   id: 1
 *                   name: "DKI Jakarta"
 *                   code: "31"
 *       "400":
 *         $ref: '#/components/responses/BadRequest'
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 */

/**
 * @swagger
 * /locations/provinces/{provinceId}/cities:
 *   get:
 *     summary: Mendapatkan kota berdasarkan provinsi
 *     description: Mengambil daftar kota/kabupaten dalam provinsi tertentu
 *     tags: [Locations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: provinceId
 *         required: true
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: ID provinsi
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           enum: [name, code]
 *           default: name
 *         description: Field untuk sorting
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *           default: asc
 *         description: Urutan sorting
 *     responses:
 *       "200":
 *         description: Daftar kota dalam provinsi berhasil diambil
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/City'
 *             example:
 *               - id: 1
 *                 name: "Jakarta Pusat"
 *                 code: "3171"
 *                 provinceId: 1
 *               - id: 2
 *                 name: "Jakarta Utara"
 *                 code: "3172"
 *                 provinceId: 1
 *       "400":
 *         $ref: '#/components/responses/BadRequest'
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "404":
 *         $ref: '#/components/responses/NotFound'
 */

/**
 * @swagger
 * /locations/search:
 *   get:
 *     summary: Pencarian provinsi dan kota
 *     description: Melakukan pencarian provinsi dan kota berdasarkan nama dengan case insensitive
 *     tags: [Locations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: q
 *         required: true
 *         schema:
 *           type: string
 *           minLength: 2
 *         description: Query pencarian (minimal 2 karakter)
 *         example: "Jakarta"
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 50
 *           default: 10
 *         description: Batas maksimal hasil pencarian per kategori
 *     responses:
 *       "200":
 *         description: Hasil pencarian berhasil diambil
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SearchResult'
 *             example:
 *               provinces:
 *                 - id: 1
 *                   name: "DKI Jakarta"
 *                   code: "31"
 *               cities:
 *                 - id: 1
 *                   name: "Jakarta Pusat"
 *                   code: "3171"
 *                   provinceId: 1
 *                   province:
 *                     id: 1
 *                     name: "DKI Jakarta"
 *                     code: "31"
 *                 - id: 2
 *                   name: "Jakarta Utara"
 *                   code: "3172"
 *                   provinceId: 1
 *                   province:
 *                     id: 1
 *                     name: "DKI Jakarta"
 *                     code: "31"
 *       "400":
 *         $ref: '#/components/responses/BadRequest'
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 */

/**
 * @swagger
 * /locations/timezones:
 *   get:
 *     summary: Mendapatkan daftar timezone Indonesia
 *     description: Mengambil daftar semua timezone yang berlaku di Indonesia (WIB, WITA, WIT)
 *     tags: [Locations]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       "200":
 *         description: Daftar timezone berhasil diambil
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Timezone'
 *             example:
 *               - id: "Asia/Jakarta"
 *                 name: "WIB (UTC+7)"
 *                 description: "Waktu Indonesia Barat"
 *                 offset: "+07:00"
 *                 regions: ["Sumatera", "Jawa", "Kalimantan Barat", "Kalimantan Tengah"]
 *               - id: "Asia/Makassar"
 *                 name: "WITA (UTC+8)"
 *                 description: "Waktu Indonesia Tengah"
 *                 offset: "+08:00"
 *                 regions: ["Kalimantan Selatan", "Kalimantan Timur", "Kalimantan Utara", "Bali", "Nusa Tenggara", "Sulawesi"]
 *               - id: "Asia/Jayapura"
 *                 name: "WIT (UTC+9)"
 *                 description: "Waktu Indonesia Timur"
 *                 offset: "+09:00"
 *                 regions: ["Maluku", "Papua"]
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 */
