"use client"

import { <PERSON><PERSON> } from "@/components/ui/badge"

import type React from "react"

import { useState } from "react"
import Link from "next/link"
import { ArrowLeft, Save, User, Mail, Phone, MapPin, Globe, Camera } from "lucide-react"
import { useRouter } from "next/navigation"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Separator } from "@/components/ui/separator"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"

export default function ProfilePage() {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState("personal")
  const [formData, setFormData] = useState({
    name: "<PERSON><PERSON>",
    email: "<EMAIL>",
    phone: "021-7654321",
    address: "Jl. Merdeka No. 123, Jakarta Selatan",
    website: "www.felislaundry.com",
    description: "Layanan laundry premium dengan kualitas terbaik",
    ownerName: "John Doe",
    ownerEmail: "<EMAIL>",
    ownerPhone: "081234567890",
  })

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // In a real app, you would update the profile in your backend
    alert("Profil berhasil diperbarui!")
  }

  return (
    <div className="flex flex-col min-h-screen bg-slate-50">
      <header className="p-4 flex justify-between items-center bg-white sticky top-0 z-10 shadow-sm">
        <div className="flex items-center">
          <Link href="/akun" className="mr-3">
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <h1 className="text-lg font-semibold">Profil</h1>
        </div>
        <Button type="submit" form="profile-form" className="bg-blue-500 hover:bg-blue-600">
          <Save className="h-4 w-4 mr-2" /> Simpan
        </Button>
      </header>

      <main className="flex-1 p-4 pb-20">
        <Card className="p-4 mb-6">
          <div className="flex flex-col items-center">
            <div className="relative">
              <Avatar className="h-24 w-24">
                <AvatarImage src="/placeholder.svg" alt="Profile" />
                <AvatarFallback>FL</AvatarFallback>
              </Avatar>
              <Button variant="outline" size="icon" className="absolute bottom-0 right-0 rounded-full bg-white h-8 w-8">
                <Camera className="h-4 w-4" />
              </Button>
            </div>
            <h2 className="text-xl font-bold mt-4">{formData.name}</h2>
            <p className="text-gray-500">{formData.email}</p>
            <Badge className="mt-2 bg-blue-500">Paket Premium</Badge>
          </div>
        </Card>

        <Tabs defaultValue="personal" className="mb-4" onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-2 w-full">
            <TabsTrigger value="personal">Informasi Bisnis</TabsTrigger>
            <TabsTrigger value="business">Informasi Pemilik</TabsTrigger>
          </TabsList>

          <form id="profile-form" onSubmit={handleSubmit}>
            <TabsContent value="personal" className="mt-4">
              <Card className="p-4 space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name" className="flex items-center gap-2">
                    <User className="h-4 w-4" /> Nama Bisnis
                  </Label>
                  <Input
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    placeholder="Nama bisnis Anda"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email" className="flex items-center gap-2">
                    <Mail className="h-4 w-4" /> Email Bisnis
                  </Label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    value={formData.email}
                    onChange={handleChange}
                    placeholder="Email bisnis Anda"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="phone" className="flex items-center gap-2">
                    <Phone className="h-4 w-4" /> Telepon Bisnis
                  </Label>
                  <Input
                    id="phone"
                    name="phone"
                    value={formData.phone}
                    onChange={handleChange}
                    placeholder="Nomor telepon bisnis"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="address" className="flex items-center gap-2">
                    <MapPin className="h-4 w-4" /> Alamat Bisnis
                  </Label>
                  <Textarea
                    id="address"
                    name="address"
                    value={formData.address}
                    onChange={handleChange}
                    placeholder="Alamat lengkap bisnis"
                    rows={3}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="website" className="flex items-center gap-2">
                    <Globe className="h-4 w-4" /> Website
                  </Label>
                  <Input
                    id="website"
                    name="website"
                    value={formData.website}
                    onChange={handleChange}
                    placeholder="Website bisnis (opsional)"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Deskripsi Bisnis</Label>
                  <Textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleChange}
                    placeholder="Deskripsi singkat tentang bisnis Anda"
                    rows={4}
                  />
                </div>
              </Card>
            </TabsContent>

            <TabsContent value="business" className="mt-4">
              <Card className="p-4 space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="ownerName" className="flex items-center gap-2">
                    <User className="h-4 w-4" /> Nama Pemilik
                  </Label>
                  <Input
                    id="ownerName"
                    name="ownerName"
                    value={formData.ownerName}
                    onChange={handleChange}
                    placeholder="Nama lengkap pemilik"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="ownerEmail" className="flex items-center gap-2">
                    <Mail className="h-4 w-4" /> Email Pemilik
                  </Label>
                  <Input
                    id="ownerEmail"
                    name="ownerEmail"
                    type="email"
                    value={formData.ownerEmail}
                    onChange={handleChange}
                    placeholder="Email pemilik"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="ownerPhone" className="flex items-center gap-2">
                    <Phone className="h-4 w-4" /> Telepon Pemilik
                  </Label>
                  <Input
                    id="ownerPhone"
                    name="ownerPhone"
                    value={formData.ownerPhone}
                    onChange={handleChange}
                    placeholder="Nomor telepon pemilik"
                    required
                  />
                </div>

                <Separator />

                <div className="space-y-2">
                  <h3 className="font-semibold">Keamanan Akun</h3>
                  <Button type="button" variant="outline" className="w-full">
                    Ubah Password
                  </Button>
                </div>

                <div className="space-y-2">
                  <h3 className="font-semibold">Verifikasi Akun</h3>
                  <Button type="button" variant="outline" className="w-full">
                    Verifikasi Email
                  </Button>
                </div>
              </Card>
            </TabsContent>
          </form>
        </Tabs>
      </main>
    </div>
  )
}
