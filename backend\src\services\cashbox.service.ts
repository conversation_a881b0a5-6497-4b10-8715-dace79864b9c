import prisma from '../client';
import { CashboxType, User, Role } from '@prisma/client';
import ApiError from '../utils/ApiError';
import httpStatus from 'http-status';

/**
 * Create cashbox
 * @param {Object} cashboxBody
 * @param {number} outletId
 * @param {User} user
 * @returns {Promise<Cashbox>}
 */
export const createCashbox = async (
  cashboxBody: {
    name: string;
    type: CashboxType;
    balance?: number;
  },
  outletId: number,
  user: User
) => {
  // Validasi outlet exists dan user memiliki akses
  const outlet = await prisma.outlet.findFirst({
    where: {
      id: outletId,
      isDeleted: false,
      ...(user.role !== Role.ADMIN ? { ownerId: user.id } : {})
    }
  });
  if (!outlet) throw new ApiError(httpStatus.NOT_FOUND, 'Outlet tidak ditemukan');

  // Validasi nama cashbox unik per outlet
  const existingCashbox = await prisma.cashbox.findFirst({
    where: {
      outletId,
      name: cashboxBody.name
    }
  });
  if (existingCashbox) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Nama cashbox sudah digunakan di outlet ini');
  }

  const cashbox = await prisma.cashbox.create({
    data: {
      outletId,
      name: cashboxBody.name,
      type: cashboxBody.type,
      balance: cashboxBody.balance || 0
    },
    include: {
      outlet: { select: { name: true } }
    }
  });

  return cashbox;
};

/**
 * Get cashboxes by outlet
 * @param {number} outletId
 * @param {User} user
 * @param {Object} options
 * @returns {Promise<Object>}
 */
export const getCashboxes = async (
  outletId: number,
  user: User,
  options: {
    type?: CashboxType;
    isActive?: boolean;
    page?: number;
    limit?: number;
  } = {}
) => {
  // Validasi outlet exists dan user memiliki akses
  const outlet = await prisma.outlet.findFirst({
    where: {
      id: outletId,
      isDeleted: false,
      ...(user.role !== Role.ADMIN ? { ownerId: user.id } : {})
    }
  });
  if (!outlet) throw new ApiError(httpStatus.NOT_FOUND, 'Outlet tidak ditemukan');

  const page = options.page ?? 1;
  const limit = options.limit ?? 10;

  const whereClause: any = { outletId };

  if (options.type) {
    whereClause.type = options.type;
  }

  if (options.isActive !== undefined) {
    whereClause.isActive = options.isActive;
  }

  const [cashboxes, total] = await Promise.all([
    prisma.cashbox.findMany({
      where: whereClause,
      include: {
        outlet: { select: { name: true } },
        _count: {
          select: {
            payments: true,
            depositTransactions: true
          }
        }
      },
      skip: (page - 1) * limit,
      take: limit,
      orderBy: { createdAt: 'desc' }
    }),
    prisma.cashbox.count({ where: whereClause })
  ]);

  return {
    results: cashboxes,
    page,
    limit,
    totalPages: Math.ceil(total / limit),
    totalResults: total
  };
};

/**
 * Get cashbox by ID
 * @param {number} cashboxId
 * @param {number} outletId
 * @param {User} user
 * @returns {Promise<Cashbox>}
 */
export const getCashboxById = async (cashboxId: number, outletId: number, user: User) => {
  // Validasi outlet exists dan user memiliki akses
  const outlet = await prisma.outlet.findFirst({
    where: {
      id: outletId,
      isDeleted: false,
      ...(user.role !== Role.ADMIN ? { ownerId: user.id } : {})
    }
  });
  if (!outlet) throw new ApiError(httpStatus.NOT_FOUND, 'Outlet tidak ditemukan');

  const cashbox = await prisma.cashbox.findFirst({
    where: { id: cashboxId, outletId },
    include: {
      outlet: { select: { name: true } },
      _count: {
        select: {
          payments: true,
          depositTransactions: true
        }
      }
    }
  });

  if (!cashbox) throw new ApiError(httpStatus.NOT_FOUND, 'Cashbox tidak ditemukan');

  return cashbox;
};

/**
 * Update cashbox
 * @param {number} cashboxId
 * @param {number} outletId
 * @param {Object} updateBody
 * @param {User} user
 * @returns {Promise<Cashbox>}
 */
export const updateCashbox = async (
  cashboxId: number,
  outletId: number,
  updateBody: {
    name?: string;
    type?: CashboxType;
    isActive?: boolean;
    balance?: number;
  },
  user: User
) => {
  const cashbox = await getCashboxById(cashboxId, outletId, user);

  // Validasi nama cashbox unik per outlet (jika nama diubah)
  if (updateBody.name && updateBody.name !== cashbox.name) {
    const existingCashbox = await prisma.cashbox.findFirst({
      where: {
        outletId,
        name: updateBody.name,
        id: { not: cashboxId }
      }
    });
    if (existingCashbox) {
      throw new ApiError(httpStatus.BAD_REQUEST, 'Nama cashbox sudah digunakan di outlet ini');
    }
  }

  const updatedCashbox = await prisma.cashbox.update({
    where: { id: cashboxId },
    data: updateBody,
    include: {
      outlet: { select: { name: true } }
    }
  });

  return updatedCashbox;
};

/**
 * Delete cashbox
 * @param {number} cashboxId
 * @param {number} outletId
 * @param {User} user
 * @returns {Promise<void>}
 */
export const deleteCashbox = async (cashboxId: number, outletId: number, user: User) => {
  const cashbox = await getCashboxById(cashboxId, outletId, user);

  // Cek apakah cashbox masih memiliki transaksi
  const transactionCount = await prisma.payment.count({
    where: { cashboxId }
  });

  const depositTransactionCount = await prisma.customerDepositTransaction.count({
    where: { cashboxId }
  });

  if (transactionCount > 0 || depositTransactionCount > 0) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      'Cashbox tidak dapat dihapus karena masih memiliki transaksi'
    );
  }

  await prisma.cashbox.delete({
    where: { id: cashboxId }
  });
};

/**
 * Get cashbox balance
 * @param {number} cashboxId
 * @param {number} outletId
 * @param {User} user
 * @returns {Promise<number>}
 */
export const getCashboxBalance = async (cashboxId: number, outletId: number, user: User) => {
  const cashbox = await getCashboxById(cashboxId, outletId, user);
  return cashbox.balance;
};

/**
 * Adjust cashbox balance (untuk koreksi manual)
 * @param {number} cashboxId
 * @param {number} outletId
 * @param {number} newBalance
 * @param {User} user
 * @param {string} reason
 * @returns {Promise<Cashbox>}
 */
export const adjustCashboxBalance = async (
  cashboxId: number,
  outletId: number,
  newBalance: number,
  user: User,
  reason: string
) => {
  const cashbox = await getCashboxById(cashboxId, outletId, user);

  const updatedCashbox = await prisma.cashbox.update({
    where: { id: cashboxId },
    data: { balance: newBalance },
    include: {
      outlet: { select: { name: true } }
    }
  });

  // TODO: Log adjustment untuk audit trail
  // Bisa ditambahkan tabel CashboxAdjustment jika diperlukan

  return updatedCashbox;
};

export default {
  createCashbox,
  getCashboxes,
  getCashboxById,
  updateCashbox,
  deleteCashbox,
  getCashboxBalance,
  adjustCashboxBalance
};
