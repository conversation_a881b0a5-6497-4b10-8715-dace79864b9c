import httpStatus from 'http-status';
import { Customer, CustomerStatus, CustomerType, Prisma } from '@prisma/client';
import prisma from '../client';
import ApiError from '../utils/ApiError';

/**
 * Create a customer
 * @param {Object} customerBody
 * @param {number} outletId
 * @returns {Promise<Customer>}
 */
const createCustomer = async (customerBody: any, outletId: number): Promise<any> => {
  // Check if phone already exists in the same outlet
  if (customerBody.phone) {
    const existingCustomerByPhone = await prisma.customer.findFirst({
      where: {
        phone: customerBody.phone,
        outletId: outletId
      }
    });
    if (existingCustomerByPhone) {
      throw new ApiError(httpStatus.BAD_REQUEST, 'Phone number already exists in this outlet');
    }
  }

  // Check if email already exists in the same outlet
  if (customerBody.email) {
    const existingCustomerByEmail = await prisma.customer.findFirst({
      where: {
        email: customerBody.email,
        outletId: outletId
      }
    });
    if (existingCustomerByEmail) {
      throw new ApiError(httpStatus.BAD_REQUEST, 'Email already exists in this outlet');
    }
  }

  // Prepare customer data
  const customerData = {
    name: customerBody.name,
    phone: customerBody.phone || null,
    email: customerBody.email || null,
    address: customerBody.address || null,
    mapLink: customerBody.mapLink || null,
    latitude: customerBody.latitude || null,
    longitude: customerBody.longitude || null,
    provinceId: customerBody.provinceId || null,
    cityId: customerBody.cityId || null,
    status: customerBody.status || CustomerStatus.ACTIVE,
    customerType: customerBody.customerType || CustomerType.INDIVIDUAL,
    source: customerBody.source || null,
    labels: customerBody.labels || [],
    photos: customerBody.photos || [],
    notes: customerBody.notes || null,
    totalOrders: 0,
    outletId: outletId
  };

  // Prepare financial data
  const financialData = customerBody.financialData || {};
  const defaultFinancialData = {
    totalSpent: financialData.totalSpent || 0,
    loyaltyPoints: financialData.loyaltyPoints || 0,
    deposit: financialData.deposit || 0,
    debt: financialData.debt || 0,
    cashback: financialData.cashback || 0,
    preferredPaymentMethod: financialData.preferredPaymentMethod || null,
    creditLimit: financialData.creditLimit || 0
  };

  // Create customer with financial data
  const customer = await prisma.customer.create({
    data: {
      ...customerData,
      financialData: {
        create: defaultFinancialData
      }
    },
    include: {
      financialData: true,
      province: true,
      city: true,
      customerNotes: {
        orderBy: { createdAt: 'desc' }
      }
    }
  });

  return customer;
};

/**
 * Query for customers
 * @param {Object} filter - Prisma filter
 * @param {Object} options - Query options
 * @param {string} [options.sortBy] - Sort option in the format: sortField:(desc|asc)
 * @param {number} [options.limit] - Maximum number of results per page (default = 10)
 * @param {number} [options.page] - Current page (default = 1)
 * @param {number} outletId - Outlet ID for filtering
 * @returns {Promise<QueryResult>}
 */
const queryCustomers = async (
  filter: any,
  options: any,
  outletId: number,
  userRole: string
): Promise<any> => {
  // Build where clause
  const where: any = {};

  // Non-admin users can only access customers from their outlet
  if (userRole !== 'ADMIN') {
    where.outletId = outletId;
  }

  // Search filter
  if (filter.search) {
    where.OR = [
      { name: { contains: filter.search, mode: 'insensitive' } },
      { phone: { contains: filter.search, mode: 'insensitive' } },
      { email: { contains: filter.search, mode: 'insensitive' } }
    ];
  }

  // Status filter
  if (filter.status) {
    where.status = filter.status;
  }

  // Customer type filter
  if (filter.customerType) {
    where.customerType = filter.customerType;
  }

  // Source filter
  if (filter.source) {
    where.source = { contains: filter.source, mode: 'insensitive' };
  }

  // Labels filter
  if (filter.labels) {
    const labelsArray = filter.labels.split(',').map((label: string) => label.trim());
    where.labels = { hasSome: labelsArray };
  }

  // Province filter
  if (filter.provinceId) {
    where.provinceId = parseInt(filter.provinceId);
  }

  // City filter
  if (filter.cityId) {
    where.cityId = parseInt(filter.cityId);
  }

  // Sorting
  const orderBy: any = {};
  if (options.sortBy) {
    const [field, order] = options.sortBy.split(':');
    orderBy[field] = order === 'desc' ? 'desc' : 'asc';
  } else {
    orderBy.createdAt = 'desc';
  }

  // Pagination
  const page = options.page ? parseInt(options.page) : 1;
  const limit = options.limit ? parseInt(options.limit) : 10;
  const skip = (page - 1) * limit;

  // Execute query
  const [customers, totalResults] = await Promise.all([
    prisma.customer.findMany({
      where,
      orderBy,
      skip,
      take: limit,
      include: {
        financialData: true,
        province: true,
        city: true,
        _count: {
          select: { orders: true }
        }
      }
    }),
    prisma.customer.count({ where })
  ]);

  const totalPages = Math.ceil(totalResults / limit);

  return {
    results: customers,
    page,
    limit,
    totalPages,
    totalResults
  };
};

/**
 * Get customer by id
 * @param {number} id
 * @param {number} outletId
 * @param {string} userRole
 * @returns {Promise<Customer>}
 */
const getCustomerById = async (id: number, outletId: number, userRole: string): Promise<any> => {
  const where: any = { id };

  // Non-admin users can only access customers from their outlet
  if (userRole !== 'ADMIN') {
    where.outletId = outletId;
  }

  const customer = await prisma.customer.findFirst({
    where,
    include: {
      financialData: true,
      province: true,
      city: true,
      customerNotes: {
        orderBy: { createdAt: 'desc' }
      },
      _count: {
        select: { orders: true }
      }
    }
  });

  if (!customer) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Customer not found');
  }

  return customer;
};

/**
 * Update customer by id
 * @param {number} customerId
 * @param {Object} updateBody
 * @param {number} outletId
 * @param {string} userRole
 * @returns {Promise<Customer>}
 */
const updateCustomerById = async (
  customerId: number,
  updateBody: any,
  outletId: number,
  userRole: string
): Promise<any> => {
  const customer = await getCustomerById(customerId, outletId, userRole);

  // Check if phone already exists in the same outlet (excluding current customer)
  if (updateBody.phone && updateBody.phone !== customer.phone) {
    const existingCustomerByPhone = await prisma.customer.findFirst({
      where: {
        phone: updateBody.phone,
        outletId: customer.outletId,
        id: { not: customerId }
      }
    });
    if (existingCustomerByPhone) {
      throw new ApiError(httpStatus.BAD_REQUEST, 'Phone number already exists in this outlet');
    }
  }

  // Check if email already exists in the same outlet (excluding current customer)
  if (updateBody.email && updateBody.email !== customer.email) {
    const existingCustomerByEmail = await prisma.customer.findFirst({
      where: {
        email: updateBody.email,
        outletId: customer.outletId,
        id: { not: customerId }
      }
    });
    if (existingCustomerByEmail) {
      throw new ApiError(httpStatus.BAD_REQUEST, 'Email already exists in this outlet');
    }
  }

  // Prepare update data
  const updateData: any = {};

  // Basic customer fields
  const customerFields = [
    'name',
    'phone',
    'email',
    'address',
    'mapLink',
    'latitude',
    'longitude',
    'provinceId',
    'cityId',
    'status',
    'customerType',
    'source',
    'labels',
    'photos',
    'notes'
  ];

  customerFields.forEach((field) => {
    if (updateBody[field] !== undefined) {
      updateData[field] = updateBody[field];
    }
  });

  // Handle financial data update
  if (updateBody.financialData) {
    updateData.financialData = {
      update: updateBody.financialData
    };
  }

  const updatedCustomer = await prisma.customer.update({
    where: { id: customerId },
    data: updateData,
    include: {
      financialData: true,
      province: true,
      city: true,
      customerNotes: {
        orderBy: { createdAt: 'desc' }
      },
      _count: {
        select: { orders: true }
      }
    }
  });

  return updatedCustomer;
};

/**
 * Delete customer by id
 * @param {number} customerId
 * @param {number} outletId
 * @param {string} userRole
 * @returns {Promise<Customer>}
 */
const deleteCustomerById = async (
  customerId: number,
  outletId: number,
  userRole: string
): Promise<void> => {
  const customer = await getCustomerById(customerId, outletId, userRole);

  // Check if customer has orders
  const orderCount = await prisma.order.count({
    where: { customerId: customerId }
  });

  if (orderCount > 0) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Cannot delete customer with existing orders');
  }

  await prisma.customer.delete({
    where: { id: customerId }
  });
};

/**
 * Create customer note
 * @param {number} customerId
 * @param {Object} noteBody
 * @param {number} outletId
 * @param {string} userRole
 * @returns {Promise<CustomerNote>}
 */
const createCustomerNote = async (
  customerId: number,
  noteBody: any,
  outletId: number,
  userRole: string
): Promise<any> => {
  // Verify customer exists and user has access
  await getCustomerById(customerId, outletId, userRole);

  const note = await prisma.customerNote.create({
    data: {
      customerId,
      text: noteBody.text,
      author: noteBody.author
    }
  });

  return note;
};

/**
 * Get customer notes
 * @param {number} customerId
 * @param {number} outletId
 * @param {string} userRole
 * @returns {Promise<CustomerNote[]>}
 */
const getCustomerNotes = async (
  customerId: number,
  outletId: number,
  userRole: string
): Promise<any[]> => {
  // Verify customer exists and user has access
  await getCustomerById(customerId, outletId, userRole);

  const notes = await prisma.customerNote.findMany({
    where: { customerId },
    orderBy: { createdAt: 'desc' }
  });

  return notes;
};

export default {
  createCustomer,
  queryCustomers,
  getCustomerById,
  updateCustomerById,
  deleteCustomerById,
  createCustomerNote,
  getCustomerNotes
};
