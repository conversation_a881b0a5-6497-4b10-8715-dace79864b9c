# Example Environment Configuration untuk SMTP Provider
# Copy file ini ke .env dan sesuaikan dengan kredensial Anda

# Node environment
NODE_ENV=development
PORT=3000

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_ACCESS_EXPIRATION_MINUTES=30
JWT_REFRESH_EXPIRATION_DAYS=30
JWT_RESET_PASSWORD_EXPIRATION_MINUTES=10
JWT_VERIFY_EMAIL_EXPIRATION_MINUTES=10

# Email Provider Selection
EMAIL_PROVIDER=smtp

# SMTP Configuration (Gmail Example)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-gmail-app-password
EMAIL_FROM=<EMAIL>

# SendGrid Configuration (Optional - tidak digunakan jika EMAIL_PROVIDER=smtp)
# SENDGRID_API_KEY=your-sendgrid-api-key
# SENDGRID_FROM_EMAIL=<EMAIL>

# Database
DATABASE_URL="postgresql://username:password@localhost:5432/laundry_app?schema=public"

# Instruksi Setup Gmail SMTP:
# 1. Enable 2-Factor Authentication di Google Account
# 2. Pergi ke Google Account > Security > App passwords
# 3. Generate App Password khusus untuk aplikasi ini
# 4. Gunakan App Password tersebut di SMTP_PASSWORD (bukan password Gmail biasa)
# 5. Pastikan SMTP_USERNAME dan EMAIL_FROM menggunakan email yang sama 