import { Perfume, Prisma } from '@prisma/client';
import httpStatus from 'http-status';
import prisma from '../client';
import ApiError from '../utils/ApiError';
import logger from '../config/logger';

/**
 * Create a perfume
 * @param {Object} perfumeBody
 * @param {number} outletId
 * @returns {Promise<Perfume>}
 */
const createPerfume = async (perfumeBody: any): Promise<Perfume> => {
  const { outletId } = perfumeBody;
  logger.info(`Creating perfume: ${perfumeBody.name} at outlet ${outletId}`, {
    name: perfumeBody.name,
    brand: perfumeBody.brand,
    outletId
  });

  // Check if perfume name already exists in this outlet
  const existingPerfume = await prisma.perfume.findFirst({
    where: {
      name: perfumeBody.name,
      outletId: outletId
    }
  });

  //  check outletId exists
  const outlet = await prisma.outlet.findUnique({
    where: {
      id: outletId
    }
  });

  if (!outlet) {
    logger.error(`Outlet not found: ${outletId}`);
    throw new ApiError(httpStatus.NOT_FOUND, 'Outlet not found');
  }

  if (existingPerfume) {
    logger.error(`Perfume name already exists: ${perfumeBody.name} at outlet ${outletId}`);
    throw new ApiError(httpStatus.BAD_REQUEST, 'Perfume name already exists in this outlet');
  }

  const perfume = await prisma.perfume.create({
    data: {
      ...perfumeBody,
      outletId
    }
  });

  logger.info(`Perfume created successfully: ${perfume.name} (${perfume.id})`, {
    perfumeId: perfume.id,
    name: perfume.name,
    brand: perfume.brand,
    outletId
  });

  return perfume;
};

/**
 * Query for perfumes
 * @param {Object} filter - Mongo filter
 * @param {Object} options - Query options
 * @param {number} outletId
 * @returns {Promise<QueryResult>}
 */
const queryPerfumes = async (filter: any, options: any) => {
  const search = filter.search;
  const sortBy = options.sortBy;
  const limit = options.limit || 10;
  const page = options.page || 1;
  const outletId = filter.outletId;
  const isActive = filter.isActive;

  logger.debug(`Querying perfumes for outlet ${outletId}`, {
    search,
    sortBy,
    limit,
    page
  });

  // Build where condition
  const where: Prisma.PerfumeWhereInput = {
    outletId: outletId
  };

  if (search) {
    where.OR = [
      { name: { contains: search, mode: 'insensitive' } },
      { brand: { contains: search, mode: 'insensitive' } },
      { description: { contains: search, mode: 'insensitive' } },
      { scent: { contains: search, mode: 'insensitive' } }
    ];
  }

  where.isActive = isActive;

  // Build orderBy
  let orderBy: Prisma.PerfumeOrderByWithRelationInput = { createdAt: 'desc' };
  if (sortBy) {
    const [field, direction] = sortBy.split(':');
    orderBy = { [field]: direction === 'desc' ? 'desc' : 'asc' };
  }

  // Execute query
  const [perfumes, totalResults] = await Promise.all([
    prisma.perfume.findMany({
      where,
      orderBy,
      skip: (page - 1) * limit,
      take: limit
    }),
    prisma.perfume.count({ where })
  ]);

  const totalPages = Math.ceil(totalResults / limit);

  logger.debug(`Found ${totalResults} perfumes for outlet ${outletId}`);

  return {
    results: perfumes,
    page,
    limit,
    totalPages,
    totalResults
  };
};

/**
 * Get perfume by id
 * @param {number} id
 * @param {number} outletId
 * @returns {Promise<Perfume>}
 */
const getPerfumeById = async (id: number): Promise<Perfume | null> => {
  const perfume = await prisma.perfume.findFirst({
    where: {
      id
    }
  });

  if (perfume) {
    logger.debug(`Perfume retrieved: ${perfume.name} (${perfume.id})`);
  } else {
    logger.warn(`Perfume not found: ${id}`);
  }

  return perfume;
};

/**
 * Update perfume by id
 * @param {number} perfumeId
 * @param {Object} updateBody
 * @param {number} outletId
 * @returns {Promise<Perfume>}
 */
const updatePerfumeById = async (
  perfumeId: number,
  updateBody: any,
  outletId: number
): Promise<Perfume> => {
  logger.info(`Updating perfume: ${perfumeId}`, {
    perfumeId,
    updateFields: Object.keys(updateBody),
    outletId
  });

  const perfume = await getPerfumeById(perfumeId);
  if (!perfume) {
    logger.error(`Perfume not found for update: ${perfumeId} at outlet ${outletId}`);
    throw new ApiError(httpStatus.NOT_FOUND, 'Perfume not found');
  }

  // Check if updating name and it already exists
  if (updateBody.name && updateBody.name !== perfume.name) {
    const existingPerfume = await prisma.perfume.findFirst({
      where: {
        name: updateBody.name,
        outletId: outletId,
        id: { not: perfumeId }
      }
    });

    if (existingPerfume) {
      logger.error(`Perfume name already exists: ${updateBody.name} at outlet ${outletId}`);
      throw new ApiError(httpStatus.BAD_REQUEST, 'Perfume name already exists in this outlet');
    }
  }

  const updatedPerfume = await prisma.perfume.update({
    where: {
      id: perfumeId
    },
    data: updateBody
  });

  logger.info(`Perfume updated successfully: ${updatedPerfume.name} (${perfumeId})`, {
    perfumeId,
    name: updatedPerfume.name,
    outletId
  });

  return updatedPerfume;
};

/**
 * Delete perfume by id
 * @param {number} perfumeId
 * @param {number} outletId
 * @returns {Promise<Perfume>}
 */
const deletePerfumeById = async (perfumeId: number, outletId: number): Promise<void> => {
  logger.info(`Deleting perfume: ${perfumeId}`, {
    perfumeId,
    outletId
  });

  const perfume = await getPerfumeById(perfumeId);
  if (!perfume) {
    logger.error(`Perfume not found for deletion: ${perfumeId} at outlet ${outletId}`);
    throw new ApiError(httpStatus.NOT_FOUND, 'Perfume not found');
  }

  // Check if perfume is being used in any orders
  const ordersUsingPerfume = await prisma.order.count({
    where: {
      perfumeId: perfumeId,
      outletId: outletId
    }
  });

  if (ordersUsingPerfume > 0) {
    logger.warn(`Cannot delete perfume ${perfumeId}: used in ${ordersUsingPerfume} orders`);
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      `Cannot delete perfume. It is being used in ${ordersUsingPerfume} order(s).`
    );
  }

  await prisma.perfume.delete({
    where: {
      id: perfumeId
    }
  });

  logger.info(`Perfume deleted successfully: ${perfume.name} (${perfumeId})`, {
    perfumeId,
    name: perfume.name,
    outletId
  });
};

export default {
  createPerfume,
  queryPerfumes,
  getPerfumeById,
  updatePerfumeById,
  deletePerfumeById
};
