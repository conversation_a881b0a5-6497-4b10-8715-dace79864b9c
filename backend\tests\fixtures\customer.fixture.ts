import { faker } from '@faker-js/faker';
import {
  Customer,
  CustomerStatus,
  CustomerType,
  CustomerFinancial,
  CustomerNote
} from '@prisma/client';
import prisma from '../../src/client';

const password = 'password1';

// Customer fixture data
export const customerOne = {
  id: 1,
  name: '<PERSON><PERSON>',
  phone: `08123456${Math.floor(Math.random() * 10000)
    .toString()
    .padStart(4, '0')}`,
  email: `budi.santoso.${Math.floor(Math.random() * 10000)}@example.com`,
  address: 'Jl. Merdeka No. 123, Jakarta Selatan',
  mapLink: 'https://maps.google.com/example',
  latitude: -6.2088,
  longitude: 106.8456,
  status: CustomerStatus.ACTIVE,
  customerType: CustomerType.INDIVIDUAL,
  source: 'Referral',
  labels: ['VIP', 'Langganan'],
  photos: ['https://example.com/photo1.jpg'],
  notes: '<PERSON><PERSON>nggan VIP, selalu tepat waktu',
  totalOrders: 5,
  lastOrderDate: new Date('2025-03-20'),
  joinDate: new Date('2025-01-15')
};

export const customerTwo = {
  id: 2,
  name: 'Siti Rahayu',
  phone: `08234567${Math.floor(Math.random() * 10000)
    .toString()
    .padStart(4, '0')}`,
  email: `siti.rahayu.${Math.floor(Math.random() * 10000)}@example.com`,
  address: 'Jl. Sudirman No. 45, Jakarta Pusat',
  status: CustomerStatus.ACTIVE,
  customerType: CustomerType.INDIVIDUAL,
  source: 'Walk-in',
  labels: ['Baru'],
  totalOrders: 1,
  joinDate: new Date('2025-03-01')
};

export const customerThree = {
  id: 3,
  name: 'PT. Maju Jaya',
  phone: `08345678${Math.floor(Math.random() * 10000)
    .toString()
    .padStart(4, '0')}`,
  email: `admin.${Math.floor(Math.random() * 10000)}@majujaya.com`,
  address: 'Jl. Gatot Subroto No. 67, Jakarta Selatan',
  status: CustomerStatus.ACTIVE,
  customerType: CustomerType.CORPORATE,
  source: 'Marketing',
  labels: ['Korporat', 'VIP'],
  totalOrders: 15,
  lastOrderDate: new Date('2025-03-25'),
  joinDate: new Date('2024-12-01')
};

export const customerInactive = {
  id: 4,
  name: 'Ahmad Hidayat',
  phone: `08456789${Math.floor(Math.random() * 10000)
    .toString()
    .padStart(4, '0')}`,
  email: `ahmad.hidayat.${Math.floor(Math.random() * 10000)}@example.com`,
  address: 'Jl. Thamrin No. 88, Jakarta Pusat',
  status: CustomerStatus.INACTIVE,
  customerType: CustomerType.INDIVIDUAL,
  source: 'Online',
  labels: ['Nonaktif'],
  totalOrders: 0,
  joinDate: new Date('2025-02-01')
};

// Customer without phone and address (minimal data)
export const customerMinimal = {
  id: 5,
  name: 'Jane Doe',
  email: `jane.doe.${Math.floor(Math.random() * 10000)}@example.com`,
  status: CustomerStatus.NEW,
  customerType: CustomerType.INDIVIDUAL,
  labels: ['Baru'],
  totalOrders: 0,
  joinDate: new Date()
};

// Financial data fixtures
export const financialDataOne = {
  totalSpent: 1250000,
  loyaltyPoints: 500,
  deposit: 100000,
  debt: 0,
  cashback: 25000,
  preferredPaymentMethod: 'CASH',
  creditLimit: 500000
};

export const financialDataTwo = {
  totalSpent: 75000,
  loyaltyPoints: 15,
  deposit: 0,
  debt: 0,
  cashback: 0,
  preferredPaymentMethod: 'TRANSFER',
  creditLimit: 0
};

// Customer notes fixtures
export const noteOne = {
  text: 'Pelanggan meminta untuk selalu menggunakan deterjen khusus',
  author: 'Admin'
};

export const noteTwo = {
  text: 'Pelanggan sering terlambat mengambil laundry',
  author: 'Kasir'
};

// Helper function to insert customers
export const insertCustomers = async (customers: any[], outletId: number) => {
  const insertedCustomers = [];

  for (const customer of customers) {
    const { id, ...customerData } = customer; // Remove id from data to let DB auto-generate
    const insertedCustomer = await prisma.customer.create({
      data: {
        ...customerData,
        outletId,
        financialData: {
          create: {
            totalSpent: 0,
            loyaltyPoints: 0,
            deposit: 0,
            debt: 0,
            cashback: 0
          }
        }
      },
      include: {
        financialData: true,
        province: true,
        city: true,
        customerNotes: true
      }
    });
    insertedCustomers.push(insertedCustomer);
  }

  return insertedCustomers;
};

// Helper function to insert customer with financial data
export const insertCustomerWithFinancial = async (
  customer: any,
  financial: any,
  outletId: number
) => {
  const { id, ...customerData } = customer; // Remove id from data to let DB auto-generate
  return await prisma.customer.create({
    data: {
      ...customerData,
      outletId,
      financialData: {
        create: financial
      }
    },
    include: {
      financialData: true,
      province: true,
      city: true,
      customerNotes: true
    }
  });
};

// Helper function to insert customer notes
export const insertCustomerNotes = async (customerId: number, notes: any[]) => {
  const insertedNotes = [];

  for (const note of notes) {
    const insertedNote = await prisma.customerNote.create({
      data: {
        ...note,
        customerId
      }
    });
    insertedNotes.push(insertedNote);
  }

  return insertedNotes;
};

// Generate random customer data
export const generateCustomerData = (overrides: any = {}) => {
  return {
    name: faker.name.fullName(),
    phone: faker.phone.number('08##########'),
    email: faker.internet.email().toLowerCase(),
    address: faker.address.streetAddress(),
    status: CustomerStatus.ACTIVE,
    customerType: CustomerType.INDIVIDUAL,
    source: faker.helpers.arrayElement(['Referral', 'Walk-in', 'Online', 'Marketing']),
    labels: faker.helpers.arrayElements(['VIP', 'Langganan', 'Baru', 'Korporat'], 2),
    totalOrders: faker.datatype.number({ min: 0, max: 20 }),
    joinDate: faker.date.past(),
    ...overrides
  };
};

export { password };
