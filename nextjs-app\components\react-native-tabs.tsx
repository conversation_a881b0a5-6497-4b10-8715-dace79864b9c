'use client';

import type React from 'react';

import { useState, useRef, useEffect, type ReactNode } from 'react';

interface Tab {
  id: string;
  label: string;
  content: ReactNode;
}

interface ReactNativeTabsProps {
  tabs: Tab[];
  defaultTabIndex?: number;
  className?: string; 
}

export function ReactNativeTabs({
  tabs,
  defaultTabIndex = 0,
  className,
}: ReactNativeTabsProps) {
  const [activeTab, setActiveTab] = useState(defaultTabIndex);
  const tabsContainerRef = useRef<HTMLDivElement>(null);
  const indicatorRef = useRef<HTMLDivElement>(null);
  const tabRefs = useRef<(HTMLButtonElement | null)[]>([]);
  const contentRef = useRef<HTMLDivElement>(null);

  // Touch handling variables
  const [touchStart, setTouchStart] = useState<number | null>(null);
  const [touchEnd, setTouchEnd] = useState<number | null>(null);
  const minSwipeDistance = 50;

  // Update indicator position when active tab changes
  useEffect(() => {
    updateIndicatorPosition();
    scrollActiveTabIntoView();
  }, [activeTab]);

  // Initialize tab refs array
  useEffect(() => {
    tabRefs.current = tabRefs.current.slice(0, tabs.length);
  }, [tabs.length]);

  // Update indicator position based on active tab
  const updateIndicatorPosition = () => {
    const activeTabElement = tabRefs.current[activeTab];
    const indicator = indicatorRef.current;

    if (activeTabElement && indicator) {
      indicator.style.width = `${activeTabElement.offsetWidth}px`;
      indicator.style.transform = `translateX(${activeTabElement.offsetLeft}px)`;
    }
  };

  // Scroll active tab into view
  const scrollActiveTabIntoView = () => {
    const activeTabElement = tabRefs.current[activeTab];
    const tabsContainer = tabsContainerRef.current;

    if (activeTabElement && tabsContainer) {
      const containerWidth = tabsContainer.offsetWidth;
      const tabWidth = activeTabElement.offsetWidth;
      const tabLeft = activeTabElement.offsetLeft;
      const scrollLeft = tabsContainer.scrollLeft;

      // Calculate the center position
      const targetScrollLeft = tabLeft - containerWidth / 2 + tabWidth / 2;

      // Smooth scroll to center the tab
      tabsContainer.scrollTo({
        left: targetScrollLeft,
        behavior: 'smooth',
      });
    }
  };

  // Handle tab click
  const handleTabClick = (index: number) => {
    setActiveTab(index);
  };

  // Touch event handlers for content swiping
  const onTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
  };

  const onTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const onTouchEnd = () => {
    if (!touchStart || !touchEnd) return;

    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > minSwipeDistance;
    const isRightSwipe = distance < -minSwipeDistance;

    if (isLeftSwipe && activeTab < tabs.length - 1) {
      // Swipe left - go to next tab
      setActiveTab(activeTab + 1);
    } else if (isRightSwipe && activeTab > 0) {
      // Swipe right - go to previous tab
      setActiveTab(activeTab - 1);
    }
  };

  // Add visual feedback for swipe
  useEffect(() => {
    const contentElement = contentRef.current;
    if (!contentElement || !touchStart || !touchEnd) return;

    const distance = touchStart - touchEnd;

    // Only apply transform if the swipe is significant but not enough to trigger a tab change
    if (Math.abs(distance) < minSwipeDistance && Math.abs(distance) > 10) {
      contentElement.style.transform = `translateX(${-distance / 5}px)`;
      contentElement.style.transition = 'none';
    }

    return () => {
      // Reset transform when touch ends
      if (contentElement) {
        contentElement.style.transform = '';
        contentElement.style.transition = 'transform 0.3s ease';
      }
    };
  }, [touchEnd, touchStart]);

  return (
    <div className={className}>
      {/* Scrollable Tab Bar */}
      <div className="relative">
        <div
          ref={tabsContainerRef}
          className="flex overflow-x-auto scrollbar-hide py-2"
          style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
        >
          <div className="flex min-w-full">
            {tabs.map((tab, index) => (
              <button
                key={tab.id}
                ref={(el) => (tabRefs.current[index] = el)}
                className={`px-4 py-2 whitespace-nowrap text-sm font-medium transition-colors ${
                  activeTab === index
                    ? 'text-primary'
                    : 'text-muted-foreground hover:text-foreground hover:bg-muted/50'
                }`}
                onClick={() => handleTabClick(index)}
              >
                {tab.label}
              </button>
            ))}
          </div>
        </div>
        {/* Animated indicator */}
        <div
          ref={indicatorRef}
          className="absolute bottom-0 h-0.5 bg-primary transition-all duration-300 ease-in-out"
        />
      </div>

      {/* Tab Content */}
      <div
        ref={contentRef}
        className="mt-4 transition-transform"
        onTouchStart={onTouchStart}
        onTouchMove={onTouchMove}
        onTouchEnd={onTouchEnd}
      >
        {tabs.map((tab, index) => (
          <div
            key={tab.id}
            className={`transition-opacity duration-300 ${
              activeTab === index ? 'block opacity-100' : 'hidden opacity-0'
            }`}
          >
            {tab.content}
          </div>
        ))}
      </div>
    </div>
  );
}
