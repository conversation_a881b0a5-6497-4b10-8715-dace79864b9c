"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { MapPin, Calendar, CheckCircle, XCircle, User, Users } from "lucide-react"
import Link from "next/link"
import { format } from "date-fns"
import { id } from "date-fns/locale"

import { Card } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import BottomNavigation from "@/components/bottom-navigation"

export default function AbsensiPage() {
  const [currentTime, setCurrentTime] = useState(new Date())
  const [pin, setPin] = useState("")
  const [status, setStatus] = useState<"idle" | "success" | "error">("idle")
  const [message, setMessage] = useState("")
  const [activeTab, setActiveTab] = useState("absen")

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    if (pin === "1234") {
      setStatus("success")
      setMessage("Absensi berhasil dicatat!")
      setPin("")
    } else {
      setStatus("error")
      setMessage("PIN tidak valid. Silakan coba lagi.")
    }

    setTimeout(() => {
      setStatus("idle")
      setMessage("")
    }, 3000)
  }

  return (
    <div className="flex flex-col min-h-screen bg-slate-50">
      <header className="p-4 flex justify-between items-center bg-white shadow-sm">
        <h1 className="text-xl font-semibold">Absensi</h1>
        <Link href="/absensi/history">
          <Button variant="ghost" size="sm" className="text-blue-500">
            <Calendar className="h-4 w-4 mr-1" />
            Riwayat
          </Button>
        </Link>
      </header>

      <main className="flex-1 p-4 pb-20">
        <Tabs defaultValue="absen" className="w-full" onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2 mb-4">
            <TabsTrigger value="absen">Absensi</TabsTrigger>
            <TabsTrigger value="team">Tim Saya</TabsTrigger>
          </TabsList>

          <TabsContent value="absen" className="space-y-4">
            <Card className="p-4">
              <div className="text-center mb-4">
                <div className="text-4xl font-bold mb-1">{format(currentTime, "HH:mm:ss")}</div>
                <div className="text-gray-500">{format(currentTime, "EEEE, dd MMMM yyyy", { locale: id })}</div>
              </div>

              <div className="flex items-center justify-center mb-4">
                <div className="bg-blue-100 text-blue-600 rounded-full p-3">
                  <MapPin className="h-6 w-6" />
                </div>
                <span className="ml-2 text-gray-600">Felis Laundry - Outlet Pusat</span>
              </div>

              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <Input
                    type="password"
                    placeholder="Masukkan PIN Anda"
                    value={pin}
                    onChange={(e) => setPin(e.target.value)}
                    className="text-center"
                    maxLength={4}
                  />
                </div>
                <div className="grid grid-cols-2 gap-3">
                  <Button type="submit" className="bg-green-500 hover:bg-green-600">
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Check In
                  </Button>
                  <Button type="submit" className="bg-blue-500 hover:bg-blue-600">
                    <XCircle className="h-4 w-4 mr-2" />
                    Check Out
                  </Button>
                </div>
              </form>

              {status !== "idle" && (
                <div
                  className={`mt-4 p-3 rounded-md text-center ${
                    status === "success" ? "bg-green-100 text-green-700" : "bg-red-100 text-red-700"
                  }`}
                >
                  {message}
                </div>
              )}
            </Card>

            <Card className="p-4">
              <h2 className="font-semibold mb-3">Status Absensi Hari Ini</h2>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <div className="flex items-center">
                    <div className="bg-green-100 text-green-600 rounded-full p-2 mr-3">
                      <CheckCircle className="h-5 w-5" />
                    </div>
                    <div>
                      <p className="font-medium">Check In</p>
                      <p className="text-sm text-gray-500">08:05:32</p>
                    </div>
                  </div>
                  <Badge className="bg-green-500">Tepat Waktu</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <div className="flex items-center">
                    <div className="bg-blue-100 text-blue-600 rounded-full p-2 mr-3">
                      <XCircle className="h-5 w-5" />
                    </div>
                    <div>
                      <p className="font-medium">Check Out</p>
                      <p className="text-sm text-gray-500">--:--:--</p>
                    </div>
                  </div>
                  <Badge variant="outline" className="text-gray-500">
                    Belum
                  </Badge>
                </div>
              </div>
            </Card>

            <div className="flex justify-between">
              <Link href="/absensi/statistics">
                <Button variant="outline" className="w-full mr-2">
                  <Calendar className="h-4 w-4 mr-2" />
                  Statistik
                </Button>
              </Link>
              <Link href="/absensi/team">
                <Button variant="outline" className="w-full ml-2">
                  <Users className="h-4 w-4 mr-2" />
                  Tim
                </Button>
              </Link>
            </div>
          </TabsContent>

          <TabsContent value="team">
            <Card className="p-4 mb-4">
              <h2 className="font-semibold mb-3">Status Tim Hari Ini</h2>
              <div className="space-y-4">
                {[
                  { name: "Ahmad", position: "Kasir", checkIn: "07:55", status: "present" },
                  { name: "Budi", position: "Operator", checkIn: "08:10", status: "late" },
                  { name: "Cindy", position: "Admin", checkIn: "07:50", status: "present" },
                  { name: "Deni", position: "Kurir", checkIn: null, status: "absent" },
                ].map((employee, index) => (
                  <div key={index} className="flex justify-between items-center">
                    <div className="flex items-center">
                      <div
                        className={`rounded-full p-2 mr-3 ${
                          employee.status === "present"
                            ? "bg-green-100 text-green-600"
                            : employee.status === "late"
                              ? "bg-yellow-100 text-yellow-600"
                              : "bg-gray-100 text-gray-600"
                        }`}
                      >
                        <User className="h-5 w-5" />
                      </div>
                      <div>
                        <p className="font-medium">{employee.name}</p>
                        <p className="text-sm text-gray-500">{employee.position}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <Badge
                        className={
                          employee.status === "present"
                            ? "bg-green-500"
                            : employee.status === "late"
                              ? "bg-yellow-500"
                              : "bg-gray-500"
                        }
                      >
                        {employee.status === "present"
                          ? "Hadir"
                          : employee.status === "late"
                            ? "Terlambat"
                            : "Belum Hadir"}
                      </Badge>
                      <p className="text-sm text-gray-500 mt-1">{employee.checkIn ? `${employee.checkIn}` : "--:--"}</p>
                    </div>
                  </div>
                ))}
              </div>
            </Card>

            <Link href="/absensi/team">
              <Button className="w-full bg-blue-500 hover:bg-blue-600">Lihat Semua Anggota Tim</Button>
            </Link>
          </TabsContent>
        </Tabs>
      </main>

      <BottomNavigation activePage="absensi" />
    </div>
  )
}
