import axios from 'axios';
import { api } from '../auth-context';

// Types - Updated for new single Service model
export interface Service {
  id: number;
  name: string;
  description?: string;
  price: number; // Changed from basePrice
  unit: string;
  estimationHours?: number; // Added from old OutletService
  isActive: boolean;
  outletId: number; // Added outlet relation
  categoryId?: number; // Added category relation
  createdAt: string;
  updatedAt: string;
  outlet?: {
    id: number;
    name: string;
  };
  category?: {
    id: number;
    name: string;
    description?: string;
  };
}

// Removed OutletService interface as it's no longer used

export interface CreateServiceRequest {
  name: string;
  description?: string;
  price: number; // Changed from basePrice
  unit: string;
  estimationHours?: number;
  isActive?: boolean;
  outletId: number;
  categoryId?: number;
}

export interface UpdateServiceRequest {
  name?: string;
  description?: string;
  price?: number; // Changed from basePrice
  unit?: string;
  estimationHours?: number;
  isActive?: boolean;
  outletId: number;
  categoryId?: number;
}

export interface ServicesResponse {
  results: Service[];
  page: number;
  limit: number;
  totalPages: number;
  totalResults: number;
}

export interface ServiceFilters {
  search?: string;
  isActive?: boolean;
  unit?: string;
  sortBy?: string;
  limit?: number;
  page?: number;
  outletId: number;
}

// API functions
export const servicesAPI = {
  // Get all services
  getServices: async (filters: ServiceFilters): Promise<ServicesResponse> => {
    const response = await api.get('/services', { params: filters });
    return response.data;
  },

  // Get single service
  getService: async (id: number, outletId: number): Promise<Service> => {
    const response = await api.get(`/services/${id}`, {
      params: { outletId },
    });
    return response.data;
  },

  // Create service
  createService: async (data: CreateServiceRequest): Promise<Service> => {
    const response = await api.post('/services', data);
    return response.data;
  },

  // Update service
  updateService: async (
    id: number,
    data: UpdateServiceRequest
  ): Promise<Service> => {
    const response = await api.patch(`/services/${id}`, data);
    return response.data;
  },

  // Delete service
  deleteService: async (id: number, outletId: number): Promise<void> => {
    await api.delete(`/services/${id}`, {
      params: { outletId },
    });
  },
};

export default servicesAPI;
