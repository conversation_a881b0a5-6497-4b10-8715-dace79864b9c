import {
  Shirt,
  Bed,
  WashingMachine,
  Footprints,
  ShowerHead,
  Package,
} from 'lucide-react';

export type IconName =
  | 'tshirt'
  | 'bed'
  | 'washing-machine'
  | 'shoe'
  | 'iron'
  | 'package';

interface IconProps {
  className?: string;
}

export const getIcon = (name: IconName, props?: IconProps) => {
  const iconProps = {
    className: `h-4 w-4 ${props?.className || ''}`,
  };

  switch (name) {
    case 'tshirt':
      return <Shirt {...iconProps} />;
    case 'bed':
      return <Bed {...iconProps} />;
    case 'washing-machine':
      return <WashingMachine {...iconProps} />;
    case 'shoe':
      return <Footprints {...iconProps} />;
    case 'iron':
      return <ShowerHead {...iconProps} />;
    case 'package':
      return <Package {...iconProps} />;
    default:
      return <Shirt {...iconProps} />;
  }
};

export const getIconColor = (name: IconName): string => {
  switch (name) {
    case 'tshirt':
      return 'text-blue-500';
    case 'bed':
      return 'text-amber-500';
    case 'washing-machine':
      return 'text-indigo-500';
    case 'shoe':
      return 'text-green-500';
    case 'iron':
      return 'text-purple-500';
    case 'package':
      return 'text-orange-500';
    default:
      return 'text-gray-500';
  }
};
