"use client"

import { useState } from "react"
import Link from "next/link"
import { ArrowLeft, Bell, Settings, Check, ShoppingBag, User, DollarSign, AlertCircle } from "lucide-react"
import { useRouter } from "next/navigation"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"

// Mock data for notifications
const mockNotifications = [
  {
    id: 1,
    title: "Order Baru",
    message: "Order #TRX/250324/014 telah dibuat",
    time: "10 menit yang lalu",
    isRead: false,
    type: "order",
    link: "/pesanan/1",
  },
  {
    id: 2,
    title: "Pembayaran Diterima",
    message: "Pembayaran untuk Order #TRX/250324/009 telah diterima",
    time: "1 jam yang lalu",
    isRead: false,
    type: "payment",
    link: "/akun/finance",
  },
  {
    id: 3,
    title: "Pelanggan Baru",
    message: "Budi Santoso telah mendaftar sebagai pelanggan baru",
    time: "3 jam yang lalu",
    isRead: true,
    type: "customer",
    link: "/akun/customers/3",
  },
  {
    id: 4,
    title: "Stok Menipis",
    message: "Stok deterjen hampir habis. Segera lakukan pembelian.",
    time: "5 jam yang lalu",
    isRead: true,
    type: "inventory",
    link: "/akun/inventory",
  },
  {
    id: 5,
    title: "Order Siap Diambil",
    message: "Order #TRX/250323/005 telah selesai dan siap diambil",
    time: "1 hari yang lalu",
    isRead: true,
    type: "order",
    link: "/pesanan/5",
  },
]

export default function NotificationsPage() {
  const router = useRouter()
  const [notifications, setNotifications] = useState(mockNotifications)
  const [activeTab, setActiveTab] = useState("semua")
  const [showClearDialog, setShowClearDialog] = useState(false)

  // Filter notifications based on active tab
  const filteredNotifications = notifications.filter((notification) => {
    if (activeTab === "semua") return true
    if (activeTab === "belum-dibaca") return !notification.isRead
    if (activeTab === "order" && notification.type === "order") return true
    return false
  })

  const markAllAsRead = () => {
    setNotifications(
      notifications.map((notification) => ({
        ...notification,
        isRead: true,
      })),
    )
  }

  const markAsRead = (id: number) => {
    setNotifications(
      notifications.map((notification) => (notification.id === id ? { ...notification, isRead: true } : notification)),
    )
  }

  const clearAllNotifications = () => {
    setNotifications([])
    setShowClearDialog(false)
  }

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case "order":
        return <ShoppingBag className="h-5 w-5 text-blue-500" />
      case "payment":
        return <DollarSign className="h-5 w-5 text-green-500" />
      case "customer":
        return <User className="h-5 w-5 text-purple-500" />
      case "inventory":
        return <AlertCircle className="h-5 w-5 text-orange-500" />
      default:
        return <Bell className="h-5 w-5 text-gray-500" />
    }
  }

  return (
    <div className="flex flex-col min-h-screen bg-slate-50">
      <header className="p-4 flex justify-between items-center bg-white sticky top-0 z-10 shadow-sm">
        <div className="flex items-center">
          <Link href="/" className="mr-3">
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <h1 className="text-lg font-semibold">Notifikasi</h1>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="sm" onClick={markAllAsRead}>
            <Check className="h-4 w-4 mr-1" /> Tandai Semua Dibaca
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon">
                <Settings className="h-5 w-5" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => router.push("/notifikasi/settings")}>
                Pengaturan Notifikasi
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setShowClearDialog(true)} className="text-red-500">
                Hapus Semua Notifikasi
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </header>

      <div className="p-4">
        <Tabs defaultValue="semua" className="mb-4" onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-3 w-full">
            <TabsTrigger value="semua">
              Semua
              <Badge variant="secondary" className="ml-2">
                {notifications.length}
              </Badge>
            </TabsTrigger>
            <TabsTrigger value="belum-dibaca">
              Belum Dibaca
              <Badge variant="secondary" className="ml-2">
                {notifications.filter((n) => !n.isRead).length}
              </Badge>
            </TabsTrigger>
            <TabsTrigger value="order">
              Order
              <Badge variant="secondary" className="ml-2">
                {notifications.filter((n) => n.type === "order").length}
              </Badge>
            </TabsTrigger>
          </TabsList>
        </Tabs>

        <div className="space-y-3 mb-20">
          {filteredNotifications.length > 0 ? (
            filteredNotifications.map((notification) => (
              <Card
                key={notification.id}
                className={`p-4 ${notification.isRead ? "bg-white" : "bg-blue-50 border-blue-100"}`}
              >
                <div className="flex gap-3">
                  <div className="mt-1">{getNotificationIcon(notification.type)}</div>
                  <div className="flex-1">
                    <div className="flex justify-between items-start">
                      <h3 className={`font-medium ${notification.isRead ? "" : "font-semibold"}`}>
                        {notification.title}
                      </h3>
                      <span className="text-xs text-gray-500">{notification.time}</span>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">{notification.message}</p>
                    <div className="flex justify-between items-center mt-2">
                      <Link href={notification.link}>
                        <Button variant="link" className="p-0 h-auto text-blue-500">
                          Lihat Detail
                        </Button>
                      </Link>
                      {!notification.isRead && (
                        <Button variant="ghost" size="sm" className="h-8" onClick={() => markAsRead(notification.id)}>
                          <Check className="h-4 w-4 mr-1" /> Tandai Dibaca
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              </Card>
            ))
          ) : (
            <div className="text-center py-8 text-gray-500">Tidak ada notifikasi</div>
          )}
        </div>
      </div>

      {/* Clear All Confirmation Dialog */}
      <Dialog open={showClearDialog} onOpenChange={setShowClearDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Hapus Semua Notifikasi</DialogTitle>
            <DialogDescription>
              Apakah Anda yakin ingin menghapus semua notifikasi? Tindakan ini tidak dapat dibatalkan.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowClearDialog(false)}>
              Batal
            </Button>
            <Button variant="destructive" onClick={clearAllNotifications}>
              Hapus Semua
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
