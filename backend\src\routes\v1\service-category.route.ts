import express from 'express';
import auth from '../../middlewares/auth';
import validate from '../../middlewares/validate';
import serviceCategoryValidation from '../../validations/service-category.validation';
import serviceCategoryController from '../../controllers/service-category.controller';

const router = express.Router();

router
  .route('/')
  .post(
    auth('manageServices'),
    validate(serviceCategoryValidation.createServiceCategory),
    serviceCategoryController.createServiceCategory
  )
  .get(
    auth('getServices'),
    validate(serviceCategoryValidation.getServiceCategories),
    serviceCategoryController.getServiceCategories
  );

router
  .route('/select')
  .get(
    auth('getServices'),
    validate(serviceCategoryValidation.getServiceCategoriesForSelect),
    serviceCategoryController.getServiceCategoriesForSelect
  );

router.route('/count').get(auth('getServices'), serviceCategoryController.queryCount);

router
  .route('/:id')
  .get(
    auth('getServices'),
    validate(serviceCategoryValidation.getServiceCategory),
    serviceCategoryController.getServiceCategory
  )
  .patch(
    auth('manageServices'),
    validate(serviceCategoryValidation.updateServiceCategory),
    serviceCategoryController.updateServiceCategory
  )
  .delete(
    auth('manageServices'),
    validate(serviceCategoryValidation.deleteServiceCategory),
    serviceCategoryController.deleteServiceCategory
  );

export default router;

/**
 * @swagger
 * components:
 *   schemas:
 *     ServiceCategory:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           example: 1
 *         name:
 *           type: string
 *           example: "Cuci Kering"
 *         description:
 *           type: string
 *           example: "Layanan cuci dan setrika kering"
 *         productionProcess:
 *           type: array
 *           items:
 *             type: string
 *           example: ["sorting", "washing", "drying", "ironing", "packaging"]
 *         outletId:
 *           type: integer
 *           example: 1
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 *         services:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/Service'
 *         outlet:
 *           $ref: '#/components/schemas/Outlet'
 *
 *     CreateServiceCategoryRequest:
 *       type: object
 *       required:
 *         - name
 *       properties:
 *         name:
 *           type: string
 *           example: "Cuci Kering"
 *         description:
 *           type: string
 *           example: "Layanan cuci dan setrika kering"
 *         productionProcess:
 *           type: array
 *           items:
 *             type: string
 *           example: ["sorting", "washing", "drying", "ironing", "packaging"]
 *
 *     UpdateServiceCategoryRequest:
 *       type: object
 *       properties:
 *         name:
 *           type: string
 *           example: "Cuci Kering Premium"
 *         description:
 *           type: string
 *           example: "Layanan cuci dan setrika kering dengan kualitas premium"
 *         productionProcess:
 *           type: array
 *           items:
 *             type: string
 *           example: ["sorting", "washing", "drying", "ironing", "quality_control", "packaging"]
 */

/**
 * @swagger
 * /v1/service-categories:
 *   post:
 *     summary: Create a new service category
 *     tags: [Service Categories]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateServiceCategoryRequest'
 *     responses:
 *       201:
 *         description: Service category created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Kategori layanan berhasil dibuat"
 *                 data:
 *                   $ref: '#/components/schemas/ServiceCategory'
 *   get:
 *     summary: Get all service categories
 *     tags: [Service Categories]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search by category name or description
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 50
 *         description: Items per page
 *       - in: query
 *         name: outletId
 *         schema:
 *           type: integer
 *         description: Filter by outlet ID (optional if user has outlet context)
 *     responses:
 *       200:
 *         description: Service categories retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Kategori layanan berhasil diambil"
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/ServiceCategory'
 *                 pagination:
 *                   $ref: '#/components/schemas/Pagination'
 */
/**
 * @swagger
 * /v1/service-categories/select:
 *   get:
 *     summary: Get service categories for select dropdown
 *     tags: [Service Categories]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: outletId
 *         schema:
 *           type: integer
 *         description: Filter by outlet ID (optional if user has outlet context)
 *     responses:
 *       200:
 *         description: Service categories for select retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Kategori layanan untuk pilihan berhasil diambil"
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                         example: 1
 *                       name:
 *                         type: string
 *                         example: "Cuci Kering"
 *                       description:
 *                         type: string
 *                         example: "Layanan cuci dan setrika kering"
 *                       _count:
 *                         type: object
 *                         properties:
 *                           services:
 *                             type: integer
 *                             example: 5
 */

/**
 * @swagger
 * /v1/service-categories/{id}:
 *   get:
 *     summary: Get a single service category
 *     tags: [Service Categories]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Service category ID
 *       - in: query
 *         name: outletId
 *         schema:
 *           type: integer
 *         description: Filter by outlet ID (optional if user has outlet context)
 *     responses:
 *       200:
 *         description: Service category retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Kategori layanan berhasil diambil"
 *                 data:
 *                   $ref: '#/components/schemas/ServiceCategory'
 *   patch:
 *     summary: Update a service category
 *     tags: [Service Categories]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Service category ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateServiceCategoryRequest'
 *     responses:
 *       200:
 *         description: Service category updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Kategori layanan berhasil diperbarui"
 *                 data:
 *                   $ref: '#/components/schemas/ServiceCategory'
 *   delete:
 *     summary: Delete a service category
 *     tags: [Service Categories]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Service category ID
 *       - in: query
 *         name: outletId
 *         schema:
 *           type: integer
 *         description: Filter by outlet ID (optional if user has outlet context)
 *     responses:
 *       200:
 *         description: Service category deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Kategori layanan berhasil dihapus"
 *
 * @swagger
 * /v1/service-categories/count:
 *   get:
 *     summary: Get count of service categories
 *     tags: [Service Categories]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: outletId
 *         schema:
 *           type: integer
 *         description: Filter by outlet ID (required)
 *     responses:
 *       200:
 *         description: Count of service categories retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Jumlah kategori layanan berhasil diambil"
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       count:
 *                         type: integer
 *                         example: 10
 *                       outletId:
 */
