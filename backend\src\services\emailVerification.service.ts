import httpStatus from 'http-status';
import prisma from '../client';
import ApiError from '../utils/ApiError';
import config from '../config/config';
import logger from '../config/logger';

/**
 * Generate random 6-digit verification code
 * @returns {string}
 */
const generateVerificationCode = (): string => {
  return Math.floor(100000 + Math.random() * 900000).toString();
};

/**
 * Send email verification OTP
 * @param {string} email
 * @returns {Promise<string>}
 */
const sendEmailVerificationOTP = async (email: string): Promise<string> => {
  logger.debug(`[EMAIL_VERIFICATION] Starting email OTP generation - Email: ${email}`);

  try {
    // Generate verification code
    const code = generateVerificationCode();
    const expiresAt = new Date(
      Date.now() + config.verification.emailOtpExpirationMinutes * 60 * 1000
    );

    logger.debug(
      `[EMAIL_VERIFICATION] Generated OTP code - Email: ${email}, Code: ${code}, ExpiresAt: ${expiresAt.toISOString()}`
    );

    // Invalidate previous codes for this email
    const invalidatedResult = await prisma.emailVerification.updateMany({
      where: {
        email,
        isUsed: false,
        expiresAt: {
          gt: new Date()
        }
      },
      data: {
        isUsed: true
      }
    });

    if (invalidatedResult.count > 0) {
      logger.debug(
        `[EMAIL_VERIFICATION] Invalidated ${invalidatedResult.count} previous codes - Email: ${email}`
      );
    }

    // Save new verification code
    await prisma.emailVerification.create({
      data: {
        email,
        code,
        expiresAt,
        isUsed: false
      }
    });

    logger.debug(`[EMAIL_VERIFICATION] OTP code saved to database - Email: ${email}`);

    // For development/test, log the code
    if (config.env === 'development' || config.env === 'test') {
      logger.info(
        `[EMAIL_VERIFICATION] Development mode - Email verification OTP for ${email}: ${code}`
      );
    }

    logger.info(`[EMAIL_VERIFICATION] Email OTP generated successfully - Email: ${email}`);
    return code;
  } catch (error: any) {
    logger.error(
      `[EMAIL_VERIFICATION] Failed to generate email OTP - Email: ${email}, Error: ${error.message}`
    );
    throw error;
  }
};

/**
 * Verify email with OTP code
 * @param {string} email
 * @param {string} code
 * @param {number} userId
 * @returns {Promise<void>}
 */
const verifyEmailOTP = async (email: string, code: string, userId: number): Promise<void> => {
  logger.debug(
    `[EMAIL_VERIFICATION] Starting email OTP verification - Email: ${email}, Code: ${code.substring(
      0,
      2
    )}***, UserID: ${userId}`
  );

  try {
    // Find valid verification code
    const verificationRecord = await prisma.emailVerification.findFirst({
      where: {
        email,
        code,
        isUsed: false,
        expiresAt: {
          gt: new Date()
        }
      }
    });

    if (!verificationRecord) {
      logger.warn(
        `[EMAIL_VERIFICATION] Email OTP verification failed - Invalid or expired code - Email: ${email}, Code: ${code.substring(
          0,
          2
        )}***, UserID: ${userId}`
      );
      throw new ApiError(httpStatus.BAD_REQUEST, 'Invalid or expired verification code');
    }

    logger.debug(
      `[EMAIL_VERIFICATION] Valid OTP code found - Email: ${email}, Code: ${code.substring(
        0,
        2
      )}***, UserID: ${userId}, RecordID: ${verificationRecord.id}`
    );

    // Mark code as used
    await prisma.emailVerification.update({
      where: {
        id: verificationRecord.id
      },
      data: {
        isUsed: true
      }
    });

    logger.debug(
      `[EMAIL_VERIFICATION] OTP code marked as used - RecordID: ${verificationRecord.id}`
    );

    // Update user email verification status
    await prisma.user.update({
      where: {
        id: userId
      },
      data: {
        isEmailVerified: true
      }
    });

    logger.info(
      `[EMAIL_VERIFICATION] Email OTP verification successful - Email: ${email}, UserID: ${userId}`
    );
  } catch (error: any) {
    if (error instanceof ApiError) {
      throw error;
    }
    logger.error(
      `[EMAIL_VERIFICATION] Email OTP verification error - Email: ${email}, Code: ${code.substring(
        0,
        2
      )}***, UserID: ${userId}, Error: ${error.message}`
    );
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Email verification failed');
  }
};

/**
 * Verify email with OTP code without user context (for public verification)
 * @param {string} code
 * @returns {Promise<void>}
 */
const verifyEmailOTPByCode = async (code: string): Promise<void> => {
  logger.debug(
    `[EMAIL_VERIFICATION] Starting email OTP verification by code - Code: ${code.substring(
      0,
      2
    )}***`
  );

  try {
    // Find verification record (including used ones to check if it was used)
    const verificationRecord = await prisma.emailVerification.findFirst({
      where: {
        code
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    if (!verificationRecord) {
      logger.warn(
        `[EMAIL_VERIFICATION] Email OTP verification failed - Invalid code - Code: ${code.substring(
          0,
          2
        )}***`
      );
      throw new ApiError(httpStatus.BAD_REQUEST, 'Invalid verification code');
    }

    logger.debug(
      `[EMAIL_VERIFICATION] Verification record found - Code: ${code.substring(0, 2)}***, Email: ${
        verificationRecord.email
      }, RecordID: ${verificationRecord.id}, IsUsed: ${verificationRecord.isUsed}`
    );

    // Check if already used
    if (verificationRecord.isUsed) {
      logger.warn(
        `[EMAIL_VERIFICATION] Email OTP verification failed - Code already used - Code: ${code.substring(
          0,
          2
        )}***, Email: ${verificationRecord.email}`
      );
      throw new ApiError(httpStatus.BAD_REQUEST, 'Verification code has been used');
    }

    // Check if expired
    if (verificationRecord.expiresAt < new Date()) {
      logger.warn(
        `[EMAIL_VERIFICATION] Email OTP verification failed - Code expired - Code: ${code.substring(
          0,
          2
        )}***, Email: ${
          verificationRecord.email
        }, ExpiredAt: ${verificationRecord.expiresAt.toISOString()}`
      );
      throw new ApiError(httpStatus.BAD_REQUEST, 'Verification code has expired');
    }

    // Find user by email
    const user = await prisma.user.findUnique({
      where: { email: verificationRecord.email }
    });

    if (!user) {
      logger.error(
        `[EMAIL_VERIFICATION] Email OTP verification failed - User not found - Email: ${
          verificationRecord.email
        }, Code: ${code.substring(0, 2)}***`
      );
      throw new ApiError(httpStatus.BAD_REQUEST, 'User not found');
    }

    logger.debug(
      `[EMAIL_VERIFICATION] User found for verification - UserID: ${user.id}, Email: ${user.email}`
    );

    // Mark code as used
    await prisma.emailVerification.update({
      where: {
        id: verificationRecord.id
      },
      data: {
        isUsed: true
      }
    });

    logger.debug(
      `[EMAIL_VERIFICATION] OTP code marked as used - RecordID: ${verificationRecord.id}`
    );

    // Update user email verification status
    await prisma.user.update({
      where: {
        id: user.id
      },
      data: {
        isEmailVerified: true
      }
    });

    logger.info(
      `[EMAIL_VERIFICATION] Email OTP verification by code successful - UserID: ${
        user.id
      }, Email: ${user.email}, Code: ${code.substring(0, 2)}***`
    );
  } catch (error: any) {
    if (error instanceof ApiError) {
      throw error;
    }
    logger.error(
      `[EMAIL_VERIFICATION] Email OTP verification by code error - Code: ${code.substring(
        0,
        2
      )}***, Error: ${error.message}`
    );
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Email verification failed');
  }
};

/**
 * Clean up expired email verification codes
 * @returns {Promise<number>}
 */
const cleanupExpiredCodes = async (): Promise<number> => {
  logger.debug(`[EMAIL_VERIFICATION] Starting cleanup of expired email verification codes`);

  try {
    const result = await prisma.emailVerification.deleteMany({
      where: {
        expiresAt: {
          lt: new Date()
        }
      }
    });

    logger.info(
      `[EMAIL_VERIFICATION] Cleanup completed - Deleted ${result.count} expired email verification codes`
    );
    return result.count;
  } catch (error: any) {
    logger.error(`[EMAIL_VERIFICATION] Error during cleanup - Error: ${error.message}`);
    throw error;
  }
};

export default {
  generateVerificationCode,
  sendEmailVerificationOTP,
  verifyEmailOTP,
  verifyEmailOTPByCode,
  cleanupExpiredCodes
};
