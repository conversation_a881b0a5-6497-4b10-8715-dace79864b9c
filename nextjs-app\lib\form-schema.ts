import { z } from 'zod';

// Schema untuk Login Form
export const loginSchema = z.object({
  email: z
    .string()
    .min(1, { message: 'Email wajib diisi' })
    .email({ message: 'Format email tidak valid' }),
  password: z
    .string()
    .min(1, { message: 'Password wajib diisi' })
    .min(6, { message: 'Password minimal 6 karakter' }),
});

export type LoginFormValues = z.infer<typeof loginSchema>;

// Schema untuk Register Form
export const registerSchema = z
  .object({
    name: z.string().min(1, { message: 'Nama wajib diisi' }),
    email: z
      .string()
      .min(1, { message: 'Email wajib diisi' })
      .email({ message: 'Format email tidak valid' }),
    phone: z
      .string()
      .min(1, { message: 'Nomor HP wajib diisi' })
      .regex(/^[0-9+]+$/, { message: 'Nomor HP hanya boleh berisi angka' }),
    password: z
      .string()
      .min(1, { message: 'Password wajib diisi' })
      .min(6, { message: 'Password minimal 6 karakter' }),
    confirmPassword: z
      .string()
      .min(1, { message: 'Konfirmasi password wajib diisi' }),
    agreed: z.boolean().refine((val) => val === true, {
      message: 'Anda harus menyetujui Syarat & Ketentuan',
    }),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: 'Konfirmasi password tidak cocok',
    path: ['confirmPassword'],
  });

export type RegisterFormValues = z.infer<typeof registerSchema>;

// Schema untuk Forgot Password Form
export const forgotPasswordSchema = z.object({
  email: z
    .string()
    .min(1, { message: 'Email wajib diisi' })
    .email({ message: 'Format email tidak valid' }),
});

export type ForgotPasswordFormValues = z.infer<typeof forgotPasswordSchema>;

// Schema untuk Reset Password Form
export const resetPasswordSchema = z
  .object({
    password: z
      .string()
      .min(1, { message: 'Password baru wajib diisi' })
      .min(6, { message: 'Password minimal 6 karakter' }),
    confirmPassword: z
      .string()
      .min(1, { message: 'Konfirmasi password wajib diisi' }),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: 'Konfirmasi password tidak cocok',
    path: ['confirmPassword'],
  });

export type ResetPasswordFormValues = z.infer<typeof resetPasswordSchema>;

// Schema untuk Resend Verification
export const resendVerificationSchema = z.object({
  email: z
    .string()
    .min(1, { message: 'Email wajib diisi' })
    .email({ message: 'Format email tidak valid' }),
});

export type ResendVerificationFormValues = z.infer<
  typeof resendVerificationSchema
>;
