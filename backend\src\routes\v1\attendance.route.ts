import express from 'express';
import multer from 'multer';
import path from 'path';
import { attendanceController } from '../../controllers';
import auth from '../../middlewares/auth';
import validate from '../../middlewares/validate';
import { attendanceValidation } from '../../validations';

const router = express.Router();

// Configure multer for photo uploads
const storage = multer.diskStorage({
  destination: (req: any, file: any, cb: any) => {
    cb(null, 'uploads/attendance/');
  },
  filename: (req: any, file: any, cb: any) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1e9);
    cb(null, `${file.fieldname}-${uniqueSuffix}${path.extname(file.originalname)}`);
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB limit
  },
  fileFilter: (req: any, file: any, cb: any) => {
    // Accept only images
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed!'));
    }
  }
});

// Attendance routes
router
  .route('/check-in')
  .post(
    auth(),
    upload.single('photo'),
    validate(attendanceValidation.checkIn),
    attendanceController.checkIn
  );

router
  .route('/check-out')
  .post(
    auth(),
    upload.single('photo'),
    validate(attendanceValidation.checkOut),
    attendanceController.checkOut
  );

router
  .route('/status/today')
  .get(auth(), validate(attendanceValidation.getTodayStatus), attendanceController.getTodayStatus);

router
  .route('/history')
  .get(
    auth(),
    validate(attendanceValidation.getAttendanceHistory),
    attendanceController.getAttendanceHistory
  );

router
  .route('/team/today')
  .get(
    auth(),
    validate(attendanceValidation.getTeamAttendance),
    attendanceController.getTeamAttendance
  );

// Work schedule routes
router
  .route('/schedules')
  .post(
    auth('manageUsers'), // Only managers/owners can create schedules
    validate(attendanceValidation.createWorkSchedule),
    attendanceController.createWorkSchedule
  )
  .get(
    auth(),
    validate(attendanceValidation.getWorkSchedules),
    attendanceController.getWorkSchedules
  );

// Attendance settings routes
router.route('/settings/:outletId').put(
  auth('manageUsers'), // Only managers/owners can update settings
  validate(attendanceValidation.updateAttendanceSettings),
  attendanceController.updateAttendanceSettings
);

router
  .route('/settings')
  .get(
    auth(),
    validate(attendanceValidation.getAttendanceSettings),
    attendanceController.getAttendanceSettings
  );

export default router;
