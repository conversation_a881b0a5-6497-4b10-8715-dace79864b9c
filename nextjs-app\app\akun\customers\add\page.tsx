'use client';

import type React from 'react';

import { useState, useRef } from 'react';
import { ArrowLeft, MapPin, X, Plus, Tag, Camera, Loader2 } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';
import { toast } from 'sonner';

import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Textarea } from '@/components/ui/textarea';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { useRouter, useSearchParams } from 'next/navigation';
import {
  useCreateCustomer,
  getCustomerFieldErrors,
} from '@/hooks/useCustomers';

export default function AddCustomerPage() {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const router = useRouter();
  const searchParams = useSearchParams();
  const createCustomerMutation = useCreateCustomer();

  const [formData, setFormData] = useState({
    name: '',
    phone: '',
    email: '',
    address: '',
    mapLink: '',
    latitude: '',
    longitude: '',
    status: 'ACTIVE' as 'ACTIVE' | 'INACTIVE',
    customerType: 'INDIVIDUAL' as 'INDIVIDUAL' | 'CORPORATE',
  });

  const [photos, setPhotos] = useState<string[]>([]);
  const [labels, setLabels] = useState<string[]>([]);
  const [newLabel, setNewLabel] = useState('');
  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));

    // Clear field error when user starts typing
    if (errors[name]) {
      setErrors((prev) => ({ ...prev, [name]: '' }));
    }
  };

  const handleStatusChange = (value: string) => {
    setFormData((prev) => ({
      ...prev,
      status: value as 'ACTIVE' | 'INACTIVE',
    }));
  };

  const handleCustomerTypeChange = (value: string) => {
    setFormData((prev) => ({
      ...prev,
      customerType: value as 'INDIVIDUAL' | 'CORPORATE',
    }));
  };

  const handlePhotoUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const newPhotos = Array.from(e.target.files).map((file) =>
        URL.createObjectURL(file)
      );
      setPhotos((prev) => [...prev, ...newPhotos]);
    }
  };

  const removePhoto = (index: number) => {
    setPhotos(photos.filter((_, i) => i !== index));
  };

  const triggerFileInput = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const addLabel = () => {
    if (newLabel.trim() && !labels.includes(newLabel.trim())) {
      setLabels([...labels, newLabel.trim()]);
      setNewLabel('');
    }
  };

  const removeLabel = (labelToRemove: string) => {
    setLabels(labels.filter((label) => label !== labelToRemove));
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Nama lengkap wajib diisi';
    }

    if (!formData.phone.trim()) {
      newErrors.phone = 'Nomor telepon wajib diisi';
    } else if (!/^(\+62|62|0)8[1-9][0-9]{6,9}$/.test(formData.phone)) {
      newErrors.phone = 'Format nomor telepon tidak valid';
    }

    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Format email tidak valid';
    }

    if (!formData.address.trim()) {
      newErrors.address = 'Alamat wajib diisi';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      toast.error('Mohon periksa kembali form Anda');
      return;
    }

    try {
      await createCustomerMutation.mutateAsync({
        name: formData.name,
        phone: formData.phone,
        email: formData.email || undefined,
        address: formData.address,
        status: formData.status,
        customerType: formData.customerType,
        labels: labels.length > 0 ? labels : undefined,
      });

      toast.success('Pelanggan berhasil ditambahkan!');

      // Redirect berdasarkan parameter redirect
      const redirectParam = searchParams.get('redirect');
      if (redirectParam === 'select-customer') {
        router.push('/orders/select-customer');
      } else {
        router.push('/akun/customers');
      }
    } catch (error: any) {
      console.error('Error creating customer:', error);

      // Handle field-specific errors
      const fieldErrors = getCustomerFieldErrors(error);
      if (fieldErrors) {
        setErrors(fieldErrors);
        toast.error('Mohon periksa kembali form Anda');
      } else {
        toast.error(
          error.response?.data?.message || 'Gagal menambahkan pelanggan'
        );
      }
    }
  };

  const predefinedLabels = [
    'VIP',
    'Langganan',
    'Korporat',
    'Reseller',
    'Keluarga',
    'Tetangga',
  ];

  return (
    <div className="flex flex-col min-h-screen bg-slate-50">
      <header className="p-4 flex items-center bg-white sticky top-0 z-10 shadow-sm">
        <Button onClick={() => router.back()} variant="ghost" size="icon">
          <ArrowLeft className="h-5 w-5" />
        </Button>
        <h1 className="text-xl font-semibold ml-4">Tambah Pelanggan Baru</h1>
      </header>

      <div className="p-4 pb-20">
        <Card className="p-4 mb-4">
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">Nama Lengkap *</Label>
              <Input
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                placeholder="Masukkan nama lengkap"
                className={errors.name ? 'border-red-500' : ''}
                required
              />
              {errors.name && (
                <p className="text-sm text-red-600">{errors.name}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="phone">Nomor Telepon *</Label>
              <Input
                id="phone"
                name="phone"
                value={formData.phone}
                onChange={handleChange}
                placeholder="Contoh: 081234567890"
                className={errors.phone ? 'border-red-500' : ''}
                required
              />
              {errors.phone && (
                <p className="text-sm text-red-600">{errors.phone}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="email">Email (Opsional)</Label>
              <Input
                id="email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleChange}
                placeholder="Contoh: <EMAIL>"
                className={errors.email ? 'border-red-500' : ''}
              />
              {errors.email && (
                <p className="text-sm text-red-600">{errors.email}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="address">Alamat *</Label>
              <Textarea
                id="address"
                name="address"
                value={formData.address}
                onChange={handleChange}
                placeholder="Masukkan alamat lengkap"
                rows={3}
                className={errors.address ? 'border-red-500' : ''}
                required
              />
              {errors.address && (
                <p className="text-sm text-red-600">{errors.address}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label>Tipe Pelanggan</Label>
              <RadioGroup
                value={formData.customerType}
                onValueChange={handleCustomerTypeChange}
                className="flex gap-4"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="INDIVIDUAL" id="individual" />
                  <Label htmlFor="individual">Individu</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="CORPORATE" id="corporate" />
                  <Label htmlFor="corporate">Korporat</Label>
                </div>
              </RadioGroup>
            </div>

            <div className="space-y-2">
              <Label>Status</Label>
              <RadioGroup
                value={formData.status}
                onValueChange={handleStatusChange}
                className="flex gap-4"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="ACTIVE" id="active" />
                  <Label htmlFor="active">Aktif</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="INACTIVE" id="inactive" />
                  <Label htmlFor="inactive">Tidak Aktif</Label>
                </div>
              </RadioGroup>
            </div>

            <div className="space-y-2">
              <Label className="flex items-center gap-1">
                <MapPin className="h-4 w-4" /> Lokasi Maps (Opsional)
              </Label>
              <div className="flex gap-2">
                <Input
                  id="mapLink"
                  name="mapLink"
                  value={formData.mapLink}
                  onChange={handleChange}
                  placeholder="Link Google Maps (https://maps.google.com/...)"
                  className="flex-1"
                />
                <Button
                  type="button"
                  variant="outline"
                  onClick={() =>
                    window.open('https://www.google.com/maps', '_blank')
                  }
                >
                  <MapPin className="h-4 w-4" /> Buka Maps
                </Button>
              </div>
            </div>

            <Separator />

            <div className="space-y-2">
              <Label className="flex items-center gap-1">
                <Tag className="h-4 w-4" /> Label Pelanggan
              </Label>
              <div className="flex flex-wrap gap-2 mt-2">
                {labels.map((label) => (
                  <Badge
                    key={label}
                    className="bg-blue-100 text-blue-800 hover:bg-blue-200 px-2 py-1"
                  >
                    {label}
                    <button
                      type="button"
                      onClick={() => removeLabel(label)}
                      className="ml-1 text-blue-800 hover:text-blue-900"
                    >
                      <X className="h-3 w-3 inline" />
                    </button>
                  </Badge>
                ))}
              </div>
              <div className="flex mt-2">
                <Input
                  value={newLabel}
                  onChange={(e) => setNewLabel(e.target.value)}
                  placeholder="Tambah label baru"
                  className="rounded-r-none h-10"
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      addLabel();
                    }
                  }}
                />
                <Button
                  type="button"
                  onClick={addLabel}
                  variant="default"
                  size="default"
                  className="rounded-l-none h-10"
                >
                  Tambah
                </Button>
              </div>
              <div className="mt-2">
                <p className="text-xs text-gray-500 mb-1">
                  Label yang disarankan:
                </p>
                <div className="flex flex-wrap gap-1">
                  {predefinedLabels.map((label) => (
                    <Badge
                      key={label}
                      className="bg-gray-100 text-gray-800 hover:bg-gray-200 cursor-pointer"
                      onClick={() => {
                        if (!labels.includes(label)) {
                          setLabels([...labels, label]);
                        }
                      }}
                    >
                      {label}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>

            <Separator />

            <div className="pt-4">
              <Button
                type="submit"
                variant="default"
                className="w-full"
                disabled={createCustomerMutation.isPending}
              >
                {createCustomerMutation.isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Menyimpan...
                  </>
                ) : (
                  'Simpan Pelanggan'
                )}
              </Button>
            </div>
          </form>
        </Card>
      </div>
    </div>
  );
}
