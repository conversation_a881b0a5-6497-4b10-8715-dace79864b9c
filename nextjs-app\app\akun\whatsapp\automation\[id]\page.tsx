"use client"

import { useState } from "react"
import Link from "next/link"
import { ArrowLeft, Save, Trash2, <PERSON><PERSON>, Bar<PERSON>hart2 } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"

// Sample automation data
const automationData = {
  id: "1",
  name: "Konfirmasi Otomatis",
  status: "active",
  triggerType: "event",
  eventTrigger: "new_order",
  templateId: "1",
  templateName: "Konfirma<PERSON> Pesanan",
  customerFilter: "all",
  lastTriggered: "2 jam yang lalu",
  triggerCount: 128,
}

// Sample template data for selection
const templates = [
  { id: "1", name: "Konfirmasi Pesanan", category: "order" },
  { id: "2", name: "Status Pesanan", category: "order" },
  { id: "3", name: "Pesanan Selesai", category: "order" },
  { id: "4", name: "Pengingat Pembayaran", category: "payment" },
  { id: "5", name: "Promosi Bulanan", category: "promotion" },
]

export default function AutomationDetailPage({ params }: { params: { id: string } }) {
  const [automation, setAutomation] = useState(automationData)

  const handleStatusChange = (checked: boolean) => {
    setAutomation({
      ...automation,
      status: checked ? "active" : "inactive",
    })
  }

  const handleSave = () => {
    alert("Otomasi berhasil disimpan!")
  }

  const handleDelete = () => {
    if (confirm("Apakah Anda yakin ingin menghapus otomasi ini?")) {
      alert("Otomasi berhasil dihapus!")
    }
  }

  const handleDuplicate = () => {
    alert("Otomasi berhasil diduplikasi!")
  }

  return (
    <div className="flex flex-col min-h-screen bg-slate-50">
      <header className="p-4 flex justify-between items-center bg-white sticky top-0 z-10 shadow-sm">
        <div className="flex items-center">
          <Link href="/akun/whatsapp" className="mr-3">
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <h1 className="text-lg font-semibold">Detail Otomasi</h1>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={handleDelete} className="text-red-500 hover:text-red-600">
            <Trash2 className="h-4 w-4 mr-1" /> Hapus
          </Button>
          <Button variant="outline" size="sm" onClick={handleDuplicate}>
            <Copy className="h-4 w-4 mr-1" /> Duplikat
          </Button>
          <Button onClick={handleSave} size="sm" className="bg-green-500 hover:bg-green-600">
            <Save className="h-4 w-4 mr-1" /> Simpan
          </Button>
        </div>
      </header>

      <div className="p-4 pb-20">
        <Tabs defaultValue="edit">
          <TabsList className="grid grid-cols-2 mb-4">
            <TabsTrigger value="edit">Edit Otomasi</TabsTrigger>
            <TabsTrigger value="analytics">Analitik</TabsTrigger>
          </TabsList>

          <TabsContent value="edit" className="space-y-4">
            <Card>
              <CardContent className="p-4 space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Nama Otomasi</Label>
                  <Input
                    id="name"
                    value={automation.name}
                    onChange={(e) => setAutomation({ ...automation, name: e.target.value })}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="status" className="cursor-pointer">
                    Status Otomasi
                  </Label>
                  <div className="flex items-center gap-2">
                    <Switch id="status" checked={automation.status === "active"} onCheckedChange={handleStatusChange} />
                    <Badge variant={automation.status === "active" ? "default" : "secondary"} className="text-xs">
                      {automation.status === "active" ? "Aktif" : "Nonaktif"}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <h3 className="font-medium mb-3">Pemicu Otomasi</h3>

                <div className="space-y-4">
                  <RadioGroup
                    value={automation.triggerType}
                    onValueChange={(value) => setAutomation({ ...automation, triggerType: value })}
                    className="space-y-2"
                  >
                    <div className="flex items-start space-x-2">
                      <RadioGroupItem value="event" id="trigger-event" />
                      <Label htmlFor="trigger-event" className="font-normal cursor-pointer">
                        Berbasis Peristiwa
                        <p className="text-xs text-gray-500">Kirim pesan saat terjadi peristiwa tertentu</p>
                      </Label>
                    </div>
                    <div className="flex items-start space-x-2">
                      <RadioGroupItem value="schedule" id="trigger-schedule" />
                      <Label htmlFor="trigger-schedule" className="font-normal cursor-pointer">
                        Terjadwal
                        <p className="text-xs text-gray-500">Kirim pesan pada waktu yang ditentukan</p>
                      </Label>
                    </div>
                  </RadioGroup>

                  {automation.triggerType === "event" ? (
                    <div className="space-y-2 pt-2">
                      <Label htmlFor="eventTrigger">Jenis Peristiwa</Label>
                      <Select
                        value={automation.eventTrigger}
                        onValueChange={(value) => setAutomation({ ...automation, eventTrigger: value })}
                      >
                        <SelectTrigger id="eventTrigger">
                          <SelectValue placeholder="Pilih jenis peristiwa" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="new_order">Pesanan Baru</SelectItem>
                          <SelectItem value="status_change">Perubahan Status Pesanan</SelectItem>
                          <SelectItem value="order_complete">Pesanan Selesai</SelectItem>
                          <SelectItem value="payment_due">Pembayaran Tertunda</SelectItem>
                          <SelectItem value="payment_received">Pembayaran Diterima</SelectItem>
                          <SelectItem value="customer_birthday">Ulang Tahun Pelanggan</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  ) : (
                    <div className="space-y-2 pt-2">
                      <p className="text-sm text-gray-500">Pengaturan jadwal tidak tersedia dalam mode edit.</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <h3 className="font-medium mb-3">Template Pesan</h3>

                <div className="space-y-2">
                  <Label htmlFor="templateId">Pilih Template</Label>
                  <Select
                    value={automation.templateId}
                    onValueChange={(value) => setAutomation({ ...automation, templateId: value })}
                  >
                    <SelectTrigger id="templateId">
                      <SelectValue placeholder="Pilih template pesan" />
                    </SelectTrigger>
                    <SelectContent>
                      {templates.map((template) => (
                        <SelectItem key={template.id} value={template.id}>
                          {template.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <div className="flex justify-end">
                    <Link href={`/akun/whatsapp/templates/${automation.templateId}`}>
                      <Button variant="link" size="sm" className="h-auto p-0">
                        Lihat template
                      </Button>
                    </Link>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <h3 className="font-medium mb-3">Penerima</h3>

                <div className="space-y-4">
                  <RadioGroup
                    value={automation.customerFilter}
                    onValueChange={(value) => setAutomation({ ...automation, customerFilter: value })}
                    className="space-y-2"
                  >
                    <div className="flex items-start space-x-2">
                      <RadioGroupItem value="all" id="customer-all" />
                      <Label htmlFor="customer-all" className="font-normal cursor-pointer">
                        Semua Pelanggan
                        <p className="text-xs text-gray-500">Kirim ke semua pelanggan yang terkait dengan pemicu</p>
                      </Label>
                    </div>
                    <div className="flex items-start space-x-2">
                      <RadioGroupItem value="segment" id="customer-segment" />
                      <Label htmlFor="customer-segment" className="font-normal cursor-pointer">
                        Segmen Pelanggan
                        <p className="text-xs text-gray-500">Kirim hanya ke pelanggan dalam segmen tertentu</p>
                      </Label>
                    </div>
                  </RadioGroup>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="analytics" className="space-y-4">
            <Card>
              <CardContent className="p-4 space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="font-medium">Statistik Otomasi</h3>
                  <Button variant="outline" size="sm">
                    <BarChart2 className="h-4 w-4 mr-1" /> Laporan Lengkap
                  </Button>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-gray-500">Terakhir terpicu</p>
                    <p className="font-medium">{automation.lastTriggered}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Total terpicu</p>
                    <p className="font-medium">{automation.triggerCount}x</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Tingkat pengiriman</p>
                    <p className="font-medium">98.5%</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Pesan terkirim</p>
                    <p className="font-medium">126</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <h3 className="font-medium mb-4">Aktivitas (30 Hari Terakhir)</h3>
                <div className="h-40 bg-slate-100 rounded-md flex items-center justify-center">
                  <p className="text-gray-500">Grafik aktivitas otomasi</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <h3 className="font-medium mb-3">Riwayat Pemicu Terbaru</h3>

                <div className="space-y-3">
                  {[1, 2, 3, 4, 5].map((i) => (
                    <div key={i} className="flex justify-between items-center py-2 border-b last:border-0">
                      <div>
                        <p className="font-medium">Pesanan #{1000 + i}</p>
                        <p className="text-xs text-gray-500">{i} jam yang lalu</p>
                      </div>
                      <Badge variant="outline" className="text-xs">
                        Terkirim
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
