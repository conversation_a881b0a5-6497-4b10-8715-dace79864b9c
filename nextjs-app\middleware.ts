import { NextRequest, NextResponse } from 'next/server';

// Define public routes that don't require authentication
const publicRoutes = [
  '/',
  '/auth/login',
  '/auth/register',
  '/auth/forgot-password',
  '/auth/reset-password',
  '/auth/verify-request',
  '/auth/select-outlet',
  '/terms',
  '/privacy',
];

// Define protected routes that require authentication
const protectedRoutes = [
  '/dashboard',
  '/akun',
  '/orders',
  '/laporan',
  '/loyalty',
  '/absensi',
  '/notifications',
  '/notifikasi',
];

// Define auth routes that should redirect authenticated users
const authRoutes = [
  '/auth/login',
  '/auth/register',
  '/auth/forgot-password',
  '/auth/reset-password',
];

// Helper function to check if tokens are valid (not expired)
function isTokenValid(tokensStr: string): boolean {
  try {
    const tokens = JSON.parse(tokensStr);
    if (!tokens?.access?.token || !tokens?.access?.expires) {
      return false;
    }

    // Check if access token is not expired (with 5 minute buffer)
    const expiresAt = new Date(tokens.access.expires);
    const now = new Date();
    const bufferTime = 5 * 60 * 1000; // 5 minutes in milliseconds

    return expiresAt.getTime() > now.getTime() + bufferTime;
  } catch (error) {
    return false;
  }
}

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Skip middleware for static files and API routes
  if (
    pathname.startsWith('/_next/') ||
    pathname.startsWith('/api/') ||
    pathname.includes('.')
  ) {
    return NextResponse.next();
  }

  // Get tokens from cookies
  const userCookie = request.cookies.get('user');
  const tokensCookie = request.cookies.get('tokens');
  const activeOutletCookie = request.cookies.get('activeOutlet');

  // Check if user is authenticated with valid tokens
  const hasUserData = !!userCookie?.value;
  const hasValidTokens = !!(
    tokensCookie?.value && isTokenValid(tokensCookie.value)
  );
  const isAuthenticated = hasUserData && hasValidTokens;
  const hasActiveOutlet = !!activeOutletCookie?.value;

  // Check if current path is protected
  const isProtectedRoute = protectedRoutes.some((route) =>
    pathname.startsWith(route)
  );

  // Check if current path is an auth route
  const isAuthRoute = authRoutes.some((route) => pathname.startsWith(route));

  // Check if current path is a public route
  const isPublicRoute = publicRoutes.some(
    (route) => route === pathname || (route === '/' && pathname === '/')
  );

  // If accessing protected route without proper authentication
  if (isProtectedRoute && !isAuthenticated) {
    const loginUrl = new URL('/auth/login', request.url);
    loginUrl.searchParams.set('callbackUrl', pathname);
    return NextResponse.redirect(loginUrl);
  }

  // If accessing protected route while authenticated but no active outlet selected
  if (
    isProtectedRoute &&
    isAuthenticated &&
    !hasActiveOutlet &&
    !['/auth/select-outlet', '/akun/outlets/add'].includes(pathname)
  ) {
    return NextResponse.redirect(new URL('/auth/select-outlet', request.url));
  }

  // // If accessing select-outlet page but already has active outlet
  // if (
  //   pathname === '/auth/select-outlet' &&
  //   isAuthenticated &&
  //   hasActiveOutlet
  // ) {
  //   return NextResponse.redirect(new URL('/dashboard', request.url));
  // }

  // If accessing auth routes while authenticated
  if (isAuthRoute && isAuthenticated) {
    const callbackUrl =
      request.nextUrl.searchParams.get('callbackUrl') ||
      (hasActiveOutlet ? '/dashboard' : '/auth/select-outlet');
    console.log('🚀 ~ middleware ~ callbackUrl:', callbackUrl);
    return NextResponse.redirect(new URL(callbackUrl, request.url));
  }

  // Allow the request to continue
  return NextResponse.next();
}

// Configure which routes the middleware should run on
export const config = {
  // Match all paths except static files and API routes
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public files (images, etc.)
     */
    '/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
};
