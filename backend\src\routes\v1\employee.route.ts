import express from 'express';
import auth from '../../middlewares/auth';
import validate from '../../middlewares/validate';
import { employeeValidation } from '../../validations';
import { employeeController } from '../../controllers';

const router = express.Router();

router
  .route('/')
  .post(
    auth('manageEmployees'),
    validate(employeeValidation.createEmployee),
    employeeController.createEmployee
  )
  .get(
    auth('getEmployees'),
    validate(employeeValidation.getEmployees),
    employeeController.getEmployees
  );

router
  .route('/:employeeId')
  .get(
    auth('getEmployees'),
    validate(employeeValidation.getEmployee),
    employeeController.getEmployee
  )
  .put(
    auth('manageEmployees'),
    validate(employeeValidation.updateEmployee),
    employeeController.updateEmployee
  )
  .delete(
    auth('manageEmployees'),
    validate(employeeValidation.deleteEmployee),
    employeeController.deleteEmployee
  );

export default router;

/**
 * @swagger
 * tags:
 *   name: Employees
 *   description: Employee management and retrieval
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     Employee:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         email:
 *           type: string
 *           format: email
 *         name:
 *           type: string
 *         phone:
 *           type: string
 *         role:
 *           type: string
 *           enum: [EMPLOYEE]
 *         outletId:
 *           type: integer
 *         isActive:
 *           type: boolean
 *         employeeAt:
 *           type: object
 *           properties:
 *             id:
 *               type: integer
 *             name:
 *               type: string
 *       example:
 *         id: 1
 *         email: <EMAIL>
 *         name: John Doe
 *         phone: "081234567890"
 *         role: EMPLOYEE
 *         outletId: 1
 *         isActive: true
 *         employeeAt:
 *           id: 1
 *           name: "Main Outlet"
 */

/**
 * @swagger
 * /users/employees:
 *   post:
 *     summary: Create an employee
 *     description: Only owners can create employees for their outlets. Admins can create employees for any outlet.
 *     tags: [Employees]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - email
 *               - phone
 *               - password
 *               - outletId
 *             properties:
 *               name:
 *                 type: string
 *               email:
 *                 type: string
 *                 format: email
 *                 description: must be unique
 *               phone:
 *                 type: string
 *                 description: must be unique
 *               password:
 *                 type: string
 *                 format: password
 *                 minLength: 8
 *                 description: At least one number and one letter
 *               outletId:
 *                 type: integer
 *                 description: ID of the outlet where employee will work
 *             example:
 *               name: John Doe
 *               email: <EMAIL>
 *               phone: "081234567890"
 *               password: password1
 *               outletId: 1
 *     responses:
 *       "201":
 *         description: Created
 *         content:
 *           application/json:
 *             schema:
 *                $ref: '#/components/schemas/Employee'
 *       "400":
 *         $ref: '#/components/responses/BadRequest'
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "403":
 *         $ref: '#/components/responses/Forbidden'
 *
 *   get:
 *     summary: Get all employees
 *     description: Owners can retrieve employees from their outlets only. Admins can retrieve all employees.
 *     tags: [Employees]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: name
 *         schema:
 *           type: string
 *         description: Employee name
 *       - in: query
 *         name: outletId
 *         schema:
 *           type: integer
 *         description: Filter by outlet ID
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *         description: sort by query in the form of field:desc/asc (ex. name:asc)
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *         default: 10
 *         description: Maximum number of employees
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number
 *     responses:
 *       "200":
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 results:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Employee'
 *                 page:
 *                   type: integer
 *                   example: 1
 *                 limit:
 *                   type: integer
 *                   example: 10
 *                 totalPages:
 *                   type: integer
 *                   example: 1
 *                 totalResults:
 *                   type: integer
 *                   example: 1
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "403":
 *         $ref: '#/components/responses/Forbidden'
 */

/**
 * @swagger
 * /users/employees/{employeeId}:
 *   get:
 *     summary: Get an employee
 *     description: Owners can fetch employees from their outlets only. Admins can fetch any employee.
 *     tags: [Employees]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: employeeId
 *         required: true
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Employee ID
 *     responses:
 *       "200":
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *                $ref: '#/components/schemas/Employee'
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "403":
 *         $ref: '#/components/responses/Forbidden'
 *       "404":
 *         $ref: '#/components/responses/NotFound'
 *
 *   put:
 *     summary: Update an employee
 *     description: Owners can update employees from their outlets only. Admins can update any employee.
 *     tags: [Employees]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: employeeId
 *         required: true
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Employee ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               email:
 *                 type: string
 *                 format: email
 *                 description: must be unique
 *               phone:
 *                 type: string
 *                 description: must be unique
 *               password:
 *                 type: string
 *                 format: password
 *                 minLength: 8
 *                 description: At least one number and one letter
 *               outletId:
 *                 type: integer
 *                 description: Transfer employee to different outlet
 *               isActive:
 *                 type: boolean
 *                 description: Employee active status
 *             example:
 *               name: John Doe Updated
 *               email: <EMAIL>
 *               outletId: 2
 *               isActive: true
 *     responses:
 *       "200":
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *                $ref: '#/components/schemas/Employee'
 *       "400":
 *         $ref: '#/components/responses/BadRequest'
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "403":
 *         $ref: '#/components/responses/Forbidden'
 *       "404":
 *         $ref: '#/components/responses/NotFound'
 *
 *   delete:
 *     summary: Delete an employee
 *     description: Soft delete an employee. Owners can delete employees from their outlets only. Admins can delete any employee.
 *     tags: [Employees]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: employeeId
 *         required: true
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Employee ID
 *     responses:
 *       "200":
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Employee deleted successfully"
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "403":
 *         $ref: '#/components/responses/Forbidden'
 *       "404":
 *         $ref: '#/components/responses/NotFound'
 */
