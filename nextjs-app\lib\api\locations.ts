import { api } from '../auth-context';

export interface Province {
  id: number;
  name: string;
  code: string;
}

export interface City {
  id: number;
  name: string;
  code: string;
  provinceId: number;
  province?: Province;
}

export interface Timezone {
  id: string;
  name: string;
  description: string;
  offset: string;
  regions: string[];
}

export interface SearchResult {
  provinces: Province[];
  cities: City[];
}

export const locationAPI = {
  // Get all provinces
  getProvinces: async (params?: {
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }): Promise<Province[]> => {
    const response = await api.get('/locations/provinces', { params });
    return response.data;
  },

  // Get all cities
  getCities: async (params?: {
    provinceId?: number;
    includeProvince?: boolean;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }): Promise<City[]> => {
    const response = await api.get('/locations/cities', { params });
    return response.data;
  },

  // Get cities by province ID
  getCitiesByProvince: async (
    provinceId: number,
    params?: {
      sortBy?: string;
      sortOrder?: 'asc' | 'desc';
    }
  ): Promise<City[]> => {
    const response = await api.get(
      `/locations/provinces/${provinceId}/cities`,
      { params }
    );
    return response.data;
  },

  // Search locations
  searchLocations: async (
    query: string,
    params?: {
      limit?: number;
    }
  ): Promise<SearchResult> => {
    const response = await api.get('/locations/search', {
      params: { q: query, ...params },
    });
    return response.data;
  },

  // Get all Indonesian timezones
  getTimezones: async (): Promise<Timezone[]> => {
    const response = await api.get('/locations/timezones');
    return response.data;
  },
};
