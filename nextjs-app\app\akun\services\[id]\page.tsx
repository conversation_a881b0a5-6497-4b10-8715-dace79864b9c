"use client"
import Link from "next/link"
import Image from "next/image"
import { ArrowLeft, Edit, Clock, Tag, Info } from "lucide-react"
import { useRouter } from "next/navigation"

import { But<PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"

// Mock data for service details
const mockServiceDetails = {
  id: 1,
  name: "Cuci + Setrika",
  price: 8000,
  unit: "Kg",
  image: "/icons/tshirt.png",
  category: "reguler",
  isActive: true,
  description: "Layanan cuci dan setrika pakaian reguler dengan kualitas terbaik dan wangi tahan lama.",
  duration: 24,
  durationType: "jam",
  createdAt: "01/01/2023",
  updatedAt: "15/03/2023",
  stats: {
    ordersCount: 1250,
    revenue: 10000000,
    averageWeight: 3.5,
  },
}

export default function ServiceDetailPage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const serviceId = params.id
  const service = mockServiceDetails // In a real app, fetch the service by ID

  return (
    <div className="flex flex-col min-h-screen bg-slate-50">
      <header className="p-4 flex justify-between items-center bg-white sticky top-0 z-10 shadow-sm">
        <div className="flex items-center">
          <Link href="/akun/services" className="mr-3">
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <h1 className="text-lg font-semibold">Detail Layanan</h1>
        </div>
        <Button
          onClick={() => router.push(`/akun/services/edit/${serviceId}`)}
          className="bg-blue-500 hover:bg-blue-600"
        >
          <Edit className="h-4 w-4 mr-2" /> Edit
        </Button>
      </header>

      <main className="flex-1 p-4 pb-20">
        <Card className="p-4 mb-4">
          <div className="flex items-start gap-4">
            <div className="w-16 h-16 bg-gray-100 rounded-md flex items-center justify-center">
              <Image
                src={service.image || "/placeholder.svg?height=64&width=64"}
                alt={service.name}
                width={40}
                height={40}
              />
            </div>
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-1">
                <h2 className="text-xl font-semibold">{service.name}</h2>
                <Badge
                  variant="outline"
                  className={service.isActive ? "border-green-500 text-green-500" : "border-red-500 text-red-500"}
                >
                  {service.isActive ? "Aktif" : "Tidak Aktif"}
                </Badge>
              </div>
              <p className="text-gray-500">{service.description}</p>
              <div className="flex items-center gap-4 mt-2">
                <div className="flex items-center gap-1">
                  <Tag className="h-4 w-4 text-gray-500" />
                  <span className="font-medium">Rp {service.price.toLocaleString()}</span>
                  <span className="text-gray-500">/ {service.unit}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Clock className="h-4 w-4 text-gray-500" />
                  <span>
                    {service.duration} {service.durationType}
                  </span>
                </div>
                <Badge variant="secondary">
                  {service.category === "reguler" ? "Reguler" : service.category === "express" ? "Express" : "Satuan"}
                </Badge>
              </div>
            </div>
          </div>
        </Card>

        <div className="grid grid-cols-3 gap-4 mb-4">
          <Card className="p-4">
            <h3 className="text-sm font-medium text-gray-500 mb-1">Total Order</h3>
            <p className="text-2xl font-bold">{service.stats.ordersCount.toLocaleString()}</p>
          </Card>
          <Card className="p-4">
            <h3 className="text-sm font-medium text-gray-500 mb-1">Total Pendapatan</h3>
            <p className="text-2xl font-bold">Rp {service.stats.revenue.toLocaleString()}</p>
          </Card>
          <Card className="p-4">
            <h3 className="text-sm font-medium text-gray-500 mb-1">Rata-rata Berat</h3>
            <p className="text-2xl font-bold">
              {service.stats.averageWeight} {service.unit}
            </p>
          </Card>
        </div>

        <Card className="p-4">
          <h3 className="font-medium flex items-center gap-2 mb-3">
            <Info className="h-4 w-4" /> Informasi Tambahan
          </h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-500">ID Layanan</span>
              <span className="font-medium">{service.id}</span>
            </div>
            <Separator />
            <div className="flex justify-between">
              <span className="text-gray-500">Kategori</span>
              <span className="font-medium capitalize">{service.category}</span>
            </div>
            <Separator />
            <div className="flex justify-between">
              <span className="text-gray-500">Satuan</span>
              <span className="font-medium">{service.unit}</span>
            </div>
            <Separator />
            <div className="flex justify-between">
              <span className="text-gray-500">Durasi Pengerjaan</span>
              <span className="font-medium">
                {service.duration} {service.durationType}
              </span>
            </div>
            <Separator />
            <div className="flex justify-between">
              <span className="text-gray-500">Tanggal Dibuat</span>
              <span className="font-medium">{service.createdAt}</span>
            </div>
            <Separator />
            <div className="flex justify-between">
              <span className="text-gray-500">Terakhir Diperbarui</span>
              <span className="font-medium">{service.updatedAt}</span>
            </div>
          </div>
        </Card>
      </main>
    </div>
  )
}
