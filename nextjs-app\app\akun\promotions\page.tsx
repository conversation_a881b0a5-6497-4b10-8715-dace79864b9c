'use client';

import { useState } from 'react';
import Link from 'next/link';
import {
  ArrowLeft,
  Plus,
  Search,
  Tag,
  Calendar,
  Percent,
  DollarSign,
  Edit,
  Trash2,
  Eye,
  Filter,
} from 'lucide-react';
import { format } from 'date-fns';
import { id } from 'date-fns/locale';

import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/components/ui/use-toast';
import { usePromotions, useDeletePromotion } from '@/hooks/usePromotions';

export default function PromotionsPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState('all');
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [promotionToDelete, setPromotionToDelete] = useState<number | null>(
    null
  );

  const { toast } = useToast();

  // Get promotions data from API
  const {
    data: promotionsResponse,
    isLoading,
    isError,
    refetch,
  } = usePromotions({
    search: searchQuery,
    isActive:
      activeTab === 'active'
        ? true
        : activeTab === 'inactive'
        ? false
        : undefined,
    limit: 50,
    sortBy: 'createdAt:desc',
  });

  const deletePromotionMutation = useDeletePromotion();

  const promotions = promotionsResponse?.results || [];

  // Filter promotions based on search query and active tab
  const filteredPromotions = promotions.filter((promo) => {
    const now = new Date();
    const validFrom = new Date(promo.validFrom);
    const validUntil = new Date(promo.validUntil);

    // Determine status based on dates and isActive
    let status = 'inactive';
    if (promo.isActive) {
      if (now < validFrom) {
        status = 'upcoming';
      } else if (now >= validFrom && now <= validUntil) {
        status = 'active';
      } else {
        status = 'inactive';
      }
    }

    if (activeTab === 'all') return true;
    if (activeTab === 'upcoming') return status === 'upcoming';
    if (activeTab === 'active') return status === 'active';
    if (activeTab === 'inactive') return status === 'inactive';

    return true;
  });

  const handleDeleteClick = (id: number) => {
    setPromotionToDelete(id);
    setShowDeleteDialog(true);
  };

  const confirmDelete = async () => {
    if (promotionToDelete) {
      try {
        await deletePromotionMutation.mutateAsync(promotionToDelete);
        setShowDeleteDialog(false);
        setPromotionToDelete(null);
        refetch();
        toast({
          title: 'Berhasil',
          description: 'Promosi berhasil dihapus',
        });
      } catch (error) {
        console.error('Error deleting promotion:', error);
        toast({
          title: 'Error',
          description: 'Gagal menghapus promosi',
          variant: 'destructive',
        });
      }
    }
  };

  const getStatusBadge = (promo: any) => {
    const now = new Date();
    const validFrom = new Date(promo.validFrom);
    const validUntil = new Date(promo.validUntil);

    let status = 'inactive';
    if (promo.isActive) {
      if (now < validFrom) {
        status = 'upcoming';
      } else if (now >= validFrom && now <= validUntil) {
        status = 'active';
      } else {
        status = 'inactive';
      }
    }

    switch (status) {
      case 'active':
        return <Badge className="bg-green-500">Aktif</Badge>;
      case 'inactive':
        return <Badge className="bg-gray-500">Tidak Aktif</Badge>;
      case 'upcoming':
        return <Badge className="bg-blue-500">Akan Datang</Badge>;
      default:
        return <Badge className="bg-gray-500">Tidak Aktif</Badge>;
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'd MMM yyyy', { locale: id });
    } catch (error) {
      return dateString;
    }
  };

  if (isError) {
    return (
      <div className="flex flex-col min-h-screen bg-slate-50">
        <header className="p-4 flex items-center bg-white sticky top-0 z-10 shadow-sm">
          <Link href="/akun" className="mr-3">
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <h1 className="text-xl font-semibold">Kelola Promosi</h1>
        </header>
        <main className="flex-1 p-4 pb-20">
          <div className="text-center py-12">
            <Tag className="h-12 w-12 mx-auto text-gray-300" />
            <h3 className="mt-4 text-lg font-medium">
              Gagal memuat data promosi
            </h3>
            <p className="mt-1 text-gray-500">
              Terjadi kesalahan saat mengambil data promosi
            </p>
            <Button onClick={() => refetch()} className="mt-4">
              Coba Lagi
            </Button>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen bg-slate-50">
      <header className="p-4 flex items-center bg-white sticky top-0 z-10 shadow-sm">
        <Link href="/akun" className="mr-3">
          <ArrowLeft className="h-5 w-5" />
        </Link>
        <h1 className="text-xl font-semibold">Kelola Promosi</h1>
      </header>

      <main className="flex-1 p-4 pb-20">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
          <div className="relative w-full sm:w-auto flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Cari promosi..."
              className="pl-9"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <div className="flex gap-2 w-full sm:w-auto">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="h-10">
                  <Filter className="h-4 w-4 mr-2" />
                  Filter
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => setActiveTab('all')}>
                  Semua
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setActiveTab('active')}>
                  Aktif
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setActiveTab('inactive')}>
                  Tidak Aktif
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setActiveTab('upcoming')}>
                  Akan Datang
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            <Link href="/akun/promotions/add">
              <Button className="h-10">
                <Plus className="h-4 w-4 mr-2" />
                Tambah Promosi
              </Button>
            </Link>
          </div>
        </div>

        <Tabs
          defaultValue="all"
          value={activeTab}
          onValueChange={setActiveTab}
          className="mb-6"
        >
          <TabsList className="grid grid-cols-4 mb-4">
            <TabsTrigger value="all">Semua</TabsTrigger>
            <TabsTrigger value="active">Aktif</TabsTrigger>
            <TabsTrigger value="inactive">Tidak Aktif</TabsTrigger>
            <TabsTrigger value="upcoming">Akan Datang</TabsTrigger>
          </TabsList>
        </Tabs>

        {isLoading ? (
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <Card key={i} className="overflow-hidden">
                <CardContent className="p-4">
                  <div className="animate-pulse">
                    <div className="h-4 bg-gray-200 rounded w-1/3 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-2/3 mb-4"></div>
                    <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
                      {[...Array(4)].map((_, j) => (
                        <div key={j}>
                          <div className="h-3 bg-gray-200 rounded w-1/2 mb-1"></div>
                          <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : filteredPromotions.length > 0 ? (
          <div className="space-y-4">
            {filteredPromotions.map((promo) => (
              <Card key={promo.id} className="overflow-hidden">
                <CardContent className="p-0">
                  <div className="p-4">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="font-semibold text-lg">{promo.name}</h3>
                        <p className="text-sm text-gray-500 mt-1">
                          {promo.description}
                        </p>
                      </div>
                      {getStatusBadge(promo)}
                    </div>

                    <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 mt-4">
                      <div>
                        <p className="text-xs text-gray-500 flex items-center">
                          <Tag className="h-3 w-3 mr-1" /> Kode
                        </p>
                        <p className="font-medium">{promo.code}</p>
                      </div>
                      <div>
                        <p className="text-xs text-gray-500 flex items-center">
                          {promo.discountType === 'PERCENTAGE' ? (
                            <Percent className="h-3 w-3 mr-1" />
                          ) : (
                            <DollarSign className="h-3 w-3 mr-1" />
                          )}
                          Diskon
                        </p>
                        <p className="font-medium">
                          {promo.discountType === 'PERCENTAGE'
                            ? `${promo.discountValue}%`
                            : `Rp ${promo.discountValue.toLocaleString()}`}
                        </p>
                      </div>
                      <div>
                        <p className="text-xs text-gray-500 flex items-center">
                          <Calendar className="h-3 w-3 mr-1" /> Periode
                        </p>
                        <p className="font-medium text-sm">
                          {formatDate(promo.validFrom)} -{' '}
                          {formatDate(promo.validUntil)}
                        </p>
                      </div>
                      <div>
                        <p className="text-xs text-gray-500">Min. Pembelian</p>
                        <p className="font-medium">
                          {promo.minOrderValue > 0
                            ? `Rp ${promo.minOrderValue.toLocaleString()}`
                            : 'Tidak ada'}
                        </p>
                      </div>
                    </div>

                    <div className="flex justify-between items-center mt-4 pt-4 border-t">
                      <div className="text-sm">
                        <span className="text-gray-500">Penggunaan: </span>
                        <span className="font-medium">
                          {promo.usageCount}
                          {promo.usageLimit && promo.usageLimit > 0
                            ? `/${promo.usageLimit}`
                            : ''}
                        </span>
                      </div>
                      <div className="flex gap-2">
                        <Link href={`/akun/promotions/${promo.id}`}>
                          <Button variant="ghost" size="sm">
                            <Eye className="h-4 w-4 mr-1" />
                            Detail
                          </Button>
                        </Link>
                        <Link href={`/akun/promotions/edit/${promo.id}`}>
                          <Button variant="ghost" size="sm">
                            <Edit className="h-4 w-4 mr-1" />
                            Edit
                          </Button>
                        </Link>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteClick(promo.id)}
                          disabled={deletePromotionMutation.isPending}
                        >
                          <Trash2 className="h-4 w-4 mr-1" />
                          Hapus
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <Tag className="h-12 w-12 mx-auto text-gray-300" />
            <h3 className="mt-4 text-lg font-medium">
              Tidak ada promosi ditemukan
            </h3>
            <p className="mt-1 text-gray-500">
              {searchQuery
                ? 'Coba ubah kata kunci pencarian Anda'
                : 'Tambahkan promosi baru untuk meningkatkan penjualan'}
            </p>
            {!searchQuery && (
              <Link href="/akun/promotions/add">
                <Button className="mt-4">
                  <Plus className="h-4 w-4 mr-2" />
                  Tambah Promosi
                </Button>
              </Link>
            )}
          </div>
        )}
      </main>

      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Hapus Promosi</DialogTitle>
            <DialogDescription>
              Apakah Anda yakin ingin menghapus promosi ini? Tindakan ini tidak
              dapat dibatalkan.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowDeleteDialog(false)}
            >
              Batal
            </Button>
            <Button
              variant="destructive"
              onClick={confirmDelete}
              disabled={deletePromotionMutation.isPending}
            >
              {deletePromotionMutation.isPending ? 'Menghapus...' : 'Hapus'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
