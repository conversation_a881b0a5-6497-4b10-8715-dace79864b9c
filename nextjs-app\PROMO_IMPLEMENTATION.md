# Implementasi Promo di Aplikasi Laundry

## Overview

Dokumentasi ini menjelaskan implementasi sistem promo yang telah ditambahkan ke aplikasi laundry, termasuk:

1. Field parfum menjadi required
2. Pengiriman data promo ke backend
3. Logika perhitungan diskon
4. Tampilan informasi promo di halaman detail order

## Perubahan Backend

### 1. Database Schema

Model `Order` sudah memiliki field promo:

```prisma
model Order {
  // ... field lain
  promotionId           Int?               // ID promosi yang digunakan
  promotionCode         String?            // Snapshot kode promosi saat order dibuat
  promotionDiscount     Float?             @default(0) // Nominal diskon yang didapat
  // ... field lain
}
```

### 2. Order Service (`backend/src/services/order.service.ts`)

#### Validasi Promo

```typescript
// Verify promotion exists and is active if provided
let promotionData: {
  id: number;
  code: string;
  discountValue: number;
  discountType: string;
} | null = null;

if (promoId) {
  promotionData = await prisma.promotion.findFirst({
    where: {
      id: promoId,
      outletId: outletId,
      isActive: true,
      validFrom: { lte: new Date() },
      validUntil: { gte: new Date() },
    },
    select: {
      id: true,
      code: true,
      discountValue: true,
      discountType: true,
    },
  });

  if (!promotionData) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      'Promotion not found or inactive'
    );
  }
}
```

#### Perhitungan Diskon

```typescript
// Calculate total price
const originalTotalPrice = items.reduce(
  (sum: number, item: any) => sum + item.subtotal,
  0
);

// Calculate discount amount if promotion is applied
let discountAmount = 0;
if (promotionData) {
  if (promotionData.discountType === 'PERCENTAGE') {
    discountAmount = (originalTotalPrice * promotionData.discountValue) / 100;
  } else {
    discountAmount = promotionData.discountValue;
  }
}

// Final total price after discount
const totalPrice = originalTotalPrice - discountAmount;
```

#### Penyimpanan Data Promo

```typescript
const createdOrder = await tx.order.create({
  data: {
    // ... field lain
    promotionId: promotionData?.id,
    promotionCode: promotionData?.code,
    promotionDiscount: discountAmount,
    // ... field lain
  },
});
```

### 3. Validasi (`backend/src/validations/order.validation.ts`)

```typescript
const createOrder = {
  body: Joi.object().keys({
    // ... field lain
    promoId: Joi.number().integer().positive().allow(null),
    // ... field lain
  }),
};
```

## Perubahan Frontend

### 1. Types (`nextjs-app/types/orders.ts`)

#### Order Interface

```typescript
export interface Order {
  // ... field lain
  promotionId?: number;
  promotionCode?: string;
  promotionDiscount?: number;
  // ... field lain
}
```

#### CreateOrderRequest Interface

```typescript
export interface CreateOrderRequest {
  // ... field lain
  promoId?: number;
  // ... field lain
}
```

#### OrderItem Interface

```typescript
export interface OrderItem {
  // ... field lain
  icon?: string;
  // ... field lain
}
```

### 2. Create Order Page (`nextjs-app/app/orders/create/page.tsx`)

#### Field Parfum Required

```typescript
// Submit Button validation
<Button
  disabled={
    !orderDetails.customerId ||
    orderDetails.items.length === 0 ||
    !orderDetails.perfume ||  // <- Added perfume validation
    createOrderMutation.isPending
  }
>
```

#### Perfume UI Validation

```typescript
<Label htmlFor="perfume" className="flex items-center gap-2">
  <Sparkles className="h-4 w-4 text-pink-500" />
  Parfum <span className="text-red-500">*</span>
</Label>
<SelectTrigger className={!orderDetails.perfume ? "border-red-300" : ""}>
  <SelectValue placeholder="Pilih parfum (wajib)" />
</SelectTrigger>
{!orderDetails.perfume && (
  <p className="text-sm text-red-500">Parfum wajib dipilih</p>
)}
```

#### Pengiriman Data Promo

```typescript
const orderData: CreateOrderRequest = {
  // ... field lain
  promoId: orderDetails.promoId || undefined,
  // ... field lain
};
```

### 3. Order Detail Page (`nextjs-app/app/orders/[id]/page.tsx`)

#### Payment Summary dengan Promo

```typescript
{
  /* Payment Summary */
}
<Card>
  <CardContent className="p-4">
    <div className="flex justify-between text-sm mb-1.5">
      <span>Subtotal</span>
      <span className="font-medium">
        Rp{' '}
        {(order.totalPrice + (order.promotionDiscount || 0)).toLocaleString()}
      </span>
    </div>
    {order.promotionCode &&
      order.promotionDiscount &&
      order.promotionDiscount > 0 && (
        <div className="flex justify-between text-sm mb-1.5 text-green-600">
          <span className="flex items-center gap-1">
            <Sparkles className="h-4 w-4" />
            Promo {order.promotionCode}
          </span>
          <span className="font-medium">
            -Rp {order.promotionDiscount.toLocaleString()}
          </span>
        </div>
      )}
    <div className="flex justify-between text-sm mb-1.5">
      <span>Biaya Antar</span>
      <span className="font-medium">Rp 0</span>
    </div>
    <div className="flex justify-between font-semibold text-base mt-2 pt-2 border-t">
      <span>Total</span>
      <span>Rp {order.totalPrice.toLocaleString()}</span>
    </div>
  </CardContent>
</Card>;
```

## Fitur yang Diimplementasi

### ✅ Completed Features

1. **Field Parfum Required**

   - Validasi UI dengan border merah dan pesan error
   - Validasi submit button disabled jika parfum belum dipilih
   - Tanda asterisk (\*) untuk menunjukkan field wajib

2. **Promo Data Handling**

   - Pengiriman `promoId` ke backend
   - Validasi promo aktif dan berlaku
   - Perhitungan diskon (percentage dan fixed amount)
   - Penyimpanan snapshot data promo di order

3. **Price Calculation**

   - Perhitungan subtotal asli
   - Perhitungan diskon berdasarkan tipe promo
   - Total harga setelah diskon
   - Status pembayaran berdasarkan harga setelah diskon

4. **Order Detail Display**
   - Tampilan subtotal asli
   - Tampilan diskon promo dengan icon dan kode
   - Tampilan total setelah diskon
   - Visual yang konsisten dengan halaman create order

### 🔧 Technical Implementation

#### Logging

```typescript
logger.info(`Generated order number: ${orderNumber}`, {
  originalTotalPrice,
  discountAmount,
  totalPrice,
  perfumeId: perfumeData?.id,
  promoId: promotionData?.id,
  paymentAmount: payment?.amount,
});

logger.debug('Payment status calculation:', {
  newPaidAmount,
  totalPrice,
  originalTotalPrice,
  discountAmount,
  promotionCode: promotionData?.code,
});
```

#### Error Handling

- Validasi promo tidak ditemukan atau tidak aktif
- Validasi parfum wajib dipilih
- Validasi TypeScript untuk optional fields

## Testing

### Build Status

- ✅ Backend build: Success
- ✅ Frontend build: Success
- ✅ TypeScript validation: Passed

### Test Cases

1. **Order dengan Promo**

   - Subtotal: Rp 20.000
   - Promo: 25% discount
   - Diskon: Rp 5.000
   - Total: Rp 15.000
   - Status pembayaran: PAID (jika dibayar Rp 15.000)

2. **Order tanpa Promo**

   - Subtotal: Rp 20.000
   - Total: Rp 20.000
   - Tidak ada tampilan promo

3. **Field Parfum Required**
   - Submit button disabled jika parfum belum dipilih
   - Border merah dan pesan error ditampilkan
   - Validation berhasil setelah parfum dipilih

## Kesimpulan

Implementasi promo telah berhasil dilakukan dengan:

1. **Backend**: Validasi promo, perhitungan diskon, dan penyimpanan data
2. **Frontend**: UI validation, pengiriman data, dan tampilan informasi promo
3. **Integration**: Data promo terintegrasi dari create order hingga detail order

Sistem sekarang dapat menangani:

- Promo percentage dan fixed amount
- Validasi parfum wajib
- Perhitungan harga yang akurat
- Status pembayaran yang tepat
- Tampilan informasi promo yang konsisten
