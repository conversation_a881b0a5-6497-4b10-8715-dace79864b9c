import Joi from 'joi';
import { CustomerStatus, CustomerType } from '@prisma/client';

const createCustomer = {
  body: Joi.object()
    .keys({
      outletId: Joi.number().integer().positive().required(),
      name: Joi.string().required().trim().min(1).max(100),
      phone: Joi.string()
        .optional()
        .pattern(/^08[0-9]{8,13}$/)
        .allow(null),
      email: Joi.string().optional().email().allow(null),
      address: Joi.string().optional().max(500).allow(null),
      mapLink: Joi.string().optional().uri().allow(null),
      latitude: Joi.number().optional().min(-90).max(90).allow(null),
      longitude: Joi.number().optional().min(-180).max(180).allow(null),
      provinceId: Joi.number().optional().integer().positive().allow(null),
      cityId: Joi.number().optional().integer().positive().allow(null),
      status: Joi.string()
        .optional()
        .valid(...Object.values(CustomerStatus)),
      customerType: Joi.string()
        .optional()
        .valid(...Object.values(CustomerType)),
      source: Joi.string().optional().max(100).allow(null),
      labels: Joi.array().optional().items(Joi.string().max(50)),
      photos: Joi.array().optional().items(Joi.string().uri()),
      notes: Joi.string().optional().max(1000).allow(null),
      financialData: Joi.object()
        .optional()
        .keys({
          totalSpent: Joi.number().optional().min(0),
          loyaltyPoints: Joi.number().optional().integer().min(0),
          deposit: Joi.number().optional().min(0),
          debt: Joi.number().optional().min(0),
          cashback: Joi.number().optional().min(0),
          preferredPaymentMethod: Joi.string().optional().max(50).allow(null),
          creditLimit: Joi.number().optional().min(0)
        })
    })
    .custom((value, helpers) => {
      // At least phone or email is required
      if (!value.phone && !value.email) {
        return helpers.error('any.custom', {
          message: 'At least phone or email is required'
        });
      }
      return value;
    }, 'At least phone or email is required')
};

const getCustomers = {
  query: Joi.object().keys({
    search: Joi.string().optional().trim().allow(''),
    status: Joi.string()
      .optional()
      .valid(...Object.values(CustomerStatus)),
    customerType: Joi.string()
      .optional()
      .valid(...Object.values(CustomerType)),
    source: Joi.string().optional(),
    labels: Joi.string().optional(), // comma-separated labels
    provinceId: Joi.number().optional().integer().positive(),
    cityId: Joi.number().optional().integer().positive(),
    sortBy: Joi.string().optional(),
    limit: Joi.number().optional().integer().min(1).max(100),
    page: Joi.number().optional().integer().min(1),
    outletId: Joi.number().integer().positive().required()
  })
};

const getCustomer = {
  params: Joi.object().keys({
    customerId: Joi.number().integer().positive().required()
  }),
  query: Joi.object().keys({
    outletId: Joi.number().integer().positive().required()
  })
};

const updateCustomer = {
  params: Joi.object().keys({
    customerId: Joi.number().integer().positive().required()
  }),
  body: Joi.object()
    .keys({
      outletId: Joi.number().integer().positive().required(),
      name: Joi.string().optional().trim().min(1).max(100),
      phone: Joi.string()
        .optional()
        .pattern(/^08[0-9]{8,13}$/)
        .allow(null),
      email: Joi.string().optional().email().allow(null),
      address: Joi.string().optional().max(500).allow(null),
      mapLink: Joi.string().optional().uri().allow(null),
      latitude: Joi.number().optional().min(-90).max(90).allow(null),
      longitude: Joi.number().optional().min(-180).max(180).allow(null),
      provinceId: Joi.number().optional().integer().positive().allow(null),
      cityId: Joi.number().optional().integer().positive().allow(null),
      status: Joi.string()
        .optional()
        .valid(...Object.values(CustomerStatus)),
      customerType: Joi.string()
        .optional()
        .valid(...Object.values(CustomerType)),
      source: Joi.string().optional().max(100).allow(null),
      labels: Joi.array().optional().items(Joi.string().max(50)),
      photos: Joi.array().optional().items(Joi.string().uri()),
      notes: Joi.string().optional().max(1000).allow(null),
      financialData: Joi.object()
        .optional()
        .keys({
          totalSpent: Joi.number().optional().min(0),
          loyaltyPoints: Joi.number().optional().integer().min(0),
          deposit: Joi.number().optional().min(0),
          debt: Joi.number().optional().min(0),
          cashback: Joi.number().optional().min(0),
          preferredPaymentMethod: Joi.string().optional().max(50).allow(null),
          creditLimit: Joi.number().optional().min(0)
        })
    })
    .min(1)
};

const deleteCustomer = {
  params: Joi.object().keys({
    customerId: Joi.number().integer().positive().required()
  }),
  query: Joi.object().keys({
    outletId: Joi.number().integer().positive().required()
  })
};

const createCustomerNote = {
  params: Joi.object().keys({
    customerId: Joi.number().integer().positive().required()
  }),
  body: Joi.object().keys({
    text: Joi.string().required().trim().min(1).max(1000),
    author: Joi.string().required().trim().min(1).max(100),
    outletId: Joi.number().integer().positive().required()
  })
};

const getCustomerNotes = {
  params: Joi.object().keys({
    customerId: Joi.number().integer().positive().required()
  }),
  query: Joi.object().keys({
    outletId: Joi.number().integer().positive().required()
  })
};

export default {
  createCustomer,
  getCustomers,
  getCustomer,
  updateCustomer,
  deleteCustomer,
  createCustomerNote,
  getCustomerNotes
};
