# 🧪 Cypress E2E Testing - Laundry App

## 📁 Struktur Project

Aplikasi ini menggunakan **Cypress di root level** untuk testing yang lebih comprehensive:

```
laundry-app/
├── backend/                 # Express.js API
├── nextjs-app/             # Next.js Frontend
├── cypress/                # 🎯 E2E Testing (MAIN)
│   ├── e2e/               # Test files
│   │   └── auth/          # Authentication tests
│   ├── fixtures/          # Test data
│   ├── support/           # Custom commands & helpers
│   │   ├── commands.ts    # Custom Cypress commands
│   │   └── e2e.ts        # Global setup
│   └── README.md         # This file
├── cypress.config.ts      # Cypress configuration
└── package.json          # Root package with Cypress scripts
```

## 🚀 Quick Start

### 1. Install Dependencies

```bash
npm install
```

### 2. Start Applications

```bash
# Terminal 1: Start Backend API
npm run dev:backend

# Terminal 2: Start Frontend
npm run dev:nextjs

# Terminal 3: Run Cypress Tests
npm run cypress:open
```

## 📝 Available Commands

### Interactive Testing (Development)

```bash
npm run cypress:open          # Open Cypress GUI
```

### Headless Testing (CI/CD)

```bash
npm run cypress:run           # Run all tests headless
npm run cypress:run:chrome    # Run with Chrome
npm run cypress:run:firefox   # Run with Firefox
npm run test:e2e             # Alias for cypress:run
npm run test:e2e:headed      # Run with browser visible
```

## 🔧 Configuration

### Environment Variables

Edit `cypress.config.ts` untuk menyesuaikan environment:

```typescript
env: {
  apiUrl: 'http://localhost:3000/api',  // Backend API URL
  // Tambahkan env vars lain sesuai kebutuhan
}
```

### Base URL

```typescript
baseUrl: 'http://localhost:3001',  // Frontend URL
```

## 🛠 Custom Commands

Cypress sudah dilengkapi dengan custom commands yang powerful:

### Authentication

```javascript
// Login with session persistence
cy.loginAs('<EMAIL>', 'password123');

// Clear all auth data
cy.clearAuthData();
```

### User Management

```javascript
// Create test user via API
const user = generateTestUser({ email: '<EMAIL>' });
cy.createTestUser(user);

// Verify user in database
cy.verifyUserInDatabase(userId);
```

### Wait Helpers

```javascript
// Wait for page to fully load
cy.waitForPageLoad();

// Wait for element with timeout
cy.waitForElement('[data-testid="submit-button"]');
```

### Form Helpers

```javascript
// Fill multiple form fields at once
cy.fillForm({
  email: '<EMAIL>',
  password: 'password123',
  name: 'Test User',
});
```

### API Helpers

```javascript
// Direct API calls for setup/teardown
apiHelpers.createUser(userData);
apiHelpers.deleteUser(userId);
```

## 📊 Test Data

### Fixtures

Test data disimpan dalam `cypress/fixtures/`:

```javascript
// Load fixture data
cy.fixture('users').then((users) => {
  cy.loginAs(users.validUser.email, users.validUser.password);
});
```

### Dynamic Test Data

```javascript
// Generate unique test data
const testUser = generateTestUser({
  email: '<EMAIL>',
  phone: '************',
});
```

## 🎯 Current Test Coverage

### ✅ Authentication Tests (`cypress/e2e/auth/`)

- **Login Flow**

  - Valid credentials → Dashboard redirect
  - Invalid credentials → Error handling
  - Callback URL redirect
  - Session persistence
  - Auto-redirect when authenticated

- **Form Validation**

  - Empty fields validation
  - Email format validation
  - Password length validation
  - Password visibility toggle

- **Error Scenarios**
  - Unverified email
  - Inactive account
  - Loading states

## 📋 Best Practices

### 1. Use data-testid for Stable Selectors

```html
<button data-testid="login-button">Login</button>
```

```javascript
cy.get('[data-testid="login-button"]').click();
```

### 2. Create Independent Tests

```javascript
describe('Feature Tests', () => {
  beforeEach(() => {
    // Setup fresh state for each test
    cy.clearAuthData();
    cy.visit('/');
  });
});
```

### 3. Use Sessions for Authentication

```javascript
// Efficient login that persists across tests
cy.loginAs(user.email, user.password);
```

### 4. Handle Loading States

```javascript
cy.waitForPageLoad();
cy.get('[data-testid="loading"]').should('not.exist');
cy.get('[data-testid="content"]').should('be.visible');
```

### 5. Mock API Calls When Needed

```javascript
cy.intercept('GET', '/api/orders', { fixture: 'orders.json' });
```

## 🔍 Debugging

### Screenshots & Videos

- Screenshots diambil otomatis saat test gagal
- Videos direkam untuk semua test runs
- Lihat di `cypress/screenshots/` dan `cypress/videos/`

### Debug Commands

```javascript
cy.pause(); // Pause execution
cy.debug(); // Debug current subject
cy.log('Custom message'); // Custom logging
```

### Browser DevTools

```javascript
cy.window().then((win) => {
  console.log(win.localStorage);
  debugger; // Breakpoint in browser
});
```

## 🌍 Environment Setup

### Development

```typescript
baseUrl: 'http://localhost:3000';
env: {
  apiUrl: 'http://localhost:5000/api';
}
```

### Staging

```typescript
baseUrl: 'https://staging-app.example.com';
env: {
  apiUrl: 'https://staging-api.example.com';
}
```

### Production

```typescript
baseUrl: 'https://app.example.com';
env: {
  apiUrl: 'https://api.example.com';
}
```

## 🔄 CI/CD Integration

### GitHub Actions Example

```yaml
name: E2E Tests
on: [push, pull_request]

jobs:
  e2e:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'

      - name: Install dependencies
        run: npm ci

      - name: Start backend
        run: npm run dev:backend &

      - name: Start frontend
        run: npm run dev:nextjs &

      - name: Wait for servers
        run: npx wait-on http://localhost:3000 http://localhost:5000

      - name: Run Cypress tests
        run: npm run cypress:run

      - name: Upload screenshots
        uses: actions/upload-artifact@v3
        if: failure()
        with:
          name: cypress-screenshots
          path: cypress/screenshots
```

## 🛡️ Error Handling

Cypress sudah dikonfigurasi untuk menangani error umum:

```javascript
// Ignore ResizeObserver errors
// Ignore non-critical promise rejections
// Custom error handling dalam commands.ts
```

## 📈 Next Steps

### Rencana Pengembangan Test:

- [ ] Order Management Tests
- [ ] Customer Management Tests
- [ ] Inventory Tests
- [ ] Reports & Analytics Tests
- [ ] Payment Integration Tests
- [ ] WhatsApp Integration Tests
- [ ] Mobile Responsive Tests
- [ ] Performance Tests

### Advanced Features:

- [ ] Visual Regression Testing
- [ ] Database Seeding/Cleanup
- [ ] Email Testing Integration
- [ ] File Upload Testing
- [ ] Multi-tenant Testing

---

## 💡 Tips & Tricks

1. **Gunakan cy.session()** untuk login yang efisien
2. **Buat data test yang unique** dengan timestamp
3. **Test critical user journeys** bukan hanya fitur individual
4. **Gunakan Page Object Model** untuk test yang complex
5. **Parallel testing** dengan `--parallel` flag untuk speed

**Happy Testing! 🎉**
