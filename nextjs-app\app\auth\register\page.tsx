'use client';

import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Eye } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { toast } from 'sonner';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { RegisterFormValues, registerSchema } from '@/lib/form-schema';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { useAuth } from '@/lib/auth-context';
import { useMutation } from '@tanstack/react-query';

export default function RegisterPage() {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirm, setShowConfirm] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const router = useRouter();
  const {
    register,
    isAuthenticated,
    isLoading: authLoading,
    sendVerificationEmail,
  } = useAuth();

  const form = useForm<RegisterFormValues>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      name: '',
      email: '',
      phone: '',
      password: '',
      confirmPassword: '',
      agreed: false,
    },
  });

  // Mutation untuk mengirim email verifikasi
  const sendVerificationMutation = useMutation({
    mutationFn: async (token: string) => {
      const response = await fetch(
        `${
          process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/v1'
        }/auth/send-verification-email?method=otp`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.message ||
            errorData.error ||
            'Gagal mengirim email verifikasi'
        );
      }

      return response.json();
    },
    onSuccess: () => {
      console.log('Verification email sent successfully');
      toast.success('Registrasi berhasil! Email verifikasi telah dikirim.');
    },
    onError: (error: any) => {
      console.error('Error sending verification email:', error);
      toast.success(
        'Registrasi berhasil! Silakan coba kirim ulang email verifikasi.'
      );
    },
  });

  // Mutation untuk registrasi
  const registerMutation = useMutation({
    mutationFn: async (values: RegisterFormValues) => {
      console.log(values);
      const registerData = await register(
        values.name,
        values.email,
        values.phone,
        values.password
      );
      console.log('Register success:', registerData);
      return { registerData, email: values.email };
    },
    onSuccess: async ({ registerData, email }) => {
      if (registerData?.tokens?.access?.token) {
        // Trigger send verification email
        sendVerificationMutation.mutate(registerData.tokens.access.token);
      } else {
        toast.success(
          'Registrasi berhasil! Silakan coba kirim ulang email verifikasi.'
        );
      }

      router.push(`/auth/verify-request?email=${encodeURIComponent(email)}`);
    },
    onError: (error: any) => {
      console.error('Error registering:', error);
      const message = error.message || 'Registrasi gagal';
      setErrorMessage(message);
      toast.error(message);
    },
  });

  // useEffect(() => {
  //   if (isAuthenticated && !authLoading) {
  //     router.push('/');
  //   }
  // }, [isAuthenticated, authLoading, router]);

  if (authLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <p>Loading...</p>
      </div>
    );
  }

  // if (isAuthenticated) {
  //   return (
  //     <div className="flex items-center justify-center min-h-screen">
  //       <p>Redirecting...</p>
  //     </div>
  //   );
  // }

  function onSubmit(values: RegisterFormValues) {
    setErrorMessage('');
    registerMutation.mutate(values);
  }

  return (
    <div className="flex min-h-screen flex-col items-center justify-center">
      <div className="w-full max-w-md px-6 mb-10">
        <div className="text-center mb-6">
          <Image
            src="/logo.png"
            alt="SuperLaundry Logo"
            width={200}
            height={80}
            className="mx-auto mb-4"
          />
          <p className="text-sm mb-4">Manajemen Usaha Laundry</p>
        </div>

        <h1 className="text-2xl font-bold text-center mb-6">Daftar Akun</h1>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nama Lengkap</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Masukkan nama lengkap"
                      {...field}
                      disabled={registerMutation.isPending}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input
                      type="email"
                      placeholder="Masukkan email"
                      {...field}
                      disabled={registerMutation.isPending}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="phone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nomor HP</FormLabel>
                  <FormControl>
                    <Input
                      type="tel"
                      placeholder="Contoh: 08123456789"
                      {...field}
                      disabled={registerMutation.isPending}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Kata Sandi</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Input
                        type={showPassword ? 'text' : 'password'}
                        placeholder="Masukkan kata sandi"
                        className="pr-10"
                        {...field}
                        disabled={registerMutation.isPending}
                      />
                      <button
                        type="button"
                        className="absolute right-3 top-1/2 -translate-y-1/2"
                        onClick={() => setShowPassword((v) => !v)}
                        tabIndex={-1}
                      >
                        <Eye className="h-5 w-5" />
                      </button>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="confirmPassword"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Konfirmasi Kata Sandi</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Input
                        type={showConfirm ? 'text' : 'password'}
                        placeholder="Konfirmasi kata sandi"
                        className="pr-10"
                        {...field}
                        disabled={registerMutation.isPending}
                      />
                      <button
                        type="button"
                        className="absolute right-3 top-1/2 -translate-y-1/2"
                        onClick={() => setShowConfirm((v) => !v)}
                        tabIndex={-1}
                      >
                        <Eye className="h-5 w-5" />
                      </button>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="agreed"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                      disabled={registerMutation.isPending}
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel>
                      Saya setuju dengan{' '}
                      <Link
                        href="/terms"
                        className="text-primary hover:underline"
                      >
                        Syarat & Ketentuan
                      </Link>{' '}
                      dan{' '}
                      <Link
                        href="/privacy"
                        className="text-primary hover:underline"
                      >
                        Kebijakan Privasi
                      </Link>
                    </FormLabel>
                    <FormMessage />
                  </div>
                </FormItem>
              )}
            />

            {errorMessage && (
              <div className="bg-red-50 border border-red-200 rounded-md p-3 mb-4">
                <p className="text-red-600 text-sm">{errorMessage}</p>
              </div>
            )}

            <Button
              type="submit"
              className="w-full"
              disabled={registerMutation.isPending}
            >
              {registerMutation.isPending ? 'MEMPROSES...' : 'DAFTAR'}
            </Button>
          </form>
        </Form>

        <div className="mt-6 text-center">
          <p>
            Sudah punya akun?{' '}
            <Link href="/auth/login" className="text-primary hover:underline">
              Masuk disini
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}
