import axios from 'axios';

const API_BASE_URL =
  process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/v1';

// Helper function to get token from localStorage or cookies
function getAccessToken(): string | null {
  // First try localStorage
  if (typeof window !== 'undefined') {
    const token = localStorage.getItem('accessToken');
    if (token) return token;

    // If not in localStorage, try to get from cookies via auth context
    const tokensStr = localStorage.getItem('tokens');
    if (tokensStr) {
      try {
        const tokens = JSON.parse(tokensStr);
        return tokens?.access?.token || null;
      } catch (error) {
        console.error('Error parsing tokens from localStorage:', error);
      }
    }
  }

  return null;
}

// Helper function to get refresh token
function getRefreshToken(): string | null {
  if (typeof window !== 'undefined') {
    const token = localStorage.getItem('refreshToken');
    if (token) return token;

    const tokensStr = localStorage.getItem('tokens');
    if (tokensStr) {
      try {
        const tokens = JSON.parse(tokensStr);
        return tokens?.refresh?.token || null;
      } catch (error) {
        console.error('Error parsing tokens from localStorage:', error);
      }
    }
  }

  return null;
}

// Create axios instance with base configuration
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = getAccessToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor to handle token refresh
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        const refreshToken = getRefreshToken();
        if (!refreshToken) {
          throw new Error('No refresh token');
        }

        const response = await axios.post(
          `${API_BASE_URL}/auth/refresh-tokens`,
          {
            refreshToken,
          }
        );

        const { access, refresh } = response.data;

        // Update both localStorage formats for compatibility
        localStorage.setItem('accessToken', access.token);
        localStorage.setItem('refreshToken', refresh.token);
        localStorage.setItem('tokens', JSON.stringify(response.data));

        // Update cookies for middleware
        document.cookie = `tokens=${JSON.stringify(
          response.data
        )}; path=/; max-age=${7 * 24 * 60 * 60}`;

        return api(originalRequest);
      } catch (refreshError) {
        console.error('Token refresh failed:', refreshError);
        localStorage.removeItem('accessToken');
        localStorage.removeItem('refreshToken');
        localStorage.removeItem('tokens');
        localStorage.removeItem('user');

        // Clear cookies
        document.cookie =
          'tokens=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
        document.cookie =
          'user=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';

        window.location.href = '/auth/login';
        return Promise.reject(refreshError);
      }
    }

    return Promise.reject(error);
  }
);

// TypeScript interfaces
export interface CustomerFinancialData {
  totalSpent: number;
  loyaltyPoints: number;
  deposit: number;
  debt: number;
  cashback: number;
  preferredPaymentMethod?: string | null;
  creditLimit: number;
}

export interface Province {
  id: number;
  name: string;
  code: string;
}

export interface City {
  id: number;
  name: string;
  code: string;
  provinceId: number;
}

export interface Customer {
  id: number;
  name: string;
  phone?: string | null;
  email?: string | null;
  address?: string | null;
  mapLink?: string | null;
  latitude?: number | null;
  longitude?: number | null;
  provinceId?: number | null;
  cityId?: number | null;
  status: 'ACTIVE' | 'INACTIVE' | 'NEW';
  customerType: 'INDIVIDUAL' | 'CORPORATE';
  source?: string | null;
  labels: string[];
  photos: string[];
  notes?: string | null;
  totalOrders: number;
  lastOrderDate?: string | null;
  joinDate: string;
  createdAt: string;
  updatedAt: string;
  outletId: number;
  financialData?: CustomerFinancialData;
  province?: Province;
  city?: City;
  _count?: {
    orders: number;
  };
}

export interface CustomerNote {
  id: number;
  customerId: number;
  text: string;
  author: string;
  createdAt: string;
}

export interface CreateCustomerRequest {
  name: string;
  phone?: string;
  email?: string;
  address?: string;
  mapLink?: string;
  latitude?: number;
  longitude?: number;
  provinceId?: number;
  cityId?: number;
  status?: 'ACTIVE' | 'INACTIVE' | 'NEW';
  customerType?: 'INDIVIDUAL' | 'CORPORATE';
  source?: string;
  labels?: string[];
  photos?: string[];
  notes?: string;
  financialData?: Partial<CustomerFinancialData>;
}

export interface UpdateCustomerRequest {
  name?: string;
  phone?: string;
  email?: string;
  address?: string;
  mapLink?: string;
  latitude?: number;
  longitude?: number;
  provinceId?: number;
  cityId?: number;
  status?: 'ACTIVE' | 'INACTIVE' | 'NEW';
  customerType?: 'INDIVIDUAL' | 'CORPORATE';
  source?: string;
  labels?: string[];
  photos?: string[];
  notes?: string;
  financialData?: Partial<CustomerFinancialData>;
}

export interface GetCustomersParams {
  search?: string;
  status?: 'ACTIVE' | 'INACTIVE' | 'NEW';
  customerType?: 'INDIVIDUAL' | 'CORPORATE';
  source?: string;
  labels?: string;
  provinceId?: number;
  cityId?: number;
  sortBy?: string;
  limit?: number;
  page?: number;
}

export interface GetCustomersResponse {
  results: Customer[];
  page: number;
  limit: number;
  totalPages: number;
  totalResults: number;
}

export interface CreateCustomerNoteRequest {
  text: string;
  author: string;
}

// API methods
export const customerAPI = {
  // Get all customers with pagination and filtering
  async getCustomers(
    params?: GetCustomersParams & { outletId: number }
  ): Promise<GetCustomersResponse> {
    const response = await api.get('/customers', { params });
    return response.data;
  },

  // Get single customer by ID
  async getCustomer(id: number, outletId: number): Promise<Customer> {
    const response = await api.get(`/customers/${id}`, {
      params: { outletId },
    });
    return response.data;
  },

  // Create new customer
  async createCustomer(
    data: CreateCustomerRequest & { outletId: number }
  ): Promise<Customer> {
    const response = await api.post('/customers', data);
    return response.data.customer;
  },

  // Update customer
  async updateCustomer(
    id: number,
    data: UpdateCustomerRequest & { outletId: number }
  ): Promise<Customer> {
    const response = await api.patch(`/customers/${id}`, data);
    return response.data;
  },

  // Delete customer
  async deleteCustomer(id: number, outletId: number): Promise<void> {
    await api.delete(`/customers/${id}`, {
      params: { outletId },
    });
  },

  // Create customer note
  async createCustomerNote(
    customerId: number,
    data: CreateCustomerNoteRequest & { outletId: number }
  ): Promise<CustomerNote> {
    const response = await api.post(`/customers/${customerId}/notes`, data);
    return response.data;
  },

  // Get customer notes
  async getCustomerNotes(
    customerId: number,
    outletId: number
  ): Promise<CustomerNote[]> {
    const response = await api.get(`/customers/${customerId}/notes`, {
      params: { outletId },
    });
    return response.data;
  },
};
