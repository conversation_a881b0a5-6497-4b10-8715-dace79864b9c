{"name": "prisma-express-typescript-boilerplate", "version": "1.0.0", "description": "REST API Boilerplate with Node JS, TypeScript, Express and Prisma", "main": "src/index.ts", "repository": "https://github.com/antonio-lazaro/prisma-express-typescript-boilerplate.git", "scripts": {"start": "yarn build && pm2 start ecosystem.config.json --no-daemon", "dev": "cross-env NODE_ENV=development nodemon src/index.ts", "test": "docker compose -f docker-compose.only-db-test.yml up -d && DATABASE_URL=postgresql://postgres:secret@localhost:5432/postgres yarn db:push && DATABASE_URL=postgresql://postgres:secret@localhost:5432/postgres jest -i --colors --verbose --detectOpenHandles && docker compose -f docker-compose.only-db-test.yml down", "test:watch": "docker compose -f docker-compose.only-db-test.yml up -d && DATABASE_URL=postgresql://postgres:secret@localhost:5432/postgres yarn db:push && DATABASE_URL=postgresql://postgres:secret@localhost:5432/postgres jest -i --watchAll && docker compose -f docker-compose.only-db-test.yml down", "test:auth": "docker compose -f docker-compose.only-db-test.yml up -d && DATABASE_URL=postgresql://postgres:secret@localhost:5432/postgres yarn db:push && DATABASE_URL=postgresql://postgres:secret@localhost:5432/postgres jest tests/integration/auth.test.ts -i --colors --verbose --detectO<PERSON><PERSON><PERSON><PERSON> && docker compose -f docker-compose.only-db-test.yml down", "test:auth:fast": "DATABASE_URL=postgresql://postgres:secret@localhost:5432/postgres jest tests/integration/auth.test.ts -i --colors --verbose --detectO<PERSON>Handles", "test:employee": "docker compose -f docker-compose.only-db-test.yml up -d && DATABASE_URL=postgresql://postgres:secret@localhost:5432/postgres yarn db:push && DATABASE_URL=postgresql://postgres:secret@localhost:5432/postgres jest tests/integration/employee.test.ts -i --colors --verbose --detect<PERSON><PERSON><PERSON><PERSON><PERSON> && docker compose -f docker-compose.only-db-test.yml down", "test:employee:fast": "DATABASE_URL=postgresql://postgres:secret@localhost:5432/postgres jest tests/integration/employee.test.ts -i --colors --verbose --detect<PERSON><PERSON>Handles", "test:outlet": "docker compose -f docker-compose.only-db-test.yml up -d && DATABASE_URL=postgresql://postgres:secret@localhost:5432/postgres yarn db:push && DATABASE_URL=postgresql://postgres:secret@localhost:5432/postgres jest tests/integration/outlet.test.ts -i --colors --verbose --detect<PERSON><PERSON>H<PERSON>les && docker compose -f docker-compose.only-db-test.yml down", "test:outlet:fast": "DATABASE_URL=postgresql://postgres:secret@localhost:5432/postgres jest tests/integration/outlet.test.ts -i --colors --verbose --detect<PERSON><PERSON>Handles", "test:location": "docker compose -f docker-compose.only-db-test.yml up -d && DATABASE_URL=postgresql://postgres:secret@localhost:5432/postgres yarn db:push && DATABASE_URL=postgresql://postgres:secret@localhost:5432/postgres jest tests/integration/location.test.ts -i --colors --verbose --detect<PERSON><PERSON><PERSON><PERSON>les && docker compose -f docker-compose.only-db-test.yml down", "test:location:fast": "DATABASE_URL=postgresql://postgres:secret@localhost:5432/postgres jest tests/integration/location.test.ts -i --colors --verbose --detect<PERSON><PERSON>Handles", "test:customer": "docker compose -f docker-compose.only-db-test.yml up -d && DATABASE_URL=postgresql://postgres:secret@localhost:5432/postgres yarn db:push && DATABASE_URL=postgresql://postgres:secret@localhost:5432/postgres jest tests/integration/customer.test.ts -i --colors --verbose --detect<PERSON><PERSON>H<PERSON>les && docker compose -f docker-compose.only-db-test.yml down", "test:customer:fast": "DATABASE_URL=postgresql://postgres:secret@localhost:5432/postgres jest tests/integration/customer.test.ts -i --colors --verbose --detectO<PERSON>Handles", "test:service": "docker compose -f docker-compose.only-db-test.yml up -d && DATABASE_URL=postgresql://postgres:secret@localhost:5432/postgres yarn db:push && DATABASE_URL=postgresql://postgres:secret@localhost:5432/postgres jest tests/integration/service.test.ts -i --colors --verbose --detect<PERSON><PERSON>H<PERSON>les && docker compose -f docker-compose.only-db-test.yml down", "test:service:fast": "DATABASE_URL=postgresql://postgres:secret@localhost:5432/postgres jest tests/integration/service.test.ts -i --colors --verbose --detect<PERSON><PERSON>Handles", "test:attendance": "docker compose -f docker-compose.only-db-test.yml up -d && DATABASE_URL=postgresql://postgres:secret@localhost:5432/postgres yarn db:push && DATABASE_URL=postgresql://postgres:secret@localhost:5432/postgres jest tests/integration/attendance.test.ts -i --colors --verbose --detect<PERSON><PERSON><PERSON><PERSON><PERSON> && docker compose -f docker-compose.only-db-test.yml down", "test:attendance:fast": "DATABASE_URL=postgresql://postgres:secret@localhost:5432/postgres jest tests/integration/attendance.test.ts -i --colors --verbose --detect<PERSON><PERSON>Handles", "test:attendance:unit": "docker compose -f docker-compose.only-db-test.yml up -d && DATABASE_URL=postgresql://postgres:secret@localhost:5432/postgres yarn db:push && DATABASE_URL=postgresql://postgres:secret@localhost:5432/postgres jest tests/unit/attendance.service.test.ts -i --colors --verbose --detectO<PERSON><PERSON><PERSON><PERSON> && docker compose -f docker-compose.only-db-test.yml down", "test:attendance:unit:fast": "DATABASE_URL=postgresql://postgres:secret@localhost:5432/postgres jest tests/unit/attendance.service.test.ts -i --colors --verbose --detectO<PERSON>Handles", "test:order": "docker compose -f docker-compose.only-db-test.yml up -d && DATABASE_URL=postgresql://postgres:secret@localhost:5432/postgres yarn db:push && DATABASE_URL=postgresql://postgres:secret@localhost:5432/postgres jest tests/integration/order.test.ts -i --colors --verbose --detect<PERSON><PERSON><PERSON><PERSON><PERSON> && docker compose -f docker-compose.only-db-test.yml down", "test:order:fast": "DATABASE_URL=postgresql://postgres:secret@localhost:5432/postgres jest tests/integration/order.test.ts -i --colors --verbose --detectO<PERSON>Handles", "test:file": "docker compose -f docker-compose.only-db-test.yml up -d && DATABASE_URL=postgresql://postgres:secret@localhost:5432/postgres yarn db:push && DATABASE_URL=postgresql://postgres:secret@localhost:5432/postgres jest -i --colors --verbose --detectOpenHandles --testPathPattern", "test:file:fast": "DATABASE_URL=postgresql://postgres:secret@localhost:5432/postgres jest -i --colors --verbose --detectOpenHandles --testPathPattern", "test:setup": "docker compose -f docker-compose.only-db-test.yml up -d && DATABASE_URL=postgresql://postgres:secret@localhost:5432/postgres yarn db:push", "test:teardown": "docker compose -f docker-compose.only-db-test.yml down", "test:db:reset": "docker compose -f docker-compose.only-db-test.yml down && docker compose -f docker-compose.only-db-test.yml up -d && DATABASE_URL=postgresql://postgres:secret@localhost:5432/postgres yarn db:push", "lint": "eslint .", "lint:fix": "eslint . --fix", "prettier": "prettier --check **/*.ts", "prettier:fix": "prettier --write **/*.ts", "db:push": "prisma db push", "db:studio": "prisma studio", "db:seed": "ts-node prisma/seeders/index.ts", "db:seed:admin": "ts-node prisma/seeders/admin.seeder.ts", "db:seed:location": "ts-node prisma/seeders/location.seeder.ts", "docker:prod": "docker compose -f docker-compose.yml -f docker-compose.prod.yml up", "docker:dev": "docker compose -f docker-compose.yml -f docker-compose.dev.yml up", "docker:test": "docker compose -f docker-compose.yml -f docker-compose.test.yml up", "docker:dev-db:start": "docker compose -f docker-compose.only-db-dev.yml up -d", "docker:dev-db:stop": "docker compose -f docker-compose.only-db-dev.yml down", "prepare": "husky install", "build": "rimraf build && tsc -p tsconfig.json"}, "keywords": ["node", "node.js", "typescript", "boilerplate", "express", "rest", "api", "prisma", "postgresql", "es6", "es7", "es8", "es9", "docker", "passport", "joi", "eslint", "prettier"], "author": "<PERSON>", "license": "ISC", "devDependencies": {"@faker-js/faker": "^7.6.0", "@jest/globals": "^29.3.1", "@types/compression": "^1.7.2", "@types/cors": "^2.8.13", "@types/express": "^4.17.14", "@types/jest": "^29.2.5", "@types/morgan": "^1.9.3", "@types/multer": "^1.4.12", "@types/node": "^18.11.13", "@types/passport": "^1.0.11", "@types/passport-jwt": "^3.0.7", "@types/supertest": "^2.0.12", "@types/swagger-jsdoc": "^6.0.1", "@types/swagger-ui-express": "^4.1.3", "@types/xss-filters": "^0.0.27", "@typescript-eslint/eslint-plugin": "^5.46.1", "@typescript-eslint/parser": "^5.46.1", "cross-env": "^7.0.3", "eslint": "^8.29.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "husky": "^8.0.2", "jest": "^29.3.1", "lint-staged": "^13.1.0", "node-mocks-http": "^1.12.1", "nodemon": "^2.0.20", "prettier": "^2.8.1", "prisma": "^4.10.1", "supertest": "^6.3.3", "swagger-jsdoc": "^6.2.5", "swagger-ui-express": "^4.6.0", "ts-jest": "^29.0.3", "ts-node": "^10.9.1", "typescript": "^4.9.4"}, "dependencies": {"@prisma/client": "^4.7.1", "@sendgrid/mail": "^8.1.5", "@types/bcryptjs": "^2.4.2", "@types/nodemailer": "^6.4.7", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "express-rate-limit": "^6.7.0", "helmet": "^6.0.1", "http-status": "^1.5.3", "joi": "^17.7.0", "moment": "^2.29.4", "morgan": "^1.10.0", "multer": "^2.0.0", "nodemailer": "^6.8.0", "passport": "^0.6.0", "passport-jwt": "^4.0.0", "pm2": "^5.2.2", "winston": "^3.8.2", "xss-filters": "^1.2.7"}}