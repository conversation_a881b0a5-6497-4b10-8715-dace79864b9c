import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from '@/components/ui/use-toast';
import {
  employeeAPI,
  type Employee,
  type CreateEmployeeRequest,
  type UpdateEmployeeRequest,
  type GetEmployeesParams,
} from '@/lib/api/employees';

// Hook untuk mengambil semua employees dengan pagination dan filter
export function useEmployees(params?: GetEmployeesParams) {
  return useQuery({
    queryKey: ['employees', params],
    queryFn: () => employeeAPI.getEmployees(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook untuk mengambil employee berdasarkan ID
export function useEmployee(id: number | null) {
  return useQuery({
    queryKey: ['employees', id],
    queryFn: () => employeeAPI.getEmployee(id!),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook untuk membuat employee baru
export function useCreateEmployee() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateEmployeeRequest) =>
      employeeAPI.createEmployee(data),
    onSuccess: (newEmployee) => {
      // Invalidate dan refetch employees list
      queryClient.invalidateQueries({ queryKey: ['employees'] });

      toast({
        title: 'Berhasil',
        description: `Pegawai ${newEmployee.name} berhasil ditambahkan.`,
      });
    },
    // Tidak langsung show error toast, biarkan component handle error
  });
}

// Hook untuk update employee
export function useUpdateEmployee() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateEmployeeRequest }) =>
      employeeAPI.updateEmployee(id, data),
    onSuccess: (updatedEmployee) => {
      // Invalidate employees list dan detail
      queryClient.invalidateQueries({ queryKey: ['employees'] });
      queryClient.setQueryData(
        ['employees', updatedEmployee.id],
        updatedEmployee
      );

      toast({
        title: 'Berhasil',
        description: `Data pegawai ${updatedEmployee.name} berhasil diperbarui.`,
      });
    },
    // Tidak langsung show error toast, biarkan component handle error
  });
}

// Hook untuk delete employee
export function useDeleteEmployee() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => employeeAPI.deleteEmployee(id),
    onSuccess: () => {
      // Invalidate employees list
      queryClient.invalidateQueries({ queryKey: ['employees'] });

      toast({
        title: 'Berhasil',
        description: 'Pegawai berhasil dihapus.',
      });
    },
    onError: (error: any) => {
      const message =
        error.response?.data?.message || 'Gagal menghapus pegawai';
      toast({
        title: 'Error',
        description: message,
        variant: 'destructive',
      });
    },
  });
}

// Helper function untuk extract error message dari API response
export function getErrorMessage(error: any): string {
  if (error?.response?.data?.message) {
    return error.response.data.message;
  }

  if (error?.message) {
    return error.message;
  }

  return 'Terjadi kesalahan yang tidak diketahui';
}

// Helper function untuk extract field errors dari API response
export function getFieldErrors(error: any): Record<string, string> {
  const fieldErrors: Record<string, string> = {};

  if (error?.response?.data?.message) {
    const message = error.response.data.message;

    // Check for specific validation patterns
    const patterns = [
      // Email validation
      {
        pattern: /email.*already taken/i,
        field: 'email',
        message: 'Email sudah digunakan',
      },
      {
        pattern: /phone.*already taken/i,
        field: 'phone',
        message: 'Nomor telepon sudah digunakan',
      },

      // Required field validation
      {
        pattern: /"name" is required/i,
        field: 'name',
        message: 'Nama wajib diisi',
      },
      {
        pattern: /"email" is required/i,
        field: 'email',
        message: 'Email wajib diisi',
      },
      {
        pattern: /"phone" is required/i,
        field: 'phone',
        message: 'Nomor telepon wajib diisi',
      },
      {
        pattern: /"password" is required/i,
        field: 'password',
        message: 'Password wajib diisi',
      },
      {
        pattern: /"outletId" is required/i,
        field: 'outletId',
        message: 'Outlet wajib dipilih',
      },
      {
        pattern: /"isActive" is required/i,
        field: 'isActive',
        message: 'Status aktif wajib dipilih',
      },

      // Format validation
      {
        pattern: /"email" must be a valid email/i,
        field: 'email',
        message: 'Format email tidak valid',
      },
      {
        pattern: /"phone" with value .* fails to match the required pattern/i,
        field: 'phone',
        message: 'Format nomor telepon tidak valid',
      },
      {
        pattern: /password must be at least 8 characters/i,
        field: 'password',
        message: 'Password minimal 8 karakter',
      },
      {
        pattern: /password must contain at least 1 letter and 1 number/i,
        field: 'password',
        message: 'Password harus mengandung minimal 1 huruf dan 1 angka',
      },
      {
        pattern: /"isActive" must be a boolean/i,
        field: 'isActive',
        message: 'Status aktif harus berupa true/false',
      },

      // Length validation
      {
        pattern: /"name" length must be at least \d+ characters long/i,
        field: 'name',
        message: 'Nama terlalu pendek',
      },
      {
        pattern: /"phone" length must be at least \d+ characters long/i,
        field: 'phone',
        message: 'Nomor telepon terlalu pendek',
      },
      {
        pattern:
          /"phone" length must be less than or equal to \d+ characters long/i,
        field: 'phone',
        message: 'Nomor telepon terlalu panjang',
      },

      // Not found validation
      {
        pattern: /outlet not found/i,
        field: 'outletId',
        message: 'Outlet tidak ditemukan',
      },

      // Not allowed validation (like the isActive error we encountered)
      {
        pattern: /"isActive" is not allowed/i,
        field: 'isActive',
        message: 'Field status aktif tidak diizinkan',
      },
    ];

    // Check each pattern
    for (const { pattern, field, message: msg } of patterns) {
      if (pattern.test(message)) {
        fieldErrors[field] = msg;
      }
    }

    // If no specific field error found but there's a validation error in the message,
    // try to extract field name from Joi error format
    if (Object.keys(fieldErrors).length === 0) {
      const joiFieldMatch = message.match(/"(\w+)"/);
      if (joiFieldMatch) {
        const fieldName = joiFieldMatch[1];
        fieldErrors[fieldName] = message;
      }
    }
  }

  return fieldErrors;
}
