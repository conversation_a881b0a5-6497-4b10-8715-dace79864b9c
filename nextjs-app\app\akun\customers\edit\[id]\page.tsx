'use client';

import type React from 'react';

import { useState, useRef, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import {
  ArrowLeft,
  Save,
  X,
  Tag,
  MapPin,
  Camera,
  Plus,
  Loader2,
} from 'lucide-react';
import Image from 'next/image';
import { toast } from 'sonner';

import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  useCustomer,
  useUpdateCustomer,
  getCustomerFieldErrors,
} from '@/hooks/useCustomers';

interface EditCustomerPageProps {
  params: Promise<{ id: string }>;
}

export default function EditCustomerPage({ params }: EditCustomerPageProps) {
  const router = useRouter();
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [customerId, setCustomerId] = useState<string | null>(null);

  // Resolve params
  useEffect(() => {
    params.then(({ id }) => {
      setCustomerId(id);
    });
  }, [params]);

  const {
    data: customer,
    isLoading,
    error,
  } = useCustomer(customerId ? parseInt(customerId) : null);
  const updateCustomerMutation = useUpdateCustomer();

  const [formData, setFormData] = useState({
    name: '',
    phone: '',
    email: '',
    address: '',
    mapLink: '',
    status: 'ACTIVE' as 'ACTIVE' | 'INACTIVE',
    customerType: 'INDIVIDUAL' as 'INDIVIDUAL' | 'CORPORATE',
  });
  const [labels, setLabels] = useState<string[]>([]);
  const [newLabel, setNewLabel] = useState('');
  const [photos, setPhotos] = useState<string[]>([]);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Populate form when customer data is loaded
  useEffect(() => {
    if (customer) {
      setFormData({
        name: customer.name || '',
        phone: customer.phone || '',
        email: customer.email || '',
        address: customer.address || '',
        mapLink: customer.mapLink || '',
        status:
          customer.status === 'ACTIVE' || customer.status === 'INACTIVE'
            ? customer.status
            : 'ACTIVE',
        customerType: customer.customerType || 'INDIVIDUAL',
      });
      setLabels(customer.labels || []);
    }
  }, [customer]);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));

    // Clear field error when user starts typing
    if (errors[name]) {
      setErrors((prev) => ({ ...prev, [name]: '' }));
    }
  };

  const handleStatusChange = (value: string) => {
    setFormData((prev) => ({
      ...prev,
      status: value as 'ACTIVE' | 'INACTIVE',
    }));
  };

  const handleCustomerTypeChange = (value: string) => {
    setFormData((prev) => ({
      ...prev,
      customerType: value as 'INDIVIDUAL' | 'CORPORATE',
    }));
  };

  const handlePhotoUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const newPhotos = Array.from(e.target.files).map((file) =>
        URL.createObjectURL(file)
      );
      setPhotos((prev) => [...prev, ...newPhotos]);
    }
  };

  const removePhoto = (index: number) => {
    setPhotos(photos.filter((_, i) => i !== index));
  };

  const triggerFileInput = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const addLabel = () => {
    if (newLabel.trim() && !labels.includes(newLabel.trim())) {
      setLabels([...labels, newLabel.trim()]);
      setNewLabel('');
    }
  };

  const removeLabel = (labelToRemove: string) => {
    setLabels(labels.filter((label) => label !== labelToRemove));
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Nama lengkap wajib diisi';
    }

    if (!formData.phone.trim()) {
      newErrors.phone = 'Nomor telepon wajib diisi';
    } else if (!/^(\+62|62|0)8[1-9][0-9]{6,9}$/.test(formData.phone)) {
      newErrors.phone = 'Format nomor telepon tidak valid';
    }

    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Format email tidak valid';
    }

    if (!formData.address.trim()) {
      newErrors.address = 'Alamat wajib diisi';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!customerId || !customer) {
      toast.error('Data pelanggan tidak ditemukan');
      return;
    }

    if (!validateForm()) {
      toast.error('Mohon periksa kembali form Anda');
      return;
    }

    try {
      await updateCustomerMutation.mutateAsync({
        id: parseInt(customerId),
        data: {
          name: formData.name,
          phone: formData.phone,
          email: formData.email || undefined,
          address: formData.address,
          status: formData.status,
          customerType: formData.customerType,
          labels: labels.length > 0 ? labels : undefined,
        },
      });

      toast.success('Pelanggan berhasil diperbarui!');
      router.push(`/akun/customers/${customerId}`);
    } catch (error: any) {
      console.error('Error updating customer:', error);

      // Handle field-specific errors
      const fieldErrors = getCustomerFieldErrors(error);
      if (fieldErrors) {
        setErrors(fieldErrors);
        toast.error('Mohon periksa kembali form Anda');
      } else {
        toast.error(
          error.response?.data?.message || 'Gagal memperbarui pelanggan'
        );
      }
    }
  };

  const predefinedLabels = [
    'VIP',
    'Langganan',
    'Korporat',
    'Reseller',
    'Keluarga',
    'Tetangga',
  ];

  if (!customerId) {
    return (
      <div className="flex flex-col min-h-screen bg-slate-50">
        <div className="flex items-center justify-center flex-1">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex flex-col min-h-screen bg-slate-50">
        <header className="p-4 flex justify-between items-center bg-white sticky top-0 z-10 shadow-sm">
          <div className="flex items-center">
            <Button onClick={() => router.back()} variant="ghost" size="icon">
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <h1 className="text-lg font-semibold ml-4">Edit Pelanggan</h1>
          </div>
        </header>
        <div className="flex items-center justify-center flex-1">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p className="text-gray-600">Memuat data pelanggan...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error || !customer) {
    return (
      <div className="flex flex-col min-h-screen bg-slate-50">
        <header className="p-4 flex justify-between items-center bg-white sticky top-0 z-10 shadow-sm">
          <div className="flex items-center">
            <Button onClick={() => router.back()} variant="ghost" size="icon">
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <h1 className="text-lg font-semibold ml-4">Edit Pelanggan</h1>
          </div>
        </header>
        <div className="flex items-center justify-center flex-1">
          <div className="text-center">
            <p className="text-red-600 mb-4">Pelanggan tidak ditemukan</p>
            <Button onClick={() => router.push('/akun/customers')}>
              Kembali ke Daftar Pelanggan
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen bg-slate-50">
      <header className="p-4 flex justify-between items-center bg-white sticky top-0 z-10 shadow-sm">
        <div className="flex items-center">
          <Button onClick={() => router.back()} variant="ghost" size="icon">
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <h1 className="text-lg font-semibold ml-4">Edit Pelanggan</h1>
        </div>
        <Button
          type="submit"
          form="edit-customer-form"
          disabled={updateCustomerMutation.isPending}
        >
          {updateCustomerMutation.isPending ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Menyimpan
            </>
          ) : (
            <>
              <Save className="h-4 w-4 mr-2" /> Simpan
            </>
          )}
        </Button>
      </header>

      <main className="flex-1 p-4 pb-20">
        <Card className="p-4 mb-4">
          <form
            id="edit-customer-form"
            onSubmit={handleSubmit}
            className="space-y-4"
          >
            <div className="space-y-2">
              <Label htmlFor="name">Nama Lengkap *</Label>
              <Input
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                placeholder="Masukkan nama lengkap"
                className={errors.name ? 'border-red-500' : ''}
                required
              />
              {errors.name && (
                <p className="text-sm text-red-600">{errors.name}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="phone">Nomor Telepon *</Label>
              <Input
                id="phone"
                name="phone"
                value={formData.phone}
                onChange={handleChange}
                placeholder="Contoh: 081234567890"
                className={errors.phone ? 'border-red-500' : ''}
                required
              />
              {errors.phone && (
                <p className="text-sm text-red-600">{errors.phone}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="email">Email (Opsional)</Label>
              <Input
                id="email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleChange}
                placeholder="Contoh: <EMAIL>"
                className={errors.email ? 'border-red-500' : ''}
              />
              {errors.email && (
                <p className="text-sm text-red-600">{errors.email}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="address">Alamat *</Label>
              <Textarea
                id="address"
                name="address"
                value={formData.address}
                onChange={handleChange}
                placeholder="Masukkan alamat lengkap"
                rows={3}
                className={errors.address ? 'border-red-500' : ''}
                required
              />
              {errors.address && (
                <p className="text-sm text-red-600">{errors.address}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label>Tipe Pelanggan</Label>
              <RadioGroup
                value={formData.customerType}
                onValueChange={handleCustomerTypeChange}
                className="flex gap-4"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="INDIVIDUAL" id="individual" />
                  <Label htmlFor="individual">Individu</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="CORPORATE" id="corporate" />
                  <Label htmlFor="corporate">Korporat</Label>
                </div>
              </RadioGroup>
            </div>

            <div className="space-y-2">
              <Label>Status</Label>
              <RadioGroup
                value={formData.status}
                onValueChange={handleStatusChange}
                className="flex gap-4"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="ACTIVE" id="active" />
                  <Label htmlFor="active">Aktif</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="INACTIVE" id="inactive" />
                  <Label htmlFor="inactive">Tidak Aktif</Label>
                </div>
              </RadioGroup>
            </div>

            <div className="space-y-2">
              <Label className="flex items-center gap-1">
                <MapPin className="h-4 w-4" /> Lokasi Maps (Opsional)
              </Label>
              <div className="flex gap-2">
                <Input
                  id="mapLink"
                  name="mapLink"
                  value={formData.mapLink}
                  onChange={handleChange}
                  placeholder="Link Google Maps (https://maps.google.com/...)"
                  className="flex-1"
                />
                <Button
                  type="button"
                  variant="outline"
                  onClick={() =>
                    window.open('https://www.google.com/maps', '_blank')
                  }
                >
                  <MapPin className="h-4 w-4" /> Buka Maps
                </Button>
              </div>
            </div>

            <Separator />

            <div className="space-y-2">
              <Label className="flex items-center gap-1">
                <Tag className="h-4 w-4" /> Label Pelanggan
              </Label>
              <div className="flex flex-wrap gap-2 mt-2">
                {labels.map((label) => (
                  <Badge
                    key={label}
                    className="bg-blue-100 text-blue-800 hover:bg-blue-200 px-2 py-1"
                  >
                    {label}
                    <button
                      type="button"
                      onClick={() => removeLabel(label)}
                      className="ml-1 text-blue-800 hover:text-blue-900"
                    >
                      <X className="h-3 w-3 inline" />
                    </button>
                  </Badge>
                ))}
              </div>
              <div className="flex mt-2">
                <Input
                  value={newLabel}
                  onChange={(e) => setNewLabel(e.target.value)}
                  placeholder="Tambah label baru"
                  className="rounded-r-none h-10"
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      addLabel();
                    }
                  }}
                />
                <Button
                  type="button"
                  onClick={addLabel}
                  variant="default"
                  size="default"
                  className="rounded-l-none h-10"
                >
                  Tambah
                </Button>
              </div>
              <div className="mt-2">
                <p className="text-xs text-gray-500 mb-1">
                  Label yang disarankan:
                </p>
                <div className="flex flex-wrap gap-1">
                  {predefinedLabels.map((label) => (
                    <Badge
                      key={label}
                      className="bg-gray-100 text-gray-800 hover:bg-gray-200 cursor-pointer"
                      onClick={() => {
                        if (!labels.includes(label)) {
                          setLabels([...labels, label]);
                        }
                      }}
                    >
                      {label}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          </form>
        </Card>
      </main>
    </div>
  );
}
