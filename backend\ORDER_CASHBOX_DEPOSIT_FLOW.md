# Alur Order, Cashbox, dan Customer Deposit - Dokumentasi Lengkap

## Overview

Sistem terintegrasi antara Order, Cashbox, dan Customer Deposit yang memungkinkan berbagai metode pembayaran dengan tracking balance yang akurat.

## 🔄 Alur Utama

### 1. Create Order dengan Payment

#### Skenario A: Payment dengan Cashbox (CASH, TRANSFER, dll)

```
1. User create order dengan payment non-DEPOSIT
2. Validasi cashbox exists dan active
3. Create order dengan status PAID/PARTIAL
4. Create payment record
5. Update cashbox balance (+amount)
6. Return order dengan payment info
```

#### Skenario B: Payment dengan Deposit

```
1. User create order dengan payment DEPOSIT
2. Validasi customer deposit balance mencukupi
3. Create order dengan status PAID/PARTIAL
4. Create customer deposit transaction (type: PAYMENT)
5. Update customer balance (-amount)
6. Return order dengan payment info
```

#### Skenario C: Order tanpa Payment

```
1. User create order tanpa payment object
2. Create order dengan status UNPAID
3. Payment bisa ditambahkan nanti via separate endpoint
```

### 2. Customer Deposit Management

#### Deposit In (Setor Saldo)

```
1. Customer setor uang ke outlet
2. Create deposit transaction (type: DEPOSIT)
3. Update customer balance (+amount)
4. Update cashbox balance (+amount) - uang masuk ke kas
```

#### Deposit Out (Tarik Saldo)

```
1. Customer tarik saldo deposit
2. Validasi balance mencukupi
3. Create deposit transaction (type: WITHDRAW)
4. Update customer balance (-amount)
5. Update cashbox balance (-amount) - uang keluar dari kas
```

## 📊 Detail Alur per Skenario

### Skenario 1: Order dengan Payment Cash

```json
POST /v1/outlets/1/orders
{
  "customerId": 1,
  "items": [{"serviceId": 1, "quantity": 2, "price": 5000, "subtotal": 10000}],
  "payment": {
    "amount": 10000,
    "method": "CASH",
    "cashboxId": 1,
    "reference": "CASH-001"
  }
}
```

**Database Changes:**

1. **Order Table**:

   - `totalPrice`: 10000
   - `paidAmount`: 10000
   - `paymentStatus`: "PAID"
   - `paymentMethod`: "CASH"

2. **Payment Table**:

   - `amount`: 10000
   - `method`: "CASH"
   - `cashboxId`: 1

3. **Cashbox Table**:
   - `balance`: +10000

### Skenario 2: Order dengan Payment Deposit

```json
POST /v1/outlets/1/orders
{
  "customerId": 1,
  "items": [{"serviceId": 1, "quantity": 1.5, "price": 5000, "subtotal": 7500}],
  "payment": {
    "amount": 7500,
    "method": "DEPOSIT",
    "reference": "DEPOSIT-PAY-001"
  }
}
```

**Database Changes:**

1. **Order Table**:

   - `totalPrice`: 7500
   - `paidAmount`: 7500
   - `paymentStatus`: "PAID"
   - `paymentMethod`: "DEPOSIT"

2. **CustomerDepositTransaction Table**:

   - `type`: "PAYMENT"
   - `amount`: 7500
   - `orderId`: [order_id]

3. **CustomerFinancial Table**:

   - `deposit`: -7500

4. **Cashbox**: Tidak berubah (deposit tidak affect cashbox)

### Skenario 3: Customer Deposit In

```json
POST /v1/deposit/in
{
  "customerId": 1,
  "amount": 50000,
  "cashboxId": 1,
  "reference": "DEPOSIT-IN-001"
}
```

**Database Changes:**

1. **CustomerDepositTransaction Table**:

   - `type`: "DEPOSIT"
   - `amount`: 50000
   - `cashboxId`: 1

2. **CustomerFinancial Table**:

   - `deposit`: +50000

3. **Cashbox Table**:
   - `balance`: +50000 (uang fisik masuk ke kas)

### Skenario 4: Customer Deposit Out

```json
POST /v1/deposit/out
{
  "customerId": 1,
  "amount": 20000,
  "cashboxId": 1,
  "reference": "DEPOSIT-OUT-001"
}
```

**Database Changes:**

1. **CustomerDepositTransaction Table**:

   - `type`: "WITHDRAW"
   - `amount`: 20000
   - `cashboxId`: 1

2. **CustomerFinancial Table**:

   - `deposit`: -20000

3. **Cashbox Table**:
   - `balance`: -20000 (uang fisik keluar dari kas)

## 🔍 Perbedaan Key antara Payment Methods

### Payment dengan Cashbox (CASH, TRANSFER, dll)

- ✅ Memerlukan `cashboxId`
- ✅ Membuat record di `Payment` table
- ✅ Menambah balance cashbox
- ✅ Uang fisik/digital masuk ke cashbox

### Payment dengan Deposit

- ❌ Tidak memerlukan `cashboxId`
- ❌ Tidak membuat record di `Payment` table
- ✅ Membuat record di `CustomerDepositTransaction` table
- ✅ Mengurangi saldo deposit customer
- ❌ Tidak mengubah balance cashbox (sudah ada saat deposit in)

## 📈 Balance Tracking

### Cashbox Balance

```
Initial: 0
+ Deposit In: +50000 = 50000
+ Payment Cash: +10000 = 60000
- Deposit Out: -20000 = 40000
+ Payment Transfer: +15000 = 55000
```

### Customer Deposit Balance

```
Initial: 0
+ Deposit In: +50000 = 50000
- Payment with Deposit: -7500 = 42500
- Deposit Out: -20000 = 22500
+ Deposit In: +30000 = 52500
```

## 🔄 Transaction Flow Examples

### Complete Customer Journey

#### Step 1: Customer Deposit In

```bash
POST /v1/deposit/in
{
  "customerId": 1,
  "amount": 100000,
  "cashboxId": 1,
  "reference": "TOPUP-001"
}
```

**Result**: Customer balance = 100000, Cashbox balance = +100000

#### Step 2: Create Order dengan Deposit Payment

```bash
POST /v1/outlets/1/orders
{
  "customerId": 1,
  "items": [...],
  "payment": {
    "amount": 25000,
    "method": "DEPOSIT"
  }
}
```

**Result**: Customer balance = 75000, Cashbox balance = unchanged

#### Step 3: Create Order dengan Cash Payment

```bash
POST /v1/outlets/1/orders
{
  "customerId": 1,
  "items": [...],
  "payment": {
    "amount": 15000,
    "method": "CASH",
    "cashboxId": 1
  }
}
```

**Result**: Customer balance = 75000, Cashbox balance = +15000

#### Step 4: Customer Deposit Out

```bash
POST /v1/deposit/out
{
  "customerId": 1,
  "amount": 30000,
  "cashboxId": 1
}
```

**Result**: Customer balance = 45000, Cashbox balance = -30000

## 🛡️ Validasi dan Business Rules

### Order Payment Validation

1. **Amount Validation**: Payment amount ≤ order total
2. **Deposit Validation**: Customer balance ≥ payment amount
3. **Cashbox Validation**: Cashbox exists, active, dan belong to outlet
4. **Method Validation**: Valid payment method enum

### Deposit Transaction Validation

1. **Deposit In**: Amount > 0, cashbox valid
2. **Deposit Out**: Amount > 0, customer balance ≥ amount, cashbox valid
3. **Payment**: Customer balance ≥ amount, order exists

### Cashbox Validation

1. **Exists**: Cashbox must exist
2. **Active**: Cashbox must be active
3. **Ownership**: Cashbox must belong to user's outlet
4. **Balance**: Sufficient balance untuk withdraw operations

## 🔧 Error Handling

### Common Errors

```json
// Insufficient deposit balance
{
  "error": "Saldo deposit tidak mencukupi",
  "code": 400
}

// Invalid cashbox
{
  "error": "Cashbox tidak ditemukan atau tidak aktif",
  "code": 404
}

// Payment exceeds order total
{
  "error": "Payment amount exceeds order total",
  "code": 400
}
```

## 📊 Reporting & Analytics

### Cashbox Reports

- Balance per cashbox
- Transaction history per cashbox
- Cash flow in/out

### Customer Deposit Reports

- Customer balance summary
- Deposit transaction history
- Usage patterns

### Order Payment Reports

- Payment method distribution
- Revenue per payment method
- Outstanding payments

## 🔄 Integration Points

### Frontend Integration

```javascript
// Check customer deposit balance
const balance = await api.get('/v1/deposit/balance', { customerId: 1 });

// Create order with payment
const order = await api.post('/v1/outlets/1/orders', {
  customerId: 1,
  items: [...],
  payment: {
    amount: balance.available >= orderTotal ? orderTotal : 0,
    method: balance.available >= orderTotal ? 'DEPOSIT' : 'CASH',
    cashboxId: balance.available >= orderTotal ? null : 1
  }
});
```

### Receipt/Struk Integration

```json
{
  "orderNumber": "ORD-001",
  "totalPrice": 25000,
  "paidAmount": 25000,
  "paymentStatus": "PAID",
  "paymentMethod": "DEPOSIT",
  "customerBalance": 75000,
  "cashboxUsed": null
}
```

## 🎯 Best Practices

### 1. Transaction Safety

- Selalu gunakan database transactions
- Rollback jika ada error
- Validate sebelum commit

### 2. Balance Consistency

- Real-time balance calculation
- Periodic balance reconciliation
- Audit trail untuk semua perubahan

### 3. User Experience

- Show available deposit balance
- Suggest payment method based on balance
- Clear error messages

### 4. Security

- Validate user permissions
- Audit log untuk sensitive operations
- Rate limiting untuk deposit operations

## 📋 Monitoring

### Key Metrics

- Total cashbox balance per outlet
- Total customer deposits
- Payment method distribution
- Failed transaction rate

### Alerts

- Negative cashbox balance
- Large deposit transactions
- Failed payment attempts
- Balance discrepancies

Sistem ini memberikan fleksibilitas maksimal untuk berbagai skenario pembayaran sambil menjaga integritas data dan kemudahan tracking finansial! 🚀
