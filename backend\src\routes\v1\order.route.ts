import express from 'express';
import auth from '../../middlewares/auth';
import validate from '../../middlewares/validate';
import orderValidation from '../../validations/order.validation';
import orderController from '../../controllers/order.controller';

const router = express.Router();

router
  .route('/')
  .post(auth('manageOrders'), validate(orderValidation.createOrder), orderController.createOrder)
  .get(auth('getOrders'), validate(orderValidation.getOrders), orderController.getOrders);

router
  .route('/:orderId')
  .get(auth('getOrders'), validate(orderValidation.getOrder), orderController.getOrder)
  .patch(auth('manageOrders'), validate(orderValidation.updateOrder), orderController.updateOrder)
  .delete(auth('manageOrders'), validate(orderValidation.deleteOrder), orderController.deleteOrder);

router
  .route('/:orderId/items')
  .patch(
    auth('manageOrders'),
    validate(orderValidation.updateOrderItems),
    orderController.updateOrderItems
  );

router
  .route('/:orderId/status')
  .patch(
    auth('manageOrders'),
    validate(orderValidation.updateOrderStatus),
    orderController.updateOrderStatus
  );

router
  .route('/:orderId/payments')
  .post(auth('manageOrders'), validate(orderValidation.createPayment), orderController.addPayment);

router
  .route('/:orderId/items/:itemId/status')
  .patch(
    auth('manageOrders'),
    validate(orderValidation.updateOrderItemStatus),
    orderController.updateOrderItemStatus
  );

router
  .route('/:orderId/history')
  .get(
    auth('getOrders'),
    validate(orderValidation.getOrder),
    orderController.getOrderStatusHistory
  );

router
  .route('/:orderId/items/:itemId/history')
  .get(
    auth('getOrders'),
    validate(orderValidation.updateOrderItemStatus),
    orderController.getOrderItemStatusHistory
  );

router
  .route('/:orderId/timeline')
  .get(auth('getOrders'), validate(orderValidation.getOrder), orderController.getOrderTimeline);

router.route('/summary/counts').get(auth('getOrders'), orderController.getOrderSummary);

export default router;

/**
 * @swagger
 * tags:
 *   name: Orders
 *   description: Order management and retrieval
 */

/**
 * @swagger
 * /orders:
 *   post:
 *     summary: Create an order
 *     description: Create a new order with items for a customer.
 *     tags: [Orders]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - customerId
 *               - items
 *             properties:
 *               customerId:
 *                 type: integer
 *                 minimum: 1
 *                 description: Customer ID
 *               items:
 *                 type: array
 *                 minItems: 1
 *                 items:
 *                   type: object
 *                   required:
 *                     - serviceId
 *                     - quantity
 *                     - unit
 *                     - price
 *                     - subtotal
 *                   properties:
 *                     serviceId:
 *                       type: integer
 *                       minimum: 1
 *                       description: Service ID
 *                     quantity:
 *                       type: number
 *                       minimum: 0.1
 *                       description: Quantity of service
 *                     unit:
 *                       type: string
 *                       maxLength: 50
 *                       description: Unit for this item (kg, pcs, etc)
 *                     price:
 *                       type: number
 *                       minimum: 0
 *                       description: Unit price
 *                     subtotal:
 *                       type: number
 *                       minimum: 0
 *                       description: Total price for this item
 *                     notes:
 *                       type: string
 *                       maxLength: 500
 *                       description: Notes for this item
 *                     status:
 *                       type: string
 *                       enum: [PENDING, WASHING, DRYING, IRONING, PACKING, COMPLETED, CANCELLED]
 *                       default: PENDING
 *                       description: Item status
 *               notes:
 *                 type: string
 *                 maxLength: 1000
 *                 description: General order notes
 *               pickupDate:
 *                 type: string
 *                 format: date-time
 *                 description: Pickup date and time
 *               deliveryDate:
 *                 type: string
 *                 format: date-time
 *                 description: Delivery date and time
 *               estimatedFinish:
 *                 type: string
 *                 format: date-time
 *                 description: Estimated completion date
 *               perfumeId:
 *                 type: integer
 *                 minimum: 1
 *                 description: Perfume ID (optional)
 *               perfumeName:
 *                 type: string
 *                 maxLength: 255
 *                 description: Perfume name (snapshot)
 *               perfumeDescription:
 *                 type: string
 *                 maxLength: 500
 *                 description: Perfume description (snapshot)
 *               payment:
 *                 type: object
 *                 description: Payment information (optional - if not provided, order will be UNPAID)
 *                 properties:
 *                   amount:
 *                     type: number
 *                     minimum: 0.01
 *                     description: Payment amount
 *                   method:
 *                     type: string
 *                     enum: [CASH, TRANSFER, CREDIT_CARD, DEBIT_CARD, E_WALLET, DEPOSIT]
 *                     description: Payment method
 *                   cashboxId:
 *                     type: integer
 *                     minimum: 1
 *                     description: Cashbox ID (required for non-DEPOSIT methods)
 *                   reference:
 *                     type: string
 *                     maxLength: 100
 *                     description: Payment reference number
 *                   notes:
 *                     type: string
 *                     maxLength: 500
 *                     description: Payment notes
 *             example:
 *               customerId: 1
 *               items:
 *                 - serviceId: 1
 *                   quantity: 2.5
 *                   unit: "kg"
 *                   price: 10000
 *                   subtotal: 25000
 *                   notes: "Setrika rapi"
 *                   status: "PENDING"
 *               notes: "Tolong hati-hati dengan pakaian putih"
 *               pickupDate: "2024-01-15T09:00:00Z"
 *               estimatedFinish: "2024-01-17T17:00:00Z"
 *               perfumeId: 1
 *               payment:
 *                 amount: 25000
 *                 method: "CASH"
 *                 cashboxId: 1
 *                 reference: "CASH-001"
 *                 notes: "Pembayaran tunai"
 *     responses:
 *       "201":
 *         description: Created
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 id:
 *                   type: integer
 *                 orderNumber:
 *                   type: string
 *                 status:
 *                   type: string
 *                   enum: [KONFIRMASI, PICKUP, PENDING, PROCESSING, READY, READY_FOR_PICKUP, COMPLETED, CANCELLED]
 *                 totalPrice:
 *                   type: number
 *                 totalWeight:
 *                   type: number
 *                 paidAmount:
 *                   type: number
 *                 paymentStatus:
 *                   type: string
 *                   enum: [UNPAID, PARTIAL, PAID, REFUNDED]
 *                 paymentMethod:
 *                   type: string
 *                 notes:
 *                   type: string
 *                 pickupDate:
 *                   type: string
 *                   format: date-time
 *                 deliveryDate:
 *                   type: string
 *                   format: date-time
 *                 estimatedFinish:
 *                   type: string
 *                   format: date-time
 *                 actualFinish:
 *                   type: string
 *                   format: date-time
 *                 outletId:
 *                   type: integer
 *                 customerId:
 *                   type: integer
 *                 items:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                       serviceId:
 *                         type: integer
 *                       quantity:
 *                         type: number
 *                       weight:
 *                         type: number
 *                       price:
 *                         type: number
 *                       subtotal:
 *                         type: number
 *                       notes:
 *                         type: string
 *                 createdAt:
 *                   type: string
 *                   format: date-time
 *                 updatedAt:
 *                   type: string
 *                   format: date-time
 *       "400":
 *         $ref: '#/components/responses/BadRequest'
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "403":
 *         $ref: '#/components/responses/Forbidden'
 *
 *   get:
 *     summary: Get all orders
 *     description: Retrieve all orders with optional filtering and pagination.
 *     tags: [Orders]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search in order number, notes, or customer name
 *       - in: query
 *         name: paymentStatus
 *         schema:
 *           type: string
 *           enum: [UNPAID, PARTIAL, PAID, REFUNDED]
 *         description: Filter by payment status
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [KONFIRMASI, PICKUP, PENDING, PROCESSING, READY, READY_FOR_PICKUP, COMPLETED, CANCELLED]
 *         description: Filter by order status
 *       - in: query
 *         name: customerId
 *         schema:
 *           type: integer
 *         description: Filter by customer ID
 *       - in: query
 *         name: dateFrom
 *         schema:
 *           type: string
 *           format: date-time
 *         description: Filter orders from this date
 *       - in: query
 *         name: dateTo
 *         schema:
 *           type: string
 *           format: date-time
 *         description: Filter orders until this date
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *         description: sort by query in the form of field:desc/asc (ex. name:asc)
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *         default: 10
 *         description: Maximum number of orders
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         default: 1
 *         description: Page number
 *     responses:
 *       "200":
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 results:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                       orderNumber:
 *                         type: string
 *                       status:
 *                         type: string
 *                       totalPrice:
 *                         type: number
 *                       paymentStatus:
 *                         type: string
 *                       customer:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                           name:
 *                             type: string
 *                           phone:
 *                             type: string
 *                       items:
 *                         type: array
 *                         items:
 *                           type: object
 *                       createdAt:
 *                         type: string
 *                         format: date-time
 *                       updatedAt:
 *                         type: string
 *                         format: date-time
 *                 page:
 *                   type: integer
 *                   example: 1
 *                 limit:
 *                   type: integer
 *                   example: 10
 *                 totalPages:
 *                   type: integer
 *                   example: 1
 *                 totalResults:
 *                   type: integer
 *                   example: 1
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "403":
 *         $ref: '#/components/responses/Forbidden'
 */

/**
 * @swagger
 * /orders/{id}:
 *   get:
 *     summary: Get an order
 *     description: Retrieve order information by id.
 *     tags: [Orders]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Order id
 *     responses:
 *       "200":
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 id:
 *                   type: integer
 *                 orderNumber:
 *                   type: string
 *                 status:
 *                   type: string
 *                 totalPrice:
 *                   type: number
 *                 totalWeight:
 *                   type: number
 *                 paidAmount:
 *                   type: number
 *                 paymentStatus:
 *                   type: string
 *                 paymentMethod:
 *                   type: string
 *                 notes:
 *                   type: string
 *                 pickupDate:
 *                   type: string
 *                   format: date-time
 *                 deliveryDate:
 *                   type: string
 *                   format: date-time
 *                 estimatedFinish:
 *                   type: string
 *                   format: date-time
 *                 actualFinish:
 *                   type: string
 *                   format: date-time
 *                 customer:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                     name:
 *                       type: string
 *                     phone:
 *                       type: string
 *                     email:
 *                       type: string
 *                     address:
 *                       type: string
 *                 items:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                       serviceId:
 *                         type: integer
 *                       quantity:
 *                         type: number
 *                       weight:
 *                         type: number
 *                       price:
 *                         type: number
 *                       subtotal:
 *                         type: number
 *                       notes:
 *                         type: string
 *                       service:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                           name:
 *                             type: string
 *                           unit:
 *                             type: string
 *                           description:
 *                             type: string
 *                 payments:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                       amount:
 *                         type: number
 *                       method:
 *                         type: string
 *                       reference:
 *                         type: string
 *                       notes:
 *                         type: string
 *                       createdAt:
 *                         type: string
 *                         format: date-time
 *                 createdAt:
 *                   type: string
 *                   format: date-time
 *                 updatedAt:
 *                   type: string
 *                   format: date-time
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "403":
 *         $ref: '#/components/responses/Forbidden'
 *       "404":
 *         $ref: '#/components/responses/NotFound'
 *
 *   patch:
 *     summary: Update an order
 *     description: Update order information.
 *     tags: [Orders]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Order id
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               notes:
 *                 type: string
 *                 maxLength: 1000
 *                 description: Order notes
 *               pickupDate:
 *                 type: string
 *                 format: date-time
 *                 description: Pickup date and time
 *               deliveryDate:
 *                 type: string
 *                 format: date-time
 *                 description: Delivery date and time
 *               estimatedFinish:
 *                 type: string
 *                 format: date-time
 *                 description: Estimated completion date
 *               actualFinish:
 *                 type: string
 *                 format: date-time
 *                 description: Actual completion date
 *               paymentMethod:
 *                 type: string
 *                 enum: [CASH, TRANSFER, CREDIT_CARD, DEBIT_CARD, E_WALLET]
 *                 description: Payment method
 *             example:
 *               notes: "Updated notes"
 *               deliveryDate: "2024-01-18T10:00:00Z"
 *     responses:
 *       "200":
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Order'
 *       "400":
 *         $ref: '#/components/responses/BadRequest'
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "403":
 *         $ref: '#/components/responses/Forbidden'
 *       "404":
 *         $ref: '#/components/responses/NotFound'
 *
 *   delete:
 *     summary: Delete an order
 *     description: Delete an order by id.
 *     tags: [Orders]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Order id
 *     responses:
 *       "204":
 *         description: No content
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "403":
 *         $ref: '#/components/responses/Forbidden'
 *       "404":
 *         $ref: '#/components/responses/NotFound'
 */

/**
 * @swagger
 * /orders/{id}/status:
 *   patch:
 *     summary: Update order status
 *     description: Update the status of an order (PENDING, PROCESSING, WASHING, etc.). Automatically sets actualFinish when status is DELIVERED.
 *     tags: [Orders]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Order id
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - status
 *             properties:
 *               status:
 *                 type: string
 *                 enum: [KONFIRMASI, PICKUP, PENDING, PROCESSING, READY, READY_FOR_PICKUP, COMPLETED, CANCELLED]
 *                 description: New order status
 *               notes:
 *                 type: string
 *                 maxLength: 500
 *                 description: Notes for status change
 *             example:
 *               status: "PROCESSING"
 *               notes: "Mulai diproses oleh tim produksi"
 *     responses:
 *       "200":
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Order'
 *       "400":
 *         $ref: '#/components/responses/BadRequest'
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "403":
 *         $ref: '#/components/responses/Forbidden'
 *       "404":
 *         $ref: '#/components/responses/NotFound'
 */

/**
 * @swagger
 * /orders/{id}/payments:
 *   post:
 *     summary: Add payment to order
 *     description: Add a payment to an existing order. Updates order payment status automatically.
 *     tags: [Orders]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Order id
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - amount
 *               - method
 *             properties:
 *               amount:
 *                 type: number
 *                 minimum: 0.01
 *                 description: Payment amount
 *               method:
 *                 type: string
 *                 enum: [CASH, TRANSFER, CREDIT_CARD, DEBIT_CARD, E_WALLET]
 *                 description: Payment method
 *               reference:
 *                 type: string
 *                 maxLength: 100
 *                 description: Payment reference number
 *               notes:
 *                 type: string
 *                 maxLength: 500
 *                 description: Payment notes
 *             example:
 *               amount: 50000
 *               method: "CASH"
 *               reference: "CASH-001"
 *               notes: "Pembayaran sebagian"
 *     responses:
 *       "201":
 *         description: Created
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 id:
 *                   type: integer
 *                 amount:
 *                   type: number
 *                 method:
 *                   type: string
 *                 reference:
 *                   type: string
 *                 notes:
 *                   type: string
 *                 orderId:
 *                   type: integer
 *                 createdAt:
 *                   type: string
 *                   format: date-time
 *                 updatedAt:
 *                   type: string
 *                   format: date-time
 *       "400":
 *         $ref: '#/components/responses/BadRequest'
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "403":
 *         $ref: '#/components/responses/Forbidden'
 *       "404":
 *         $ref: '#/components/responses/NotFound'
 */

/**
 * @swagger
 * /orders/{orderId}/items/{itemId}/status:
 *   patch:
 *     summary: Update order item status
 *     description: Update the status of a specific order item (PENDING, PROCESSING, WASHING, etc.). Automatically updates order status based on all items status.
 *     tags: [Orders]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: orderId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Order id
 *       - in: path
 *         name: itemId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Order item id
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - status
 *             properties:
 *               status:
 *                 type: string
 *                 enum: [PENDING, WASHING, DRYING, IRONING, PACKING, COMPLETED, CANCELLED]
 *                 description: New order item status
 *               notes:
 *                 type: string
 *                 maxLength: 500
 *                 description: Notes for status change
 *             example:
 *               status: "PROCESSING"
 *               notes: "Item mulai diproses"
 *     responses:
 *       "200":
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 id:
 *                   type: integer
 *                 orderId:
 *                   type: integer
 *                 serviceId:
 *                   type: integer
 *                 quantity:
 *                   type: number
 *                 weight:
 *                   type: number
 *                 price:
 *                   type: number
 *                 subtotal:
 *                   type: number
 *                 notes:
 *                   type: string
 *                 status:
 *                   type: string
 *                   enum: [PENDING, WASHING, DRYING, IRONING, PACKING, COMPLETED, CANCELLED]
 *                 serviceName:
 *                   type: string
 *                   description: Service name (snapshot)
 *                 serviceDescription:
 *                   type: string
 *                   description: Service description (snapshot)
 *                 serviceUnit:
 *                   type: string
 *                   description: Service unit (snapshot)
 *                 serviceEstimationHours:
 *                   type: integer
 *                   description: Service estimation hours (snapshot)
 *                 createdAt:
 *                   type: string
 *                   format: date-time
 *                 updatedAt:
 *                   type: string
 *                   format: date-time
 *       "400":
 *         $ref: '#/components/responses/BadRequest'
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "403":
 *         $ref: '#/components/responses/Forbidden'
 *       "404":
 *         $ref: '#/components/responses/NotFound'
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     Order:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         orderNumber:
 *           type: string
 *         status:
 *           type: string
 *           enum: [KONFIRMASI, PICKUP, PENDING, PROCESSING, READY, READY_FOR_PICKUP, COMPLETED, CANCELLED]
 *         totalPrice:
 *           type: number
 *         totalWeight:
 *           type: number
 *         paidAmount:
 *           type: number
 *         paymentStatus:
 *           type: string
 *           enum: [UNPAID, PARTIAL, PAID, REFUNDED]
 *         paymentMethod:
 *           type: string
 *           enum: [CASH, TRANSFER, CREDIT_CARD, DEBIT_CARD, E_WALLET]
 *         notes:
 *           type: string
 *         pickupDate:
 *           type: string
 *           format: date-time
 *         deliveryDate:
 *           type: string
 *           format: date-time
 *         estimatedFinish:
 *           type: string
 *           format: date-time
 *         actualFinish:
 *           type: string
 *           format: date-time
 *         outletId:
 *           type: integer
 *         customerId:
 *           type: integer
 *         perfumeId:
 *           type: integer
 *         perfumeName:
 *           type: string
 *         perfumeDescription:
 *           type: string
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 *       example:
 *         id: 1
 *         orderNumber: "ORD/240115/001"
 *         status: "PENDING"
 *         totalPrice: 25000
 *         totalWeight: 2.5
 *         paidAmount: 0
 *         paymentStatus: "UNPAID"
 *         paymentMethod: null
 *         notes: "Tolong hati-hati dengan pakaian putih"
 *         pickupDate: "2024-01-15T09:00:00Z"
 *         deliveryDate: null
 *         estimatedFinish: "2024-01-17T17:00:00Z"
 *         actualFinish: null
 *         outletId: 1
 *         customerId: 1
 *         perfumeId: 1
 *         perfumeName: "Lavender Fresh"
 *         perfumeDescription: "Aroma lavender yang menenangkan"
 *         createdAt: "2024-01-15T08:00:00Z"
 *         updatedAt: "2024-01-15T08:00:00Z"
 *
 *     OrderItem:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         orderId:
 *           type: integer
 *         serviceId:
 *           type: integer
 *         quantity:
 *           type: number
 *         unit:
 *           type: string
 *         price:
 *           type: number
 *         subtotal:
 *           type: number
 *         notes:
 *           type: string
 *         status:
 *           type: string
 *           enum: [PENDING, WASHING, DRYING, IRONING, PACKING, COMPLETED, CANCELLED]
 *         serviceName:
 *           type: string
 *           description: Service name (snapshot)
 *         serviceDescription:
 *           type: string
 *           description: Service description (snapshot)
 *         serviceUnit:
 *           type: string
 *           description: Service unit (snapshot)
 *         serviceEstimationHours:
 *           type: integer
 *           description: Service estimation hours (snapshot)
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 *       example:
 *         id: 1
 *         orderId: 1
 *         serviceId: 1
 *         quantity: 2.5
 *         unit: "kg"
 *         price: 10000
 *         subtotal: 25000
 *         notes: "Setrika rapi"
 *         status: "PENDING"
 *         serviceName: "Cuci Setrika"
 *         serviceDescription: "Layanan cuci dan setrika pakaian"
 *         serviceUnit: "kg"
 *         serviceEstimationHours: 24
 *         createdAt: "2024-01-15T08:00:00Z"
 *         updatedAt: "2024-01-15T08:00:00Z"
 *
 *     OrderWithItems:
 *       allOf:
 *         - $ref: '#/components/schemas/Order'
 *         - type: object
 *           properties:
 *             items:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/OrderItem'
 *             customer:
 *               type: object
 *               properties:
 *                 id:
 *                   type: integer
 *                 name:
 *                   type: string
 *                 phone:
 *                   type: string
 *                 email:
 *                   type: string
 *                 address:
 *                   type: string
 *             perfume:
 *               type: object
 *               properties:
 *                 id:
 *                   type: integer
 *                 name:
 *                   type: string
 *                 description:
 *                   type: string
 *                 brand:
 *                   type: string
 *                 scent:
 *                   type: string
 *             payments:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: integer
 *                   amount:
 *                     type: number
 *                   method:
 *                     type: string
 *                     enum: [CASH, TRANSFER, CREDIT_CARD, DEBIT_CARD, E_WALLET]
 *                   reference:
 *                     type: string
 *                   notes:
 *                     type: string
 *                   createdAt:
 *                     type: string
 *                     format: date-time
 */

/**
 * @swagger
 * /orders/{orderId}/items:
 *   patch:
 *     summary: Update order items
 *     description: Update, add, or delete order items. Only works for orders without payments.
 *     tags: [Orders]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: orderId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Order id
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               toUpdate:
 *                 type: array
 *                 description: Items to update
 *                 items:
 *                   type: object
 *                   required:
 *                     - id
 *                   properties:
 *                     id:
 *                       type: integer
 *                       description: Order item ID to update
 *                     quantity:
 *                       type: number
 *                       minimum: 0.1
 *                       description: New quantity
 *                     unit:
 *                       type: string
 *                       maxLength: 50
 *                       description: New unit
 *                     notes:
 *                       type: string
 *                       maxLength: 500
 *                       description: New notes
 *               toDelete:
 *                 type: array
 *                 description: Array of order item IDs to delete
 *                 items:
 *                   type: integer
 *               toAdd:
 *                 type: array
 *                 description: New items to add
 *                 items:
 *                   type: object
 *                   required:
 *                     - serviceId
 *                     - quantity
 *                     - unit
 *                   properties:
 *                     serviceId:
 *                       type: integer
 *                       minimum: 1
 *                       description: Service ID
 *                     quantity:
 *                       type: number
 *                       minimum: 0.1
 *                       description: Quantity
 *                     unit:
 *                       type: string
 *                       maxLength: 50
 *                       description: Unit (kg, pcs, etc)
 *                     notes:
 *                       type: string
 *                       maxLength: 500
 *                       description: Notes
 *             example:
 *               toUpdate:
 *                 - id: 1
 *                   quantity: 3.0
 *                   unit: "kg"
 *                   notes: "Updated quantity"
 *               toDelete: [2, 3]
 *               toAdd:
 *                 - serviceId: 5
 *                   quantity: 1.5
 *                   unit: "kg"
 *                   notes: "Additional service"
 *     responses:
 *       "200":
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/OrderWithItems'
 *       "400":
 *         $ref: '#/components/responses/BadRequest'
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "403":
 *         $ref: '#/components/responses/Forbidden'
 *       "404":
 *         $ref: '#/components/responses/NotFound'
 */

/**
 * @swagger
 * /orders/{orderId}/history:
 *   get:
 *     summary: Get order status history
 *     description: Retrieve the history of status changes for an order.
 *     tags: [Orders]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: orderId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Order id
 *     responses:
 *       "200":
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: integer
 *                   orderId:
 *                     type: integer
 *                   previousStatus:
 *                     type: string
 *                     enum: [KONFIRMASI, PICKUP, PENDING, PROCESSING, READY, READY_FOR_PICKUP, COMPLETED, CANCELLED]
 *                   newStatus:
 *                     type: string
 *                     enum: [KONFIRMASI, PICKUP, PENDING, PROCESSING, READY, READY_FOR_PICKUP, COMPLETED, CANCELLED]
 *                   changedBy:
 *                     type: integer
 *                   notes:
 *                     type: string
 *                   createdAt:
 *                     type: string
 *                     format: date-time
 *                   user:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                       name:
 *                         type: string
 *                       email:
 *                         type: string
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "403":
 *         $ref: '#/components/responses/Forbidden'
 *       "404":
 *         $ref: '#/components/responses/NotFound'
 */

/**
 * @swagger
 * /orders/{orderId}/items/{itemId}/history:
 *   get:
 *     summary: Get order item status history
 *     description: Retrieve the history of status changes for a specific order item.
 *     tags: [Orders]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: orderId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Order id
 *       - in: path
 *         name: itemId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Order item id
 *     responses:
 *       "200":
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: integer
 *                   orderItemId:
 *                     type: integer
 *                   orderId:
 *                     type: integer
 *                   previousStatus:
 *                     type: string
 *                     enum: [PENDING, WASHING, DRYING, IRONING, PACKING, COMPLETED, CANCELLED]
 *                   newStatus:
 *                     type: string
 *                     enum: [PENDING, WASHING, DRYING, IRONING, PACKING, COMPLETED, CANCELLED]
 *                   changedBy:
 *                     type: integer
 *                   notes:
 *                     type: string
 *                   createdAt:
 *                     type: string
 *                     format: date-time
 *                   user:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                       name:
 *                         type: string
 *                       email:
 *                         type: string
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "403":
 *         $ref: '#/components/responses/Forbidden'
 *       "404":
 *         $ref: '#/components/responses/NotFound'
 */

/**
 * @swagger
 * /orders/{orderId}/timeline:
 *   get:
 *     summary: Get complete order timeline
 *     description: Retrieve complete timeline including order status and all order items status changes.
 *     tags: [Orders]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: orderId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Order id
 *     responses:
 *       "200":
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 orderId:
 *                   type: integer
 *                 orderNumber:
 *                   type: string
 *                 timeline:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                       type:
 *                         type: string
 *                         enum: [ORDER_STATUS, ORDER_ITEM_STATUS]
 *                       entityId:
 *                         type: integer
 *                         description: Order ID or Order Item ID
 *                       entityName:
 *                         type: string
 *                         description: Order number or item description
 *                       previousStatus:
 *                         type: string
 *                       newStatus:
 *                         type: string
 *                       changedBy:
 *                         type: integer
 *                       notes:
 *                         type: string
 *                       createdAt:
 *                         type: string
 *                         format: date-time
 *                       user:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                           name:
 *                             type: string
 *                           email:
 *                             type: string
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "403":
 *         $ref: '#/components/responses/Forbidden'
 *       "404":
 *         $ref: '#/components/responses/NotFound'
 */
