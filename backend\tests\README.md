# Testing API Outlet

Dokumentasi ini menjelaskan cara menjalankan testing untuk API outlet dalam aplikasi laundry.

## Struktur Testing

```
tests/
├── fixtures/
│   ├── user.fixture.ts      # Data testing untuk user
│   └── outlet.fixture.ts    # Data testing untuk outlet
├── integration/
│   ├── auth.test.ts         # Testing untuk authentication
│   └── outlet.test.ts       # Testing untuk outlet API
└── utils/
    └── setupTestDb.ts       # Setup database untuk testing
```

## File Testing Outlet

### `tests/fixtures/outlet.fixture.ts`

File ini berisi data dummy untuk testing outlet:

- `outletOne`: Outlet aktif dengan data lengkap
- `outletTwo`: Outlet aktif kedua untuk testing multiple data
- `outletThree`: Outlet tidak aktif untuk testing filter
- `insertOutlets()`: Function helper untuk insert data outlet ke database

### `tests/integration/outlet.test.ts`

File testing utama yang mencakup semua endpoint outlet:

#### Endpoint yang ditest:

1. **POST /v1/outlets** - Membuat outlet baru
2. **GET /v1/outlets** - Mengambil daftar outlet dengan filter dan pagination
3. **GET /v1/outlets/:outletId** - Mengambil detail outlet
4. **PATCH /v1/outlets/:outletId** - Update outlet
5. **DELETE /v1/outlets/:outletId** - Hapus outlet
6. **GET /v1/outlets/:outletId/services** - Mengambil services outlet
7. **POST /v1/outlets/:outletId/copy-services** - Copy services antar outlet
8. **GET /v1/outlets/:outletId/stats** - Mengambil statistik outlet

#### Skenario Testing:

- ✅ Success cases (200, 201, 204)
- ❌ Authentication errors (401)
- ❌ Authorization errors (403)
- ❌ Validation errors (400)
- ❌ Not found errors (404)
- 🔍 Filter dan sorting functionality
- 📄 Pagination testing

## Cara Menjalankan Testing

### Prasyarat

1. Docker harus berjalan
2. Database testing harus tersedia
3. Environment variables sudah diset

### Menjalankan Testing

```bash
# Menjalankan semua testing
npm test

# Menjalankan testing outlet saja
npm test -- outlet.test.ts

# Menjalankan testing dengan coverage
npm run test:coverage

# Menjalankan testing dalam mode watch
npm run test:watch
```

### Dengan Docker

```bash
# Start database testing
docker compose -f docker-compose.only-db-test.yml up -d

# Push schema ke database
yarn db:push

# Jalankan testing
jest tests/integration/outlet.test.ts -i --colors --verbose --detectOpenHandles

# Stop database testing
docker compose -f docker-compose.only-db-test.yml down
```

## Struktur Test Case

Setiap endpoint memiliki test case untuk:

### 1. Success Cases

```javascript
test('should return 201 and successfully create outlet if data is ok', async () => {
  // Test implementasi
});
```

### 2. Authentication

```javascript
test('should return 401 error if access token is missing', async () => {
  // Test tanpa token
});
```

### 3. Authorization

```javascript
test('should return 403 error if user does not have required permission', async () => {
  // Test dengan user yang tidak memiliki permission
});
```

### 4. Validation

```javascript
test('should return 400 error if name is missing', async () => {
  // Test dengan data yang tidak valid
});
```

### 5. Not Found

```javascript
test('should return 404 error if outlet is not found', async () => {
  // Test dengan ID yang tidak ada
});
```

## Data Testing

### User Fixtures

- `userOne`: User biasa tanpa permission khusus
- `admin`: User dengan role ADMIN yang memiliki semua permission
- `insertUsers()`: Helper untuk insert user ke database

### Outlet Fixtures

- Data outlet dengan semua field yang diperlukan
- Menggunakan faker.js untuk generate data random
- Support untuk multiple outlet testing

## Permission Testing

Testing outlet menggunakan sistem permission:

- `manageOutlets`: Required untuk CREATE, UPDATE, DELETE
- `getOutlets`: Required untuk READ operations

## Database Cleanup

Setiap test case menggunakan:

- `beforeEach`: Setup data fresh untuk setiap test
- `afterAll`: Cleanup database setelah semua test selesai
- Isolated testing environment

## Tips Testing

1. **Gunakan data yang realistis**: Faker.js membantu generate data yang mirip real-world
2. **Test edge cases**: Pastikan test mencakup semua kemungkinan error
3. **Isolasi test**: Setiap test harus independen
4. **Descriptive test names**: Nama test harus jelas menggambarkan apa yang ditest
5. **Assertion yang tepat**: Gunakan matcher yang sesuai untuk setiap case

## Troubleshooting

### Error: Docker daemon not running

```bash
# Start Docker Desktop atau Docker daemon
sudo systemctl start docker  # Linux
```

### Error: Database connection

```bash
# Pastikan database testing berjalan
docker compose -f docker-compose.only-db-test.yml ps

# Reset database jika perlu
docker compose -f docker-compose.only-db-test.yml down -v
docker compose -f docker-compose.only-db-test.yml up -d
```

### Error: Permission denied

```bash
# Pastikan user memiliki permission yang tepat
# Check role configuration di src/config/roles.ts
```

## Coverage Target

Target coverage untuk outlet testing:

- **Statements**: > 90%
- **Branches**: > 85%
- **Functions**: > 90%
- **Lines**: > 90%

Jalankan `npm run test:coverage` untuk melihat coverage report.
