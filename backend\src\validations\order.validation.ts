import Joi from 'joi';
import { password } from './custom.validation';

const createOrderItem = {
  serviceId: Joi.number().integer().positive().required(),
  quantity: Joi.number().positive().required(),
  unit: Joi.string().max(50).required(), // Unit untuk item ini (kg, pcs, etc)
  price: Joi.number().positive().required(),
  subtotal: Joi.number().positive().required(),
  notes: Joi.string().max(500).allow('', null),
  status: Joi.string()
    .valid('PENDING', 'WASHING', 'DRYING', 'IRONING', 'PACKING', 'COMPLETED', 'CANCELLED')
    .default('PENDING'),

  // Denormalized service data - akan diisi otomatis dari database
  serviceName: Joi.string().max(255),
  serviceDescription: Joi.string().max(1000).allow('', null),
  serviceUnit: Joi.string().max(50),
  serviceEstimationHours: Joi.number().integer().positive().allow(null)
};

const createOrder = {
  body: Joi.object().keys({
    customerId: Joi.number().integer().positive().required(),
    items: Joi.array().items(Joi.object().keys(createOrderItem)).min(1).required(),
    notes: Joi.string().max(1000).allow('', null),
    pickupDate: Joi.date().iso().allow(null),
    deliveryDate: Joi.date().iso().allow(null),
    estimatedFinish: Joi.date().iso().allow(null),
    perfumeId: Joi.number().integer().positive().allow(null),
    perfumeName: Joi.string().max(255).allow('', null),
    perfumeDescription: Joi.string().max(500).allow('', null),
    promoId: Joi.number().integer().positive().allow(null),
    outletId: Joi.number().integer().positive().required(),
    // Payment opsional saat create order
    payment: Joi.object()
      .keys({
        amount: Joi.number().positive().required(),
        method: Joi.string().valid('CASH', 'TRANSFER', 'DEPOSIT').required(),
        cashboxId: Joi.number().integer().positive().optional(),
        reference: Joi.string().max(100).allow('', null),
        notes: Joi.string().max(500).allow('', null)
      })
      .optional()
  })
};

const getOrders = {
  query: Joi.object().keys({
    search: Joi.string().allow(''),
    sortBy: Joi.string(),
    limit: Joi.number().integer(),
    page: Joi.number().integer(),
    paymentStatus: Joi.string().valid('UNPAID', 'PARTIAL', 'PAID', 'REFUNDED'),
    status: Joi.string().valid(
      'KONFIRMASI',
      'PICKUP',
      'PENDING',
      'PROCESSING',
      'READY',
      'READY_FOR_PICKUP',
      'COMPLETED',
      'CANCELLED'
    ),
    customerId: Joi.number().integer().positive(),
    dateFrom: Joi.date().iso(),
    dateTo: Joi.date().iso(),
    outletId: Joi.number().integer().positive().allow(null),
    filter: Joi.string().valid('masuk', 'hari-ini', 'besok', 'lusa', 'terlambat'),
    processingStatus: Joi.string().valid(
      'PENDING',
      'WASHING',
      'DRYING',
      'IRONING',
      'PACKING',
      'COMPLETED',
      'CANCELLED'
    )
  })
};

const getOrder = {
  params: Joi.object().keys({
    orderId: Joi.number().integer().positive().required()
  })
};

const updateOrder = {
  params: Joi.object().keys({
    orderId: Joi.number().integer().positive().required()
  }),
  body: Joi.object()
    .keys({
      notes: Joi.string().max(1000).allow('', null),
      pickupDate: Joi.date().iso().allow(null),
      deliveryDate: Joi.date().iso().allow(null),
      estimatedFinish: Joi.date().iso().allow(null),
      actualFinish: Joi.date().iso().allow(null)
    })
    .min(1)
};

const updateOrderStatus = {
  params: Joi.object().keys({
    orderId: Joi.number().integer().positive().required()
  }),
  body: Joi.object().keys({
    status: Joi.string()
      .valid(
        'KONFIRMASI',
        'PICKUP',
        'PENDING',
        'PROCESSING',
        'READY',
        'READY_FOR_PICKUP',
        'COMPLETED',
        'CANCELLED'
      )
      .required(),
    notes: Joi.string().max(500).allow('', null)
  })
};

const deleteOrder = {
  params: Joi.object().keys({
    orderId: Joi.number().integer().positive().required()
  })
};

const createPayment = {
  params: Joi.object().keys({
    orderId: Joi.number().integer().positive().required()
  }),
  body: Joi.object().keys({
    amount: Joi.number().positive().required(),
    method: Joi.string().valid('CASH', 'TRANSFER', 'DEPOSIT').required(),
    cashboxId: Joi.number().integer().positive().optional(),
    reference: Joi.string().max(100).allow('', null),
    notes: Joi.string().max(500).allow('', null)
  })
};

const updateOrderItems = {
  params: Joi.object().keys({
    orderId: Joi.number().integer().positive().required()
  }),
  body: Joi.object()
    .keys({
      toUpdate: Joi.array().items(
        Joi.object().keys({
          id: Joi.number().integer().positive().required(),
          quantity: Joi.number().positive(),
          unit: Joi.string().max(50),
          notes: Joi.string().max(500).allow('', null)
        })
      ),
      toDelete: Joi.array().items(Joi.number().integer().positive()),
      toAdd: Joi.array().items(
        Joi.object().keys({
          serviceId: Joi.number().integer().positive().required(),
          quantity: Joi.number().positive().required(),
          unit: Joi.string().max(50).required(),
          notes: Joi.string().max(500).allow('', null)
        })
      )
    })
    .min(1) // At least one operation must be provided
};

const updateOrderItemStatus = {
  params: Joi.object().keys({
    orderId: Joi.number().integer().positive().required(),
    itemId: Joi.number().integer().positive().required()
  }),
  body: Joi.object().keys({
    status: Joi.string()
      .valid('PENDING', 'WASHING', 'DRYING', 'IRONING', 'PACKING', 'COMPLETED', 'CANCELLED')
      .required(),
    notes: Joi.string().max(500).allow('', null)
  })
};

export default {
  createOrder,
  getOrders,
  getOrder,
  updateOrder,
  updateOrderItems,
  updateOrderStatus,
  deleteOrder,
  createPayment,
  updateOrderItemStatus
};
