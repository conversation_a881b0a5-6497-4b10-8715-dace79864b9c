import httpStatus from 'http-status';
import pick from '../utils/pick';
import ApiError from '../utils/ApiError';
import catchAsync from '../utils/catchAsync';
import { employeeService } from '../services';
import { User } from '@prisma/client';

const createEmployee = catchAsync(async (req, res) => {
  const { email, password, name, phone, outletId } = req.body;
  const currentUser = req.user as User;
  const employee = await employeeService.createEmployee(
    email,
    password,
    name,
    phone,
    outletId,
    currentUser
  );
  res.status(httpStatus.CREATED).send(employee);
});

const getEmployees = catchAsync(async (req, res) => {
  const filter = pick(req.query, ['name', 'outletId']);
  const options = pick(req.query, ['sortBy', 'limit', 'page']);
  const currentUser = req.user as User;
  const result = await employeeService.queryEmployees(filter, options, currentUser);
  res.send(result);
});

const getEmployee = catchAsync(async (req, res) => {
  const currentUser = req.user as User;
  const employee = await employeeService.getEmployeeById(
    parseInt(req.params.employeeId),
    currentUser
  );
  if (!employee) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Employee not found');
  }
  res.send(employee);
});

const updateEmployee = catchAsync(async (req, res) => {
  const currentUser = req.user as User;
  const employee = await employeeService.updateEmployeeById(
    parseInt(req.params.employeeId),
    req.body,
    currentUser
  );
  res.send(employee);
});

const deleteEmployee = catchAsync(async (req, res) => {
  const currentUser = req.user as User;
  await employeeService.deleteEmployeeById(parseInt(req.params.employeeId), currentUser);
  res.status(httpStatus.OK).send({ message: 'Employee deleted successfully' });
});

export default {
  createEmployee,
  getEmployees,
  getEmployee,
  updateEmployee,
  deleteEmployee
};
