'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Building2, MapPin, Phone, Loader2, ArrowRight } from 'lucide-react';
import Image from 'next/image';

import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { useAuth } from '@/lib/auth-context';
import { useOutlets } from '@/hooks/useOutlets';
import { toast } from 'sonner';

export default function SelectOutletPage() {
  const router = useRouter();
  const {
    user,
    activeOutlet,
    setActiveOutlet,
    isAuthenticated,
    isLoading: authLoading,
  } = useAuth();
  const { data: outletsData, isLoading: outletsLoading, error } = useOutlets();
  const [selectedOutletId, setSelectedOutletId] = useState<number | null>(null);
  const [isSelectingOutlet, setIsSelectingOutlet] = useState(false);

  // Redirect if not authenticated
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/auth/login');
    }
  }, [isAuthenticated, authLoading, router]);

  // // If user already has active outlet, redirect to dashboard
  // useEffect(() => {
  //   if (activeOutlet && isAuthenticated) {
  //     console.log(
  //       '✅ User already has active outlet, redirecting to dashboard:',
  //       activeOutlet
  //     );
  //     router.push('/dashboard');
  //   }
  // }, [activeOutlet, isAuthenticated, router]);

  // Auto-select outlet if only one exists
  useEffect(() => {
    if (outletsData?.results && outletsData.results.length === 1) {
      setSelectedOutletId(outletsData.results[0].id);
    }
  }, [outletsData]);

  const handleSelectOutlet = async (outletId: number) => {
    if (!outletsData?.results) return;

    console.log('🔄 Selecting outlet with ID:', outletId);
    setIsSelectingOutlet(true);

    try {
      const selectedOutlet = outletsData.results.find((o) => o.id === outletId);
      console.log('🏢 Found outlet:', selectedOutlet);

      if (selectedOutlet) {
        setActiveOutlet(selectedOutlet);
        toast.success(`Outlet "${selectedOutlet.name}" berhasil dipilih!`);
        console.log('🔄 Navigating to dashboard...');
        router.push('/dashboard');
      } else {
        throw new Error('Outlet not found');
      }
    } catch (error) {
      console.error('❌ Error selecting outlet:', error);
      toast.error('Gagal memilih outlet');
    } finally {
      setIsSelectingOutlet(false);
    }
  };

  const handleCardClick = (outletId: number) => {
    setSelectedOutletId(outletId);
  };

  if (authLoading || !isAuthenticated) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>Memuat...</p>
        </div>
      </div>
    );
  }

  if (outletsLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>Memuat outlet...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <p className="text-red-600 mb-4">Gagal memuat outlet</p>
          <Button onClick={() => window.location.reload()}>Coba Lagi</Button>
        </div>
      </div>
    );
  }

  if (!outletsData?.results || outletsData.results.length === 0) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center max-w-md">
          <Building2 className="h-16 w-16 mx-auto mb-4 text-gray-400" />
          <h2 className="text-xl font-semibold mb-2">Belum ada outlet</h2>
          <p className="text-gray-600 mb-6">
            Anda belum memiliki outlet. Silakan hubungi administrator untuk
            membuat outlet.
          </p>
          <Button onClick={() => router.push('/auth/login')}>
            Kembali ke Login
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-slate-50">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="max-w-4xl mx-auto px-4 py-6">
          <div className="text-center">
            <Image
              src="/images/logo.png"
              alt="Super Laundry Logo"
              width={60}
              height={60}
              className="mx-auto mb-4"
            />
            <h1 className="text-2xl font-bold text-gray-900">Pilih Outlet</h1>
            <p className="text-gray-600 mt-2">
              Selamat datang, {user?.name}! Pilih outlet untuk melanjutkan.
            </p>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {outletsData.results.map((outlet) => (
            <Card
              key={outlet.id}
              className={`p-6 cursor-pointer transition-all duration-200 hover:shadow-lg ${
                selectedOutletId === outlet.id
                  ? 'ring-2 ring-blue-500 bg-blue-50'
                  : 'hover:bg-gray-50'
              }`}
              onClick={() => handleCardClick(outlet.id)}
            >
              <div className="space-y-4">
                {/* Header */}
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <Building2 className="h-6 w-6 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900 text-lg">
                        {outlet.name}
                      </h3>
                      <span
                        className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                          outlet.isActive
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}
                      >
                        {outlet.isActive ? 'Aktif' : 'Tidak Aktif'}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Location */}
                <div className="space-y-2">
                  <div className="flex items-start space-x-2">
                    <MapPin className="h-4 w-4 text-gray-400 mt-0.5 flex-shrink-0" />
                    <div className="text-sm text-gray-600">
                      <p>{outlet.address}</p>
                      <p className="text-xs text-gray-500">
                        {outlet.city}, {outlet.province}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Phone className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-600">
                      {outlet.phone}
                    </span>
                  </div>
                </div>

                {/* Selected indicator */}
                {selectedOutletId === outlet.id && (
                  <div className="flex items-center justify-center pt-2">
                    <div className="flex items-center space-x-2 text-blue-600">
                      <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                      <span className="text-sm font-medium">Dipilih</span>
                    </div>
                  </div>
                )}
              </div>
            </Card>
          ))}
        </div>

        {/* Continue Button */}
        {selectedOutletId && (
          <div className="flex justify-center mt-8">
            <Button
              onClick={() => handleSelectOutlet(selectedOutletId)}
              disabled={isSelectingOutlet}
              size="lg"
              className="px-8"
            >
              {isSelectingOutlet ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Memproses...
                </>
              ) : (
                <>
                  Lanjutkan
                  <ArrowRight className="ml-2 h-4 w-4" />
                </>
              )}
            </Button>
          </div>
        )}

        {/* Info */}
        <div className="text-center mt-8">
          <p className="text-sm text-gray-500">
            Outlet yang tidak aktif tidak dapat dipilih. Hubungi administrator
            jika ada masalah.
          </p>
        </div>
      </div>
    </div>
  );
}
