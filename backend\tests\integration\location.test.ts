import request from 'supertest';
import { faker } from '@faker-js/faker';
import httpStatus from 'http-status';
import moment from 'moment';
import config from '../../src/config/config';
import { TokenType, Role } from '@prisma/client';
import tokenService from '../../src/services/token.service';
import app from '../../src/app';
import setupTestDB, { setupTestDBOptimized } from '../utils/setupTestDb';
import { describe, beforeEach, beforeAll, test, expect, jest } from '@jest/globals';
import { userOne, admin, insertUsers, createOutletForOwner } from '../fixtures/user.fixture';
import prisma from '../../src/client';
import { User } from '@prisma/client';
import {
  provinceOne,
  provinceTwo,
  provinceThree,
  allProvinces,
  cityOne,
  cityTwo,
  cityThree,
  cityFour,
  cityFive,
  allCities,
  insertProvinces,
  insertCities
} from '../fixtures/location.fixture';

// Set timeout for all tests in this file
jest.setTimeout(15000);

setupTestDBOptimized();

describe('Location routes', () => {
  let userOneAccessToken: string;
  let adminAccessToken: string;
  let dbUserOne: User;
  let dbAdmin: User;

  beforeEach(async () => {
    // Setup data for each test
    await insertUsers([userOne, admin]);
    await insertProvinces(allProvinces);
    await insertCities(allCities);

    dbUserOne = (await prisma.user.findUnique({ where: { email: userOne.email } })) as User;
    dbAdmin = (await prisma.user.findUnique({ where: { email: admin.email } })) as User;

    // Create outlets for users to have outletId
    const userOneOutlet = await createOutletForOwner(dbUserOne.id);
    const adminOutlet = await createOutletForOwner(dbAdmin.id);

    // Update users with outletId
    await prisma.user.update({
      where: { id: dbUserOne.id },
      data: { outletId: userOneOutlet.id }
    });

    await prisma.user.update({
      where: { id: dbAdmin.id },
      data: { outletId: adminOutlet.id }
    });

    // Get updated user data
    dbUserOne = (await prisma.user.findUnique({ where: { id: dbUserOne.id } })) as User;
    dbAdmin = (await prisma.user.findUnique({ where: { id: dbAdmin.id } })) as User;

    userOneAccessToken = tokenService.generateToken(
      dbUserOne.id,
      moment().add(config.jwt.accessExpirationMinutes, 'minutes'),
      TokenType.ACCESS
    );

    adminAccessToken = tokenService.generateToken(
      dbAdmin.id,
      moment().add(config.jwt.accessExpirationMinutes, 'minutes'),
      TokenType.ACCESS
    );
  }, 15000);

  describe('GET /v1/locations/provinces', () => {
    test('should return 200 and all provinces if request is ok', async () => {
      const res = await request(app)
        .get('/v1/locations/provinces')
        .set('Authorization', `Bearer ${userOneAccessToken}`)
        .expect(httpStatus.OK);

      expect(res.body).toHaveLength(3);
      expect(res.body).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            id: provinceOne.id,
            name: provinceOne.name,
            code: provinceOne.code
          }),
          expect.objectContaining({
            id: provinceTwo.id,
            name: provinceTwo.name,
            code: provinceTwo.code
          })
        ])
      );
    });

    test('should return 401 error if access token is missing', async () => {
      await request(app).get('/v1/locations/provinces').expect(httpStatus.UNAUTHORIZED);
    });

    test('should return provinces sorted by name in ascending order', async () => {
      const res = await request(app)
        .get('/v1/locations/provinces')
        .set('Authorization', `Bearer ${userOneAccessToken}`)
        .expect(httpStatus.OK);

      expect(res.body).toHaveLength(3);
      // Check if sorted by name
      for (let i = 0; i < res.body.length - 1; i++) {
        expect(res.body[i].name <= res.body[i + 1].name).toBe(true);
      }
    });

    test('should return provinces with correct structure', async () => {
      const res = await request(app)
        .get('/v1/locations/provinces')
        .set('Authorization', `Bearer ${userOneAccessToken}`)
        .expect(httpStatus.OK);

      expect(res.body[0]).toEqual(
        expect.objectContaining({
          id: expect.any(Number),
          name: expect.any(String),
          code: expect.any(String)
        })
      );
      expect(res.body[0]).not.toHaveProperty('createdAt');
      expect(res.body[0]).not.toHaveProperty('updatedAt');
    });
  });

  describe('GET /v1/locations/cities', () => {
    test('should return 200 and all cities if no provinceId filter', async () => {
      const res = await request(app)
        .get('/v1/locations/cities')
        .set('Authorization', `Bearer ${userOneAccessToken}`)
        .expect(httpStatus.OK);

      expect(res.body).toHaveLength(5);
      expect(res.body).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            id: cityOne.id,
            name: cityOne.name,
            code: cityOne.code,
            provinceId: cityOne.provinceId
          })
        ])
      );
    });

    test('should return 401 error if access token is missing', async () => {
      await request(app).get('/v1/locations/cities').expect(httpStatus.UNAUTHORIZED);
    });

    test('should filter cities by provinceId if provided', async () => {
      const res = await request(app)
        .get('/v1/locations/cities')
        .query({ provinceId: 1 })
        .set('Authorization', `Bearer ${userOneAccessToken}`)
        .expect(httpStatus.OK);

      expect(res.body).toHaveLength(2);
      expect(res.body).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            id: cityOne.id,
            name: cityOne.name,
            provinceId: 1
          }),
          expect.objectContaining({
            id: cityTwo.id,
            name: cityTwo.name,
            provinceId: 1
          })
        ])
      );
    });

    test('should return empty array if provinceId has no cities', async () => {
      const res = await request(app)
        .get('/v1/locations/cities')
        .query({ provinceId: 999 })
        .set('Authorization', `Bearer ${userOneAccessToken}`)
        .expect(httpStatus.OK);

      expect(res.body).toHaveLength(0);
      expect(res.body).toEqual([]);
    });

    test('should return 400 error if provinceId is not a valid integer', async () => {
      await request(app)
        .get('/v1/locations/cities')
        .query({ provinceId: 'invalid' })
        .set('Authorization', `Bearer ${userOneAccessToken}`)
        .expect(httpStatus.BAD_REQUEST);
    });

    test('should return cities sorted by name in ascending order', async () => {
      const res = await request(app)
        .get('/v1/locations/cities')
        .query({ provinceId: 2 })
        .set('Authorization', `Bearer ${userOneAccessToken}`)
        .expect(httpStatus.OK);

      expect(res.body).toHaveLength(2);
      // Check if sorted by name
      for (let i = 0; i < res.body.length - 1; i++) {
        expect(res.body[i].name <= res.body[i + 1].name).toBe(true);
      }
    });

    test('should return cities with correct structure', async () => {
      const res = await request(app)
        .get('/v1/locations/cities')
        .set('Authorization', `Bearer ${userOneAccessToken}`)
        .expect(httpStatus.OK);

      expect(res.body[0]).toEqual(
        expect.objectContaining({
          id: expect.any(Number),
          name: expect.any(String),
          code: expect.any(String),
          provinceId: expect.any(Number)
        })
      );
      expect(res.body[0]).not.toHaveProperty('createdAt');
      expect(res.body[0]).not.toHaveProperty('updatedAt');
    });

    test('should include province information if includeProvince query is true', async () => {
      const res = await request(app)
        .get('/v1/locations/cities')
        .query({ includeProvince: 'true' })
        .set('Authorization', `Bearer ${userOneAccessToken}`)
        .expect(httpStatus.OK);

      expect(res.body[0]).toMatchObject({
        id: expect.any(Number),
        name: expect.any(String),
        code: expect.any(String),
        provinceId: expect.any(Number),
        province: expect.objectContaining({
          id: expect.any(Number),
          name: expect.any(String),
          code: expect.any(String)
        })
      });
    });
  });

  describe('GET /v1/locations/provinces/:provinceId/cities', () => {
    test('should return 200 and cities for specific province', async () => {
      const res = await request(app)
        .get('/v1/locations/provinces/1/cities')
        .set('Authorization', `Bearer ${userOneAccessToken}`)
        .expect(httpStatus.OK);

      expect(res.body).toHaveLength(2);
      expect(res.body).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            id: cityOne.id,
            name: cityOne.name,
            provinceId: 1
          }),
          expect.objectContaining({
            id: cityTwo.id,
            name: cityTwo.name,
            provinceId: 1
          })
        ])
      );
    });

    test('should return 401 error if access token is missing', async () => {
      await request(app).get('/v1/locations/provinces/1/cities').expect(httpStatus.UNAUTHORIZED);
    });

    test('should return 400 error if provinceId is not a valid integer', async () => {
      await request(app)
        .get('/v1/locations/provinces/invalid/cities')
        .set('Authorization', `Bearer ${userOneAccessToken}`)
        .expect(httpStatus.BAD_REQUEST);
    });

    test('should return empty array if province has no cities', async () => {
      const res = await request(app)
        .get('/v1/locations/provinces/999/cities')
        .set('Authorization', `Bearer ${userOneAccessToken}`)
        .expect(httpStatus.OK);

      expect(res.body).toHaveLength(0);
      expect(res.body).toEqual([]);
    });

    test('should return cities sorted by name', async () => {
      const res = await request(app)
        .get('/v1/locations/provinces/2/cities')
        .set('Authorization', `Bearer ${userOneAccessToken}`)
        .expect(httpStatus.OK);

      expect(res.body).toHaveLength(2);
      // Check if sorted by name (Bandung should come before Bekasi)
      expect(res.body[0].name).toBe('Bandung');
      expect(res.body[1].name).toBe('Bekasi');
    });
  });

  describe('GET /v1/locations/search', () => {
    test('should return 200 and search results for provinces and cities', async () => {
      const res = await request(app)
        .get('/v1/locations/search')
        .query({ q: 'Jakarta' })
        .set('Authorization', `Bearer ${userOneAccessToken}`)
        .expect(httpStatus.OK);

      expect(res.body).toEqual(
        expect.objectContaining({
          provinces: expect.arrayContaining([
            expect.objectContaining({
              id: provinceOne.id,
              name: 'DKI Jakarta'
            })
          ]),
          cities: expect.arrayContaining([
            expect.objectContaining({
              name: 'Jakarta Pusat'
            }),
            expect.objectContaining({
              name: 'Jakarta Selatan'
            })
          ])
        })
      );
    });

    test('should return 401 error if access token is missing', async () => {
      await request(app)
        .get('/v1/locations/search')
        .query({ q: 'Jakarta' })
        .expect(httpStatus.UNAUTHORIZED);
    });

    test('should return 400 error if search query is missing', async () => {
      await request(app)
        .get('/v1/locations/search')
        .set('Authorization', `Bearer ${userOneAccessToken}`)
        .expect(httpStatus.BAD_REQUEST);
    });

    test('should return 400 error if search query is too short', async () => {
      await request(app)
        .get('/v1/locations/search')
        .query({ q: 'a' })
        .set('Authorization', `Bearer ${userOneAccessToken}`)
        .expect(httpStatus.BAD_REQUEST);
    });

    test('should return empty results if no matches found', async () => {
      const res = await request(app)
        .get('/v1/locations/search')
        .query({ q: 'NonExistentLocation' })
        .set('Authorization', `Bearer ${userOneAccessToken}`)
        .expect(httpStatus.OK);

      expect(res.body).toEqual({
        provinces: [],
        cities: []
      });
    });

    test('should limit search results if limit query is provided', async () => {
      const res = await request(app)
        .get('/v1/locations/search')
        .query({ q: 'Jakarta', limit: 2 })
        .set('Authorization', `Bearer ${userOneAccessToken}`)
        .expect(httpStatus.OK);

      const totalResults = res.body.provinces.length + res.body.cities.length;
      expect(totalResults).toBeLessThanOrEqual(4); // 2 provinces + 2 cities max
    });
  });

  describe('GET /v1/locations/timezones', () => {
    test('should return 200 and all Indonesian timezones', async () => {
      const res = await request(app)
        .get('/v1/locations/timezones')
        .set('Authorization', `Bearer ${userOneAccessToken}`)
        .expect(httpStatus.OK);

      expect(res.body).toHaveLength(3);
      expect(res.body).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            id: 'Asia/Jakarta',
            name: 'WIB (UTC+7)',
            description: 'Waktu Indonesia Barat',
            offset: '+07:00',
            regions: expect.arrayContaining([
              'Sumatera',
              'Jawa',
              'Kalimantan Barat',
              'Kalimantan Tengah'
            ])
          }),
          expect.objectContaining({
            id: 'Asia/Makassar',
            name: 'WITA (UTC+8)',
            description: 'Waktu Indonesia Tengah',
            offset: '+08:00',
            regions: expect.arrayContaining([
              'Kalimantan Selatan',
              'Kalimantan Timur',
              'Kalimantan Utara',
              'Bali',
              'Nusa Tenggara',
              'Sulawesi'
            ])
          }),
          expect.objectContaining({
            id: 'Asia/Jayapura',
            name: 'WIT (UTC+9)',
            description: 'Waktu Indonesia Timur',
            offset: '+09:00',
            regions: expect.arrayContaining(['Maluku', 'Papua'])
          })
        ])
      );
    });

    test('should return 401 error if access token is missing', async () => {
      await request(app).get('/v1/locations/timezones').expect(httpStatus.UNAUTHORIZED);
    });

    test('should return timezones with correct structure', async () => {
      const res = await request(app)
        .get('/v1/locations/timezones')
        .set('Authorization', `Bearer ${userOneAccessToken}`)
        .expect(httpStatus.OK);

      expect(res.body[0]).toEqual(
        expect.objectContaining({
          id: expect.any(String),
          name: expect.any(String),
          description: expect.any(String),
          offset: expect.any(String),
          regions: expect.any(Array)
        })
      );

      // Verify regions is array of strings
      expect(res.body[0].regions).toEqual(expect.arrayContaining([expect.any(String)]));
    });

    test('should return timezone data consistently', async () => {
      const res1 = await request(app)
        .get('/v1/locations/timezones')
        .set('Authorization', `Bearer ${userOneAccessToken}`)
        .expect(httpStatus.OK);

      const res2 = await request(app)
        .get('/v1/locations/timezones')
        .set('Authorization', `Bearer ${userOneAccessToken}`)
        .expect(httpStatus.OK);

      expect(res1.body).toEqual(res2.body);
      expect(res1.body).toHaveLength(3);
    });

    test('should include all required timezone properties', async () => {
      const res = await request(app)
        .get('/v1/locations/timezones')
        .set('Authorization', `Bearer ${userOneAccessToken}`)
        .expect(httpStatus.OK);

      res.body.forEach((timezone: any) => {
        expect(timezone).toHaveProperty('id');
        expect(timezone).toHaveProperty('name');
        expect(timezone).toHaveProperty('description');
        expect(timezone).toHaveProperty('offset');
        expect(timezone).toHaveProperty('regions');

        // Verify timezone id follows IANA format
        expect(timezone.id).toMatch(/^Asia\//);

        // Verify offset format
        expect(timezone.offset).toMatch(/^[+-]\d{2}:\d{2}$/);

        // Verify regions is non-empty array
        expect(Array.isArray(timezone.regions)).toBe(true);
        expect(timezone.regions.length).toBeGreaterThan(0);
      });
    });
  });
});
