import { Role } from '@prisma/client';

const allRoles = {
  [Role.ADMIN]: [
    'getUsers',
    'manageUsers',
    'getOutlets',
    'manageOutlets',
    'manageEmployees',
    'getEmployees',
    'getCustomers',
    'manageCustomers',
    'getOrders',
    'manageOrders',
    'getServices',
    'manageServices'
  ],
  [Role.OWNER]: [
    'getOutlets',
    'manageOutlets',
    'manageEmployees',
    'getEmployees',
    'getCustomers',
    'manageCustomers',
    'getOrders',
    'manageOrders',
    'getServices',
    'manageServices',
    'manageCashbox',
    'getCashbox'
  ],
  [Role.EMPLOYEE]: [
    'getOutlets',
    'getCustomers',
    'manageCustomers',
    'getOrders',
    'manageOrders',
    'getServices'
  ]
};

export const roles = Object.keys(allRoles);
export const roleRights = new Map(Object.entries(allRoles));
