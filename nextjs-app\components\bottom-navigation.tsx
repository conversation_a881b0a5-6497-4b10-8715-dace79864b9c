import Link from 'next/link';
import {
  Home,
  ShoppingBag,
  User,
  <PERSON><PERSON>hart2,
  PlusCircle,
  UserCheck,
} from 'lucide-react';

interface BottomNavigationProps {
  activePage: string;
}

export default function BottomNavigation({
  activePage,
}: BottomNavigationProps) {
  return (
    <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-10">
      <div className="flex justify-between items-center px-2 relative">
        <Link
          href="/dashboard"
          className={`flex flex-col items-center py-2 px-3 ${
            activePage === 'beranda'
              ? 'text-blue-600'
              : 'text-gray-500 hover:text-gray-700'
          }`}
        >
          <Home className="h-6 w-6" />
          <span className="text-xs mt-1">Beranda</span>
        </Link>

        <Link
          href="/absensi"
          className={`flex flex-col items-center py-2 px-3 ${
            activePage === 'absensi'
              ? 'text-blue-600'
              : 'text-gray-500 hover:text-gray-700'
          }`}
        >
          <UserCheck className="h-6 w-6" />
          <span className="text-xs mt-1">Absensi</span>
        </Link>

        {/* Create Order Button */}
        <Link
          href="/orders/select-customer"
          className="flex flex-col items-center -mt-5"
        >
          <div className="bg-blue-500 rounded-full p-3">
            <PlusCircle className="h-8 w-8 text-white" />
          </div>
          <span className="text-xs mt-1 text-blue-500">Order</span>
        </Link>

        <Link
          href="/laporan"
          className={`flex flex-col items-center py-2 px-3 ${
            activePage === 'laporan'
              ? 'text-blue-600'
              : 'text-gray-500 hover:text-gray-700'
          }`}
        >
          <BarChart2 className="h-6 w-6" />
          <span className="text-xs mt-1">Laporan</span>
        </Link>

        <Link
          href="/akun"
          className={`flex flex-col items-center py-2 px-3 ${
            activePage === 'akun'
              ? 'text-blue-600'
              : 'text-gray-500 hover:text-gray-700'
          }`}
        >
          <User className="h-6 w-6" />
          <span className="text-xs mt-1">Akun</span>
        </Link>
      </div>
    </div>
  );
}
