import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from '@/components/ui/use-toast';
import { useAuth } from '@/lib/auth-context';
import {
  perfumeAPI,
  type Perfume,
  type CreatePerfumeRequest,
  type UpdatePerfumeRequest,
  type GetPerfumesParams,
} from '@/lib/api/perfumes';

// Hook untuk mengambil semua perfumes dengan pagination dan filter
export function usePerfumes(params?: GetPerfumesParams) {
  const { activeOutlet } = useAuth();

  return useQuery({
    queryKey: ['perfumes', activeOutlet?.id, params],
    queryFn: () =>
      activeOutlet
        ? perfumeAPI.getPerfumes({ ...params, outletId: activeOutlet.id })
        : Promise.reject('No active outlet'),
    enabled: !!activeOutlet,
    staleTime: 5 * 60 * 1000, // 5 menit
  });
}

// Hook untuk mengambil parfum yang aktif saja (untuk dropdown)
export function useActivePerfumes() {
  const { activeOutlet } = useAuth();

  return useQuery({
    queryKey: ['active-perfumes', activeOutlet?.id],
    queryFn: () =>
      activeOutlet
        ? perfumeAPI.getPerfumes({
            outletId: activeOutlet.id,
            isActive: true,
            limit: 100, // Ambil semua parfum aktif
          })
        : Promise.reject('No active outlet'),
    enabled: !!activeOutlet,
    staleTime: 5 * 60 * 1000, // 5 menit
  });
}

// Hook untuk mengambil perfume berdasarkan ID
export function usePerfume(id: number | null) {
  const { activeOutlet } = useAuth();

  return useQuery({
    queryKey: ['perfumes', activeOutlet?.id, id],
    queryFn: () =>
      activeOutlet && id
        ? perfumeAPI.getPerfume(id, activeOutlet.id)
        : Promise.reject('No active outlet or ID'),
    enabled: !!id && !!activeOutlet,
    staleTime: 5 * 60 * 1000, // 5 menit
  });
}

// Hook untuk membuat perfume baru
export function useCreatePerfume() {
  const queryClient = useQueryClient();
  const { activeOutlet } = useAuth();

  return useMutation({
    mutationFn: (data: CreatePerfumeRequest) => {
      if (!activeOutlet) throw new Error('No active outlet');
      return perfumeAPI.createPerfume({ ...data, outletId: activeOutlet.id });
    },
    onSuccess: (newPerfume) => {
      queryClient.invalidateQueries({ queryKey: ['perfumes'] });
      queryClient.invalidateQueries({ queryKey: ['active-perfumes'] });
      toast({
        title: 'Berhasil',
        description: `Parfum ${newPerfume.name} berhasil ditambahkan.`,
      });
    },
    // Tidak langsung show error toast, biarkan komponen handle error
  });
}

// Hook untuk update perfume
export function useUpdatePerfume() {
  const queryClient = useQueryClient();
  const { activeOutlet } = useAuth();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdatePerfumeRequest }) => {
      if (!activeOutlet) throw new Error('No active outlet');
      return perfumeAPI.updatePerfume(id, {
        ...data,
        outletId: activeOutlet.id,
      });
    },
    onSuccess: (updatedPerfume) => {
      queryClient.invalidateQueries({ queryKey: ['perfumes'] });
      queryClient.invalidateQueries({ queryKey: ['active-perfumes'] });
      queryClient.setQueryData(['perfumes', updatedPerfume.id], updatedPerfume);
      toast({
        title: 'Berhasil',
        description: `Data parfum ${updatedPerfume.name} berhasil diperbarui.`,
      });
    },
    // Tidak langsung show error toast, biarkan komponen handle error
  });
}

// Hook untuk delete perfume
export function useDeletePerfume() {
  const queryClient = useQueryClient();
  const { activeOutlet } = useAuth();

  return useMutation({
    mutationFn: (id: number) => {
      if (!activeOutlet) throw new Error('No active outlet');
      return perfumeAPI.deletePerfume(id, activeOutlet.id);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['perfumes'] });
      queryClient.invalidateQueries({ queryKey: ['active-perfumes'] });
      toast({
        title: 'Berhasil',
        description: 'Parfum berhasil dihapus.',
      });
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || 'Gagal menghapus parfum';
      toast({
        title: 'Error',
        description: message,
        variant: 'destructive',
      });
    },
  });
}

// Helper function untuk extract error message dari API response
export function getPerfumeErrorMessage(error: any): string {
  if (error?.response?.data?.message) {
    return error.response.data.message;
  }
  if (error?.message) {
    return error.message;
  }
  return 'Terjadi kesalahan yang tidak diketahui';
}
