#!/bin/bash

# Script untuk menjalankan testing API outlet
# Usage: ./scripts/test-outlet.sh

echo "🧪 Memulai testing API outlet..."

# Warna untuk output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function untuk print dengan warna
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Check apakah Docker berjalan
if ! docker info > /dev/null 2>&1; then
    print_error "Docker tidak berjalan. Silakan start Docker terlebih dahulu."
    exit 1
fi

print_status "Docker sudah berjalan"

# Start database testing
print_status "Memulai database testing..."
docker compose -f docker-compose.only-db-test.yml up -d

# Tunggu database siap
print_status "Menunggu database siap..."
sleep 5

# Push schema ke database
print_status "Melakukan database migration..."
yarn db:push

if [ $? -ne 0 ]; then
    print_error "Database migration gagal"
    docker compose -f docker-compose.only-db-test.yml down
    exit 1
fi

print_status "Database migration berhasil"

# Jalankan testing
print_status "Menjalankan testing outlet..."
echo ""

npx jest tests/integration/outlet.test.ts -i --colors --verbose --detectOpenHandles

TEST_EXIT_CODE=$?

echo ""

# Cleanup database
print_status "Membersihkan database testing..."
docker compose -f docker-compose.only-db-test.yml down

if [ $TEST_EXIT_CODE -eq 0 ]; then
    print_status "Semua testing berhasil! 🎉"
else
    print_error "Beberapa testing gagal. Silakan periksa output di atas."
fi

exit $TEST_EXIT_CODE 