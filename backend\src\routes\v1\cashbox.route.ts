import express from 'express';
import auth from '../../middlewares/auth';
import validate from '../../middlewares/validate';
import requireVerification from '../../middlewares/verification';
import { cashboxValidation } from '../../validations';
import { cashboxController } from '../../controllers';

const router = express.Router();

router
  .route('/outlets/:outletId/cashboxes')
  .post(
    auth('manageCashbox'),
    requireVerification(),
    validate(cashboxValidation.createCashbox),
    cashboxController.createCashbox
  )
  .get(
    auth('getCashbox'),
    requireVerification(),
    validate(cashboxValidation.getCashboxes),
    cashboxController.getCashboxes
  );

router
  .route('/outlets/:outletId/cashboxes/:cashboxId')
  .get(
    auth('getCashbox'),
    requireVerification(),
    validate(cashboxValidation.getCashbox),
    cashboxController.getCashbox
  )
  .patch(
    auth('manageCashbox'),
    requireVerification(),
    validate(cashboxValidation.updateCashbox),
    cashboxController.updateCashbox
  )
  .delete(
    auth('manageCashbox'),
    requireVerification(),
    validate(cashboxValidation.deleteCashbox),
    cashboxController.deleteCashbox
  );

router
  .route('/outlets/:outletId/cashboxes/:cashboxId/balance')
  .get(
    auth('getCashbox'),
    requireVerification(),
    validate(cashboxValidation.getCashboxBalance),
    cashboxController.getCashboxBalance
  )
  .patch(
    auth('manageCashbox'),
    requireVerification(),
    validate(cashboxValidation.adjustCashboxBalance),
    cashboxController.adjustCashboxBalance
  );

export default router;

/**
 * @swagger
 * tags:
 *   name: Cashbox
 *   description: Cashbox management for outlets
 */

/**
 * @swagger
 * /outlets/{outletId}/cashboxes:
 *   post:
 *     summary: Create a new cashbox
 *     description: Create a new cashbox for an outlet. Each outlet can have multiple cashboxes for different payment types.
 *     tags: [Cashbox]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: outletId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Outlet ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - type
 *             properties:
 *               name:
 *                 type: string
 *                 maxLength: 100
 *                 description: Cashbox name (e.g., "Kas Utama", "BCA", "Gopay")
 *               type:
 *                 type: string
 *                 enum: [TUNAI, NON_TUNAI]
 *                 description: Cashbox type
 *               isActive:
 *                 type: boolean
 *                 default: true
 *                 description: Whether the cashbox is active
 *               balance:
 *                 type: number
 *                 default: 0
 *                 description: Initial balance
 *             example:
 *               name: "Kas Utama"
 *               type: "TUNAI"
 *               isActive: true
 *               balance: 0
 *     responses:
 *       "201":
 *         description: Created
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                     outletId:
 *                       type: integer
 *                     name:
 *                       type: string
 *                     type:
 *                       type: string
 *                       enum: [TUNAI, NON_TUNAI]
 *                     isActive:
 *                       type: boolean
 *                     balance:
 *                       type: number
 *                     createdAt:
 *                       type: string
 *                       format: date-time
 *                     updatedAt:
 *                       type: string
 *                       format: date-time
 *       "400":
 *         $ref: '#/components/responses/BadRequest'
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "403":
 *         $ref: '#/components/responses/Forbidden'
 *
 *   get:
 *     summary: Get all cashboxes for an outlet
 *     description: Retrieve all cashboxes for a specific outlet with optional filtering.
 *     tags: [Cashbox]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: outletId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Outlet ID
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [TUNAI, NON_TUNAI]
 *         description: Filter by cashbox type
 *       - in: query
 *         name: isActive
 *         schema:
 *           type: boolean
 *         description: Filter by active status
 *     responses:
 *       "200":
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                       outletId:
 *                         type: integer
 *                       name:
 *                         type: string
 *                       type:
 *                         type: string
 *                         enum: [TUNAI, NON_TUNAI]
 *                       isActive:
 *                         type: boolean
 *                       balance:
 *                         type: number
 *                       createdAt:
 *                         type: string
 *                         format: date-time
 *                       updatedAt:
 *                         type: string
 *                         format: date-time
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "403":
 *         $ref: '#/components/responses/Forbidden'
 */

/**
 * @swagger
 * /outlets/{outletId}/cashboxes/{cashboxId}:
 *   get:
 *     summary: Get cashbox by ID
 *     description: Retrieve a specific cashbox by its ID.
 *     tags: [Cashbox]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: outletId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Outlet ID
 *       - in: path
 *         name: cashboxId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Cashbox ID
 *     responses:
 *       "200":
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                     outletId:
 *                       type: integer
 *                     name:
 *                       type: string
 *                     type:
 *                       type: string
 *                       enum: [TUNAI, NON_TUNAI]
 *                     isActive:
 *                       type: boolean
 *                     balance:
 *                       type: number
 *                     createdAt:
 *                       type: string
 *                       format: date-time
 *                     updatedAt:
 *                       type: string
 *                       format: date-time
 *       "404":
 *         $ref: '#/components/responses/NotFound'
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "403":
 *         $ref: '#/components/responses/Forbidden'
 *
 *   patch:
 *     summary: Update cashbox
 *     description: Update cashbox information such as name, type, or active status.
 *     tags: [Cashbox]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: outletId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Outlet ID
 *       - in: path
 *         name: cashboxId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Cashbox ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 maxLength: 100
 *                 description: Cashbox name
 *               type:
 *                 type: string
 *                 enum: [TUNAI, NON_TUNAI]
 *                 description: Cashbox type
 *               isActive:
 *                 type: boolean
 *                 description: Whether the cashbox is active
 *             example:
 *               name: "Kas Utama - Updated"
 *               isActive: false
 *     responses:
 *       "200":
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                     outletId:
 *                       type: integer
 *                     name:
 *                       type: string
 *                     type:
 *                       type: string
 *                       enum: [TUNAI, NON_TUNAI]
 *                     isActive:
 *                       type: boolean
 *                     balance:
 *                       type: number
 *                     createdAt:
 *                       type: string
 *                       format: date-time
 *                     updatedAt:
 *                       type: string
 *                       format: date-time
 *       "400":
 *         $ref: '#/components/responses/BadRequest'
 *       "404":
 *         $ref: '#/components/responses/NotFound'
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "403":
 *         $ref: '#/components/responses/Forbidden'
 *
 *   delete:
 *     summary: Delete cashbox
 *     description: Delete a cashbox. Only cashboxes with zero balance can be deleted.
 *     tags: [Cashbox]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: outletId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Outlet ID
 *       - in: path
 *         name: cashboxId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Cashbox ID
 *     responses:
 *       "200":
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *       "400":
 *         description: Bad Request - Cashbox has non-zero balance
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Cannot delete cashbox with non-zero balance"
 *       "404":
 *         $ref: '#/components/responses/NotFound'
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "403":
 *         $ref: '#/components/responses/Forbidden'
 */

/**
 * @swagger
 * /outlets/{outletId}/cashboxes/{cashboxId}/balance:
 *   get:
 *     summary: Get cashbox balance
 *     description: Retrieve the current balance of a specific cashbox.
 *     tags: [Cashbox]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: outletId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Outlet ID
 *       - in: path
 *         name: cashboxId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Cashbox ID
 *     responses:
 *       "200":
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     cashboxId:
 *                       type: integer
 *                     name:
 *                       type: string
 *                     type:
 *                       type: string
 *                       enum: [TUNAI, NON_TUNAI]
 *                     balance:
 *                       type: number
 *                     lastUpdated:
 *                       type: string
 *                       format: date-time
 *       "404":
 *         $ref: '#/components/responses/NotFound'
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "403":
 *         $ref: '#/components/responses/Forbidden'
 *
 *   patch:
 *     summary: Adjust cashbox balance
 *     description: Manually adjust the cashbox balance. This creates an audit trail for the adjustment.
 *     tags: [Cashbox]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: outletId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Outlet ID
 *       - in: path
 *         name: cashboxId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Cashbox ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - amount
 *               - reason
 *             properties:
 *               amount:
 *                 type: number
 *                 description: Adjustment amount (positive to add, negative to subtract)
 *               reason:
 *                 type: string
 *                 maxLength: 500
 *                 description: Reason for the adjustment
 *               reference:
 *                 type: string
 *                 maxLength: 100
 *                 description: Reference number for the adjustment
 *             example:
 *               amount: 50000
 *               reason: "Initial cash deposit"
 *               reference: "ADJ-001"
 *     responses:
 *       "200":
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     cashboxId:
 *                       type: integer
 *                     previousBalance:
 *                       type: number
 *                     adjustmentAmount:
 *                       type: number
 *                     newBalance:
 *                       type: number
 *                     reason:
 *                       type: string
 *                     reference:
 *                       type: string
 *                     adjustedAt:
 *                       type: string
 *                       format: date-time
 *                     adjustedBy:
 *                       type: integer
 *       "400":
 *         $ref: '#/components/responses/BadRequest'
 *       "404":
 *         $ref: '#/components/responses/NotFound'
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "403":
 *         $ref: '#/components/responses/Forbidden'
 */
