# Auto Create Cashbox saat Create Outlet - Dokumentasi

## Overview

Saat user membuat outlet baru, sistem akan otomatis membuat 2 cashbox default untuk memastikan outlet siap digunakan untuk transaksi pembayaran.

## Cashbox Default yang Dibuat

### 1. <PERSON><PERSON> (TUNAI)

- **Name**: "Kas Utam<PERSON>"
- **Type**: TUNAI
- **Balance**: 0
- **Status**: Active
- **Purpose**: Untuk menampung pembayaran cash/tunai

### 2. Transfer Bank (NON_TUNAI)

- **Name**: "Transfer Bank"
- **Type**: NON_TUNAI
- **Balance**: 0
- **Status**: Active
- **Purpose**: Untuk menampung pembayaran non-tunai (transfer, e-wallet, dll)

## Implementation Details

### Database Transaction

Pembuatan outlet dan cashbox dilakukan dalam satu database transaction untuk memastikan konsistensi data:

```typescript
const result = await prisma.$transaction(async (tx) => {
  // 1. Create outlet
  const outlet = await tx.outlet.create({...});

  // 2. Create default cashboxes
  await tx.cashbox.createMany({
    data: [
      {
        outletId: outlet.id,
        name: 'Kas Utama',
        type: 'TUNAI',
        isActive: true,
        balance: 0
      },
      {
        outletId: outlet.id,
        name: 'Transfer Bank',
        type: 'NON_TUNAI',
        isActive: true,
        balance: 0
      }
    ]
  });

  return outlet;
});
```

### Service Copy Integration

Jika outlet dibuat dengan menyalin dari outlet lain (`copiedFromId`), proses copy services tetap berjalan setelah outlet dan cashbox berhasil dibuat.

## Benefits

### 1. User Experience

- **Plug & Play**: Outlet langsung siap digunakan untuk transaksi
- **No Setup Required**: User tidak perlu manual setup cashbox
- **Consistent Setup**: Semua outlet memiliki struktur cashbox yang sama

### 2. Error Prevention

- **Avoid Payment Errors**: Mencegah error saat user langsung ingin melakukan transaksi
- **Consistent Data**: Memastikan setiap outlet memiliki minimal cashbox yang diperlukan
- **Validation Ready**: Payment validation akan selalu menemukan cashbox yang tersedia

### 3. Business Logic

- **Standard Operating**: Setiap outlet memiliki setup standar untuk operasional
- **Reporting Ready**: Cashbox siap untuk laporan keuangan
- **Scalable**: Mudah ditambahkan cashbox lain sesuai kebutuhan

## API Response

Saat create outlet berhasil, response tetap sama seperti sebelumnya. Cashbox dapat diakses melalui endpoint cashbox:

```
GET /v1/outlets/:outletId/cashboxes
```

Response akan menampilkan 2 cashbox default yang sudah dibuat.

## Customization

### Menambah Cashbox Lain

User tetap bisa menambahkan cashbox lain sesuai kebutuhan:

- Kas Kecil
- Gopay
- OVO
- Dana
- BCA
- Mandiri
- dll

### Menonaktifkan Cashbox Default

User bisa menonaktifkan cashbox default jika tidak diperlukan melalui endpoint update cashbox.

### Mengubah Nama

User bisa mengubah nama cashbox default sesuai preferensi bisnis mereka.

## Error Handling

Jika terjadi error saat pembuatan cashbox, seluruh transaction akan di-rollback termasuk pembuatan outlet, memastikan tidak ada data yang inconsistent.

## Migration untuk Outlet Existing

Untuk outlet yang sudah ada sebelum fitur ini diimplementasikan, bisa dibuat script migration untuk menambahkan cashbox default:

```sql
-- Script akan dibuat terpisah jika diperlukan
INSERT INTO "Cashbox" (outletId, name, type, isActive, balance, createdAt, updatedAt)
SELECT
  id as outletId,
  'Kas Utama' as name,
  'TUNAI' as type,
  true as isActive,
  0 as balance,
  NOW() as createdAt,
  NOW() as updatedAt
FROM "Outlet"
WHERE id NOT IN (SELECT DISTINCT outletId FROM "Cashbox");
```

## Testing

### Unit Test

- Test pembuatan outlet dengan cashbox default
- Test rollback jika cashbox creation gagal
- Test integration dengan copy services

### Integration Test

- Test end-to-end create outlet flow
- Test payment flow dengan cashbox default
- Test cashbox management setelah outlet dibuat

## Monitoring

### Metrics to Track

- Jumlah outlet yang berhasil dibuat dengan cashbox
- Error rate pada create outlet transaction
- Usage rate cashbox default vs custom cashbox

### Logging

- Log successful outlet + cashbox creation
- Log any errors during transaction
- Log cashbox usage patterns
