import { Outlet, User, Role, Prisma } from '@prisma/client';
import httpStatus from 'http-status';
import prisma from '../client';
import ApiError from '../utils/ApiError';

/**
 * Create an outlet
 * @param {Object} outletBody
 * @param {number} ownerId
 * @returns {Promise<Outlet>}
 */
const createOutlet = async (outletBody: any, ownerId: number): Promise<Outlet> => {
  const {
    name,
    address,
    province,
    city,
    provinceId,
    cityId,
    timezone = 'Asia/Jakarta',
    phone,
    latitude,
    longitude,
    copiedFromId
  } = outletBody;

  // Validasi owner exists
  const owner = await prisma.user.findUnique({ where: { id: ownerId } });
  if (!owner) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Owner not found');
  }

  // Validasi location jika provinceId atau cityId disediakan
  if (provinceId || cityId) {
    // Jika ada provinceId, validasi province exists
    if (provinceId) {
      const provinceExists = await prisma.province.findUnique({ where: { id: provinceId } });
      if (!provinceExists) {
        throw new ApiError(httpStatus.BAD_REQUEST, 'Province not found');
      }
    }

    // Jika ada cityId, validasi city exists
    if (cityId) {
      const cityExists = await prisma.city.findUnique({
        where: { id: cityId },
        include: { province: true }
      });
      if (!cityExists) {
        throw new ApiError(httpStatus.BAD_REQUEST, 'City not found');
      }

      // Jika ada provinceId dan cityId, validasi bahwa city belongs to province
      if (provinceId && cityExists.provinceId !== provinceId) {
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          'City does not belong to the specified province'
        );
      }
    }
  }

  // Validasi outlet yang akan disalin (jika ada)
  if (copiedFromId) {
    const sourceOutlet = await prisma.outlet.findFirst({
      where: {
        id: copiedFromId,
        isDeleted: false,
        OR: [
          { ownerId: ownerId }, // Owner yang sama
          { isActive: true } // Atau outlet aktif (public)
        ]
      }
    });
    if (!sourceOutlet) {
      throw new ApiError(httpStatus.NOT_FOUND, 'Source outlet not found or not accessible');
    }
  }

  // Create outlet dengan cashbox default dalam transaction
  const result = await prisma.$transaction(async (tx) => {
    // Create outlet
    const outlet = await tx.outlet.create({
      data: {
        name,
        address,
        province,
        city,
        provinceId,
        cityId,
        timezone,
        phone,
        latitude,
        longitude,
        ownerId,
        copiedFromId
      },
      include: {
        owner: {
          select: { id: true, name: true, email: true }
        },
        provinceRef: true,
        cityRef: true,
        copiedFrom: {
          select: { id: true, name: true }
        }
      }
    });

    // Create default cashboxes
    const defaultCashboxes = [
      {
        outletId: outlet.id,
        name: 'Kas Utama',
        type: 'TUNAI' as const,
        isActive: true,
        balance: 0
      },
      {
        outletId: outlet.id,
        name: 'Transfer Bank',
        type: 'NON_TUNAI' as const,
        isActive: true,
        balance: 0
      },
      {
        outletId: outlet.id,
        name: 'BCA',
        type: 'NON_TUNAI' as const,
        isActive: true,
        balance: 0
      },
      {
        outletId: outlet.id,
        name: 'BRI',
        type: 'NON_TUNAI' as const,
        isActive: true,
        balance: 0
      },
      {
        outletId: outlet.id,
        name: 'Mandiri',
        type: 'NON_TUNAI' as const,
        isActive: true,
        balance: 0
      },
      {
        outletId: outlet.id,
        name: 'Dana',
        type: 'NON_TUNAI' as const,
        isActive: true,
        balance: 0
      },
      {
        outletId: outlet.id,
        name: 'OVO',
        type: 'NON_TUNAI' as const,
        isActive: true,
        balance: 0
      },
      {
        outletId: outlet.id,
        name: 'Gopay',
        type: 'NON_TUNAI' as const,
        isActive: true,
        balance: 0
      }
    ];

    await tx.cashbox.createMany({
      data: defaultCashboxes
    });

    return outlet;
  });

  // Jika ada outlet yang disalin, copy services-nya setelah transaction selesai
  if (copiedFromId) {
    await copyServicesFromOutlet(result.id, copiedFromId, { id: ownerId } as User);
  }

  return result;
};

/**
 * Query for outlets
 * @param {Object} filter - Prisma filter
 * @param {Object} options - Query options
 * @param {User} user - Current user
 * @returns {Promise<QueryResult>}
 */
const queryOutlets = async (
  filter: any,
  options: {
    limit?: number;
    page?: number;
    sortBy?: string;
    sortType?: 'asc' | 'desc';
  },
  user: User
): Promise<any> => {
  const page = options.page ?? 1;
  const limit = options.limit ?? 10;
  const sortBy = options.sortBy ?? 'createdAt';
  const sortType = options.sortType ?? 'desc';

  // Filter berdasarkan role user
  const whereClause: any = {
    isDeleted: false,
    ...filter
  };

  // Jika bukan admin, hanya bisa lihat outlet milik sendiri
  if (user.role !== Role.ADMIN) {
    whereClause.ownerId = user.id;
  }

  const [outlets, total] = await Promise.all([
    prisma.outlet.findMany({
      where: whereClause,
      include: {
        owner: {
          select: { id: true, name: true, email: true }
        },
        _count: {
          select: {
            employees: true,
            orders: true,
            customers: true
          }
        }
      },
      skip: (page - 1) * limit,
      take: limit,
      orderBy: { [sortBy]: sortType }
    }),
    prisma.outlet.count({ where: whereClause })
  ]);

  return {
    results: outlets,
    page,
    limit,
    totalPages: Math.ceil(total / limit),
    totalResults: total
  };
};

/**
 * Get outlet by id
 * @param {number} outletId
 * @param {User} user
 * @returns {Promise<Outlet | null>}
 */
const getOutletById = async (outletId: number, user: User): Promise<any> => {
  const whereClause: any = {
    id: outletId,
    isDeleted: false
  };

  // Jika bukan admin, hanya bisa lihat outlet milik sendiri
  if (user.role !== Role.ADMIN) {
    whereClause.ownerId = user.id;
  }

  const outlet = await prisma.outlet.findFirst({
    where: whereClause,
    include: {
      owner: {
        select: { id: true, name: true, email: true }
      },
      provinceRef: true,
      cityRef: true,
      employees: {
        select: { id: true, name: true, email: true, role: true }
      },
      services: true,
      copiedFrom: {
        select: { id: true, name: true }
      },
      _count: {
        select: {
          orders: true,
          customers: true
        }
      }
    }
  });

  return outlet;
};

/**
 * Update outlet by id
 * @param {number} outletId
 * @param {Object} updateBody
 * @param {User} user
 * @returns {Promise<Outlet>}
 */
const updateOutletById = async (outletId: number, updateBody: any, user: User): Promise<Outlet> => {
  const outlet = await getOutletById(outletId, user);
  if (!outlet) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Outlet not found');
  }

  // Hanya owner atau admin yang bisa update
  if (user.role !== Role.ADMIN && outlet.ownerId !== user.id) {
    throw new ApiError(httpStatus.FORBIDDEN, 'Access denied');
  }

  // Validasi location jika provinceId atau cityId disediakan
  const { provinceId, cityId } = updateBody;
  if (provinceId || cityId) {
    // Jika ada provinceId, validasi province exists
    if (provinceId) {
      const provinceExists = await prisma.province.findUnique({ where: { id: provinceId } });
      if (!provinceExists) {
        throw new ApiError(httpStatus.BAD_REQUEST, 'Province not found');
      }
    }

    // Jika ada cityId, validasi city exists
    if (cityId) {
      const cityExists = await prisma.city.findUnique({
        where: { id: cityId },
        include: { province: true }
      });
      if (!cityExists) {
        throw new ApiError(httpStatus.BAD_REQUEST, 'City not found');
      }

      // Jika ada provinceId dan cityId, validasi bahwa city belongs to province
      if (provinceId && cityExists.provinceId !== provinceId) {
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          'City does not belong to the specified province'
        );
      }
    }
  }

  const updatedOutlet = await prisma.outlet.update({
    where: { id: outletId },
    data: {
      ...updateBody,
      updatedAt: new Date()
    },
    include: {
      owner: {
        select: { id: true, name: true, email: true }
      },
      provinceRef: true,
      cityRef: true
    }
  });

  return updatedOutlet;
};

/**
 * Delete outlet by id (soft delete)
 * @param {number} outletId
 * @param {User} user
 * @returns {Promise<void>}
 */
const deleteOutletById = async (outletId: number, user: User): Promise<void> => {
  const outlet = await getOutletById(outletId, user);
  if (!outlet) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Outlet not found');
  }

  // Hanya owner atau admin yang bisa delete
  if (user.role !== Role.ADMIN && outlet.ownerId !== user.id) {
    throw new ApiError(httpStatus.FORBIDDEN, 'Access denied');
  }

  // Soft delete
  await prisma.outlet.update({
    where: { id: outletId },
    data: {
      isDeleted: true,
      deletedAt: new Date(),
      isActive: false
    }
  });
};

/**
 * Get outlet services
 * @param {number} outletId
 * @param {User} user
 * @returns {Promise<any>}
 */
const getOutletServices = async (outletId: number, user: User): Promise<any> => {
  const outlet = await getOutletById(outletId, user);
  if (!outlet) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Outlet not found');
  }

  const services = await prisma.service.findMany({
    where: { outletId },
    include: {
      outlet: {
        select: { id: true, name: true }
      }
    },
    orderBy: { name: 'asc' }
  });

  return services;
};

/**
 * Copy services from another outlet
 * @param {number} targetOutletId
 * @param {number} sourceOutletId
 * @param {User} user
 * @returns {Promise<any>}
 */
const copyServicesFromOutlet = async (
  targetOutletId: number,
  sourceOutletId: number,
  user: User
): Promise<any> => {
  const [targetOutlet, sourceOutlet] = await Promise.all([
    getOutletById(targetOutletId, user),
    getOutletById(sourceOutletId, user)
  ]);

  if (!targetOutlet) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Target outlet not found');
  }
  if (!sourceOutlet) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Source outlet not found');
  }

  // Hanya owner atau admin yang bisa copy services
  if (user.role !== Role.ADMIN && targetOutlet.ownerId !== user.id) {
    throw new ApiError(httpStatus.FORBIDDEN, 'Access denied');
  }

  // Get services from source outlet
  const sourceServices = await prisma.service.findMany({
    where: { outletId: sourceOutletId },
    include: { outlet: true }
  });

  if (sourceServices.length === 0) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Source outlet has no services to copy');
  }

  // Delete existing services in target outlet
  await prisma.service.deleteMany({
    where: { outletId: targetOutletId }
  });

  // Copy services to target outlet
  const newServices = await Promise.all(
    sourceServices.map((sourceService: any) =>
      prisma.service.create({
        data: {
          outletId: targetOutletId,
          name: sourceService.name,
          description: sourceService.description,
          price: sourceService.price,
          unit: sourceService.unit,
          estimationHours: sourceService.estimationHours,
          isActive: sourceService.isActive
        },
        include: { outlet: true }
      })
    )
  );

  // Update copiedFromId in target outlet
  await prisma.outlet.update({
    where: { id: targetOutletId },
    data: { copiedFromId: sourceOutletId }
  });

  return {
    message: `Successfully copied ${newServices.length} services from ${sourceOutlet.name}`,
    services: newServices
  };
};

/**
 * Get outlet statistics
 * @param {number} outletId
 * @param {User} user
 * @returns {Promise<any>}
 */
const getOutletStats = async (outletId: number, user: User): Promise<any> => {
  const outlet = await getOutletById(outletId, user);
  if (!outlet) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Outlet not found');
  }

  const [totalOrders, totalCustomers, totalRevenue, pendingOrders, completedOrders] =
    await Promise.all([
      prisma.order.count({ where: { outletId } }),
      prisma.customer.count({ where: { outletId } }),
      prisma.order.aggregate({
        where: { outletId, paymentStatus: 'PAID' },
        _sum: { totalPrice: true }
      }),
      prisma.order.count({
        where: {
          outletId,
          status: { in: ['KONFIRMASI', 'PICKUP', 'PENDING', 'PROCESSING'] }
        }
      }),
      prisma.order.count({
        where: {
          outletId,
          status: { in: ['READY', 'READY_FOR_PICKUP', 'COMPLETED'] }
        }
      })
    ]);

  return {
    totalOrders,
    totalCustomers,
    totalRevenue: totalRevenue._sum.totalPrice || 0,
    pendingOrders,
    completedOrders,
    employeeCount: outlet._count?.employees || 0
  };
};

export default {
  createOutlet,
  queryOutlets,
  getOutletById,
  updateOutletById,
  deleteOutletById,
  getOutletServices,
  copyServicesFromOutlet,
  getOutletStats
};
