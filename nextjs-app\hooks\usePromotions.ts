import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from '@/components/ui/use-toast';
import { useAuth } from '@/lib/auth-context';
import {
  promotionAPI,
  type Promotion,
  type CreatePromotionRequest,
  type UpdatePromotionRequest,
  type GetPromotionsParams,
  type ValidatePromotionRequest,
} from '@/lib/api/promotions';

// Hook untuk mengambil semua promotions dengan pagination dan filter
export function usePromotions(params?: GetPromotionsParams) {
  const { activeOutlet } = useAuth();

  return useQuery({
    queryKey: ['promotions', activeOutlet?.id, params],
    queryFn: () =>
      activeOutlet
        ? promotionAPI.getPromotions({ ...params, outletId: activeOutlet.id })
        : Promise.reject('No active outlet'),
    enabled: !!activeOutlet,
    staleTime: 5 * 60 * 1000, // 5 menit
  });
}

// Hook untuk mengambil promotion by ID
export function usePromotion(id: number) {
  return useQuery({
    queryKey: ['promotion', id],
    queryFn: () => promotionAPI.getPromotion(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  });
}

// Hook untuk mengambil active promotions untuk dropdown
export function useActivePromotions() {
  const { activeOutlet } = useAuth();

  return useQuery({
    queryKey: ['promotions', 'active', activeOutlet?.id],
    queryFn: () =>
      activeOutlet
        ? promotionAPI.getActivePromotions(activeOutlet.id)
        : Promise.reject('No active outlet'),
    enabled: !!activeOutlet,
    staleTime: 10 * 60 * 1000, // 10 menit
  });
}

// Hook untuk create promotion
export function useCreatePromotion() {
  const queryClient = useQueryClient();
  const { activeOutlet } = useAuth();

  return useMutation({
    mutationFn: (data: Omit<CreatePromotionRequest, 'outletId'>) =>
      promotionAPI.createPromotion({
        ...data,
        outletId: activeOutlet!.id,
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['promotions'] });
      toast({
        title: 'Berhasil',
        description: 'Promosi berhasil dibuat',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error?.response?.data?.message || 'Gagal membuat promosi',
        variant: 'destructive',
      });
    },
  });
}

// Hook untuk update promotion
export function useUpdatePromotion() {
  const queryClient = useQueryClient();
  const { activeOutlet } = useAuth();

  return useMutation({
    mutationFn: ({
      id,
      data,
    }: {
      id: number;
      data: Omit<UpdatePromotionRequest, 'outletId'>;
    }) =>
      promotionAPI.updatePromotion(id, {
        ...data,
        outletId: activeOutlet!.id,
      }),
    onSuccess: (updatedPromotion) => {
      queryClient.invalidateQueries({ queryKey: ['promotions'] });
      queryClient.invalidateQueries({
        queryKey: ['promotion', updatedPromotion.id],
      });
      toast({
        title: 'Berhasil',
        description: 'Promosi berhasil diperbarui',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description:
          error?.response?.data?.message || 'Gagal memperbarui promosi',
        variant: 'destructive',
      });
    },
  });
}

// Hook untuk delete promotion
export function useDeletePromotion() {
  const queryClient = useQueryClient();
  const { activeOutlet } = useAuth();

  return useMutation({
    mutationFn: (id: number) =>
      promotionAPI.deletePromotion(id, activeOutlet!.id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['promotions'] });
      toast({
        title: 'Berhasil',
        description: 'Promosi berhasil dihapus',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description:
          error?.response?.data?.message || 'Gagal menghapus promosi',
        variant: 'destructive',
      });
    },
  });
}

// Hook untuk validate promotion
export function useValidatePromotion() {
  const { activeOutlet } = useAuth();

  return useMutation({
    mutationFn: (data: Omit<ValidatePromotionRequest, 'outletId'>) =>
      promotionAPI.validatePromotion({
        ...data,
        outletId: activeOutlet!.id,
      }),
    onError: (error: any) => {
      toast({
        title: 'Error',
        description:
          error?.response?.data?.message || 'Kode promosi tidak valid',
        variant: 'destructive',
      });
    },
  });
}
