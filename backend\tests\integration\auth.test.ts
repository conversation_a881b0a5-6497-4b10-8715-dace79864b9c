import request from 'supertest';
import { faker } from '@faker-js/faker';
import httpStatus from 'http-status';
import httpMocks from 'node-mocks-http';
import moment from 'moment';
import bcrypt from 'bcryptjs';
import app from '../../src/app';
import config from '../../src/config/config';
import auth from '../../src/middlewares/auth';
import { emailService, tokenService } from '../../src/services';
import ApiError from '../../src/utils/ApiError';
import { setupTestDBOptimized } from '../utils/setupTestDb';
import { describe, beforeEach, test, expect, jest, afterAll } from '@jest/globals';
import {
  userOne,
  admin,
  insertUsers,
  createOutletForOwner,
  createEmployeeWithOutlet,
  password
} from '../fixtures/user.fixture';
import { Role, TokenType, User } from '@prisma/client';
import prisma from '../../src/client';
import { roleRights } from '../../src/config/roles';

// Reduce timeout for faster tests
jest.setTimeout(30000);

// Mock email service globally for all tests
jest.spyOn(emailService.transport, 'sendMail').mockResolvedValue({} as any);

setupTestDBOptimized();

describe('Auth routes', () => {
  describe('POST /v1/auth/register', () => {
    let newOwner: { name?: string; email: string; phone?: string; password: string };
    beforeEach(() => {
      newOwner = {
        name: faker.name.fullName(),
        email: faker.internet.email().toLowerCase(),
        phone: faker.phone.number('08##########'),
        password: 'password1'
      };
    });

    test('should return 201 and successfully register owner if request data is ok', async () => {
      const res = await request(app)
        .post('/v1/auth/register')
        .send(newOwner)
        .expect(httpStatus.CREATED);

      expect(res.body.user).not.toHaveProperty('password');
      expect(res.body.user).toMatchObject({
        id: expect.anything(),
        name: newOwner.name,
        email: newOwner.email,
        phone: newOwner.phone,
        role: Role.OWNER,
        isEmailVerified: false
      });

      const dbUser = await prisma.user.findUnique({ where: { id: res.body.user.id } });
      expect(dbUser).toBeDefined();
      expect(dbUser?.password).not.toBe(newOwner.password);
      expect(dbUser).toMatchObject({
        name: newOwner.name,
        email: newOwner.email,
        phone: newOwner.phone,
        role: Role.OWNER,
        isEmailVerified: false
      });

      expect(res.body.tokens).toEqual({
        access: { token: expect.anything(), expires: expect.anything() },
        refresh: { token: expect.anything(), expires: expect.anything() }
      });
    });

    test('should return 400 error if name is missing', async () => {
      delete newOwner.name;

      await request(app).post('/v1/auth/register').send(newOwner).expect(httpStatus.BAD_REQUEST);
    });

    test('should return 400 error if phone is missing', async () => {
      delete newOwner.phone;

      await request(app).post('/v1/auth/register').send(newOwner).expect(httpStatus.BAD_REQUEST);
    });

    test('should return 400 error if email is invalid', async () => {
      newOwner.email = 'invalidEmail';

      await request(app).post('/v1/auth/register').send(newOwner).expect(httpStatus.BAD_REQUEST);
    });

    test('should return 400 error if email is already used', async () => {
      await insertUsers([userOne]);
      newOwner.email = userOne.email;

      await request(app).post('/v1/auth/register').send(newOwner).expect(httpStatus.BAD_REQUEST);
    });

    test('should return 400 error if phone is already used', async () => {
      await insertUsers([userOne]);
      newOwner.phone = userOne.phone;

      await request(app).post('/v1/auth/register').send(newOwner).expect(httpStatus.BAD_REQUEST);
    });

    test('should return 400 error if password length is less than 8 characters', async () => {
      newOwner.password = 'passwo1';

      await request(app).post('/v1/auth/register').send(newOwner).expect(httpStatus.BAD_REQUEST);
    });

    test('should return 400 error if password does not contain both letters and numbers', async () => {
      newOwner.password = 'password';

      await request(app).post('/v1/auth/register').send(newOwner).expect(httpStatus.BAD_REQUEST);

      newOwner.password = '11111111';

      await request(app).post('/v1/auth/register').send(newOwner).expect(httpStatus.BAD_REQUEST);
    });
  });

  describe('POST /v1/auth/login', () => {
    test('should return 200 and login user if email and password match', async () => {
      await insertUsers([userOne]);
      const loginCredentials = {
        email: userOne.email,
        password: userOne.password
      };

      const res = await request(app)
        .post('/v1/auth/login')
        .send(loginCredentials)
        .expect(httpStatus.OK);

      expect(res.body.user).toMatchObject({
        id: expect.anything(),
        name: userOne.name,
        email: userOne.email,
        phone: userOne.phone,
        role: userOne.role,
        isEmailVerified: userOne.isEmailVerified
      });

      expect(res.body.tokens).toEqual({
        access: { token: expect.anything(), expires: expect.anything() },
        refresh: { token: expect.anything(), expires: expect.anything() }
      });
    });

    test('should return 401 error if there are no users with that email', async () => {
      const loginCredentials = {
        email: userOne.email,
        password: userOne.password
      };

      const res = await request(app)
        .post('/v1/auth/login')
        .send(loginCredentials)
        .expect(httpStatus.UNAUTHORIZED);

      expect(res.body).toEqual({
        code: httpStatus.UNAUTHORIZED,
        message: 'Incorrect email or password'
      });
    });

    test('should return 401 error if password is wrong', async () => {
      await insertUsers([userOne]);
      const loginCredentials = {
        email: userOne.email,
        password: 'wrongPassword1'
      };

      const res = await request(app)
        .post('/v1/auth/login')
        .send(loginCredentials)
        .expect(httpStatus.UNAUTHORIZED);

      expect(res.body).toEqual({
        code: httpStatus.UNAUTHORIZED,
        message: 'Incorrect email or password'
      });
    });
  });

  describe('POST /v1/auth/logout', () => {
    test('should return 204 if refresh token is valid', async () => {
      await insertUsers([userOne]);
      const dbUser = await prisma.user.findUnique({ where: { email: userOne.email } });
      const expires = moment().add(config.jwt.refreshExpirationDays, 'days');
      const refreshToken = tokenService.generateToken(dbUser!.id, expires, TokenType.REFRESH);
      await tokenService.saveToken(refreshToken, dbUser!.id, expires, TokenType.REFRESH);

      await request(app)
        .post('/v1/auth/logout')
        .send({ refreshToken })
        .expect(httpStatus.NO_CONTENT);

      const dbRefreshTokenDoc = await prisma.token.findFirst({
        where: { token: refreshToken, type: TokenType.REFRESH, blacklisted: false }
      });
      expect(dbRefreshTokenDoc).toBe(null);
    });

    test('should return 404 error if refresh token is not found in db', async () => {
      await insertUsers([userOne]);
      const dbUser = await prisma.user.findUnique({ where: { email: userOne.email } });
      const expires = moment().add(config.jwt.refreshExpirationDays, 'days');
      const refreshToken = tokenService.generateToken(dbUser!.id, expires, TokenType.REFRESH);

      await request(app)
        .post('/v1/auth/logout')
        .send({ refreshToken })
        .expect(httpStatus.NOT_FOUND);
    });

    test('should return 404 error if refresh token is blacklisted', async () => {
      await insertUsers([userOne]);
      const dbUser = await prisma.user.findUnique({ where: { email: userOne.email } });
      const expires = moment().add(config.jwt.refreshExpirationDays, 'days');
      const refreshToken = tokenService.generateToken(dbUser!.id, expires, TokenType.REFRESH);
      await tokenService.saveToken(refreshToken, dbUser!.id, expires, TokenType.REFRESH, true);

      await request(app)
        .post('/v1/auth/logout')
        .send({ refreshToken })
        .expect(httpStatus.NOT_FOUND);
    });
  });

  describe('POST /v1/auth/refresh-tokens', () => {
    test('should return 200 and new auth tokens if refresh token is valid', async () => {
      await insertUsers([userOne]);
      const dbUser = await prisma.user.findUnique({ where: { email: userOne.email } });
      const expires = moment().add(config.jwt.refreshExpirationDays, 'days');
      const refreshToken = tokenService.generateToken(dbUser!.id, expires, TokenType.REFRESH);
      await tokenService.saveToken(refreshToken, dbUser!.id, expires, TokenType.REFRESH);

      const res = await request(app)
        .post('/v1/auth/refresh-tokens')
        .send({ refreshToken })
        .expect(httpStatus.OK);

      expect(res.body).toEqual({
        access: { token: expect.anything(), expires: expect.anything() },
        refresh: { token: expect.anything(), expires: expect.anything() }
      });

      const dbRefreshTokenDoc = await prisma.token.findFirst({
        where: { token: res.body.refresh.token, type: TokenType.REFRESH, blacklisted: false }
      });
      expect(dbRefreshTokenDoc).toMatchObject({ type: TokenType.REFRESH, blacklisted: false });

      const dbRefreshTokenCount = await prisma.token.count({
        where: { userId: dbUser!.id, type: TokenType.REFRESH, blacklisted: false }
      });
      expect(dbRefreshTokenCount).toBe(1);
    });

    test('should return 401 error if refresh token is signed using an invalid secret', async () => {
      await insertUsers([userOne]);
      const dbUser = await prisma.user.findUnique({ where: { email: userOne.email } });
      const expires = moment().add(config.jwt.refreshExpirationDays, 'days');
      const refreshToken = tokenService.generateToken(
        dbUser!.id,
        expires,
        TokenType.REFRESH,
        'invalidSecret'
      );
      await tokenService.saveToken(refreshToken, dbUser!.id, expires, TokenType.REFRESH);

      await request(app)
        .post('/v1/auth/refresh-tokens')
        .send({ refreshToken })
        .expect(httpStatus.UNAUTHORIZED);
    });

    test('should return 401 error if refresh token is not found in db', async () => {
      await insertUsers([userOne]);
      const dbUser = await prisma.user.findUnique({ where: { email: userOne.email } });
      const expires = moment().add(config.jwt.refreshExpirationDays, 'days');
      const refreshToken = tokenService.generateToken(dbUser!.id, expires, TokenType.REFRESH);

      await request(app)
        .post('/v1/auth/refresh-tokens')
        .send({ refreshToken })
        .expect(httpStatus.UNAUTHORIZED);
    });

    test('should return 401 error if refresh token is blacklisted', async () => {
      await insertUsers([userOne]);
      const dbUser = await prisma.user.findUnique({ where: { email: userOne.email } });
      const expires = moment().add(config.jwt.refreshExpirationDays, 'days');
      const refreshToken = tokenService.generateToken(dbUser!.id, expires, TokenType.REFRESH);
      await tokenService.saveToken(refreshToken, dbUser!.id, expires, TokenType.REFRESH, true);

      await request(app)
        .post('/v1/auth/refresh-tokens')
        .send({ refreshToken })
        .expect(httpStatus.UNAUTHORIZED);
    });

    test('should return 401 error if refresh token is expired', async () => {
      await insertUsers([userOne]);
      const dbUser = await prisma.user.findUnique({ where: { email: userOne.email } });
      const expires = moment().subtract(1, 'minutes');
      const refreshToken = tokenService.generateToken(dbUser!.id, expires, TokenType.REFRESH);
      await tokenService.saveToken(refreshToken, dbUser!.id, expires, TokenType.REFRESH);

      await request(app)
        .post('/v1/auth/refresh-tokens')
        .send({ refreshToken })
        .expect(httpStatus.UNAUTHORIZED);
    });

    test('should return 401 error if user is not found', async () => {
      const expires = moment().add(config.jwt.refreshExpirationDays, 'days');
      const refreshToken = tokenService.generateToken(999, expires, TokenType.REFRESH);
      // Don't save token to database since user doesn't exist

      await request(app)
        .post('/v1/auth/refresh-tokens')
        .send({ refreshToken })
        .expect(httpStatus.UNAUTHORIZED);
    });
  });

  describe('POST /v1/auth/forgot-password', () => {
    test('should return 204 and send reset password email to the user', async () => {
      await insertUsers([userOne]);
      const dbUser = await prisma.user.findUnique({ where: { email: userOne.email } });
      const sendResetPasswordEmailSpy = jest.spyOn(emailService, 'sendResetPasswordEmail');

      await request(app)
        .post('/v1/auth/forgot-password')
        .send({ email: userOne.email })
        .expect(httpStatus.NO_CONTENT);

      expect(sendResetPasswordEmailSpy).toHaveBeenCalledWith(userOne.email, expect.any(String));
      const resetPasswordToken = sendResetPasswordEmailSpy.mock.calls[0][1];
      const dbResetPasswordTokenDoc = await prisma.token.findFirst({
        where: { token: resetPasswordToken, userId: dbUser!.id }
      });
      expect(dbResetPasswordTokenDoc).toBeDefined();
    });

    test('should return 400 if email is missing', async () => {
      await request(app).post('/v1/auth/forgot-password').send().expect(httpStatus.BAD_REQUEST);
    });

    test('should return 404 if email does not belong to any user', async () => {
      await request(app)
        .post('/v1/auth/forgot-password')
        .send({ email: userOne.email })
        .expect(httpStatus.NOT_FOUND);
    });
  });

  describe('POST /v1/auth/reset-password', () => {
    test('should return 204 and reset the password', async () => {
      await insertUsers([userOne]);
      const dbUser = await prisma.user.findUnique({ where: { email: userOne.email } });
      const expires = moment().add(config.jwt.resetPasswordExpirationMinutes, 'minutes');
      const resetPasswordToken = tokenService.generateToken(
        dbUser!.id,
        expires,
        TokenType.RESET_PASSWORD
      );
      await tokenService.saveToken(
        resetPasswordToken,
        dbUser!.id,
        expires,
        TokenType.RESET_PASSWORD
      );
      const newPassword = 'password2';

      await request(app)
        .post('/v1/auth/reset-password')
        .query({ token: resetPasswordToken })
        .send({ password: newPassword })
        .expect(httpStatus.NO_CONTENT);

      const dbUserUpdated = await prisma.user.findUnique({ where: { id: dbUser!.id } });
      const isPasswordMatch = await bcrypt.compare(newPassword, dbUserUpdated!.password);
      expect(isPasswordMatch).toBe(true);

      const dbResetPasswordTokenCount = await prisma.token.count({
        where: { userId: dbUser!.id, type: TokenType.RESET_PASSWORD }
      });
      expect(dbResetPasswordTokenCount).toBe(0);
    });

    test('should return 400 if reset password token is missing', async () => {
      await insertUsers([userOne]);

      await request(app)
        .post('/v1/auth/reset-password')
        .send({ password: 'password2' })
        .expect(httpStatus.BAD_REQUEST);
    });

    test('should return 401 if reset password token is blacklisted', async () => {
      await insertUsers([userOne]);
      const dbUser = await prisma.user.findUnique({ where: { email: userOne.email } });
      const expires = moment().add(config.jwt.resetPasswordExpirationMinutes, 'minutes');
      const resetPasswordToken = tokenService.generateToken(
        dbUser!.id,
        expires,
        TokenType.RESET_PASSWORD
      );
      await tokenService.saveToken(
        resetPasswordToken,
        dbUser!.id,
        expires,
        TokenType.RESET_PASSWORD,
        true
      );

      await request(app)
        .post('/v1/auth/reset-password')
        .query({ token: resetPasswordToken })
        .send({ password: 'password2' })
        .expect(httpStatus.UNAUTHORIZED);
    });

    test('should return 401 if reset password token is expired', async () => {
      await insertUsers([userOne]);
      const dbUser = await prisma.user.findUnique({ where: { email: userOne.email } });
      const expires = moment().subtract(1, 'minutes');
      const resetPasswordToken = tokenService.generateToken(
        dbUser!.id,
        expires,
        TokenType.RESET_PASSWORD
      );
      await tokenService.saveToken(
        resetPasswordToken,
        dbUser!.id,
        expires,
        TokenType.RESET_PASSWORD
      );

      await request(app)
        .post('/v1/auth/reset-password')
        .query({ token: resetPasswordToken })
        .send({ password: 'password2' })
        .expect(httpStatus.UNAUTHORIZED);
    });

    test('should return 401 if user is not found', async () => {
      const expires = moment().add(config.jwt.resetPasswordExpirationMinutes, 'minutes');
      const resetPasswordToken = tokenService.generateToken(999, expires, TokenType.RESET_PASSWORD);
      // Don't save token to database since user doesn't exist

      await request(app)
        .post('/v1/auth/reset-password')
        .query({ token: resetPasswordToken })
        .send({ password: 'password2' })
        .expect(httpStatus.UNAUTHORIZED);
    });

    test('should return 400 if password is missing or invalid', async () => {
      await insertUsers([userOne]);
      const dbUser = await prisma.user.findUnique({ where: { email: userOne.email } });
      const expires = moment().add(config.jwt.resetPasswordExpirationMinutes, 'minutes');
      const resetPasswordToken = tokenService.generateToken(
        dbUser!.id,
        expires,
        TokenType.RESET_PASSWORD
      );
      await tokenService.saveToken(
        resetPasswordToken,
        dbUser!.id,
        expires,
        TokenType.RESET_PASSWORD
      );

      await request(app)
        .post('/v1/auth/reset-password')
        .query({ token: resetPasswordToken })
        .send()
        .expect(httpStatus.BAD_REQUEST);

      await request(app)
        .post('/v1/auth/reset-password')
        .query({ token: resetPasswordToken })
        .send({ password: 'short1' })
        .expect(httpStatus.BAD_REQUEST);

      await request(app)
        .post('/v1/auth/reset-password')
        .query({ token: resetPasswordToken })
        .send({ password: 'password' })
        .expect(httpStatus.BAD_REQUEST);

      await request(app)
        .post('/v1/auth/reset-password')
        .query({ token: resetPasswordToken })
        .send({ password: '11111111' })
        .expect(httpStatus.BAD_REQUEST);
    });
  });

  describe('POST /v1/auth/send-verification-email', () => {
    test('should return 204 and send verification email to the user', async () => {
      await insertUsers([userOne]);
      const dbUser = await prisma.user.findUnique({ where: { email: userOne.email } });
      const userOneAccessToken = await tokenService.generateAuthTokens({ id: dbUser!.id });
      const sendVerificationEmailSpy = jest.spyOn(emailService, 'sendVerificationEmail');

      const res = await request(app)
        .post('/v1/auth/send-verification-email?method=token')
        .set('Authorization', `Bearer ${userOneAccessToken.access.token}`)
        .expect(httpStatus.OK);

      expect(res.body).toEqual({
        message: 'Verification email sent successfully',
        method: 'token'
      });

      expect(sendVerificationEmailSpy).toHaveBeenCalledWith(userOne.email, expect.any(String));
      const verifyEmailToken = sendVerificationEmailSpy.mock.calls[0][1];
      const dbVerifyEmailToken = await prisma.token.findFirst({
        where: { token: verifyEmailToken, userId: dbUser!.id }
      });

      expect(dbVerifyEmailToken).toBeDefined();
    });

    test('should return 401 error if access token is missing', async () => {
      await request(app)
        .post('/v1/auth/send-verification-email')
        .send()
        .expect(httpStatus.UNAUTHORIZED);
    });
  });

  describe('POST /v1/auth/verify-email', () => {
    test('should return 200 and verify email with JWT token', async () => {
      await insertUsers([userOne]);
      const dbUser = await prisma.user.findUnique({ where: { email: userOne.email } });
      const expires = moment().add(config.jwt.verifyEmailExpirationMinutes, 'minutes');
      const verifyEmailToken = tokenService.generateToken(
        dbUser!.id,
        expires,
        TokenType.VERIFY_EMAIL
      );
      await tokenService.saveToken(verifyEmailToken, dbUser!.id, expires, TokenType.VERIFY_EMAIL);

      const res = await request(app)
        .post('/v1/auth/verify-email')
        .send({ token: verifyEmailToken })
        .expect(httpStatus.OK);

      expect(res.body).toEqual({
        message: 'Email verified successfully',
        method: 'token'
      });

      const dbUserUpdated = await prisma.user.findUnique({ where: { id: dbUser!.id } });

      expect(dbUserUpdated!.isEmailVerified).toBe(true);

      const dbVerifyEmailTokenCount = await prisma.token.count({
        where: { userId: dbUser!.id, type: TokenType.VERIFY_EMAIL }
      });
      expect(dbVerifyEmailTokenCount).toBe(0);
    });

    test('should return 400 if verify email token is missing', async () => {
      await insertUsers([userOne]);

      // Missing both token and code should return 400
      await request(app).post('/v1/auth/verify-email').send().expect(httpStatus.BAD_REQUEST);
    });

    test('should return 401 if verify email token is blacklisted', async () => {
      await insertUsers([userOne]);
      const dbUser = await prisma.user.findUnique({ where: { email: userOne.email } });
      const expires = moment().add(config.jwt.verifyEmailExpirationMinutes, 'minutes');
      const verifyEmailToken = tokenService.generateToken(
        dbUser!.id,
        expires,
        TokenType.VERIFY_EMAIL
      );
      await tokenService.saveToken(
        verifyEmailToken,
        dbUser!.id,
        expires,
        TokenType.VERIFY_EMAIL,
        true
      );

      await request(app)
        .post('/v1/auth/verify-email')
        .send({ token: verifyEmailToken })
        .expect(httpStatus.UNAUTHORIZED);
    });

    test('should return 401 if verify email token is expired', async () => {
      await insertUsers([userOne]);
      const dbUser = await prisma.user.findUnique({ where: { email: userOne.email } });
      const expires = moment().subtract(1, 'minutes');
      const verifyEmailToken = tokenService.generateToken(
        dbUser!.id,
        expires,
        TokenType.VERIFY_EMAIL
      );
      await tokenService.saveToken(verifyEmailToken, dbUser!.id, expires, TokenType.VERIFY_EMAIL);

      await request(app)
        .post('/v1/auth/verify-email')
        .send({ token: verifyEmailToken })
        .expect(httpStatus.UNAUTHORIZED);
    });

    test('should return 401 if user is not found', async () => {
      const expires = moment().add(config.jwt.verifyEmailExpirationMinutes, 'minutes');
      const verifyEmailToken = tokenService.generateToken(999, expires, TokenType.VERIFY_EMAIL);
      // Don't save token to database since user doesn't exist

      await request(app)
        .post('/v1/auth/verify-email')
        .send({ token: verifyEmailToken })
        .expect(httpStatus.UNAUTHORIZED);
    });

    test('should return 400 if both token and code are provided', async () => {
      const res = await request(app)
        .post('/v1/auth/verify-email')
        .send({
          token: 'some-token',
          code: '123456'
        })
        .expect(httpStatus.BAD_REQUEST);

      expect(res.body.message).toContain(
        'Please provide either verification token or code, not both'
      );
    });

    test('should return 200 and verify email with OTP code', async () => {
      await insertUsers([userOne]);

      // Create OTP manually for testing
      const emailVerification = await prisma.emailVerification.create({
        data: {
          email: userOne.email,
          code: '123456',
          expiresAt: new Date(Date.now() + 10 * 60 * 1000), // 10 minutes
          isUsed: false
        }
      });

      const res = await request(app)
        .post('/v1/auth/verify-email')
        .send({ code: '123456' })
        .expect(httpStatus.OK);

      expect(res.body).toEqual({
        message: 'Email verified successfully',
        method: 'otp'
      });

      // Check if user is email verified
      const updatedUser = await prisma.user.findUnique({
        where: { email: userOne.email }
      });

      expect(updatedUser?.isEmailVerified).toBe(true);

      // Check if OTP is marked as used
      const usedOtp = await prisma.emailVerification.findUnique({
        where: { id: emailVerification.id }
      });

      expect(usedOtp?.isUsed).toBe(true);
    });
  });
});

describe('Auth middleware', () => {
  test('should call next with no errors if access token is valid', async () => {
    await insertUsers([userOne]);
    const dbUser = (await prisma.user.findUnique({ where: { email: userOne.email } })) as User;
    const userOneAccessToken = tokenService.generateToken(
      dbUser.id,
      moment().add(config.jwt.accessExpirationMinutes, 'minutes'),
      TokenType.ACCESS
    );
    const req = httpMocks.createRequest({
      headers: { Authorization: `Bearer ${userOneAccessToken}` }
    });
    const next = jest.fn();

    await auth()(req, httpMocks.createResponse(), next);

    expect(next).toHaveBeenCalledWith();
    expect(req.user).toMatchObject({
      id: dbUser.id,
      name: dbUser.name,
      email: dbUser.email,
      role: dbUser.role
    });
  });

  test('should call next with unauthorized error if access token is not found in header', async () => {
    const req = httpMocks.createRequest();
    const next = jest.fn();

    await auth()(req, httpMocks.createResponse(), next);

    expect(next).toHaveBeenCalledWith(expect.any(ApiError));
    expect(next).toHaveBeenCalledWith(
      expect.objectContaining({
        statusCode: httpStatus.UNAUTHORIZED,
        message: 'Please authenticate'
      })
    );
  });

  test('should call next with unauthorized error if access token is not a valid jwt token', async () => {
    const req = httpMocks.createRequest({ headers: { Authorization: 'Bearer randomToken' } });
    const next = jest.fn();

    await auth()(req, httpMocks.createResponse(), next);

    expect(next).toHaveBeenCalledWith(expect.any(ApiError));
    expect(next).toHaveBeenCalledWith(
      expect.objectContaining({
        statusCode: httpStatus.UNAUTHORIZED,
        message: 'Please authenticate'
      })
    );
  });

  test('should call next with unauthorized error if the token is not an access token', async () => {
    await insertUsers([userOne]);
    const dbUser = (await prisma.user.findUnique({ where: { email: userOne.email } })) as User;
    const expires = moment().add(config.jwt.refreshExpirationDays, 'days');
    const refreshToken = tokenService.generateToken(dbUser.id, expires, TokenType.REFRESH);
    const req = httpMocks.createRequest({ headers: { Authorization: `Bearer ${refreshToken}` } });
    const next = jest.fn();

    await auth()(req, httpMocks.createResponse(), next);

    expect(next).toHaveBeenCalledWith(expect.any(ApiError));
    expect(next).toHaveBeenCalledWith(
      expect.objectContaining({
        statusCode: httpStatus.UNAUTHORIZED,
        message: 'Please authenticate'
      })
    );
  });

  test('should call next with unauthorized error if access token is generated with an invalid secret', async () => {
    await insertUsers([userOne]);
    const dbUser = (await prisma.user.findUnique({ where: { email: userOne.email } })) as User;
    const tokenExpires = moment().add(config.jwt.accessExpirationMinutes, 'minutes');
    const accessToken = tokenService.generateToken(
      dbUser.id,
      tokenExpires,
      TokenType.ACCESS,
      'invalidSecret'
    );
    const req = httpMocks.createRequest({ headers: { Authorization: `Bearer ${accessToken}` } });
    const next = jest.fn();

    await auth()(req, httpMocks.createResponse(), next);

    expect(next).toHaveBeenCalledWith(expect.any(ApiError));
    expect(next).toHaveBeenCalledWith(
      expect.objectContaining({
        statusCode: httpStatus.UNAUTHORIZED,
        message: 'Please authenticate'
      })
    );
  });

  test('should call next with unauthorized error if access token is expired', async () => {
    await insertUsers([userOne]);
    const dbUser = (await prisma.user.findUnique({ where: { email: userOne.email } })) as User;
    const tokenExpires = moment().subtract(1, 'minutes');
    const accessToken = tokenService.generateToken(dbUser.id, tokenExpires, TokenType.ACCESS);
    const req = httpMocks.createRequest({ headers: { Authorization: `Bearer ${accessToken}` } });
    const next = jest.fn();

    await auth()(req, httpMocks.createResponse(), next);

    expect(next).toHaveBeenCalledWith(expect.any(ApiError));
    expect(next).toHaveBeenCalledWith(
      expect.objectContaining({
        statusCode: httpStatus.UNAUTHORIZED,
        message: 'Please authenticate'
      })
    );
  });

  test('should call next with unauthorized error if user is not found', async () => {
    const userOneAccessToken = tokenService.generateToken(
      999,
      moment().add(config.jwt.accessExpirationMinutes, 'minutes'),
      TokenType.ACCESS
    );
    const req = httpMocks.createRequest({
      headers: { Authorization: `Bearer ${userOneAccessToken}` }
    });
    const next = jest.fn();

    await auth()(req, httpMocks.createResponse(), next);

    expect(next).toHaveBeenCalledWith(expect.any(ApiError));
    expect(next).toHaveBeenCalledWith(
      expect.objectContaining({
        statusCode: httpStatus.UNAUTHORIZED,
        message: 'Please authenticate'
      })
    );
  });

  test('should call next with no errors if user has required rights', async () => {
    await insertUsers([admin]);
    const dbAdmin = (await prisma.user.findUnique({ where: { email: admin.email } })) as User;
    const adminAccessToken = tokenService.generateToken(
      dbAdmin.id,
      moment().add(config.jwt.accessExpirationMinutes, 'minutes'),
      TokenType.ACCESS
    );
    const req = httpMocks.createRequest({
      headers: { Authorization: `Bearer ${adminAccessToken}` },
      params: { userId: dbAdmin.id }
    });
    const next = jest.fn();

    await auth(...(roleRights.get(Role.ADMIN) as string[]))(req, httpMocks.createResponse(), next);

    expect(next).toHaveBeenCalledWith();
  });

  test('should call next with forbidden error if user does not have required rights', async () => {
    await insertUsers([userOne]);
    const dbUser = (await prisma.user.findUnique({ where: { email: userOne.email } })) as User;
    const userOneAccessToken = tokenService.generateToken(
      dbUser.id,
      moment().add(config.jwt.accessExpirationMinutes, 'minutes'),
      TokenType.ACCESS
    );
    const req = httpMocks.createRequest({
      headers: { Authorization: `Bearer ${userOneAccessToken}` },
      params: { userId: 999 } // Different user ID to trigger forbidden
    });
    const next = jest.fn();

    await auth(...(roleRights.get(Role.ADMIN) as string[]))(req, httpMocks.createResponse(), next);

    expect(next).toHaveBeenCalledWith(expect.any(ApiError));
    expect(next).toHaveBeenCalledWith(
      expect.objectContaining({ statusCode: httpStatus.FORBIDDEN, message: 'Forbidden' })
    );
  });

  test('should call next with no errors if user does not have required rights but is accessing own resource', async () => {
    await insertUsers([userOne]);
    const dbUser = (await prisma.user.findUnique({ where: { email: userOne.email } })) as User;
    const userOneAccessToken = tokenService.generateToken(
      dbUser.id,
      moment().add(config.jwt.accessExpirationMinutes, 'minutes'),
      TokenType.ACCESS
    );
    const req = httpMocks.createRequest({
      headers: { Authorization: `Bearer ${userOneAccessToken}` },
      params: { userId: dbUser.id.toString() }
    });
    const next = jest.fn();

    await auth(...(roleRights.get(Role.ADMIN) as string[]))(req, httpMocks.createResponse(), next);

    expect(next).toHaveBeenCalledWith();
  });
});

describe('Verification Requirement Feature', () => {
  // Mock config untuk testing
  const originalVerificationConfig = config.verification;

  beforeEach(() => {
    // Reset config untuk setiap test
    config.verification = {
      requireEmailVerification: true,
      requirePhoneVerification: false,
      enabled: true,
      autoSendEmailOnRegister: false,
      phoneVerificationExpirationMinutes: 10,
      emailOtpExpirationMinutes: 10
    };
  });

  afterAll(() => {
    // Restore original config
    config.verification = originalVerificationConfig;
  });

  describe('Config Management', () => {
    test('should have verification config with default values', () => {
      expect(config.verification).toBeDefined();
      expect(config.verification.enabled).toBe(true);
      expect(config.verification.requireEmailVerification).toBe(true);
      expect(config.verification.requirePhoneVerification).toBe(false);
    });

    test('should be able to disable verification requirement', () => {
      config.verification.enabled = false;
      expect(config.verification.enabled).toBe(false);
    });
  });

  describe('Email Verification Requirement', () => {
    test('should block unverified user from accessing protected routes when verification is required', async () => {
      // Enable verification requirement
      config.verification.enabled = true;

      // Use unique user data to avoid conflicts
      const uniqueUser = {
        ...userOne,
        email: `unverified.${Math.floor(Math.random() * 100000)}@test.com`,
        phone: `081${Math.floor(Math.random() * 100000000)
          .toString()
          .padStart(8, '0')}`,
        isEmailVerified: false // Explicitly set to false
      };

      await insertUsers([uniqueUser]);

      // Get user from database to get the ID
      const dbUser = await prisma.user.findUnique({ where: { email: uniqueUser.email } });

      // Create outlet for testing protected route
      const outlet = await createOutletForOwner(dbUser!.id);

      // Get access token
      const accessTokenExpires = moment().add(config.jwt.accessExpirationMinutes, 'minutes');
      const accessToken = tokenService.generateToken(
        dbUser!.id,
        accessTokenExpires,
        TokenType.ACCESS
      );

      // User belum verified (isEmailVerified: false)
      const res = await request(app)
        .get('/v1/outlets')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(httpStatus.FORBIDDEN);

      expect(res.body).toEqual({
        code: httpStatus.FORBIDDEN,
        message: 'Email verification required to access this feature'
      });
    });

    test('should allow verified user to access protected routes', async () => {
      // Insert user dengan isEmailVerified: true
      const verifiedUser = { ...userOne, isEmailVerified: true };
      await insertUsers([verifiedUser]);

      // Get user dari database untuk memastikan data benar
      const dbUser = await prisma.user.findUnique({
        where: { email: userOne.email },
        select: {
          id: true,
          email: true,
          isEmailVerified: true,
          isPhoneVerified: true,
          role: true
        }
      });

      // Create outlet for testing
      const outlet = await createOutletForOwner(dbUser!.id);

      // Get access token
      const accessTokenExpires = moment().add(config.jwt.accessExpirationMinutes, 'minutes');
      const accessToken = tokenService.generateToken(
        dbUser!.id,
        accessTokenExpires,
        TokenType.ACCESS
      );

      const res = await request(app)
        .get('/v1/outlets')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(httpStatus.OK);

      expect(res.body).toHaveProperty('results');
    });

    test('should allow unverified user when verification requirement is disabled', async () => {
      config.verification.enabled = false;
      await insertUsers([userOne]); // isEmailVerified: false

      // Get user from database to get the ID
      const dbUser = await prisma.user.findUnique({ where: { email: userOne.email } });

      // Create outlet for testing
      const outlet = await createOutletForOwner(dbUser!.id);

      // Get access token
      const accessTokenExpires = moment().add(config.jwt.accessExpirationMinutes, 'minutes');
      const accessToken = tokenService.generateToken(
        dbUser!.id,
        accessTokenExpires,
        TokenType.ACCESS
      );

      const res = await request(app)
        .get('/v1/outlets')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(httpStatus.OK);

      expect(res.body).toHaveProperty('results');
    });

    test('should allow admin to access routes regardless of verification status', async () => {
      // Insert admin dan beberapa users untuk testing
      await insertUsers([
        { ...admin, isEmailVerified: false },
        { ...userOne, isEmailVerified: false }
      ]);

      // Get access token for admin
      const dbUser = await prisma.user.findUnique({
        where: { email: admin.email },
        select: {
          id: true,
          email: true,
          isEmailVerified: true,
          isPhoneVerified: true,
          role: true
        }
      });

      const accessTokenExpires = moment().add(config.jwt.accessExpirationMinutes, 'minutes');
      const accessToken = tokenService.generateToken(
        dbUser!.id,
        accessTokenExpires,
        TokenType.ACCESS
      );

      const res = await request(app)
        .get('/v1/users')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(httpStatus.OK);

      expect(res.body).toHaveProperty('results');
      expect(res.body.results).toBeInstanceOf(Array);
      expect(res.body.results.length).toBeGreaterThan(0);
    });
  });

  describe('Phone Verification Requirement', () => {
    test('should block user without phone verification when phone verification is required', async () => {
      config.verification.requirePhoneVerification = true;
      // Pastikan email sudah verified tapi phone belum
      await insertUsers([
        {
          ...userOne,
          isEmailVerified: true, // Email harus verified dulu
          isPhoneVerified: false
        }
      ]);

      // Get user from database to get the ID
      const dbUser = await prisma.user.findUnique({
        where: { email: userOne.email },
        select: {
          id: true,
          email: true,
          isEmailVerified: true,
          isPhoneVerified: true,
          role: true
        }
      });

      // Create outlet for testing
      const outlet = await createOutletForOwner(dbUser!.id);

      // Get access token
      const accessTokenExpires = moment().add(config.jwt.accessExpirationMinutes, 'minutes');
      const accessToken = tokenService.generateToken(
        dbUser!.id,
        accessTokenExpires,
        TokenType.ACCESS
      );

      const res = await request(app)
        .get('/v1/outlets')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(httpStatus.FORBIDDEN);

      expect(res.body).toEqual({
        code: httpStatus.FORBIDDEN,
        message: 'Phone verification required to access this feature'
      });
    });

    test('should allow user with both email and phone verified', async () => {
      config.verification.requirePhoneVerification = true;
      await insertUsers([
        {
          ...userOne,
          isEmailVerified: true,
          isPhoneVerified: true
        }
      ]);

      // Get user from database to get the ID
      const dbUser = await prisma.user.findUnique({
        where: { email: userOne.email },
        select: {
          id: true,
          email: true,
          isEmailVerified: true,
          isPhoneVerified: true,
          role: true
        }
      });

      // Create outlet for testing
      const outlet = await createOutletForOwner(dbUser!.id);

      // Get access token
      const accessTokenExpires = moment().add(config.jwt.accessExpirationMinutes, 'minutes');
      const accessToken = tokenService.generateToken(
        dbUser!.id,
        accessTokenExpires,
        TokenType.ACCESS
      );

      const res = await request(app)
        .get('/v1/outlets')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(httpStatus.OK);

      expect(res.body).toHaveProperty('results');
    });
  });

  describe('Phone Verification Flow', () => {
    test('should send phone verification code', async () => {
      await insertUsers([userOne]);

      // Get access token
      const dbUser = await prisma.user.findUnique({ where: { email: userOne.email } });
      const accessTokenExpires = moment().add(config.jwt.accessExpirationMinutes, 'minutes');
      const accessToken = tokenService.generateToken(
        dbUser!.id,
        accessTokenExpires,
        TokenType.ACCESS
      );

      const res = await request(app)
        .post('/v1/auth/send-phone-verification')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({ phone: userOne.phone })
        .expect(httpStatus.NO_CONTENT);

      // Check if verification code is saved in database
      const verificationCode = await prisma.phoneVerification.findFirst({
        where: {
          phone: userOne.phone,
          isUsed: false
        }
      });

      expect(verificationCode).toBeDefined();
      expect(verificationCode?.code).toHaveLength(6);
      expect(verificationCode?.expiresAt).toBeInstanceOf(Date);
    });

    test('should verify phone with correct code', async () => {
      await insertUsers([userOne]);

      // Get access token
      const dbUser = await prisma.user.findUnique({ where: { email: userOne.email } });
      const accessTokenExpires = moment().add(config.jwt.accessExpirationMinutes, 'minutes');
      const accessToken = tokenService.generateToken(
        dbUser!.id,
        accessTokenExpires,
        TokenType.ACCESS
      );

      // Create verification code
      const verificationCode = await prisma.phoneVerification.create({
        data: {
          phone: userOne.phone,
          code: '123456',
          expiresAt: new Date(Date.now() + 10 * 60 * 1000), // 10 minutes
          isUsed: false
        }
      });

      const res = await request(app)
        .post('/v1/auth/verify-phone')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          phone: userOne.phone,
          code: '123456'
        })
        .expect(httpStatus.NO_CONTENT);

      // Check if user is phone verified
      const updatedUser = await prisma.user.findUnique({
        where: { id: dbUser!.id }
      });

      expect(updatedUser?.isPhoneVerified).toBe(true);

      // Check if verification code is marked as used
      const usedCode = await prisma.phoneVerification.findUnique({
        where: { id: verificationCode.id }
      });

      expect(usedCode?.isUsed).toBe(true);
    });

    test('should reject invalid phone verification code', async () => {
      await insertUsers([userOne]);

      // Get access token
      const dbUser = await prisma.user.findUnique({ where: { email: userOne.email } });
      const accessTokenExpires = moment().add(config.jwt.accessExpirationMinutes, 'minutes');
      const accessToken = tokenService.generateToken(
        dbUser!.id,
        accessTokenExpires,
        TokenType.ACCESS
      );

      const res = await request(app)
        .post('/v1/auth/verify-phone')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          phone: userOne.phone,
          code: '999999'
        })
        .expect(httpStatus.BAD_REQUEST);

      expect(res.body).toEqual({
        code: httpStatus.BAD_REQUEST,
        message: 'Invalid or expired verification code'
      });
    });

    test('should reject expired phone verification code', async () => {
      await insertUsers([userOne]);

      // Get access token
      const dbUser = await prisma.user.findUnique({ where: { email: userOne.email } });
      const accessTokenExpires = moment().add(config.jwt.accessExpirationMinutes, 'minutes');
      const accessToken = tokenService.generateToken(
        dbUser!.id,
        accessTokenExpires,
        TokenType.ACCESS
      );

      // Create expired verification code
      await prisma.phoneVerification.create({
        data: {
          phone: userOne.phone,
          code: '123456',
          expiresAt: new Date(Date.now() - 1000), // expired
          isUsed: false
        }
      });

      const res = await request(app)
        .post('/v1/auth/verify-phone')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          phone: userOne.phone,
          code: '123456'
        })
        .expect(httpStatus.BAD_REQUEST);

      expect(res.body).toEqual({
        code: httpStatus.BAD_REQUEST,
        message: 'Invalid or expired verification code'
      });
    });
  });

  describe('Auto-send Verification on Registration', () => {
    test('should automatically send email verification when user registers and auto-send is enabled', async () => {
      config.verification.autoSendEmailOnRegister = true;

      const newUser = {
        name: 'Test User',
        email: '<EMAIL>',
        phone: '081234567890',
        password: 'password1'
      };

      const res = await request(app)
        .post('/v1/auth/register')
        .send(newUser)
        .expect(httpStatus.CREATED);

      expect(res.body.user).toMatchObject({
        name: newUser.name,
        email: newUser.email,
        phone: newUser.phone,
        isEmailVerified: false
      });

      // Check if verification token is created
      const verificationToken = await prisma.token.findFirst({
        where: {
          userId: res.body.user.id,
          type: 'VERIFY_EMAIL'
        }
      });

      expect(verificationToken).toBeDefined();
    });

    test('should not send email verification when auto-send is disabled', async () => {
      config.verification.autoSendEmailOnRegister = false;

      const newUser = {
        name: 'Test User',
        email: '<EMAIL>',
        phone: '081234567890',
        password: 'password1'
      };

      const res = await request(app)
        .post('/v1/auth/register')
        .send(newUser)
        .expect(httpStatus.CREATED);

      // Check if verification token is NOT created
      const verificationToken = await prisma.token.findFirst({
        where: {
          userId: res.body.user.id,
          type: 'VERIFY_EMAIL'
        }
      });

      expect(verificationToken).toBeNull();
    });
  });
});

describe('Email Verification API (Token & OTP)', () => {
  describe('Send Email Verification', () => {
    test('should return 200 and send JWT token when method=token', async () => {
      await insertUsers([userOne]);

      // Get access token
      const dbUser = await prisma.user.findUnique({ where: { email: userOne.email } });
      const accessTokenExpires = moment().add(config.jwt.accessExpirationMinutes, 'minutes');
      const accessToken = tokenService.generateToken(
        dbUser!.id,
        accessTokenExpires,
        TokenType.ACCESS
      );

      const res = await request(app)
        .post('/v1/auth/send-verification-email?method=token')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(httpStatus.OK);

      expect(res.body).toEqual({
        message: 'Verification email sent successfully',
        method: 'token'
      });

      // Check if JWT token is created
      const verificationToken = await prisma.token.findFirst({
        where: {
          userId: dbUser!.id,
          type: TokenType.VERIFY_EMAIL
        }
      });

      expect(verificationToken).toBeDefined();
    });

    test('should return 200 and send OTP when method=otp', async () => {
      await insertUsers([userOne]);

      // Get access token
      const dbUser = await prisma.user.findUnique({ where: { email: userOne.email } });
      const accessTokenExpires = moment().add(config.jwt.accessExpirationMinutes, 'minutes');
      const accessToken = tokenService.generateToken(
        dbUser!.id,
        accessTokenExpires,
        TokenType.ACCESS
      );

      const res = await request(app)
        .post('/v1/auth/send-verification-email?method=otp')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(httpStatus.OK);

      expect(res.body).toEqual({
        message: 'Verification code sent successfully',
        method: 'otp'
      });

      // Check if OTP is created
      const emailVerification = await prisma.emailVerification.findFirst({
        where: {
          email: userOne.email,
          isUsed: false
        }
      });

      expect(emailVerification).toBeDefined();
      expect(emailVerification?.code).toHaveLength(6);
      expect(emailVerification?.code).toMatch(/^\d{6}$/);
    });

    test('should return 400 if method parameter is missing', async () => {
      await insertUsers([userOne]);

      const dbUser = await prisma.user.findUnique({ where: { email: userOne.email } });
      const accessTokenExpires = moment().add(config.jwt.accessExpirationMinutes, 'minutes');
      const accessToken = tokenService.generateToken(
        dbUser!.id,
        accessTokenExpires,
        TokenType.ACCESS
      );

      const res = await request(app)
        .post('/v1/auth/send-verification-email')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(httpStatus.BAD_REQUEST);

      expect(res.body).toEqual({
        code: httpStatus.BAD_REQUEST,
        message: '"method" is required'
      });
    });

    test('should return 400 if method parameter is invalid', async () => {
      await insertUsers([userOne]);

      const dbUser = await prisma.user.findUnique({ where: { email: userOne.email } });
      const accessTokenExpires = moment().add(config.jwt.accessExpirationMinutes, 'minutes');
      const accessToken = tokenService.generateToken(
        dbUser!.id,
        accessTokenExpires,
        TokenType.ACCESS
      );

      const res = await request(app)
        .post('/v1/auth/send-verification-email?method=invalid')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(httpStatus.BAD_REQUEST);

      expect(res.body.message).toContain('"method" must be one of [token, otp]');
    });

    test('should return 401 error if access token is missing', async () => {
      await request(app)
        .post('/v1/auth/send-verification-email?method=token')
        .expect(httpStatus.UNAUTHORIZED);
    });
  });

  describe('Verify Email', () => {
    test('should return 200 and verify the email', async () => {
      await insertUsers([userOne]);
      const dbUser = await prisma.user.findUnique({ where: { email: userOne.email } });
      const expires = moment().add(config.jwt.verifyEmailExpirationMinutes, 'minutes');
      const verifyEmailToken = tokenService.generateToken(
        dbUser!.id,
        expires,
        TokenType.VERIFY_EMAIL
      );
      await tokenService.saveToken(verifyEmailToken, dbUser!.id, expires, TokenType.VERIFY_EMAIL);

      const res = await request(app)
        .post('/v1/auth/verify-email')
        .send({ token: verifyEmailToken })
        .expect(httpStatus.OK);

      expect(res.body).toEqual({
        message: 'Email verified successfully',
        method: 'token'
      });

      const dbUserUpdated = await prisma.user.findUnique({ where: { id: dbUser!.id } });

      expect(dbUserUpdated!.isEmailVerified).toBe(true);

      const dbVerifyEmailTokenCount = await prisma.token.count({
        where: { userId: dbUser!.id, type: TokenType.VERIFY_EMAIL }
      });
      expect(dbVerifyEmailTokenCount).toBe(0);
    });

    test('should return 200 and verify email with OTP code', async () => {
      await insertUsers([userOne]);

      // Create OTP manually for testing
      const emailVerification = await prisma.emailVerification.create({
        data: {
          email: userOne.email,
          code: '123456',
          expiresAt: new Date(Date.now() + 10 * 60 * 1000), // 10 minutes
          isUsed: false
        }
      });

      const res = await request(app)
        .post('/v1/auth/verify-email')
        .send({ code: '123456' })
        .expect(httpStatus.OK);

      expect(res.body).toEqual({
        message: 'Email verified successfully',
        method: 'otp'
      });

      // Check if user is email verified
      const updatedUser = await prisma.user.findUnique({
        where: { email: userOne.email }
      });

      expect(updatedUser?.isEmailVerified).toBe(true);

      // Check if OTP is marked as used
      const usedOtp = await prisma.emailVerification.findUnique({
        where: { id: emailVerification.id }
      });

      expect(usedOtp?.isUsed).toBe(true);
    });

    test('should return 400 if neither token nor code is provided', async () => {
      const res = await request(app)
        .post('/v1/auth/verify-email')
        .send({})
        .expect(httpStatus.BAD_REQUEST);

      expect(res.body).toEqual({
        code: httpStatus.BAD_REQUEST,
        message: 'Please provide either verification token or code'
      });
    });

    test('should return 400 if both token and code are provided', async () => {
      const res = await request(app)
        .post('/v1/auth/verify-email')
        .send({
          token: 'some-token',
          code: '123456'
        })
        .expect(httpStatus.BAD_REQUEST);

      expect(res.body.message).toContain(
        'Please provide either verification token or code, not both'
      );
    });

    test('should return 401 with user-friendly message for invalid JWT token', async () => {
      const res = await request(app)
        .post('/v1/auth/verify-email')
        .send({ token: 'invalid-token' })
        .expect(httpStatus.UNAUTHORIZED);

      expect(res.body).toEqual({
        code: httpStatus.UNAUTHORIZED,
        message: 'Invalid or expired verification link'
      });
    });

    test('should return 400 with user-friendly message for invalid OTP', async () => {
      const res = await request(app)
        .post('/v1/auth/verify-email')
        .send({ code: '999999' })
        .expect(httpStatus.BAD_REQUEST);

      expect(res.body).toEqual({
        code: httpStatus.BAD_REQUEST,
        message: 'Invalid verification code'
      });
    });

    test('should return 400 with user-friendly message for expired OTP', async () => {
      await insertUsers([userOne]);

      // Create expired OTP
      await prisma.emailVerification.create({
        data: {
          email: userOne.email,
          code: '123456',
          expiresAt: new Date(Date.now() - 1000), // expired
          isUsed: false
        }
      });

      const res = await request(app)
        .post('/v1/auth/verify-email')
        .send({ code: '123456' })
        .expect(httpStatus.BAD_REQUEST);

      expect(res.body).toEqual({
        code: httpStatus.BAD_REQUEST,
        message: 'Verification code has expired'
      });
    });

    test('should return 400 with user-friendly message for used OTP', async () => {
      await insertUsers([userOne]);

      // Create used OTP
      await prisma.emailVerification.create({
        data: {
          email: userOne.email,
          code: '123456',
          expiresAt: new Date(Date.now() + 10 * 60 * 1000),
          isUsed: true
        }
      });

      const res = await request(app)
        .post('/v1/auth/verify-email')
        .send({ code: '123456' })
        .expect(httpStatus.BAD_REQUEST);

      expect(res.body).toEqual({
        code: httpStatus.BAD_REQUEST,
        message: 'Verification code has been used'
      });
    });

    test('should return 400 for invalid OTP format', async () => {
      const res = await request(app)
        .post('/v1/auth/verify-email')
        .send({ code: '12345' }) // 5 digits instead of 6
        .expect(httpStatus.BAD_REQUEST);

      expect(res.body.message).toContain('"code" length must be 6 characters long');
    });

    test('should return 400 for non-numeric OTP', async () => {
      const res = await request(app)
        .post('/v1/auth/verify-email')
        .send({ code: 'abc123' })
        .expect(httpStatus.BAD_REQUEST);

      expect(res.body.message).toContain(
        '"code" with value "abc123" fails to match the required pattern'
      );
    });
  });

  describe('OTP Management', () => {
    test('should invalidate previous OTP when sending new one', async () => {
      await insertUsers([userOne]);

      const dbUser = await prisma.user.findUnique({ where: { email: userOne.email } });
      const accessTokenExpires = moment().add(config.jwt.accessExpirationMinutes, 'minutes');
      const accessToken = tokenService.generateToken(
        dbUser!.id,
        accessTokenExpires,
        TokenType.ACCESS
      );

      // Send first OTP
      await request(app)
        .post('/v1/auth/send-verification-email?method=otp')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(httpStatus.OK);

      const firstOtp = await prisma.emailVerification.findFirst({
        where: {
          email: userOne.email,
          isUsed: false
        }
      });

      // Send second OTP
      await request(app)
        .post('/v1/auth/send-verification-email?method=otp')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(httpStatus.OK);

      // Check that first OTP is invalidated
      const invalidatedOtp = await prisma.emailVerification.findUnique({
        where: { id: firstOtp!.id }
      });
      expect(invalidatedOtp?.isUsed).toBe(true);

      // Check that new OTP exists
      const newOtp = await prisma.emailVerification.findFirst({
        where: {
          email: userOne.email,
          isUsed: false
        }
      });
      expect(newOtp).toBeDefined();
      expect(newOtp?.id).not.toBe(firstOtp?.id);
    });
  });
});
