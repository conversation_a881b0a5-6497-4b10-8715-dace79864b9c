import moment from 'moment';
import prisma from '../client';

/**
 * Generate unique order number with timestamp and random component
 * @param {number} outletId
 * @returns {Promise<string>}
 */
const generateOrderNumber = async (outletId: number): Promise<string> => {
  const today = moment().format('DDMMYY');
  const now = moment();

  // Create a more unique identifier using timestamp + random
  const timestamp = now.format('HHmmss');
  const random = Math.floor(Math.random() * 1000)
    .toString()
    .padStart(3, '0');
  const orderNumber = `ORD/${today}/${timestamp}${random}`;

  // Check if this order number already exists (very unlikely but safety check)
  const existingOrder = await prisma.order.findFirst({
    where: {
      orderNumber: orderNumber,
      outletId
    }
  });

  if (existingOrder) {
    // If collision occurs (extremely rare), add extra random component
    const extraRandom = Math.floor(Math.random() * 100)
      .toString()
      .padStart(2, '0');
    return `ORD/${today}/${timestamp}${random}${extraRandom}`;
  }

  return orderNumber;
};

/**
 * Calculate order total from items
 * @param {Array} items
 * @returns {number}
 */
const calculateOrderTotal = (items: any[]): number => {
  return items.reduce((total, item) => total + item.subtotal, 0);
};

/**
 * Calculate total weight from items
 * @param {Array} items
 * @returns {number}
 */
const calculateTotalWeight = (items: any[]): number => {
  return items.reduce((total, item) => total + (item.weight || 0), 0);
};

/**
 * Validate order items
 * @param {Array} items
 * @returns {boolean}
 */
const validateOrderItems = (items: any[]): boolean => {
  if (!Array.isArray(items) || items.length === 0) {
    return false;
  }

  return items.every((item) => {
    return item.serviceId && item.quantity > 0 && item.price >= 0 && item.subtotal >= 0;
  });
};

export { generateOrderNumber, calculateOrderTotal, calculateTotalWeight, validateOrderItems };
