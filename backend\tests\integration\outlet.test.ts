import request from 'supertest';
import { faker } from '@faker-js/faker';
import httpStatus from 'http-status';
import app from '../../src/app';
import { setupTestDBOptimized } from '../utils/setupTestDb';
import { describe, beforeEach, test, expect, jest } from '@jest/globals';
import { userOne, admin, employee, insertUsers } from '../fixtures/user.fixture';
import { outletOne, outletTwo, outletThree, insertOutlets } from '../fixtures/outlet.fixture';
import { Role, User } from '@prisma/client';
import prisma from '../../src/client';
import { tokenService } from '../../src/services';
import { TokenType } from '@prisma/client';
import moment from 'moment';
import config from '../../src/config/config';

// Reduce timeout for faster tests
jest.setTimeout(30000);

setupTestDBOptimized();

describe('Outlet routes', () => {
  let userOneAccessToken: string;
  let adminAccessToken: string;
  let employeeAccessToken: string;
  let dbUserOne: User;
  let dbAdmin: User;
  let dbEmployee: User;
  let testProvince: any;
  let testCity: any;

  beforeEach(async () => {
    await insertUsers([userOne, admin, employee]);

    dbUserOne = (await prisma.user.findUnique({ where: { email: userOne.email } })) as User;
    dbAdmin = (await prisma.user.findUnique({ where: { email: admin.email } })) as User;
    dbEmployee = (await prisma.user.findUnique({ where: { email: employee.email } })) as User;

    userOneAccessToken = tokenService.generateToken(
      dbUserOne.id,
      moment().add(config.jwt.accessExpirationMinutes, 'minutes'),
      TokenType.ACCESS
    );

    adminAccessToken = tokenService.generateToken(
      dbAdmin.id,
      moment().add(config.jwt.accessExpirationMinutes, 'minutes'),
      TokenType.ACCESS
    );

    employeeAccessToken = tokenService.generateToken(
      dbEmployee.id,
      moment().add(config.jwt.accessExpirationMinutes, 'minutes'),
      TokenType.ACCESS
    );

    // Create test location data with unique names
    const timestamp = Date.now();
    testProvince = await prisma.province.create({
      data: {
        name: `DKI Jakarta ${timestamp}`,
        code: `JK${timestamp}`
      }
    });

    testCity = await prisma.city.create({
      data: {
        name: `Jakarta Pusat ${timestamp}`,
        code: `JKP${timestamp}`,
        provinceId: testProvince.id
      }
    });
  });

  describe('POST /v1/outlets', () => {
    let newOutlet: any;

    beforeEach(() => {
      newOutlet = {
        name: faker.company.name(),
        address: faker.address.streetAddress(),
        province: faker.address.state(),
        city: faker.address.city(),
        timezone: 'Asia/Jakarta',
        phone: faker.phone.number('08##########'),
        latitude: parseFloat(faker.address.latitude()),
        longitude: parseFloat(faker.address.longitude())
      };
    });

    test('should return 201 and allow owner to create outlet', async () => {
      const res = await request(app)
        .post('/v1/outlets')
        .set('Authorization', `Bearer ${userOneAccessToken}`)
        .send(newOutlet)
        .expect(httpStatus.CREATED);

      expect(res.body).toMatchObject({
        id: expect.any(Number),
        name: newOutlet.name,
        ownerId: dbUserOne.id
      });
    });

    test('should return 201 and allow admin to create outlet', async () => {
      const res = await request(app)
        .post('/v1/outlets')
        .set('Authorization', `Bearer ${adminAccessToken}`)
        .send(newOutlet)
        .expect(httpStatus.CREATED);

      expect(res.body).toMatchObject({
        id: expect.any(Number),
        name: newOutlet.name,
        ownerId: dbAdmin.id
      });
    });

    test('should return 401 error if access token is missing', async () => {
      await request(app).post('/v1/outlets').send(newOutlet).expect(httpStatus.UNAUTHORIZED);
    });

    test('should return 403 error if employee tries to create outlet', async () => {
      await request(app)
        .post('/v1/outlets')
        .set('Authorization', `Bearer ${employeeAccessToken}`)
        .send(newOutlet)
        .expect(httpStatus.FORBIDDEN);
    });

    test('should return 400 error if name is missing', async () => {
      delete newOutlet.name;

      await request(app)
        .post('/v1/outlets')
        .set('Authorization', `Bearer ${adminAccessToken}`)
        .send(newOutlet)
        .expect(httpStatus.BAD_REQUEST);
    });

    test('should return 400 error if name is too short', async () => {
      newOutlet.name = 'ab';

      await request(app)
        .post('/v1/outlets')
        .set('Authorization', `Bearer ${adminAccessToken}`)
        .send(newOutlet)
        .expect(httpStatus.BAD_REQUEST);
    });

    test('should return 400 error if address is missing', async () => {
      delete newOutlet.address;

      await request(app)
        .post('/v1/outlets')
        .set('Authorization', `Bearer ${adminAccessToken}`)
        .send(newOutlet)
        .expect(httpStatus.BAD_REQUEST);
    });

    test('should return 400 error if address is too short', async () => {
      newOutlet.address = 'short';

      await request(app)
        .post('/v1/outlets')
        .set('Authorization', `Bearer ${adminAccessToken}`)
        .send(newOutlet)
        .expect(httpStatus.BAD_REQUEST);
    });

    test('should return 400 error if province is missing', async () => {
      delete newOutlet.province;

      await request(app)
        .post('/v1/outlets')
        .set('Authorization', `Bearer ${adminAccessToken}`)
        .send(newOutlet)
        .expect(httpStatus.BAD_REQUEST);
    });

    test('should return 400 error if city is missing', async () => {
      delete newOutlet.city;

      await request(app)
        .post('/v1/outlets')
        .set('Authorization', `Bearer ${adminAccessToken}`)
        .send(newOutlet)
        .expect(httpStatus.BAD_REQUEST);
    });

    test('should return 400 error if phone is missing', async () => {
      delete newOutlet.phone;

      await request(app)
        .post('/v1/outlets')
        .set('Authorization', `Bearer ${adminAccessToken}`)
        .send(newOutlet)
        .expect(httpStatus.BAD_REQUEST);
    });

    test('should return 400 error if phone format is invalid', async () => {
      newOutlet.phone = 'invalid-phone';

      await request(app)
        .post('/v1/outlets')
        .set('Authorization', `Bearer ${adminAccessToken}`)
        .send(newOutlet)
        .expect(httpStatus.BAD_REQUEST);
    });

    test('should return 400 error if latitude is out of range', async () => {
      newOutlet.latitude = 100;

      await request(app)
        .post('/v1/outlets')
        .set('Authorization', `Bearer ${adminAccessToken}`)
        .send(newOutlet)
        .expect(httpStatus.BAD_REQUEST);
    });

    test('should return 400 error if longitude is out of range', async () => {
      newOutlet.longitude = 200;

      await request(app)
        .post('/v1/outlets')
        .set('Authorization', `Bearer ${adminAccessToken}`)
        .send(newOutlet)
        .expect(httpStatus.BAD_REQUEST);
    });
  });

  describe('GET /v1/outlets', () => {
    beforeEach(async () => {
      // Create outlets with predictable names for sorting tests
      const outlet1 = { ...outletOne, name: 'A Outlet' };
      const outlet2 = { ...outletTwo, name: 'B Outlet' };
      const outlet3 = { ...outletThree, name: 'C Outlet' };
      await insertOutlets([outlet1, outlet2, outlet3], dbAdmin.id);
    });

    test('should return 200 and apply the default query options', async () => {
      const res = await request(app)
        .get('/v1/outlets')
        .set('Authorization', `Bearer ${adminAccessToken}`)
        .send()
        .expect(httpStatus.OK);

      expect(res.body).toEqual({
        results: expect.any(Array),
        page: 1,
        limit: 10,
        totalPages: expect.any(Number),
        totalResults: expect.any(Number)
      });
      expect(res.body.results).toHaveLength(3);
      expect(res.body.results[0]).toMatchObject({
        id: expect.any(Number),
        name: expect.any(String),
        address: expect.any(String),
        province: expect.any(String),
        city: expect.any(String),
        timezone: expect.any(String),
        phone: expect.any(String),
        latitude: expect.any(Number),
        longitude: expect.any(Number),
        ownerId: dbAdmin.id,
        isActive: expect.any(Boolean),
        createdAt: expect.any(String),
        updatedAt: expect.any(String)
      });
    });

    test('should return 401 if access token is missing', async () => {
      await request(app).get('/v1/outlets').send().expect(httpStatus.UNAUTHORIZED);
    });

    test('should correctly apply filter on name field', async () => {
      const res = await request(app)
        .get('/v1/outlets')
        .set('Authorization', `Bearer ${adminAccessToken}`)
        .query({ name: 'A Outlet' })
        .send()
        .expect(httpStatus.OK);

      expect(res.body.results).toHaveLength(1);
      expect(res.body.results[0].name).toBe('A Outlet');
    });

    test('should correctly apply filter on city field', async () => {
      const res = await request(app)
        .get('/v1/outlets')
        .set('Authorization', `Bearer ${adminAccessToken}`)
        .query({ city: outletOne.city })
        .send()
        .expect(httpStatus.OK);

      expect(res.body.results).toHaveLength(1);
      expect(res.body.results[0].city).toBe(outletOne.city);
    });

    test('should correctly apply filter on province field', async () => {
      const res = await request(app)
        .get('/v1/outlets')
        .set('Authorization', `Bearer ${adminAccessToken}`)
        .query({ province: outletOne.province })
        .send()
        .expect(httpStatus.OK);

      expect(res.body.results).toHaveLength(1);
      expect(res.body.results[0].province).toBe(outletOne.province);
    });

    test('should correctly apply filter on isActive field', async () => {
      const res = await request(app)
        .get('/v1/outlets')
        .set('Authorization', `Bearer ${adminAccessToken}`)
        .query({ isActive: false })
        .send()
        .expect(httpStatus.OK);

      expect(res.body.results).toHaveLength(1);
      expect(res.body.results[0].isActive).toBe(false);
    });

    test('should correctly sort the returned array if descending sort param is specified', async () => {
      const res = await request(app)
        .get('/v1/outlets')
        .set('Authorization', `Bearer ${adminAccessToken}`)
        .query({ sortBy: 'name', sortType: 'desc' })
        .send()
        .expect(httpStatus.OK);

      expect(res.body.results).toHaveLength(3);
      expect(res.body.results[0].name >= res.body.results[1].name).toBe(true);
      expect(res.body.results[1].name >= res.body.results[2].name).toBe(true);
    });

    test('should correctly sort the returned array if ascending sort param is specified', async () => {
      const res = await request(app)
        .get('/v1/outlets')
        .set('Authorization', `Bearer ${adminAccessToken}`)
        .query({ sortBy: 'name', sortType: 'asc' })
        .send()
        .expect(httpStatus.OK);

      expect(res.body.results).toHaveLength(3);
      expect(res.body.results[0].name <= res.body.results[1].name).toBe(true);
      expect(res.body.results[1].name <= res.body.results[2].name).toBe(true);
    });

    test('should limit returned array if limit param is specified', async () => {
      const res = await request(app)
        .get('/v1/outlets')
        .set('Authorization', `Bearer ${adminAccessToken}`)
        .query({ limit: 2 })
        .send()
        .expect(httpStatus.OK);

      expect(res.body.results).toHaveLength(2);
    });

    test('should return the correct page if page and limit params are specified', async () => {
      const res = await request(app)
        .get('/v1/outlets')
        .set('Authorization', `Bearer ${adminAccessToken}`)
        .query({ page: 2, limit: 2 })
        .send()
        .expect(httpStatus.OK);

      expect(res.body.results).toHaveLength(1);
      expect(res.body.page).toBe(2);
      expect(res.body.limit).toBe(2);
    });
  });

  describe('GET /v1/outlets/:outletId', () => {
    let outlet: any;

    beforeEach(async () => {
      const outlets = await insertOutlets([outletOne], dbAdmin.id);
      outlet = outlets[0];
    });

    test('should return 200 and the outlet object if data is ok', async () => {
      const res = await request(app)
        .get(`/v1/outlets/${outlet.id}`)
        .set('Authorization', `Bearer ${adminAccessToken}`)
        .send()
        .expect(httpStatus.OK);

      expect(res.body).toMatchObject({
        id: outlet.id,
        name: outlet.name,
        address: outlet.address,
        province: outlet.province,
        city: outlet.city,
        timezone: outlet.timezone,
        phone: outlet.phone,
        latitude: outlet.latitude,
        longitude: outlet.longitude,
        ownerId: outlet.ownerId,
        isActive: outlet.isActive,
        createdAt: expect.any(String),
        updatedAt: expect.any(String)
      });
    });

    test('should return 401 error if access token is missing', async () => {
      await request(app).get(`/v1/outlets/${outlet.id}`).send().expect(httpStatus.UNAUTHORIZED);
    });

    test('should return 404 error if outlet is not found', async () => {
      await request(app)
        .get('/v1/outlets/999999')
        .set('Authorization', `Bearer ${adminAccessToken}`)
        .send()
        .expect(httpStatus.NOT_FOUND);
    });

    test('should return 400 error if outletId is not a valid integer', async () => {
      await request(app)
        .get('/v1/outlets/invalidId')
        .set('Authorization', `Bearer ${adminAccessToken}`)
        .send()
        .expect(httpStatus.BAD_REQUEST);
    });
  });

  describe('PATCH /v1/outlets/:outletId', () => {
    let outlet: any;
    let updateBody: any;

    beforeEach(async () => {
      const outlets = await insertOutlets([outletOne], dbAdmin.id);
      outlet = outlets[0];
      updateBody = {
        name: faker.company.name(),
        address: faker.address.streetAddress(),
        city: faker.address.city(),
        isActive: false
      };
    });

    test('should return 200 and successfully update outlet if data is ok', async () => {
      const res = await request(app)
        .patch(`/v1/outlets/${outlet.id}`)
        .set('Authorization', `Bearer ${adminAccessToken}`)
        .send(updateBody)
        .expect(httpStatus.OK);

      expect(res.body).toMatchObject({
        id: outlet.id,
        name: updateBody.name,
        address: updateBody.address,
        province: outlet.province,
        city: updateBody.city,
        timezone: outlet.timezone,
        phone: outlet.phone,
        latitude: outlet.latitude,
        longitude: outlet.longitude,
        ownerId: outlet.ownerId,
        isActive: updateBody.isActive,
        createdAt: expect.any(String),
        updatedAt: expect.any(String)
      });

      const dbOutlet = await prisma.outlet.findUnique({ where: { id: outlet.id } });
      expect(dbOutlet).toMatchObject({
        name: updateBody.name,
        address: updateBody.address,
        city: updateBody.city,
        isActive: updateBody.isActive
      });
    });

    test('should return 401 error if access token is missing', async () => {
      await request(app)
        .patch(`/v1/outlets/${outlet.id}`)
        .send(updateBody)
        .expect(httpStatus.UNAUTHORIZED);
    });

    test('should return 403 error if user does not have required permission', async () => {
      await request(app)
        .patch(`/v1/outlets/${outlet.id}`)
        .set('Authorization', `Bearer ${employeeAccessToken}`)
        .send(updateBody)
        .expect(httpStatus.FORBIDDEN);
    });

    test('should return 404 if outlet is not found', async () => {
      await request(app)
        .patch('/v1/outlets/999999')
        .set('Authorization', `Bearer ${adminAccessToken}`)
        .send(updateBody)
        .expect(httpStatus.NOT_FOUND);
    });

    test('should return 400 error if outletId is not a valid integer', async () => {
      await request(app)
        .patch('/v1/outlets/invalidId')
        .set('Authorization', `Bearer ${adminAccessToken}`)
        .send(updateBody)
        .expect(httpStatus.BAD_REQUEST);
    });

    test('should return 400 if no update fields are provided', async () => {
      await request(app)
        .patch(`/v1/outlets/${outlet.id}`)
        .set('Authorization', `Bearer ${adminAccessToken}`)
        .send({})
        .expect(httpStatus.BAD_REQUEST);
    });

    test('should return 400 if name is too short', async () => {
      updateBody.name = 'ab';

      await request(app)
        .patch(`/v1/outlets/${outlet.id}`)
        .set('Authorization', `Bearer ${adminAccessToken}`)
        .send(updateBody)
        .expect(httpStatus.BAD_REQUEST);
    });
  });

  describe('DELETE /v1/outlets/:outletId', () => {
    let outlet: any;

    beforeEach(async () => {
      const outlets = await insertOutlets([outletOne], dbAdmin.id);
      outlet = outlets[0];
    });

    test('should return 204 if data is ok', async () => {
      await request(app)
        .delete(`/v1/outlets/${outlet.id}`)
        .set('Authorization', `Bearer ${adminAccessToken}`)
        .send()
        .expect(httpStatus.NO_CONTENT);

      const dbOutlet = await prisma.outlet.findUnique({ where: { id: outlet.id } });
      // Outlet menggunakan soft delete, jadi masih ada tapi isDeleted = true
      expect(dbOutlet?.isDeleted).toBe(true);
    });

    test('should return 401 error if access token is missing', async () => {
      await request(app).delete(`/v1/outlets/${outlet.id}`).send().expect(httpStatus.UNAUTHORIZED);
    });

    test('should return 403 error if user does not have required permission', async () => {
      await request(app)
        .delete(`/v1/outlets/${outlet.id}`)
        .set('Authorization', `Bearer ${employeeAccessToken}`)
        .send()
        .expect(httpStatus.FORBIDDEN);
    });

    test('should return 404 error if outlet is not found', async () => {
      await request(app)
        .delete('/v1/outlets/999999')
        .set('Authorization', `Bearer ${adminAccessToken}`)
        .send()
        .expect(httpStatus.NOT_FOUND);
    });

    test('should return 400 error if outletId is not a valid integer', async () => {
      await request(app)
        .delete('/v1/outlets/invalidId')
        .set('Authorization', `Bearer ${adminAccessToken}`)
        .send()
        .expect(httpStatus.BAD_REQUEST);
    });
  });

  describe('GET /v1/outlets/:outletId/services', () => {
    let outlet: any;

    beforeEach(async () => {
      const outlets = await insertOutlets([outletOne], dbAdmin.id);
      outlet = outlets[0];
    });

    test('should return 200 and outlet services if data is ok', async () => {
      const res = await request(app)
        .get(`/v1/outlets/${outlet.id}/services`)
        .set('Authorization', `Bearer ${adminAccessToken}`)
        .send()
        .expect(httpStatus.OK);

      expect(res.body).toEqual(expect.any(Array));
    });

    test('should return 401 error if access token is missing', async () => {
      await request(app)
        .get(`/v1/outlets/${outlet.id}/services`)
        .send()
        .expect(httpStatus.UNAUTHORIZED);
    });

    test('should return 404 error if outlet is not found', async () => {
      await request(app)
        .get('/v1/outlets/999999/services')
        .set('Authorization', `Bearer ${adminAccessToken}`)
        .send()
        .expect(httpStatus.NOT_FOUND);
    });

    test('should return 400 error if outletId is not a valid integer', async () => {
      await request(app)
        .get('/v1/outlets/invalidId/services')
        .set('Authorization', `Bearer ${adminAccessToken}`)
        .send()
        .expect(httpStatus.BAD_REQUEST);
    });
  });

  describe('POST /v1/outlets/:outletId/copy-services', () => {
    let sourceOutlet: any;
    let targetOutlet: any;

    beforeEach(async () => {
      const outlets = await insertOutlets([outletOne, outletTwo], dbAdmin.id);
      sourceOutlet = outlets[0];
      targetOutlet = outlets[1];

      // Create some services for source outlet
      await prisma.service.createMany({
        data: [
          {
            name: 'Cuci Kering',
            description: 'Layanan cuci kering',
            unit: 'kg',
            price: 5000,
            estimationHours: 24,
            isActive: true,
            outletId: sourceOutlet.id
          },
          {
            name: 'Setrika',
            description: 'Layanan setrika',
            unit: 'pcs',
            price: 3000,
            estimationHours: 12,
            isActive: true,
            outletId: sourceOutlet.id
          }
        ]
      });
    });

    test('should return 200 and successfully copy services if data is ok', async () => {
      const copyBody = {
        sourceOutletId: sourceOutlet.id
      };

      const res = await request(app)
        .post(`/v1/outlets/${targetOutlet.id}/copy-services`)
        .set('Authorization', `Bearer ${adminAccessToken}`)
        .send(copyBody)
        .expect(httpStatus.OK);

      expect(res.body).toMatchObject({
        message: expect.stringContaining('Successfully copied 2 services'),
        services: expect.any(Array)
      });
      expect(res.body.services).toHaveLength(2);
    });

    test('should return 401 error if access token is missing', async () => {
      const copyBody = {
        sourceOutletId: sourceOutlet.id
      };

      await request(app)
        .post(`/v1/outlets/${targetOutlet.id}/copy-services`)
        .send(copyBody)
        .expect(httpStatus.UNAUTHORIZED);
    });

    test('should return 403 error if user does not have required permission', async () => {
      // Create outlet owned by userOne for this test
      const userOneOutlets = await insertOutlets([outletOne], dbUserOne.id);
      const userOneOutlet = userOneOutlets[0];

      const copyBody = {
        sourceOutletId: sourceOutlet.id
      };

      await request(app)
        .post(`/v1/outlets/${userOneOutlet.id}/copy-services`)
        .set('Authorization', `Bearer ${employeeAccessToken}`)
        .send(copyBody)
        .expect(httpStatus.FORBIDDEN);
    });

    test('should return 400 error if sourceOutletId is missing', async () => {
      await request(app)
        .post(`/v1/outlets/${targetOutlet.id}/copy-services`)
        .set('Authorization', `Bearer ${adminAccessToken}`)
        .send({})
        .expect(httpStatus.BAD_REQUEST);
    });

    test('should return 400 error if outletId is not a valid integer', async () => {
      const copyBody = {
        sourceOutletId: sourceOutlet.id
      };

      await request(app)
        .post('/v1/outlets/invalidId/copy-services')
        .set('Authorization', `Bearer ${adminAccessToken}`)
        .send(copyBody)
        .expect(httpStatus.BAD_REQUEST);
    });

    test('should return 400 error if sourceOutletId is not a valid integer', async () => {
      const copyBody = {
        sourceOutletId: 'invalidId'
      };

      await request(app)
        .post(`/v1/outlets/${targetOutlet.id}/copy-services`)
        .set('Authorization', `Bearer ${adminAccessToken}`)
        .send(copyBody)
        .expect(httpStatus.BAD_REQUEST);
    });

    test('should return 400 if source outlet has no services to copy', async () => {
      // Create empty outlet without services
      const emptyOutlets = await insertOutlets([outletThree], dbAdmin.id);
      const emptyOutlet = emptyOutlets[0];

      const copyBody = {
        sourceOutletId: emptyOutlet.id
      };

      const res = await request(app)
        .post(`/v1/outlets/${targetOutlet.id}/copy-services`)
        .set('Authorization', `Bearer ${adminAccessToken}`)
        .send(copyBody)
        .expect(httpStatus.BAD_REQUEST);

      expect(res.body.message).toContain('Source outlet has no services to copy');
    });
  });

  describe('GET /v1/outlets/:outletId/stats', () => {
    let outlet: any;

    beforeEach(async () => {
      const outlets = await insertOutlets([outletOne], dbAdmin.id);
      outlet = outlets[0];
    });

    test('should return 200 and outlet stats if data is ok', async () => {
      const res = await request(app)
        .get(`/v1/outlets/${outlet.id}/stats`)
        .set('Authorization', `Bearer ${adminAccessToken}`)
        .send()
        .expect(httpStatus.OK);

      expect(res.body).toEqual(expect.any(Object));
    });

    test('should return 401 error if access token is missing', async () => {
      await request(app)
        .get(`/v1/outlets/${outlet.id}/stats`)
        .send()
        .expect(httpStatus.UNAUTHORIZED);
    });

    test('should return 404 error if outlet is not found', async () => {
      await request(app)
        .get('/v1/outlets/999999/stats')
        .set('Authorization', `Bearer ${adminAccessToken}`)
        .send()
        .expect(httpStatus.NOT_FOUND);
    });

    test('should return 400 error if outletId is not a valid integer', async () => {
      await request(app)
        .get('/v1/outlets/invalidId/stats')
        .set('Authorization', `Bearer ${adminAccessToken}`)
        .send()
        .expect(httpStatus.BAD_REQUEST);
    });
  });

  // ========================================
  // LOCATION INTEGRATION TESTS (TDD)
  // ========================================
  describe('Location Integration', () => {
    describe('POST /v1/outlets - with location integration', () => {
      let newOutletWithLocation: any;

      beforeEach(() => {
        newOutletWithLocation = {
          name: faker.company.name(),
          address: faker.address.streetAddress(),
          province: testProvince.name,
          city: testCity.name,
          provinceId: testProvince.id,
          cityId: testCity.id,
          timezone: 'Asia/Jakarta',
          phone: faker.phone.number('08##########'),
          latitude: parseFloat(faker.address.latitude()),
          longitude: parseFloat(faker.address.longitude())
        };
      });

      test('should return 201 and create outlet with valid provinceId and cityId', async () => {
        const res = await request(app)
          .post('/v1/outlets')
          .set('Authorization', `Bearer ${adminAccessToken}`)
          .send(newOutletWithLocation)
          .expect(httpStatus.CREATED);

        expect(res.body).toMatchObject({
          id: expect.any(Number),
          name: newOutletWithLocation.name,
          province: newOutletWithLocation.province,
          city: newOutletWithLocation.city,
          provinceId: newOutletWithLocation.provinceId,
          cityId: newOutletWithLocation.cityId,
          provinceRef: {
            id: testProvince.id,
            name: testProvince.name,
            code: testProvince.code
          },
          cityRef: {
            id: testCity.id,
            name: testCity.name,
            code: testCity.code,
            provinceId: testProvince.id
          }
        });

        const dbOutlet = await prisma.outlet.findUnique({
          where: { id: res.body.id },
          include: {
            provinceRef: true,
            cityRef: true
          }
        });
        expect(dbOutlet?.provinceId).toBe(testProvince.id);
        expect(dbOutlet?.cityId).toBe(testCity.id);
      });

      test('should return 400 error if provinceId does not exist', async () => {
        newOutletWithLocation.provinceId = 99999;

        await request(app)
          .post('/v1/outlets')
          .set('Authorization', `Bearer ${adminAccessToken}`)
          .send(newOutletWithLocation)
          .expect(httpStatus.BAD_REQUEST);
      });

      test('should return 400 error if cityId does not exist', async () => {
        newOutletWithLocation.cityId = 99999;

        await request(app)
          .post('/v1/outlets')
          .set('Authorization', `Bearer ${adminAccessToken}`)
          .send(newOutletWithLocation)
          .expect(httpStatus.BAD_REQUEST);
      });

      test('should return 400 error if cityId does not belong to provinceId', async () => {
        // Create another province
        const anotherProvince = await prisma.province.create({
          data: {
            name: 'Jawa Barat',
            code: 'JB'
          }
        });

        newOutletWithLocation.provinceId = anotherProvince.id;
        // Keep cityId from testCity which belongs to testProvince

        await request(app)
          .post('/v1/outlets')
          .set('Authorization', `Bearer ${adminAccessToken}`)
          .send(newOutletWithLocation)
          .expect(httpStatus.BAD_REQUEST);
      });

      test('should return 201 and create outlet with only province/city strings (backward compatibility)', async () => {
        const outletWithStrings = {
          name: faker.company.name(),
          address: faker.address.streetAddress(),
          province: 'Custom Province',
          city: 'Custom City',
          timezone: 'Asia/Jakarta',
          phone: faker.phone.number('08##########')
        };

        const res = await request(app)
          .post('/v1/outlets')
          .set('Authorization', `Bearer ${adminAccessToken}`)
          .send(outletWithStrings)
          .expect(httpStatus.CREATED);

        expect(res.body).toMatchObject({
          province: 'Custom Province',
          city: 'Custom City',
          provinceId: null,
          cityId: null
        });
      });
    });

    describe('PATCH /v1/outlets/:outletId - with location integration', () => {
      let outlet: any;

      beforeEach(async () => {
        const outlets = await insertOutlets([outletOne], dbAdmin.id);
        outlet = outlets[0];
      });

      test('should return 200 and update outlet with valid location IDs', async () => {
        const updateBody = {
          provinceId: testProvince.id,
          cityId: testCity.id,
          province: testProvince.name,
          city: testCity.name
        };

        const res = await request(app)
          .patch(`/v1/outlets/${outlet.id}`)
          .set('Authorization', `Bearer ${adminAccessToken}`)
          .send(updateBody)
          .expect(httpStatus.OK);

        expect(res.body).toMatchObject({
          provinceId: testProvince.id,
          cityId: testCity.id,
          province: testProvince.name,
          city: testCity.name
        });
      });

      test('should return 400 error if updating with invalid provinceId', async () => {
        const updateBody = {
          provinceId: 99999
        };

        await request(app)
          .patch(`/v1/outlets/${outlet.id}`)
          .set('Authorization', `Bearer ${adminAccessToken}`)
          .send(updateBody)
          .expect(httpStatus.BAD_REQUEST);
      });

      test('should return 400 error if updating with invalid cityId', async () => {
        const updateBody = {
          cityId: 99999
        };

        await request(app)
          .patch(`/v1/outlets/${outlet.id}`)
          .set('Authorization', `Bearer ${adminAccessToken}`)
          .send(updateBody)
          .expect(httpStatus.BAD_REQUEST);
      });
    });

    describe('GET /v1/outlets/:outletId - with location details', () => {
      let outletWithLocation: any;

      beforeEach(async () => {
        // Create outlet with location
        outletWithLocation = await prisma.outlet.create({
          data: {
            name: faker.company.name(),
            address: faker.address.streetAddress(),
            province: testProvince.name,
            city: testCity.name,
            provinceId: testProvince.id,
            cityId: testCity.id,
            phone: faker.phone.number('08##########'),
            ownerId: dbAdmin.id
          }
        });
      });

      test('should return 200 and outlet with location details', async () => {
        const res = await request(app)
          .get(`/v1/outlets/${outletWithLocation.id}`)
          .set('Authorization', `Bearer ${adminAccessToken}`)
          .send()
          .expect(httpStatus.OK);

        expect(res.body).toMatchObject({
          id: outletWithLocation.id,
          provinceId: testProvince.id,
          cityId: testCity.id,
          provinceRef: {
            id: testProvince.id,
            name: testProvince.name,
            code: testProvince.code
          },
          cityRef: {
            id: testCity.id,
            name: testCity.name,
            code: testCity.code,
            provinceId: testProvince.id
          }
        });
      });
    });

    describe('GET /v1/outlets - filter by location', () => {
      let outletJakarta: any;
      let outletBandung: any;

      beforeEach(async () => {
        // Create Bandung location with unique names
        const timestamp = Date.now();
        const jabarProvince = await prisma.province.create({
          data: { name: `Jawa Barat ${timestamp}`, code: `JB${timestamp}` }
        });
        const bandungCity = await prisma.city.create({
          data: {
            name: `Bandung ${timestamp}`,
            code: `BDG${timestamp}`,
            provinceId: jabarProvince.id
          }
        });

        // Create outlets with different locations
        outletJakarta = await prisma.outlet.create({
          data: {
            name: 'Outlet Jakarta',
            address: 'Jakarta Address',
            province: testProvince.name,
            city: testCity.name,
            provinceId: testProvince.id,
            cityId: testCity.id,
            phone: '081234567890',
            ownerId: dbAdmin.id
          }
        });

        outletBandung = await prisma.outlet.create({
          data: {
            name: 'Outlet Bandung',
            address: 'Bandung Address',
            province: jabarProvince.name,
            city: bandungCity.name,
            provinceId: jabarProvince.id,
            cityId: bandungCity.id,
            phone: '081234567891',
            ownerId: dbAdmin.id
          }
        });
      });

      test('should return outlets filtered by province', async () => {
        const res = await request(app)
          .get(`/v1/outlets?province=${testProvince.name}`)
          .set('Authorization', `Bearer ${adminAccessToken}`)
          .send()
          .expect(httpStatus.OK);

        expect(res.body.results).toHaveLength(1);
        expect(res.body.results[0].id).toBe(outletJakarta.id);
      });

      test('should return outlets filtered by city', async () => {
        const res = await request(app)
          .get(`/v1/outlets?city=${testCity.name}`)
          .set('Authorization', `Bearer ${adminAccessToken}`)
          .send()
          .expect(httpStatus.OK);

        expect(res.body.results).toHaveLength(1);
        expect(res.body.results[0].id).toBe(outletJakarta.id);
      });
    });
  });
});
