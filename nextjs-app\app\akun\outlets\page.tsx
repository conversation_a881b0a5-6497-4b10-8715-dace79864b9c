'use client';

import { useState } from 'react';
import Link from 'next/link';
import {
  ArrowLeft,
  Plus,
  MapPin,
  Phone,
  Clock,
  MoreVertical,
  Edit,
  Trash2,
  Eye,
} from 'lucide-react';
import { useRouter } from 'next/navigation';

import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Skeleton } from '@/components/ui/skeleton';
import { useOutlets, useDeleteOutlet } from '@/hooks/useOutlets';
import type { Outlet } from '@/lib/api/outlets';

export default function OutletsPage() {
  const router = useRouter();
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [outletToDelete, setOutletToDelete] = useState<number | null>(null);

  const { data: outletsData, isLoading, error } = useOutlets();
  const deleteOutletMutation = useDeleteOutlet();

  const handleDeleteOutlet = (id: number) => {
    setOutletToDelete(id);
    setShowDeleteDialog(true);
  };

  const confirmDelete = async () => {
    if (outletToDelete) {
      await deleteOutletMutation.mutateAsync(outletToDelete);
      setShowDeleteDialog(false);
      setOutletToDelete(null);
    }
  };

  if (error) {
    return (
      <div className="flex flex-col min-h-screen bg-slate-50">
        <header className="p-4 flex justify-between items-center bg-white sticky top-0 z-10 shadow-sm">
          <div className="flex items-center">
            <Link href="/akun" className="mr-3">
              <ArrowLeft className="h-5 w-5" />
            </Link>
            <h1 className="text-lg font-semibold">Kelola Outlet</h1>
          </div>
        </header>
        <main className="flex-1 p-4 pb-20 flex items-center justify-center">
          <div className="text-center">
            <p className="text-red-500 mb-4">Gagal memuat data outlet</p>
            <Button onClick={() => window.location.reload()}>Coba Lagi</Button>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen bg-slate-50">
      <header className="p-4 flex justify-between items-center bg-white sticky top-0 z-10 shadow-sm">
        <div className="flex items-center">
          <Link href="/akun" className="mr-3">
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <h1 className="text-lg font-semibold">Kelola Outlet</h1>
        </div>
        <Link href="/akun/outlets/add">
          <Button size="sm" className="bg-blue-500 hover:bg-blue-600">
            <Plus className="h-4 w-4 mr-1" /> Tambah
          </Button>
        </Link>
      </header>

      <main className="flex-1 p-4 pb-20">
        <div className="space-y-4">
          {isLoading ? (
            // Loading skeleton
            Array.from({ length: 3 }).map((_, index) => (
              <Card key={index} className="p-4">
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <Skeleton className="h-6 w-48" />
                      <Skeleton className="h-5 w-16" />
                    </div>
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-64" />
                      <Skeleton className="h-4 w-32" />
                      <Skeleton className="h-4 w-40" />
                    </div>
                  </div>
                  <Skeleton className="h-8 w-8" />
                </div>
                <div className="grid grid-cols-2 gap-4 mt-4">
                  <Skeleton className="h-16 w-full" />
                  <Skeleton className="h-16 w-full" />
                </div>
              </Card>
            ))
          ) : outletsData?.results?.length === 0 ? (
            // Empty state
            <div className="text-center py-12">
              <div className="mb-4">
                <MapPin className="h-12 w-12 text-gray-400 mx-auto" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Belum ada outlet
              </h3>
              <p className="text-gray-500 mb-4">
                Mulai dengan menambahkan outlet pertama Anda
              </p>
              <Link href="/akun/outlets/add">
                <Button className="bg-blue-500 hover:bg-blue-600">
                  <Plus className="h-4 w-4 mr-2" /> Tambah Outlet
                </Button>
              </Link>
            </div>
          ) : (
            // Outlet list
            outletsData?.results?.map((outlet: Outlet) => (
              <Card key={outlet.id} className="p-4">
                <div className="flex justify-between items-start">
                  <div>
                    <div className="flex items-center gap-2">
                      <h3 className="font-semibold text-lg">{outlet.name}</h3>
                      <Badge
                        variant={outlet.isActive ? 'default' : 'secondary'}
                      >
                        {outlet.isActive ? 'Aktif' : 'Tidak Aktif'}
                      </Badge>
                    </div>
                    <div className="space-y-1 mt-2">
                      <div className="flex items-center text-sm text-gray-500">
                        <MapPin className="h-4 w-4 mr-2" />
                        {outlet.address}
                      </div>
                      <div className="flex items-center text-sm text-gray-500">
                        <Phone className="h-4 w-4 mr-2" />
                        {outlet.phone}
                      </div>
                      <div className="flex items-center text-sm text-gray-500">
                        <Clock className="h-4 w-4 mr-2" />
                        {outlet.city}, {outlet.province}
                      </div>
                    </div>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem
                        onClick={() =>
                          router.push(`/akun/outlets/${outlet.id}`)
                        }
                      >
                        <Eye className="h-4 w-4 mr-2" /> Lihat Detail
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() =>
                          router.push(`/akun/outlets/edit/${outlet.id}`)
                        }
                      >
                        <Edit className="h-4 w-4 mr-2" /> Edit
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        className="text-red-500"
                        onClick={() => handleDeleteOutlet(outlet.id)}
                      >
                        <Trash2 className="h-4 w-4 mr-2" /> Hapus
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>

                <div className="grid grid-cols-2 gap-4 mt-4">
                  <div className="bg-blue-50 p-3 rounded-lg">
                    <p className="text-sm text-gray-500">Jumlah Pegawai</p>
                    <p className="font-semibold text-lg">
                      {outlet._count?.employees || 0} orang
                    </p>
                  </div>
                  <div className="bg-green-50 p-3 rounded-lg">
                    <p className="text-sm text-gray-500">Total Order</p>
                    <p className="font-semibold text-lg">
                      {outlet._count?.orders || 0}
                    </p>
                  </div>
                </div>
              </Card>
            ))
          )}
        </div>
      </main>

      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Hapus Outlet</DialogTitle>
            <DialogDescription>
              Apakah Anda yakin ingin menghapus outlet ini? Tindakan ini tidak
              dapat dibatalkan.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowDeleteDialog(false)}
            >
              Batal
            </Button>
            <Button
              variant="destructive"
              onClick={confirmDelete}
              disabled={deleteOutletMutation.isPending}
            >
              {deleteOutletMutation.isPending ? 'Menghapus...' : 'Hapus'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
