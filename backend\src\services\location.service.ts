import prisma from '../client';
import { Prisma } from '@prisma/client';

// Data timezone Indonesia
const indonesianTimezones = [
  {
    id: 'Asia/Jakarta',
    name: 'WIB (UTC+7)',
    description: 'Waktu Indonesia Barat',
    offset: '+07:00',
    regions: ['Sumatera', 'Jawa', 'Kalimantan Barat', 'Kalimantan Tengah']
  },
  {
    id: 'Asia/Makassar',
    name: 'WITA (UTC+8)',
    description: 'Waktu Indonesia Tengah',
    offset: '+08:00',
    regions: [
      'Kalimantan Selatan',
      'Kalimantan Timur',
      'Kalimantan Utara',
      'Bali',
      'Nusa Tenggara',
      'Sulawesi'
    ]
  },
  {
    id: 'Asia/Jayapura',
    name: 'WIT (UTC+9)',
    description: 'Waktu Indonesia Timur',
    offset: '+09:00',
    regions: ['Maluku', 'Papua']
  }
];

/**
 * Get all provinces
 * @param {Object} options - Query options
 * @returns {Promise<Province[]>}
 */
const getProvinces = async (
  options: {
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  } = {}
) => {
  const { sortBy = 'name', sortOrder = 'asc' } = options;

  const provinces = await prisma.province.findMany({
    select: {
      id: true,
      name: true,
      code: true
    },
    orderBy: {
      [sortBy]: sortOrder
    }
  });

  return provinces;
};

/**
 * Get all cities with optional filtering
 * @param {Object} filter - Filter options
 * @param {Object} options - Query options
 * @returns {Promise<City[]>}
 */
const getCities = async (
  filter: {
    provinceId?: number;
  } = {},
  options: {
    includeProvince?: boolean;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  } = {}
) => {
  const { provinceId } = filter;
  const { includeProvince = false, sortBy = 'name', sortOrder = 'asc' } = options;

  const whereClause: Prisma.CityWhereInput = {};
  if (provinceId) {
    whereClause.provinceId = provinceId;
  }

  const cities = await prisma.city.findMany({
    where: whereClause,
    select: {
      id: true,
      name: true,
      code: true,
      provinceId: true,
      ...(includeProvince && {
        province: {
          select: {
            id: true,
            name: true,
            code: true
          }
        }
      })
    },
    orderBy: {
      [sortBy]: sortOrder
    }
  });

  return cities;
};

/**
 * Get cities by province ID
 * @param {number} provinceId - Province ID
 * @param {Object} options - Query options
 * @returns {Promise<City[]>}
 */
const getCitiesByProvinceId = async (
  provinceId: number,
  options: {
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  } = {}
) => {
  const { sortBy = 'name', sortOrder = 'asc' } = options;

  const cities = await prisma.city.findMany({
    where: {
      provinceId: provinceId
    },
    select: {
      id: true,
      name: true,
      code: true,
      provinceId: true
    },
    orderBy: {
      [sortBy]: sortOrder
    }
  });

  return cities;
};

/**
 * Search provinces and cities
 * @param {string} query - Search query
 * @param {Object} options - Search options
 * @returns {Promise<{provinces: Province[], cities: City[]}>}
 */
const searchLocations = async (
  query: string,
  options: {
    limit?: number;
  } = {}
) => {
  const { limit = 10 } = options;

  const searchTerm = `%${query}%`;

  // Search provinces
  const provinces = await prisma.province.findMany({
    where: {
      name: {
        contains: query,
        mode: 'insensitive'
      }
    },
    select: {
      id: true,
      name: true,
      code: true
    },
    take: limit,
    orderBy: {
      name: 'asc'
    }
  });

  // Search cities
  const cities = await prisma.city.findMany({
    where: {
      name: {
        contains: query,
        mode: 'insensitive'
      }
    },
    select: {
      id: true,
      name: true,
      code: true,
      provinceId: true,
      province: {
        select: {
          id: true,
          name: true,
          code: true
        }
      }
    },
    take: limit,
    orderBy: {
      name: 'asc'
    }
  });

  return {
    provinces,
    cities
  };
};

/**
 * Get all Indonesian timezones
 * @returns {Promise<Timezone[]>}
 */
const getTimezones = async () => {
  return indonesianTimezones;
};

export default {
  getProvinces,
  getCities,
  getCitiesByProvinceId,
  searchLocations,
  getTimezones
};
