'use client';

import { Bell, Package, Truck, CheckCircle, AlertCircle } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Card } from '@/components/ui/card';
import BottomNavigation from '@/components/bottom-navigation';

export default function NotificationsPage() {
  const router = useRouter();

  const notifications = [
    {
      id: 1,
      type: 'order',
      title: 'Pesanan Baru',
      message: '<PERSON>an mengiri<PERSON>kan pesanan baru',
      time: '5 menit yang lalu',
      icon: Package,
      color: 'text-blue-500',
      link: '/orders?tab=konfirmasi',
    },
    {
      id: 2,
      type: 'delivery',
      title: 'Pesanan Siap Diantar',
      message: 'Pesanan #12345 siap untuk diantar',
      time: '1 jam yang lalu',
      icon: Truck,
      color: 'text-teal-500',
      link: '/orders?tab=siap-antar',
    },
    {
      id: 3,
      type: 'complete',
      title: '<PERSON><PERSON><PERSON>',
      message: 'Pesanan #12344 telah selesai',
      time: '2 jam yang lalu',
      icon: CheckCircle,
      color: 'text-green-500',
      link: '/orders?tab=selesai',
    },
    {
      id: 4,
      type: 'alert',
      title: 'Pesanan Terlambat',
      message: 'Pesanan #12343 telah melewati batas waktu',
      time: '3 jam yang lalu',
      icon: AlertCircle,
      color: 'text-red-500',
      link: '/orders?tab=terlambat',
    },
  ];

  const handleNotificationClick = (link: string) => {
    router.push(link);
  };

  return (
    <div className="flex flex-col min-h-screen bg-slate-50">
      <header className="p-4 flex justify-between items-center">
        <div className="flex items-center">
          <Image
            src="/logo.png"
            alt="Obilas"
            width={120}
            height={40}
            className="h-10 w-auto"
          />
        </div>
        <h1 className="text-xl font-semibold text-gray-800">Notifikasi</h1>
      </header>

      <main className="flex-1 px-4 pb-20">
        <div className="space-y-4">
          {notifications.map((notification) => (
            <Card
              key={notification.id}
              className="p-4 hover:bg-gray-50 transition-colors cursor-pointer"
              onClick={() => handleNotificationClick(notification.link)}
            >
              <div className="flex items-start gap-4">
                <div
                  className={`p-2 rounded-full bg-gray-100 ${notification.color}`}
                >
                  <notification.icon className="h-6 w-6" />
                </div>
                <div className="flex-1">
                  <div className="flex justify-between items-start">
                    <h3 className="font-medium text-gray-800">
                      {notification.title}
                    </h3>
                    <span className="text-sm text-gray-500">
                      {notification.time}
                    </span>
                  </div>
                  <p className="text-gray-600 text-sm mt-1">
                    {notification.message}
                  </p>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </main>

      <BottomNavigation activePage="notifikasi" />
    </div>
  );
}
