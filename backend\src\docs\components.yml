components:
  schemas:
    User:
      type: object
      properties:
        id:
          type: string
        email:
          type: string
          format: email
        name:
          type: string
        role:
          type: string
          enum: [ADMIN, OWNER, EMPLOYEE]
      example:
        id: 5ebac534954b54139806c112
        email: <EMAIL>
        name: fake name
        role: OWNER

    Token:
      type: object
      properties:
        token:
          type: string
        expires:
          type: string
          format: date-time
      example:
        token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI1ZWJhYzUzNDk1NGI1NDEzOTgwNmMxMTIiLCJpYXQiOjE1ODkyOTg0ODQsImV4cCI6MTU4OTMwMDI4NH0.m1U63blB0MLej_WfB7yC2FTMnCziif9X8yzwDEfJXAg
        expires: 2020-05-12T16:18:04.793Z

    AuthTokens:
      type: object
      properties:
        access:
          $ref: '#/components/schemas/Token'
        refresh:
          $ref: '#/components/schemas/Token'

    Customer:
      type: object
      properties:
        id:
          type: integer
          description: Customer ID
        name:
          type: string
          description: Customer name
        phone:
          type: string
          description: Customer phone number
        email:
          type: string
          format: email
          description: Customer email address
        address:
          type: string
          description: Customer address
        mapLink:
          type: string
          format: uri
          description: Google Maps or other map link
        latitude:
          type: number
          description: Latitude coordinate
        longitude:
          type: number
          description: Longitude coordinate
        provinceId:
          type: integer
          description: Province ID
        cityId:
          type: integer
          description: City ID
        status:
          type: string
          enum: [ACTIVE, INACTIVE, NEW]
          description: Customer status
        customerType:
          type: string
          enum: [INDIVIDUAL, CORPORATE]
          description: Customer type
        source:
          type: string
          description: How customer found the service
        labels:
          type: array
          items:
            type: string
          description: Customer labels/tags
        photos:
          type: array
          items:
            type: string
            format: uri
          description: Customer photo URLs
        notes:
          type: string
          description: Additional notes about customer
        totalOrders:
          type: integer
          description: Total number of orders
        lastOrderDate:
          type: string
          format: date-time
          description: Date of last order
        joinDate:
          type: string
          format: date-time
          description: Date customer joined
        createdAt:
          type: string
          format: date-time
          description: Creation timestamp
        updatedAt:
          type: string
          format: date-time
          description: Last update timestamp
        financialData:
          type: object
          properties:
            totalSpent:
              type: number
              description: Total amount spent
            loyaltyPoints:
              type: integer
              description: Loyalty points balance
            deposit:
              type: number
              description: Deposit balance
            debt:
              type: number
              description: Outstanding debt
            cashback:
              type: number
              description: Cashback balance
            preferredPaymentMethod:
              type: string
              description: Preferred payment method
            creditLimit:
              type: number
              description: Credit limit
      example:
        id: 1
        name: John Doe
        phone: '081234567890'
        email: <EMAIL>
        address: 'Jl. Sudirman No. 123, Jakarta'
        status: ACTIVE
        customerType: INDIVIDUAL
        source: 'Social Media'
        labels: ['VIP', 'Regular']
        totalOrders: 5
        joinDate: '2024-01-15T10:30:00.000Z'

    CustomerNote:
      type: object
      properties:
        id:
          type: integer
          description: Note ID
        customerId:
          type: integer
          description: Customer ID
        text:
          type: string
          description: Note content
        author:
          type: string
          description: Note author name
        createdAt:
          type: string
          format: date-time
          description: Creation timestamp
      example:
        id: 1
        customerId: 1
        text: 'Customer prefers morning pickup'
        author: 'John Admin'
        createdAt: '2024-01-15T10:30:00.000Z'

    Error:
      type: object
      properties:
        code:
          type: number
        message:
          type: string

  responses:
    DuplicateEmail:
      description: Email already taken
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: 400
            message: Email already taken
    BadRequest:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: 400
            message: Bad Request
    Unauthorized:
      description: Unauthorized
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: 401
            message: Please authenticate
    Forbidden:
      description: Forbidden
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: 403
            message: Forbidden
    NotFound:
      description: Not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: 404
            message: Not found

  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
