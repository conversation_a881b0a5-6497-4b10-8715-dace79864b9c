# 📋 **Dokumentasi Update Order Status Backend**

## 🎯 **Overview**

Update besar-besaran pada sistem status order backend telah selesai dilakukan. Perubahan ini mencakup:

1. **Update enum OrderStatus dan OrderItemStatus**
2. **Logika bisnis untuk handling status CANCELLED**
3. **Auto-update order status berdasarkan item status**
4. **Refund otomatis untuk order yang dibatalkan**

---

## 🔄 **Perubahan Status**

### **1. Order Status (Sebelum → Sesudah)**

| **Status Lama**  | **Status Baru**  | **Keterangan**                   |
| ---------------- | ---------------- | -------------------------------- |
| PICKUP           | PICKUP           | Tetap sama                       |
| PENDING          | KONFIRMASI       | Default status baru              |
| PROCESSING       | PENDING          | Untuk order yang belum diproses  |
| WASHING          | PROCESSING       | Untuk order yang sedang diproses |
| DRYING           | READY            | Order siap diambil               |
| IRONING          | READY_FOR_PICKUP | Order siap untuk delivery        |
| READY            | COMPLETED        | Order selesai                    |
| READY_FOR_PICKUP | CANCELLED        | Order dibatalkan                 |
| DELIVERED        | -                | Dihapus                          |
| CANCELLED        | -                | Tetap sama                       |

### **2. Order Item Status (Sebelum → Sesudah)**

| **Status Lama** | **Status Baru** | **Keterangan** |
| --------------- | --------------- | -------------- |
| PENDING         | PENDING         | Tetap sama     |
| PROCESSING      | -               | Dihapus        |
| WASHING         | WASHING         | Tetap sama     |
| DRYING          | DRYING          | Tetap sama     |
| IRONING         | IRONING         | Tetap sama     |
| READY           | PACKING         | Proses packing |
| COMPLETED       | COMPLETED       | Tetap sama     |
| CANCELLED       | CANCELLED       | Tetap sama     |

---

## 🚀 **Fitur Baru**

### **1. Auto-Update Order Status**

#### **Dari Item Status ke Order Status:**

- ✅ **Jika ada item berubah dari PENDING → WASHING/DRYING/IRONING/PACKING:** Order otomatis jadi **PROCESSING**
- ✅ **Jika semua item COMPLETED + tidak ada delivery_date:** Order otomatis jadi **READY**
- ✅ **Jika semua item COMPLETED + ada delivery_date:** Order otomatis jadi **READY_FOR_PICKUP**

#### **Logic Flow:**

```javascript
// Item status berubah dari PENDING ke processing stages
if (hasProcessingItems && order.status === 'PENDING') {
  order.status = 'PROCESSING';
}

// Semua item sudah completed
if (allItemsCompleted) {
  if (order.deliveryDate) {
    order.status = 'READY_FOR_PICKUP';
  } else {
    order.status = 'READY';
  }
  order.actualFinish = new Date();
}
```

### **2. Order Cancellation dengan Refund**

#### **Ketika Order Status → CANCELLED:**

1. ✅ **Semua order items** otomatis jadi **CANCELLED**
2. ✅ **Refund otomatis** jika order sudah dibayar:
   - **CASH/TRANSFER/E_WALLET:** Kurangi saldo cashbox
   - **DEPOSIT:** Tambah kembali saldo deposit customer
3. ✅ **Update payment status** jadi **REFUNDED**
4. ✅ **Reset paidAmount** jadi 0

#### **Logic Flow:**

```javascript
if (status === 'CANCELLED') {
  // Cancel all items
  await updateMany({ status: 'CANCELLED' });

  // Refund payments
  for (payment of payments) {
    if (payment.method === 'DEPOSIT') {
      // Add back to customer deposit
      await addDeposit(customerId, payment.amount);
    } else if (payment.cashboxId) {
      // Reduce cashbox balance
      await reduceCashbox(payment.cashboxId, payment.amount);
    }
  }

  // Update order
  order.paymentStatus = 'REFUNDED';
  order.paidAmount = 0;
}
```

---

## 📝 **File yang Diubah**

### **1. Database Schema**

- ✅ `prisma/schema.prisma` - Update enum OrderStatus & OrderItemStatus
- ✅ Migration: `20250619020549_update_order_status_enums`

### **2. Validation**

- ✅ `src/validations/order.validation.ts` - Update semua enum validation

### **3. Service Layer**

- ✅ `src/services/order.service.ts` - Logic auto-update & cancellation
- ✅ `src/services/outlet.service.ts` - Update stats query

### **4. API Documentation**

- ✅ `src/routes/v1/order.route.ts` - Update semua Swagger docs

---

## 🎯 **Benefits untuk Frontend**

### **1. Status Management yang Konsisten**

- Status lebih jelas dan sesuai dengan flow bisnis laundry
- Mapping yang mudah untuk UI components

### **2. Auto-Update Reduces Manual Work**

- Order status otomatis update berdasarkan progress item
- Tidak perlu manual update order status

### **3. Safe Cancellation**

- Pembatalan order otomatis handle refund
- Tidak ada kehilangan data payment

### **4. Clear Status Flow**

```
KONFIRMASI → PICKUP → PENDING → PROCESSING → READY/READY_FOR_PICKUP → COMPLETED
                                     ↓
                                 CANCELLED (dengan refund)
```

---

## ⚠️ **Breaking Changes**

### **1. Frontend Perlu Update**

- Enum status lama tidak bisa digunakan
- UI perlu mapping ulang status

### **2. API Response Changes**

- Default order status sekarang **KONFIRMASI** (bukan PENDING)
- Item status tidak ada **PROCESSING** dan **READY**

### **3. Database Migration Required**

- Existing data dengan status lama akan error
- Perlu migration data untuk production

---

## 🧪 **Testing**

### **Status yang Perlu Ditest:**

#### **Order Status:**

- ✅ KONFIRMASI (default)
- ✅ PICKUP
- ✅ PENDING
- ✅ PROCESSING (auto-update dari item)
- ✅ READY (auto-update dari item)
- ✅ READY_FOR_PICKUP (auto-update dari item)
- ✅ COMPLETED
- ✅ CANCELLED (dengan refund)

#### **Order Item Status:**

- ✅ PENDING
- ✅ WASHING → DRYING → IRONING → PACKING → COMPLETED
- ✅ CANCELLED

#### **Auto-Update Scenarios:**

- ✅ Item PENDING → WASHING: Order jadi PROCESSING
- ✅ Semua item COMPLETED: Order jadi READY/READY_FOR_PICKUP
- ✅ Order CANCELLED: Semua item jadi CANCELLED + refund

---

## 🚀 **Next Steps**

1. **Frontend Update:** Update enum dan UI mapping
2. **Data Migration:** Migrate existing orders di production
3. **Testing:** Test semua flow status baru
4. **Documentation:** Update API docs untuk frontend team

Update ini memberikan foundation yang solid untuk sistem order management yang lebih robust dan user-friendly! 🎉
