"use client"

import { <PERSON><PERSON> } from "@/components/ui/badge"

import { useState } from "react"
import Link from "next/link"
import {
  ArrowLeft,
  Save,
  Printer,
  QrCode,
  FileText,
  Wifi,
  Bluetooth,
  Usb,
  Plus,
  RefreshCw,
  Edit,
  Trash2,
  Layout,
  ImageIcon,
  Type,
  Phone,
  MapPin,
  Calendar,
  Clock,
  DollarSign,
  ShoppingBag,
  User,
} from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Slider } from "@/components/ui/slider"
import { toast } from "@/components/ui/use-toast"
import { Toaster } from "@/components/ui/toaster"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"

export default function PrinterSettingsPage() {
  const [isSearching, setIsSearching] = useState(false)
  const [selectedPrinter, setSelectedPrinter] = useState<string | null>(null)
  const [selectedTemplate, setSelectedTemplate] = useState("template1")
  const [testPrinting, setTestPrinting] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)

  // Mock printers
  const [availablePrinters, setAvailablePrinters] = useState([
    { id: "printer1", name: "Epson TM-T82", type: "Thermal", connected: true, lastUsed: "2 jam yang lalu" },
    { id: "printer2", name: "HP LaserJet Pro", type: "Laser", connected: false, lastUsed: "1 hari yang lalu" },
    { id: "printer3", name: "Brother QL-800", type: "Label", connected: false, lastUsed: "3 hari yang lalu" },
  ])

  // Receipt settings
  const [receiptSettings, setReceiptSettings] = useState({
    paperSize: "thermal_80mm",
    orientation: "portrait",
    fontSize: 10,
    showLogo: true,
    showAddress: true,
    showPhone: true,
    showEmail: true,
    showDate: true,
    showTime: true,
    showOrderId: true,
    showCustomerInfo: true,
    showItemDetails: true,
    showSubtotal: true,
    showTax: true,
    showDiscount: true,
    showTotal: true,
    showPaymentMethod: true,
    showBarcode: true,
    showQrCode: true,
    showFooterMessage: true,
    footerMessage: "Terima kasih telah menggunakan jasa kami!",
    copies: 1,
    autoPrint: true,
    printOnStatusChange: true,
    printOnPayment: true,
    marginTop: 10,
    marginBottom: 10,
    marginLeft: 5,
    marginRight: 5,
  })

  // QR Code settings
  const [qrSettings, setQrSettings] = useState({
    size: 100,
    includeOrderId: true,
    includeCustomerInfo: true,
    includeAmount: true,
    includeDate: true,
    includeOutletInfo: true,
    errorCorrection: "M",
    foregroundColor: "#000000",
    backgroundColor: "#FFFFFF",
    logo: true,
    logoSize: 20,
  })

  // Templates
  const templates = [
    { id: "template1", name: "Standar", description: "Template standar dengan logo dan informasi lengkap" },
    { id: "template2", name: "Minimalis", description: "Template simpel dengan informasi penting saja" },
    { id: "template3", name: "Detail", description: "Template dengan informasi detail dan QR code" },
  ]

  const handleSearchPrinters = () => {
    setIsSearching(true)

    // Simulate finding printers
    setTimeout(() => {
      setAvailablePrinters([
        ...availablePrinters,
        { id: "printer4", name: "Canon PIXMA TS3522", type: "Inkjet", connected: false, lastUsed: "Belum pernah" },
      ])
      setIsSearching(false)
      toast({
        title: "Pencarian selesai",
        description: "Ditemukan 1 printer baru",
      })
    }, 2000)
  }

  const handleConnectPrinter = (printerId: string) => {
    // Update the connected status
    const updatedPrinters = availablePrinters.map((printer) =>
      printer.id === printerId
        ? { ...printer, connected: true }
        : { ...printer, connected: printer.id === selectedPrinter ? false : printer.connected },
    )

    setAvailablePrinters(updatedPrinters)
    setSelectedPrinter(printerId)

    toast({
      title: "Printer terhubung",
      description: `Berhasil terhubung dengan ${updatedPrinters.find((p) => p.id === printerId)?.name}`,
    })
  }

  const handleDisconnectPrinter = (printerId: string) => {
    const updatedPrinters = availablePrinters.map((printer) =>
      printer.id === printerId ? { ...printer, connected: false } : printer,
    )

    setAvailablePrinters(updatedPrinters)
    if (selectedPrinter === printerId) {
      setSelectedPrinter(null)
    }

    toast({
      title: "Printer terputus",
      description: `Berhasil memutuskan koneksi dengan ${updatedPrinters.find((p) => p.id === printerId)?.name}`,
    })
  }

  const handleTestPrint = () => {
    if (!selectedPrinter) {
      toast({
        title: "Tidak ada printer terhubung",
        description: "Silakan hubungkan printer terlebih dahulu",
        variant: "destructive",
      })
      return
    }

    setTestPrinting(true)

    // Simulate printing
    setTimeout(() => {
      setTestPrinting(false)
      toast({
        title: "Cetak berhasil",
        description: "Nota uji coba telah dicetak",
      })
    }, 2000)
  }

  const handleSaveSettings = () => {
    toast({
      title: "Pengaturan disimpan",
      description: "Pengaturan printer dan nota berhasil disimpan",
    })
  }

  const handleDeleteTemplate = () => {
    setIsDeleting(true)

    // Simulate deletion
    setTimeout(() => {
      setIsDeleting(false)
      setSelectedTemplate("template1")
      toast({
        title: "Template dihapus",
        description: "Template berhasil dihapus",
      })
    }, 1000)
  }

  const handleSettingChange = (key: string, value: any) => {
    setReceiptSettings({
      ...receiptSettings,
      [key]: value,
    })
  }

  const handleQrSettingChange = (key: string, value: any) => {
    setQrSettings({
      ...qrSettings,
      [key]: value,
    })
  }

  return (
    <div className="flex flex-col min-h-screen bg-slate-50">
      <Toaster />
      <header className="p-4 flex justify-between items-center bg-white sticky top-0 z-10 shadow-sm">
        <div className="flex items-center">
          <Link href="/akun/settings" className="mr-3">
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <h1 className="text-lg font-semibold">Pengaturan Printer & Nota</h1>
        </div>
        <Button onClick={handleSaveSettings} className="bg-blue-500 hover:bg-blue-600">
          <Save className="h-4 w-4 mr-2" /> Simpan
        </Button>
      </header>

      <main className="flex-1 p-4 pb-20">
        <Tabs defaultValue="printers" className="w-full">
          <TabsList className="grid grid-cols-4 mb-4">
            <TabsTrigger value="printers">
              <Printer className="h-4 w-4 mr-2" /> Printer
            </TabsTrigger>
            <TabsTrigger value="receipt">
              <FileText className="h-4 w-4 mr-2" /> Nota
            </TabsTrigger>
            <TabsTrigger value="templates">
              <Layout className="h-4 w-4 mr-2" /> Template
            </TabsTrigger>
            <TabsTrigger value="qrcode">
              <QrCode className="h-4 w-4 mr-2" /> QR Code
            </TabsTrigger>
          </TabsList>

          {/* Printer Settings Tab */}
          <TabsContent value="printers" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Printer Tersedia</CardTitle>
                <CardDescription>Kelola printer yang terhubung dengan aplikasi</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex gap-2">
                  <Button variant="outline" className="flex-1" onClick={handleSearchPrinters} disabled={isSearching}>
                    {isSearching ? (
                      <>
                        <RefreshCw className="h-4 w-4 mr-2 animate-spin" /> Mencari...
                      </>
                    ) : (
                      <>
                        <RefreshCw className="h-4 w-4 mr-2" /> Cari Printer
                      </>
                    )}
                  </Button>
                  <Dialog>
                    <DialogTrigger asChild>
                      <Button variant="outline" className="flex-1">
                        <Plus className="h-4 w-4 mr-2" /> Tambah Manual
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Tambah Printer Manual</DialogTitle>
                        <DialogDescription>Masukkan detail printer yang ingin ditambahkan</DialogDescription>
                      </DialogHeader>
                      <div className="space-y-4 py-4">
                        <div className="space-y-2">
                          <Label htmlFor="printer-name">Nama Printer</Label>
                          <Input id="printer-name" placeholder="Contoh: Epson TM-T82" />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="printer-type">Tipe Printer</Label>
                          <Select defaultValue="thermal">
                            <SelectTrigger id="printer-type">
                              <SelectValue placeholder="Pilih tipe printer" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="thermal">Thermal</SelectItem>
                              <SelectItem value="inkjet">Inkjet</SelectItem>
                              <SelectItem value="laser">Laser</SelectItem>
                              <SelectItem value="label">Label</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="connection-type">Tipe Koneksi</Label>
                          <RadioGroup defaultValue="wifi" className="flex gap-4">
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="wifi" id="wifi" />
                              <Label htmlFor="wifi" className="flex items-center">
                                <Wifi className="h-4 w-4 mr-1" /> WiFi
                              </Label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="bluetooth" id="bluetooth" />
                              <Label htmlFor="bluetooth" className="flex items-center">
                                <Bluetooth className="h-4 w-4 mr-1" /> Bluetooth
                              </Label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="usb" id="usb" />
                              <Label htmlFor="usb" className="flex items-center">
                                <Usb className="h-4 w-4 mr-1" /> USB
                              </Label>
                            </div>
                          </RadioGroup>
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="printer-address">Alamat IP / ID Perangkat</Label>
                          <Input id="printer-address" placeholder="Contoh: *************" />
                        </div>
                      </div>
                      <DialogFooter>
                        <Button type="submit">Tambah Printer</Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                </div>

                <div className="space-y-3">
                  {availablePrinters.map((printer) => (
                    <Card
                      key={printer.id}
                      className={`border ${printer.connected ? "border-green-500" : "border-gray-200"}`}
                    >
                      <CardContent className="p-4">
                        <div className="flex justify-between items-center">
                          <div>
                            <h3 className="font-medium">{printer.name}</h3>
                            <div className="flex items-center gap-2 text-sm text-gray-500">
                              <span>{printer.type}</span>
                              <span>•</span>
                              <span>Terakhir digunakan: {printer.lastUsed}</span>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            {printer.connected ? (
                              <>
                                <Badge className="bg-green-500">Terhubung</Badge>
                                <Button variant="outline" size="sm" onClick={() => handleDisconnectPrinter(printer.id)}>
                                  Putuskan
                                </Button>
                              </>
                            ) : (
                              <Button variant="outline" size="sm" onClick={() => handleConnectPrinter(printer.id)}>
                                Hubungkan
                              </Button>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" onClick={handleTestPrint} disabled={!selectedPrinter || testPrinting}>
                  {testPrinting ? (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" /> Mencetak...
                    </>
                  ) : (
                    <>
                      <Printer className="h-4 w-4 mr-2" /> Cetak Uji Coba
                    </>
                  )}
                </Button>
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button variant="outline" className="text-red-500 hover:text-red-600 hover:bg-red-50">
                      <Trash2 className="h-4 w-4 mr-2" /> Hapus Printer
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Hapus Printer</AlertDialogTitle>
                      <AlertDialogDescription>
                        Apakah Anda yakin ingin menghapus printer ini? Tindakan ini tidak dapat dibatalkan.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Batal</AlertDialogCancel>
                      <AlertDialogAction className="bg-red-500 hover:bg-red-600">Hapus</AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </CardFooter>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Pengaturan Koneksi</CardTitle>
                <CardDescription>Konfigurasi koneksi printer</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="auto-connect" className="cursor-pointer">
                    <div>Hubungkan Otomatis</div>
                    <p className="text-sm text-gray-500">Hubungkan otomatis ke printer terakhir saat aplikasi dibuka</p>
                  </Label>
                  <Switch id="auto-connect" defaultChecked />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="retry-connect" className="cursor-pointer">
                    <div>Coba Hubungkan Ulang</div>
                    <p className="text-sm text-gray-500">Coba hubungkan ulang jika koneksi terputus</p>
                  </Label>
                  <Switch id="retry-connect" defaultChecked />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="retry-attempts">Jumlah Percobaan Ulang</Label>
                  <Select defaultValue="3">
                    <SelectTrigger id="retry-attempts">
                      <SelectValue placeholder="Pilih jumlah percobaan" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">1 kali</SelectItem>
                      <SelectItem value="3">3 kali</SelectItem>
                      <SelectItem value="5">5 kali</SelectItem>
                      <SelectItem value="10">10 kali</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Receipt Settings Tab */}
          <TabsContent value="receipt" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Pengaturan Kertas</CardTitle>
                <CardDescription>Konfigurasi ukuran dan orientasi kertas</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="paper-size">Ukuran Kertas</Label>
                  <Select
                    value={receiptSettings.paperSize}
                    onValueChange={(value) => handleSettingChange("paperSize", value)}
                  >
                    <SelectTrigger id="paper-size">
                      <SelectValue placeholder="Pilih ukuran kertas" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="thermal_58mm">Thermal 58mm</SelectItem>
                      <SelectItem value="thermal_80mm">Thermal 80mm</SelectItem>
                      <SelectItem value="a4">A4</SelectItem>
                      <SelectItem value="a5">A5</SelectItem>
                      <SelectItem value="letter">Letter</SelectItem>
                      <SelectItem value="custom">Kustom</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {receiptSettings.paperSize === "custom" && (
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="paper-width">Lebar (mm)</Label>
                      <Input id="paper-width" type="number" defaultValue="80" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="paper-height">Panjang (mm)</Label>
                      <Input id="paper-height" type="number" defaultValue="297" />
                    </div>
                  </div>
                )}

                <div className="space-y-2">
                  <Label htmlFor="orientation">Orientasi</Label>
                  <Select
                    value={receiptSettings.orientation}
                    onValueChange={(value) => handleSettingChange("orientation", value)}
                  >
                    <SelectTrigger id="orientation">
                      <SelectValue placeholder="Pilih orientasi" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="portrait">Portrait</SelectItem>
                      <SelectItem value="landscape">Landscape</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="font-size">Ukuran Font</Label>
                  <div className="flex items-center gap-4">
                    <Slider
                      id="font-size"
                      min={8}
                      max={16}
                      step={1}
                      value={[receiptSettings.fontSize]}
                      onValueChange={(value) => handleSettingChange("fontSize", value[0])}
                      className="flex-1"
                    />
                    <span className="w-8 text-center">{receiptSettings.fontSize}pt</span>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Margin (mm)</Label>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="margin-top" className="text-xs">
                        Atas
                      </Label>
                      <Input
                        id="margin-top"
                        type="number"
                        value={receiptSettings.marginTop}
                        onChange={(e) => handleSettingChange("marginTop", Number.parseInt(e.target.value))}
                        className="h-8"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="margin-bottom" className="text-xs">
                        Bawah
                      </Label>
                      <Input
                        id="margin-bottom"
                        type="number"
                        value={receiptSettings.marginBottom}
                        onChange={(e) => handleSettingChange("marginBottom", Number.parseInt(e.target.value))}
                        className="h-8"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="margin-left" className="text-xs">
                        Kiri
                      </Label>
                      <Input
                        id="margin-left"
                        type="number"
                        value={receiptSettings.marginLeft}
                        onChange={(e) => handleSettingChange("marginLeft", Number.parseInt(e.target.value))}
                        className="h-8"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="margin-right" className="text-xs">
                        Kanan
                      </Label>
                      <Input
                        id="margin-right"
                        type="number"
                        value={receiptSettings.marginRight}
                        onChange={(e) => handleSettingChange("marginRight", Number.parseInt(e.target.value))}
                        className="h-8"
                      />
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="copies">Jumlah Salinan</Label>
                  <Select
                    value={receiptSettings.copies.toString()}
                    onValueChange={(value) => handleSettingChange("copies", Number.parseInt(value))}
                  >
                    <SelectTrigger id="copies">
                      <SelectValue placeholder="Pilih jumlah salinan" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">1 salinan</SelectItem>
                      <SelectItem value="2">2 salinan</SelectItem>
                      <SelectItem value="3">3 salinan</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Konten Nota</CardTitle>
                <CardDescription>Pilih informasi yang akan ditampilkan pada nota</CardDescription>
              </CardHeader>
              <CardContent>
                <Accordion type="multiple" defaultValue={["header", "content", "footer"]} className="w-full">
                  <AccordionItem value="header">
                    <AccordionTrigger>Header</AccordionTrigger>
                    <AccordionContent className="space-y-4 pt-4">
                      <div className="flex items-center justify-between">
                        <Label htmlFor="show-logo" className="cursor-pointer flex items-center">
                          <ImageIcon className="h-4 w-4 mr-2 text-gray-500" />
                          <span>Tampilkan Logo</span>
                        </Label>
                        <Switch
                          id="show-logo"
                          checked={receiptSettings.showLogo}
                          onCheckedChange={(checked) => handleSettingChange("showLogo", checked)}
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <Label htmlFor="show-address" className="cursor-pointer flex items-center">
                          <MapPin className="h-4 w-4 mr-2 text-gray-500" />
                          <span>Tampilkan Alamat</span>
                        </Label>
                        <Switch
                          id="show-address"
                          checked={receiptSettings.showAddress}
                          onCheckedChange={(checked) => handleSettingChange("showAddress", checked)}
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <Label htmlFor="show-phone" className="cursor-pointer flex items-center">
                          <Phone className="h-4 w-4 mr-2 text-gray-500" />
                          <span>Tampilkan Nomor Telepon</span>
                        </Label>
                        <Switch
                          id="show-phone"
                          checked={receiptSettings.showPhone}
                          onCheckedChange={(checked) => handleSettingChange("showPhone", checked)}
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <Label htmlFor="show-date" className="cursor-pointer flex items-center">
                          <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                          <span>Tampilkan Tanggal</span>
                        </Label>
                        <Switch
                          id="show-date"
                          checked={receiptSettings.showDate}
                          onCheckedChange={(checked) => handleSettingChange("showDate", checked)}
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <Label htmlFor="show-time" className="cursor-pointer flex items-center">
                          <Clock className="h-4 w-4 mr-2 text-gray-500" />
                          <span>Tampilkan Waktu</span>
                        </Label>
                        <Switch
                          id="show-time"
                          checked={receiptSettings.showTime}
                          onCheckedChange={(checked) => handleSettingChange("showTime", checked)}
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <Label htmlFor="show-order-id" className="cursor-pointer flex items-center">
                          <FileText className="h-4 w-4 mr-2 text-gray-500" />
                          <span>Tampilkan ID Pesanan</span>
                        </Label>
                        <Switch
                          id="show-order-id"
                          checked={receiptSettings.showOrderId}
                          onCheckedChange={(checked) => handleSettingChange("showOrderId", checked)}
                        />
                      </div>
                    </AccordionContent>
                  </AccordionItem>

                  <AccordionItem value="content">
                    <AccordionTrigger>Konten Utama</AccordionTrigger>
                    <AccordionContent className="space-y-4 pt-4">
                      <div className="flex items-center justify-between">
                        <Label htmlFor="show-customer-info" className="cursor-pointer flex items-center">
                          <User className="h-4 w-4 mr-2 text-gray-500" />
                          <span>Tampilkan Info Pelanggan</span>
                        </Label>
                        <Switch
                          id="show-customer-info"
                          checked={receiptSettings.showCustomerInfo}
                          onCheckedChange={(checked) => handleSettingChange("showCustomerInfo", checked)}
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <Label htmlFor="show-item-details" className="cursor-pointer flex items-center">
                          <ShoppingBag className="h-4 w-4 mr-2 text-gray-500" />
                          <span>Tampilkan Detail Item</span>
                        </Label>
                        <Switch
                          id="show-item-details"
                          checked={receiptSettings.showItemDetails}
                          onCheckedChange={(checked) => handleSettingChange("showItemDetails", checked)}
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <Label htmlFor="show-subtotal" className="cursor-pointer flex items-center">
                          <DollarSign className="h-4 w-4 mr-2 text-gray-500" />
                          <span>Tampilkan Subtotal</span>
                        </Label>
                        <Switch
                          id="show-subtotal"
                          checked={receiptSettings.showSubtotal}
                          onCheckedChange={(checked) => handleSettingChange("showSubtotal", checked)}
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <Label htmlFor="show-tax" className="cursor-pointer flex items-center">
                          <DollarSign className="h-4 w-4 mr-2 text-gray-500" />
                          <span>Tampilkan Pajak</span>
                        </Label>
                        <Switch
                          id="show-tax"
                          checked={receiptSettings.showTax}
                          onCheckedChange={(checked) => handleSettingChange("showTax", checked)}
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <Label htmlFor="show-discount" className="cursor-pointer flex items-center">
                          <DollarSign className="h-4 w-4 mr-2 text-gray-500" />
                          <span>Tampilkan Diskon</span>
                        </Label>
                        <Switch
                          id="show-discount"
                          checked={receiptSettings.showDiscount}
                          onCheckedChange={(checked) => handleSettingChange("showDiscount", checked)}
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <Label htmlFor="show-total" className="cursor-pointer flex items-center">
                          <DollarSign className="h-4 w-4 mr-2 text-gray-500" />
                          <span>Tampilkan Total</span>
                        </Label>
                        <Switch
                          id="show-total"
                          checked={receiptSettings.showTotal}
                          onCheckedChange={(checked) => handleSettingChange("showTotal", checked)}
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <Label htmlFor="show-payment-method" className="cursor-pointer flex items-center">
                          <DollarSign className="h-4 w-4 mr-2 text-gray-500" />
                          <span>Tampilkan Metode Pembayaran</span>
                        </Label>
                        <Switch
                          id="show-payment-method"
                          checked={receiptSettings.showPaymentMethod}
                          onCheckedChange={(checked) => handleSettingChange("showPaymentMethod", checked)}
                        />
                      </div>
                    </AccordionContent>
                  </AccordionItem>

                  <AccordionItem value="footer">
                    <AccordionTrigger>Footer</AccordionTrigger>
                    <AccordionContent className="space-y-4 pt-4">
                      <div className="flex items-center justify-between">
                        <Label htmlFor="show-barcode" className="cursor-pointer flex items-center">
                          <QrCode className="h-4 w-4 mr-2 text-gray-500" />
                          <span>Tampilkan Barcode</span>
                        </Label>
                        <Switch
                          id="show-barcode"
                          checked={receiptSettings.showBarcode}
                          onCheckedChange={(checked) => handleSettingChange("showBarcode", checked)}
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <Label htmlFor="show-qr-code" className="cursor-pointer flex items-center">
                          <QrCode className="h-4 w-4 mr-2 text-gray-500" />
                          <span>Tampilkan QR Code</span>
                        </Label>
                        <Switch
                          id="show-qr-code"
                          checked={receiptSettings.showQrCode}
                          onCheckedChange={(checked) => handleSettingChange("showQrCode", checked)}
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <Label htmlFor="show-footer-message" className="cursor-pointer flex items-center">
                          <Type className="h-4 w-4 mr-2 text-gray-500" />
                          <span>Tampilkan Pesan Footer</span>
                        </Label>
                        <Switch
                          id="show-footer-message"
                          checked={receiptSettings.showFooterMessage}
                          onCheckedChange={(checked) => handleSettingChange("showFooterMessage", checked)}
                        />
                      </div>

                      {receiptSettings.showFooterMessage && (
                        <div className="space-y-2">
                          <Label htmlFor="footer-message">Pesan Footer</Label>
                          <Textarea
                            id="footer-message"
                            placeholder="Masukkan pesan footer"
                            value={receiptSettings.footerMessage}
                            onChange={(e) => handleSettingChange("footerMessage", e.target.value)}
                          />
                        </div>
                      )}
                    </AccordionContent>
                  </AccordionItem>
                </Accordion>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Pengaturan Cetak</CardTitle>
                <CardDescription>Konfigurasi kapan nota akan dicetak secara otomatis</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="auto-print" className="cursor-pointer">
                    <div>Cetak Otomatis</div>
                    <p className="text-sm text-gray-500">Cetak nota secara otomatis setelah transaksi selesai</p>
                  </Label>
                  <Switch
                    id="auto-print"
                    checked={receiptSettings.autoPrint}
                    onCheckedChange={(checked) => handleSettingChange("autoPrint", checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="print-on-status-change" className="cursor-pointer">
                    <div>Cetak Saat Status Berubah</div>
                    <p className="text-sm text-gray-500">Cetak nota saat status pesanan berubah</p>
                  </Label>
                  <Switch
                    id="print-on-status-change"
                    checked={receiptSettings.printOnStatusChange}
                    onCheckedChange={(checked) => handleSettingChange("printOnStatusChange", checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="print-on-payment" className="cursor-pointer">
                    <div>Cetak Saat Pembayaran</div>
                    <p className="text-sm text-gray-500">Cetak nota saat pembayaran diterima</p>
                  </Label>
                  <Switch
                    id="print-on-payment"
                    checked={receiptSettings.printOnPayment}
                    onCheckedChange={(checked) => handleSettingChange("printOnPayment", checked)}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Templates Tab */}
          <TabsContent value="templates" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Template Nota</CardTitle>
                <CardDescription>Pilih dan kustomisasi template nota</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {templates.map((template) => (
                    <Card
                      key={template.id}
                      className={`cursor-pointer border-2 ${selectedTemplate === template.id ? "border-blue-500" : "border-gray-200"}`}
                      onClick={() => setSelectedTemplate(template.id)}
                    >
                      <CardContent className="p-4 flex flex-col items-center">
                        <div className="w-full h-40 bg-gray-100 rounded-md mb-3 flex items-center justify-center">
                          <FileText className="h-12 w-12 text-gray-400" />
                        </div>
                        <h3 className="font-medium">{template.name}</h3>
                        <p className="text-sm text-gray-500 text-center">{template.description}</p>
                      </CardContent>
                    </Card>
                  ))}
                </div>

                <div className="flex justify-between">
                  <Button variant="outline">
                    <Plus className="h-4 w-4 mr-2" /> Buat Template Baru
                  </Button>
                  <div className="flex gap-2">
                    <Button variant="outline" disabled={selectedTemplate === "template1"}>
                      <Edit className="h-4 w-4 mr-2" /> Edit Template
                    </Button>
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button
                          variant="outline"
                          className="text-red-500 hover:text-red-600 hover:bg-red-50"
                          disabled={selectedTemplate === "template1"}
                        >
                          <Trash2 className="h-4 w-4 mr-2" /> Hapus
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Hapus Template</AlertDialogTitle>
                          <AlertDialogDescription>
                            Apakah Anda yakin ingin menghapus template ini? Tindakan ini tidak dapat dibatalkan.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Batal</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={handleDeleteTemplate}
                            className="bg-red-500 hover:bg-red-600"
                            disabled={isDeleting}
                          >
                            {isDeleting ? "Menghapus..." : "Hapus"}
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Pratinjau Template</CardTitle>
                <CardDescription>Lihat pratinjau template yang dipilih</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="bg-white border border-gray-200 rounded-md p-4 max-w-xs mx-auto">
                  <div className="flex flex-col items-center mb-4">
                    <div className="w-16 h-16 bg-gray-100 rounded-full mb-2 flex items-center justify-center">
                      <ImageIcon className="h-8 w-8 text-gray-400" />
                    </div>
                    <h3 className="font-bold text-center">Laundry App</h3>
                    <p className="text-xs text-center text-gray-500">Jl. Merdeka No. 123, Jakarta</p>
                    <p className="text-xs text-center text-gray-500">Telp: 021-1234567</p>
                  </div>

                  <div className="border-t border-b border-dashed border-gray-200 py-2 mb-3">
                    <div className="flex justify-between text-xs">
                      <span>No. Order:</span>
                      <span className="font-medium">INV/2025/03/001</span>
                    </div>
                    <div className="flex justify-between text-xs">
                      <span>Tanggal:</span>
                      <span>01/03/2025 14:30</span>
                    </div>
                    <div className="flex justify-between text-xs">
                      <span>Kasir:</span>
                      <span>Admin</span>
                    </div>
                    <div className="flex justify-between text-xs">
                      <span>Pelanggan:</span>
                      <span>Budi Santoso</span>
                    </div>
                  </div>

                  <div className="mb-3">
                    <div className="text-xs font-medium mb-1">Item:</div>
                    <div className="text-xs mb-1">
                      <div className="flex justify-between">
                        <span>Cuci + Setrika (5 Pcs)</span>
                        <span>75.000</span>
                      </div>
                      <div className="text-xs text-gray-500">@15.000</div>
                    </div>
                    <div className="text-xs mb-1">
                      <div className="flex justify-between">
                        <span>Cuci Selimut (1 Pcs)</span>
                        <span>25.000</span>
                      </div>
                      <div className="text-xs text-gray-500">@25.000</div>
                    </div>
                  </div>

                  <div className="border-t border-dashed border-gray-200 pt-2 mb-3">
                    <div className="flex justify-between text-xs">
                      <span>Subtotal:</span>
                      <span>100.000</span>
                    </div>
                    <div className="flex justify-between text-xs">
                      <span>Diskon:</span>
                      <span>0</span>
                    </div>
                    <div className="flex justify-between text-xs font-bold">
                      <span>Total:</span>
                      <span>100.000</span>
                    </div>
                    <div className="flex justify-between text-xs">
                      <span>Metode Pembayaran:</span>
                      <span>Tunai</span>
                    </div>
                  </div>

                  <div className="flex justify-center mb-2">
                    <QrCode className="h-16 w-16 text-gray-800" />
                  </div>

                  <p className="text-xs text-center">Terima kasih telah menggunakan jasa kami!</p>
                  <p className="text-xs text-center">Pesanan dapat diambil pada: 03/03/2025</p>
                </div>
              </CardContent>
              <CardFooter className="flex justify-center">
                <Button variant="outline" onClick={handleTestPrint} disabled={!selectedPrinter || testPrinting}>
                  {testPrinting ? (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" /> Mencetak...
                    </>
                  ) : (
                    <>
                      <Printer className="h-4 w-4 mr-2" /> Cetak Pratinjau
                    </>
                  )}
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>

          {/* QR Code Tab */}
          <TabsContent value="qrcode" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Pengaturan QR Code</CardTitle>
                <CardDescription>Konfigurasi QR code yang ditampilkan pada nota</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="qr-size">Ukuran QR Code</Label>
                  <div className="flex items-center gap-4">
                    <Slider
                      id="qr-size"
                      min={50}
                      max={200}
                      step={10}
                      value={[qrSettings.size]}
                      onValueChange={(value) => handleQrSettingChange("size", value[0])}
                      className="flex-1"
                    />
                    <span className="w-12 text-center">{qrSettings.size}px</span>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Informasi yang Disertakan</Label>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="include-order-id" className="cursor-pointer">
                        <div>ID Pesanan</div>
                      </Label>
                      <Switch
                        id="include-order-id"
                        checked={qrSettings.includeOrderId}
                        onCheckedChange={(checked) => handleQrSettingChange("includeOrderId", checked)}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="include-customer-info" className="cursor-pointer">
                        <div>Informasi Pelanggan</div>
                      </Label>
                      <Switch
                        id="include-customer-info"
                        checked={qrSettings.includeCustomerInfo}
                        onCheckedChange={(checked) => handleQrSettingChange("includeCustomerInfo", checked)}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="include-amount" className="cursor-pointer">
                        <div>Jumlah Pembayaran</div>
                      </Label>
                      <Switch
                        id="include-amount"
                        checked={qrSettings.includeAmount}
                        onCheckedChange={(checked) => handleQrSettingChange("includeAmount", checked)}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="include-date" className="cursor-pointer">
                        <div>Tanggal Transaksi</div>
                      </Label>
                      <Switch
                        id="include-date"
                        checked={qrSettings.includeDate}
                        onCheckedChange={(checked) => handleQrSettingChange("includeDate", checked)}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="include-outlet-info" className="cursor-pointer">
                        <div>Informasi Outlet</div>
                      </Label>
                      <Switch
                        id="include-outlet-info"
                        checked={qrSettings.includeOutletInfo}
                        onCheckedChange={(checked) => handleQrSettingChange("includeOutletInfo", checked)}
                      />
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="error-correction">Koreksi Kesalahan</Label>
                  <Select
                    value={qrSettings.errorCorrection}
                    onValueChange={(value) => handleQrSettingChange("errorCorrection", value)}
                  >
                    <SelectTrigger id="error-correction">
                      <SelectValue placeholder="Pilih level koreksi kesalahan" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="L">Rendah (7%)</SelectItem>
                      <SelectItem value="M">Sedang (15%)</SelectItem>
                      <SelectItem value="Q">Tinggi (25%)</SelectItem>
                      <SelectItem value="H">Sangat Tinggi (30%)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="foreground-color">Warna Depan</Label>
                    <div className="flex">
                      <Input
                        id="foreground-color"
                        type="color"
                        value={qrSettings.foregroundColor}
                        onChange={(e) => handleQrSettingChange("foregroundColor", e.target.value)}
                        className="w-12 p-1 h-10"
                      />
                      <Input
                        type="text"
                        value={qrSettings.foregroundColor}
                        onChange={(e) => handleQrSettingChange("foregroundColor", e.target.value)}
                        className="flex-1 ml-2"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="background-color">Warna Latar</Label>
                    <div className="flex">
                      <Input
                        id="background-color"
                        type="color"
                        value={qrSettings.backgroundColor}
                        onChange={(e) => handleQrSettingChange("backgroundColor", e.target.value)}
                        className="w-12 p-1 h-10"
                      />
                      <Input
                        type="text"
                        value={qrSettings.backgroundColor}
                        onChange={(e) => handleQrSettingChange("backgroundColor", e.target.value)}
                        className="flex-1 ml-2"
                      />
                    </div>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="include-logo" className="cursor-pointer">
                    <div>Sertakan Logo di Tengah</div>
                    <p className="text-sm text-gray-500">Tampilkan logo bisnis di tengah QR code</p>
                  </Label>
                  <Switch
                    id="include-logo"
                    checked={qrSettings.logo}
                    onCheckedChange={(checked) => handleQrSettingChange("logo", checked)}
                  />
                </div>

                {qrSettings.logo && (
                  <div className="space-y-2">
                    <Label htmlFor="logo-size">Ukuran Logo (%)</Label>
                    <div className="flex items-center gap-4">
                      <Slider
                        id="logo-size"
                        min={10}
                        max={30}
                        step={5}
                        value={[qrSettings.logoSize]}
                        onValueChange={(value) => handleQrSettingChange("logoSize", value[0])}
                        className="flex-1"
                      />
                      <span className="w-12 text-center">{qrSettings.logoSize}%</span>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Pratinjau QR Code</CardTitle>
                <CardDescription>Lihat pratinjau QR code dengan pengaturan saat ini</CardDescription>
              </CardHeader>
              <CardContent className="flex justify-center">
                <div
                  className="relative"
                  style={{
                    width: `${qrSettings.size}px`,
                    height: `${qrSettings.size}px`,
                    backgroundColor: qrSettings.backgroundColor,
                    padding: "10px",
                    borderRadius: "8px",
                  }}
                >
                  <QrCode className="w-full h-full" style={{ color: qrSettings.foregroundColor }} />

                  {qrSettings.logo && (
                    <div
                      className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white rounded-full p-1"
                      style={{
                        width: `${qrSettings.logoSize}%`,
                        height: `${qrSettings.logoSize}%`,
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                      }}
                    >
                      <ImageIcon className="w-full h-full text-blue-500" />
                    </div>
                  )}
                </div>
              </CardContent>
              <CardFooter className="flex justify-center">
                <Button variant="outline">
                  <RefreshCw className="h-4 w-4 mr-2" /> Regenerasi QR Code
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
        </Tabs>
      </main>
    </div>
  )
}
