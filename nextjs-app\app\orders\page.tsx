'use client';

import type React from 'react';

import {
  Search,
  X,
  CheckCircle,
  XCircle,
  AlertCircle,
  Settings,
  QrCode,
} from 'lucide-react';
import Image from 'next/image';
import { useState, useEffect } from 'react';
import { useSearch<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import Link from 'next/link';

import BottomNavigation from '@/components/bottom-navigation';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
  SheetClose,
} from '@/components/ui/sheet';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Card, CardContent } from '@/components/ui/card';
import { getIcon, getIconColor, type IconName } from '../../lib/icons';

// Import API hooks
import { useOrders, useOrderCounts } from '@/hooks/useOrders';
import type { Order, OrderStatus, PaymentStatus } from '@/types/orders';

type ProcessStatus =
  | 'none'
  | 'cuci'
  | 'kering'
  | 'setrika'
  | 'packing'
  | 'selesai';

interface Service {
  name: string;
  icon: IconName;
  weight?: number;
  quantity?: number;
}

interface OrderItem {
  id: number;
  name: string;
  price: number;
  quantity: number;
  unitPrice: number;
  status: ProcessStatus;
  icon: React.ReactNode;
  timeline: {
    status: string;
    time: string;
  }[];
}

const getStatusLabel = (status: ProcessStatus): string => {
  switch (status) {
    case 'none':
      return 'Belum Diproses';
    case 'cuci':
      return 'Proses Cuci';
    case 'kering':
      return 'Proses Kering';
    case 'setrika':
      return 'Proses Setrika';
    case 'packing':
      return 'Proses Packing';
    case 'selesai':
      return 'Selesai';
    default:
      return 'Proses';
  }
};

// Mapping functions to convert between UI states and API enums
const mapTabToStatus = (tab: string): OrderStatus | undefined => {
  const statusMap: Record<string, OrderStatus> = {
    konfirmasi: 'KONFIRMASI',
    penjemputan: 'PICKUP',
    validasi: 'PENDING',
    antrian: 'PENDING',
    proses: 'PROCESSING',
    'siap-ambil': 'READY',
    'siap-antar': 'READY_FOR_PICKUP',
    selesai: 'COMPLETED',
    batal: 'CANCELLED',
  };
  return statusMap[tab];
};

const mapPaymentStatus = (status: string): PaymentStatus | undefined => {
  const statusMap: Record<string, PaymentStatus> = {
    paid: 'PAID',
    unpaid: 'UNPAID',
    refunded: 'REFUNDED',
    partial: 'PARTIAL',
  };
  return status === 'all' ? undefined : statusMap[status];
};

const mapSortBy = (sort: string): string => {
  const sortMap: Record<string, string> = {
    newest: 'createdAt:desc',
    oldest: 'createdAt:asc',
    highest: 'totalPrice:desc',
    lowest: 'totalPrice:asc',
  };
  return sortMap[sort] || 'createdAt:desc';
};

const mapStatusToTab = (status: OrderStatus): string => {
  const tabMap: Record<OrderStatus, string> = {
    KONFIRMASI: 'konfirmasi',
    PICKUP: 'penjemputan',
    PENDING: 'antrian',
    PROCESSING: 'proses',
    READY: 'siap-ambil',
    READY_FOR_PICKUP: 'siap-antar',
    COMPLETED: 'selesai',
    CANCELLED: 'batal',
  };
  return tabMap[status] || 'antrian';
};

const mapStatusToProcessStage = (status: OrderStatus): string | undefined => {
  const stageMap: Partial<Record<OrderStatus, string>> = {
    PROCESSING: 'cuci',
    READY: 'packing',
  };
  return stageMap[status];
};

// Helper function to format dates
const formatApiDate = (dateString: string): string => {
  return new Intl.DateTimeFormat('id-ID', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(new Date(dateString));
};

// Helper function to get payment status labels
const getPaymentStatusLabel = (status: PaymentStatus): string => {
  const labels: Record<PaymentStatus, string> = {
    UNPAID: 'Belum Lunas',
    PARTIAL: 'Bayar Sebagian',
    PAID: 'Lunas',
    REFUNDED: 'Dikembalikan',
  };
  return labels[status];
};

// Convert API Order to UI Order format
const convertApiOrderToUI = (
  apiOrder: Order
): {
  id: string;
  orderId: number; // Add numeric ID for routing
  customer: string;
  orderDate: string;
  estimatedCompletion: string;
  status: string;
  type: string;
  paymentStatus: string;
  priority: string;
  totalAmount: number;
  services: Service[];
  perfume: string;
  notes: string;
  processStage?: string;
  completionDate?: string;
  cancelDate?: string;
  rating?: number;
} => {
  return {
    id: apiOrder.orderNumber, // Display ID
    orderId: apiOrder.id, // Numeric ID for routing
    customer: apiOrder.customer?.name || 'Unknown',
    orderDate: formatApiDate(apiOrder.createdAt),
    estimatedCompletion: apiOrder.estimatedFinish
      ? formatApiDate(apiOrder.estimatedFinish)
      : 'Belum ditentukan',
    status: getPaymentStatusLabel(apiOrder.paymentStatus),
    type: mapStatusToTab(apiOrder.status),
    paymentStatus: apiOrder.paymentStatus.toLowerCase(),
    priority: 'normal', // TODO: Add priority field to API
    totalAmount: apiOrder.totalPrice,
    services: apiOrder.items.map((item) => ({
      name: item.serviceName || 'Unknown Service',
      icon: 'tshirt' as IconName, // TODO: Map service to icon
      weight: item.quantity,
      quantity: item.quantity,
    })),
    perfume: apiOrder.perfumeName || 'No Perfume',
    notes: apiOrder.notes || '',
    processStage: mapStatusToProcessStage(apiOrder.status),
    completionDate: apiOrder.actualFinish
      ? formatApiDate(apiOrder.actualFinish)
      : undefined,
    cancelDate:
      apiOrder.status === 'CANCELLED' && apiOrder.updatedAt
        ? formatApiDate(apiOrder.updatedAt)
        : undefined,
    rating: undefined, // TODO: Add rating system
  };
};

export default function PesananPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const tabParam = searchParams.get('tab');
  const subTabParam = searchParams.get('subtab');
  const searchParam = searchParams.get('search') || '';
  const sortParam = searchParams.get('sort') || 'newest';
  const statusParam = searchParams.get('status') || 'all';

  const [activeTab, setActiveTab] = useState('antrian');
  const [activeSubTab, setActiveSubTab] = useState(subTabParam || 'cuci');
  const [searchQuery, setSearchQuery] = useState(searchParam);
  const [sortBy, setSortBy] = useState(sortParam);
  const [paymentStatus, setPaymentStatus] = useState(statusParam);
  const [selectedFilters, setSelectedFilters] = useState<string[]>([]);
  const [displayMode, setDisplayMode] = useState<'default' | 'custom'>(
    'default'
  );
  const [isFilterSheetOpen, setIsFilterSheetOpen] = useState(false);

  // API Integration - Get orders with filters
  const apiStatus = mapTabToStatus(activeTab);
  const apiPaymentStatus = mapPaymentStatus(paymentStatus);
  const apiSortBy = mapSortBy(sortBy);

  const {
    data: ordersData,
    isLoading,
    error,
  } = useOrders({
    search: searchQuery || undefined,
    status: apiStatus,
    paymentStatus: apiPaymentStatus,
    sortBy: apiSortBy,
    limit: 50,
    page: 1,
  });

  // Get order counts for tabs
  const orderCounts = useOrderCounts();

  // Convert API data to UI format
  const orders = ordersData?.results?.map(convertApiOrderToUI) || [];

  // Filter orders by sub-tab for process tab
  const filteredOrders = orders.filter((order) => {
    if (activeTab === 'proses' && order.processStage) {
      return order.processStage === activeSubTab;
    }
    return true;
  });

  useEffect(() => {
    if (tabParam) {
      setActiveTab(tabParam);
    }
  }, [tabParam]);

  useEffect(() => {
    if (subTabParam) {
      setActiveSubTab(subTabParam);
    }
  }, [subTabParam]);

  // Tab configuration dengan counts dari API
  const tabs = [
    { id: 'konfirmasi', label: 'Konfirmasi', count: orderCounts.konfirmasi },
    { id: 'penjemputan', label: 'Penjemputan', count: orderCounts.pickup },
    { id: 'antrian', label: 'Antrian', count: orderCounts.pending },
    { id: 'proses', label: 'Proses', count: orderCounts.processing },
    { id: 'siap-ambil', label: 'Siap Ambil', count: orderCounts.ready },
    {
      id: 'siap-antar',
      label: 'Siap Antar',
      count: orderCounts.ready_for_pickup,
    },
    { id: 'selesai', label: 'Selesai', count: orderCounts.completed },
    { id: 'batal', label: 'Batal', count: orderCounts.cancelled },
  ];

  const processTabs = [
    { id: 'semua', label: 'Semua', count: orderCounts.processing },
    { id: 'cuci', label: 'Cuci', count: 0 }, // TODO: Implement sub-status counts
    { id: 'kering', label: 'Kering', count: 0 },
    { id: 'setrika', label: 'Setrika', count: 0 },
    { id: 'packing', label: 'Packing', count: 0 },
  ];

  // Sort orders
  const sortedOrders = [...filteredOrders].sort((a, b) => {
    if (sortBy === 'newest') {
      return new Date(b.orderDate).getTime() - new Date(a.orderDate).getTime();
    } else if (sortBy === 'oldest') {
      return new Date(a.orderDate).getTime() - new Date(b.orderDate).getTime();
    } else if (sortBy === 'highest') {
      return b.totalAmount - a.totalAmount;
    } else if (sortBy === 'lowest') {
      return a.totalAmount - b.totalAmount;
    }
    return 0;
  });

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    updateQueryParams();
  };

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
    // Reset sub-tab when changing main tab
    if (tabId === 'proses') {
      setActiveSubTab('cuci');
    }
    updateQueryParams({ tab: tabId });
  };

  const handleSubTabChange = (subTabId: string) => {
    setActiveSubTab(subTabId);
    updateQueryParams({ subtab: subTabId });
  };

  const handleSortChange = (value: string) => {
    setSortBy(value);
    updateQueryParams({ sort: value });
  };

  const handleStatusChange = (value: string) => {
    setPaymentStatus(value);
    updateQueryParams({ status: value });
  };

  const handleFilterChange = (value: string) => {
    setSelectedFilters((prev) => {
      if (prev.includes(value)) {
        return prev.filter((item) => item !== value);
      } else {
        return [...prev, value];
      }
    });
  };

  const clearFilters = () => {
    setSearchQuery('');
    setSortBy('newest');
    setPaymentStatus('all');
    setSelectedFilters([]);
    router.push(
      `/orders?tab=${activeTab}${
        activeTab === 'proses' ? `&subtab=${activeSubTab}` : ''
      }`
    );
  };

  const updateQueryParams = (additionalParams?: Record<string, string>) => {
    const params = new URLSearchParams();

    if (activeTab) params.set('tab', additionalParams?.tab || activeTab);
    if (activeTab === 'proses' && activeSubTab)
      params.set('subtab', additionalParams?.subtab || activeSubTab);
    if (searchQuery) params.set('search', searchQuery);
    if (sortBy) params.set('sort', additionalParams?.sort || sortBy);
    if (paymentStatus)
      params.set('status', additionalParams?.status || paymentStatus);

    router.push(`/orders?${params.toString()}`);
  };

  const getTabCount = (tabId: string) => {
    const count = tabs.find((tab) => tab.id === tabId)?.count || 0;
    return count > 0 ? `(${count})` : '';
  };

  const getSubTabCount = (tabId: string) => {
    const count = processTabs.find((tab) => tab.id === tabId)?.count || 0;
    return count > 0 ? `(${count})` : '';
  };

  const getStatusBadge = (order: any) => {
    if (order.type === 'selesai') {
      return (
        <Badge
          variant="outline"
          className="border-green-500 text-green-500 flex items-center gap-1"
        >
          <CheckCircle className="h-3 w-3" />
          Selesai
        </Badge>
      );
    } else if (order.type === 'batal') {
      return (
        <Badge
          variant="outline"
          className="border-red-500 text-red-500 flex items-center gap-1"
        >
          <XCircle className="h-3 w-3" />
          Dibatalkan
        </Badge>
      );
    } else {
      return (
        <Badge
          variant={order.paymentStatus === 'paid' ? 'outline' : 'destructive'}
          className={
            order.paymentStatus === 'paid'
              ? 'border-green-500 text-green-500'
              : 'bg-red-600 hover:bg-red-700'
          }
        >
          {order.status}
        </Badge>
      );
    }
  };

  const renderServiceIcons = (services: Service[]) => {
    if (!services || services.length === 0) return null;

    return (
      <div className="flex -space-x-3 overflow-hidden">
        {services.slice(0, 3).map((service, index) => {
          // Menentukan ukuran berdasarkan jumlah layanan
          let size = 'w-12 h-12';
          let iconSize = 'w-8 h-8';

          if (services.length === 2) {
            size = 'w-9 h-9';
            iconSize = 'w-6 h-6';
          } else if (services.length >= 3) {
            size = 'w-8 h-8';
            iconSize = 'w-4 h-4';
          }

          return (
            <div
              key={index}
              className={`${size} rounded-full bg-blue-100 flex items-center justify-center border-2 border-white aspect-square`}
              title={service.name}
            >
              {getIcon(service.icon, {
                className: `${getIconColor(service.icon)} ${iconSize}`,
              })}
            </div>
          );
        })}
      </div>
    );
  };

  const getServiceDetails = (services: any) => {
    if (!services || services.length === 0) return '';

    return services
      .map((service: any) => {
        if (service.name.includes('Kiloan') && service.weight) {
          return `${service.weight} kg`;
        } else if (service.quantity) {
          return `${service.quantity} item`;
        }
        return '';
      })
      .filter(Boolean)
      .join(', ');
  };

  // Sample order for preview
  const sampleOrder = {
    id: 'TRX/250324/014',
    customer: 'Budi',
    orderDate: '24/03/2025 19:00:38',
    estimatedCompletion: 'Terlambat 8 Hari',
    status: 'Belum Lunas',
    type: 'antrian',
    paymentStatus: 'unpaid',
    priority: 'normal',
    totalAmount: 125000,
    services: [{ name: 'Laundry Kiloan', icon: 'tshirt', weight: 5.2 }],
    perfume: 'Lavender',
    notes: 'Tolong disetrika rapi',
  };

  // Set default display options based on mode
  const handleDisplayModeChange = (mode: 'default' | 'custom') => {
    setDisplayMode(mode);
    if (mode === 'default') {
      setSelectedFilters([]);
    }
  };

  // Render order item preview based on current display settings
  const renderOrderPreview = (order: any) => {
    return (
      <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-200 mt-4">
        <div className="flex justify-between items-start mb-2">
          <h3 className="text-blue-500 font-medium">{order.id}</h3>
          {getStatusBadge(order)}
        </div>
        <div className="flex gap-4">
          {/* Only show service icons in default mode */}
          {displayMode === 'default' && (
            <div className="w-20 h-20 bg-gray-100 rounded-lg flex items-center justify-center p-2">
              {renderServiceIcons(order.services)}
            </div>
          )}
          <div className="flex-1">
            <div className="flex items-center gap-2">
              <h4 className="text-lg font-medium">{order.customer}</h4>
              {order.priority === 'high' && (
                <Badge className="bg-amber-500">Prioritas</Badge>
              )}
            </div>
            <div className="flex justify-between gap-x-4 text-gray-500 text-xs">
              <div>
                {/* Default mode - show standard information */}
                {displayMode === 'default' && (
                  <>
                    <p>Tanggal Pesan</p>
                    <p>Estimasi Selesai</p>
                    <p>Total</p>
                  </>
                )}

                {/* Custom mode - show selected information */}
                {displayMode === 'custom' &&
                  selectedFilters.includes('weight') &&
                  order.services &&
                  order.services.length > 0 && <p>Layanan</p>}
                {displayMode === 'custom' &&
                  selectedFilters.includes('perfume') &&
                  order.perfume && <p>Parfum</p>}
                {displayMode === 'custom' &&
                  selectedFilters.includes('date') && <p>Tanggal Pesan</p>}
                {displayMode === 'custom' &&
                  selectedFilters.includes('date') && <p>Estimasi Selesai</p>}
                {displayMode === 'custom' &&
                  selectedFilters.includes('total') && <p>Total</p>}
              </div>
              <div className="text-right">
                {/* Default mode - show standard values */}
                {displayMode === 'default' && (
                  <>
                    <p>{order.orderDate}</p>
                    <p className="text-red-500">{order.estimatedCompletion}</p>
                    <p className="font-medium text-gray-700">
                      Rp {order.totalAmount.toLocaleString()}
                    </p>
                  </>
                )}

                {/* Custom mode - show selected values */}
                {displayMode === 'custom' &&
                  selectedFilters.includes('weight') &&
                  order.services &&
                  order.services.length > 0 && (
                    <p>{getServiceDetails(order.services)}</p>
                  )}
                {displayMode === 'custom' &&
                  selectedFilters.includes('perfume') &&
                  order.perfume && <p>{order.perfume}</p>}
                {displayMode === 'custom' &&
                  selectedFilters.includes('date') && <p>{order.orderDate}</p>}
                {displayMode === 'custom' &&
                  selectedFilters.includes('date') &&
                  (order.type === 'selesai' ? (
                    <p className="text-red-500">{order.estimatedCompletion}</p>
                  ) : order.type === 'batal' ? (
                    <p className="text-red-500">{order.cancelDate}</p>
                  ) : (
                    <p className="text-red-500">{order.estimatedCompletion}</p>
                  ))}
                {displayMode === 'custom' &&
                  selectedFilters.includes('total') && (
                    <p className="font-medium text-gray-700">
                      Rp {order.totalAmount.toLocaleString()}
                    </p>
                  )}
              </div>
            </div>
            {displayMode === 'custom' &&
              selectedFilters.includes('notes') &&
              order.notes && (
                <div className="mt-1 text-sm text-gray-500">
                  <span className="font-medium">Catatan:</span> {order.notes}
                </div>
              )}

            {/* Show order items if enabled in custom mode */}
            {displayMode === 'custom' &&
              selectedFilters.includes('items') &&
              order.services && (
                <div className="mt-3 pt-3 border-t">
                  <h5 className="text-sm font-medium mb-2">Item Pesanan:</h5>
                  <div className="divide-y">
                    {/* Menggunakan data services dari API yang sudah dikonversi */}
                    {order.services.slice(0, 3).map((service, index) => (
                      <div key={index} className="py-2.5">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            {getIcon(service.icon, {
                              className: `${getIconColor(
                                service.icon
                              )} h-4 w-4`,
                            })}
                            <span className="font-medium text-sm">
                              {service.name}
                            </span>
                          </div>
                          <div className="flex gap-1">
                            <Badge
                              variant={'outline'}
                              className="text-xs h-5 px-2"
                            >
                              {service.quantity} {service.weight ? 'kg' : 'pcs'}
                            </Badge>
                            <Badge
                              variant={'outline'}
                              className="text-xs h-5 px-2"
                            >
                              Diproses
                            </Badge>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
          </div>
        </div>
      </div>
    );
  };

  // Render loading content for list section only
  const renderOrdersList = () => {
    if (isLoading) {
      return (
        <div className="space-y-4">
          {[...Array(3)].map((_, index) => (
            <div
              key={index}
              className="bg-white rounded-lg p-4 shadow-sm border border-gray-200"
            >
              <div className="animate-pulse">
                <div className="flex justify-between items-start mb-2">
                  <div className="h-4 bg-gray-200 rounded w-32"></div>
                  <div className="h-6 bg-gray-200 rounded w-20"></div>
                </div>
                <div className="flex gap-4">
                  {displayMode === 'default' && (
                    <div className="w-20 h-20 bg-gray-200 rounded-lg"></div>
                  )}
                  <div className="flex-1">
                    <div className="h-5 bg-gray-200 rounded w-24 mb-2"></div>
                    <div className="space-y-1">
                      <div className="h-3 bg-gray-200 rounded w-full"></div>
                      <div className="h-3 bg-gray-200 rounded w-3/4"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      );
    }

    if (error) {
      return (
        <Card>
          <CardContent className="py-8 flex flex-col items-center justify-center">
            <AlertCircle className="h-8 w-8 text-red-500 mb-2" />
            <p className="text-red-600 mb-4 text-center">
              Gagal memuat pesanan
            </p>
            <Button onClick={() => window.location.reload()}>Coba Lagi</Button>
          </CardContent>
        </Card>
      );
    }

    if (sortedOrders.length > 0) {
      return (
        <div className="space-y-4">
          {sortedOrders.map((order) => (
            <Link
              key={order.id}
              href={`/orders/${order.orderId}`}
              className="block"
            >
              <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-200">
                <div className="flex justify-between items-start mb-2">
                  <h3 className="text-blue-500 font-medium">{order.id}</h3>
                  {getStatusBadge(order)}
                </div>
                <div className="flex gap-4">
                  {displayMode === 'default' && (
                    <div className="w-20 h-20 bg-gray-100 rounded-lg flex items-center justify-center p-2">
                      {renderServiceIcons(order.services)}
                    </div>
                  )}
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <h4 className="text-lg font-medium">{order.customer}</h4>
                      {order.priority === 'high' && (
                        <Badge className="bg-amber-500">Prioritas</Badge>
                      )}
                    </div>
                    <div className="flex justify-between gap-x-4 text-gray-500 text-xs">
                      <div>
                        {/* Default mode - show standard information */}
                        {displayMode === 'default' && (
                          <>
                            <p>Tanggal Pesan</p>
                            <p>Estimasi Selesai</p>
                            <p>Total</p>
                          </>
                        )}

                        {/* Custom mode - show selected information */}
                        {displayMode === 'custom' &&
                          selectedFilters.includes('weight') &&
                          order.services &&
                          order.services.length > 0 && <p>Layanan</p>}
                        {displayMode === 'custom' &&
                          selectedFilters.includes('perfume') &&
                          order.perfume && <p>Parfum</p>}
                        {displayMode === 'custom' &&
                          selectedFilters.includes('date') && (
                            <p>Tanggal Pesan</p>
                          )}
                        {displayMode === 'custom' &&
                          selectedFilters.includes('date') &&
                          (order.type === 'selesai' ? (
                            <p>Tanggal Selesai</p>
                          ) : order.type === 'batal' ? (
                            <p>Tanggal Batal</p>
                          ) : (
                            <p>Estimasi Selesai</p>
                          ))}
                        {displayMode === 'custom' &&
                          selectedFilters.includes('total') && <p>Total</p>}
                      </div>
                      <div className="text-right">
                        {/* Default mode - show standard values */}
                        {displayMode === 'default' && (
                          <>
                            <p>{order.orderDate}</p>
                            <p className="text-red-500">
                              {order.estimatedCompletion}
                            </p>
                            <p className="font-medium text-gray-700">
                              Rp {order.totalAmount.toLocaleString()}
                            </p>
                          </>
                        )}

                        {/* Custom mode - show selected values */}
                        {displayMode === 'custom' &&
                          selectedFilters.includes('weight') &&
                          order.services &&
                          order.services.length > 0 && (
                            <p>{getServiceDetails(order.services)}</p>
                          )}
                        {displayMode === 'custom' &&
                          selectedFilters.includes('perfume') &&
                          order.perfume && <p>{order.perfume}</p>}
                        {displayMode === 'custom' &&
                          selectedFilters.includes('date') && (
                            <p>{order.orderDate}</p>
                          )}
                        {displayMode === 'custom' &&
                          selectedFilters.includes('date') &&
                          (order.type === 'selesai' ? (
                            <p className="text-green-500">
                              {order.completionDate}
                            </p>
                          ) : order.type === 'batal' ? (
                            <p className="text-red-500">{order.cancelDate}</p>
                          ) : (
                            <p className="text-red-500">
                              {order.estimatedCompletion}
                            </p>
                          ))}
                        {displayMode === 'custom' &&
                          selectedFilters.includes('total') && (
                            <p className="font-medium text-gray-700">
                              Rp {order.totalAmount.toLocaleString()}
                            </p>
                          )}
                      </div>
                    </div>
                    {displayMode === 'custom' &&
                      selectedFilters.includes('notes') &&
                      order.notes && (
                        <div className="mt-1 text-sm text-gray-500">
                          <span className="font-medium">Catatan:</span>{' '}
                          {order.notes}
                        </div>
                      )}

                    {/* Show order items if enabled in custom mode */}
                    {displayMode === 'custom' &&
                      selectedFilters.includes('items') &&
                      order.services && (
                        <div className="mt-3 pt-3 border-t">
                          <h5 className="text-sm font-medium mb-2">
                            Item Pesanan:
                          </h5>
                          <div className="space-y-2">
                            <div className="divide-y">
                              {order.services
                                .slice(0, 3)
                                .map((service, index) => (
                                  <div key={index} className="py-2.5">
                                    <div className="flex items-center justify-between">
                                      <div className="flex items-center gap-2">
                                        {getIcon(service.icon, {
                                          className: `${getIconColor(
                                            service.icon
                                          )} h-4 w-4`,
                                        })}
                                        <span className="font-medium text-sm">
                                          {service.name}
                                        </span>
                                      </div>
                                      <div className="flex gap-1">
                                        <Badge
                                          variant={'outline'}
                                          className="text-xs h-5 px-2"
                                        >
                                          {service.quantity}{' '}
                                          {service.weight ? 'kg' : 'pcs'}
                                        </Badge>
                                        <Badge
                                          variant={'outline'}
                                          className="text-xs h-5 px-2"
                                        >
                                          Diproses
                                        </Badge>
                                      </div>
                                    </div>
                                  </div>
                                ))}
                            </div>
                          </div>
                        </div>
                      )}
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>
      );
    }

    return (
      <Card>
        <CardContent className="py-8 flex items-center justify-center">
          <AlertCircle className="h-6 w-6 text-gray-500 mr-2" />
          Tidak ada pesanan yang ditemukan.
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="flex flex-col min-h-screen bg-slate-50">
      <header className="p-4 flex justify-between items-center">
        <div className="flex items-center">
          <Image
            src="/logo.png"
            alt="Obilas"
            width={120}
            height={40}
            className="h-10 w-auto"
          />
        </div>
        <div className="flex items-center gap-4">
          <Sheet>
            <SheetTrigger asChild>
              <Button variant="ghost" size="icon">
                <Settings className="h-6 w-6 text-blue-500" />
              </Button>
            </SheetTrigger>
            <SheetContent className="bg-white overflow-y-auto" side="top">
              <SheetHeader>
                <SheetTitle>Pengaturan Tampilan</SheetTitle>
                <SheetDescription>
                  Pilih mode tampilan untuk daftar pesanan
                </SheetDescription>
              </SheetHeader>
              <div className="py-4 space-y-6">
                <RadioGroup
                  value={displayMode}
                  onValueChange={(value) =>
                    handleDisplayModeChange(value as 'default' | 'custom')
                  }
                  className="space-y-3"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="default" id="display-default" />
                    <Label htmlFor="display-default" className="font-medium">
                      Default
                    </Label>
                  </div>
                  <div className="pl-6 text-sm text-gray-500">
                    Menampilkan icon, nama, kode transaksi, status pembayaran,
                    estimasi selesai, tanggal pesan, dan total bayar.
                  </div>

                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="custom" id="display-custom" />
                    <Label htmlFor="display-custom" className="font-medium">
                      Custom
                    </Label>
                  </div>
                </RadioGroup>

                {displayMode === 'custom' && (
                  <div className="space-y-4 pt-2 border-t">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="weight" className="flex-1">
                        Berat/Jumlah Item
                      </Label>
                      <Switch
                        id="weight"
                        checked={selectedFilters.includes('weight')}
                        onCheckedChange={(checked) =>
                          handleFilterChange('weight')
                        }
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <Label htmlFor="perfume" className="flex-1">
                        Parfum
                      </Label>
                      <Switch
                        id="perfume"
                        checked={selectedFilters.includes('perfume')}
                        onCheckedChange={(checked) =>
                          handleFilterChange('perfume')
                        }
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <Label htmlFor="notes" className="flex-1">
                        Catatan
                      </Label>
                      <Switch
                        id="notes"
                        checked={selectedFilters.includes('notes')}
                        onCheckedChange={(checked) =>
                          handleFilterChange('notes')
                        }
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <Label htmlFor="date" className="flex-1">
                        Tanggal
                      </Label>
                      <Switch
                        id="date"
                        checked={selectedFilters.includes('date')}
                        onCheckedChange={(checked) =>
                          handleFilterChange('date')
                        }
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <Label htmlFor="total" className="flex-1">
                        Total Harga
                      </Label>
                      <Switch
                        id="total"
                        checked={selectedFilters.includes('total')}
                        onCheckedChange={(checked) =>
                          handleFilterChange('total')
                        }
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <Label htmlFor="items" className="flex-1">
                        Tampilkan List Item Pesanan
                      </Label>
                      <Switch
                        id="items"
                        checked={selectedFilters.includes('items')}
                        onCheckedChange={(checked) =>
                          handleFilterChange('items')
                        }
                      />
                    </div>
                  </div>
                )}

                <div className="pt-4 border-t">
                  <h3 className="text-sm font-medium mb-2">
                    Preview Tampilan:
                  </h3>
                  {renderOrderPreview(sampleOrder)}
                </div>

                <div className="pt-4 flex justify-end">
                  <SheetClose asChild>
                    <Button className="bg-blue-500 hover:bg-blue-600">
                      Simpan
                    </Button>
                  </SheetClose>
                </div>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </header>

      <main className="flex-1 px-4 pb-20">
        <div className="mb-6 relative">
          <h1 className="text-2xl font-semibold text-gray-800">
            Hai, Felis Laundry
          </h1>
          <p className="text-gray-600">Ini status orderan kamu</p>
          <button className="absolute right-0 top-1/2 -translate-y-1/2">
            <QrCode className="h-10 w-10 text-blue-500" />
          </button>
        </div>

        <div className="overflow-x-auto -mx-4 px-4">
          <div className="flex space-x-6 min-w-max pb-2">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                className={`pb-2 px-1 font-medium ${
                  activeTab === tab.id
                    ? 'text-blue-500 border-b-2 border-blue-500'
                    : 'text-gray-600'
                }`}
                onClick={() => handleTabChange(tab.id)}
              >
                {tab.label} {getTabCount(tab.id)}
              </button>
            ))}
          </div>
        </div>
        <Separator className="mb-4" />

        {/* Process sub-tabs */}
        {activeTab === 'proses' && (
          <div className="mb-4">
            <Tabs
              value={activeSubTab}
              onValueChange={handleSubTabChange}
              className="w-full"
            >
              <TabsList className="grid grid-cols-5 mb-2">
                {processTabs.map((tab) => (
                  <TabsTrigger key={tab.id} value={tab.id} className="text-xs">
                    {tab.label} {getSubTabCount(tab.id)}
                  </TabsTrigger>
                ))}
              </TabsList>
            </Tabs>
          </div>
        )}

        <div className="mb-4 flex flex-col gap-3">
          <form onSubmit={handleSearch} className="flex gap-2">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <Input
                placeholder="Cari ID atau nama pelanggan..."
                className="pl-10"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </form>

          {(searchQuery ||
            sortBy !== 'newest' ||
            paymentStatus !== 'all' ||
            selectedFilters.length > 0) && (
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-500">
                {sortedOrders.length} pesanan ditemukan
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={clearFilters}
                className="h-8 text-xs"
              >
                <X className="h-3 w-3 mr-1" />
                Reset Filter
              </Button>
            </div>
          )}
        </div>

        {renderOrdersList()}
      </main>

      <BottomNavigation activePage="pesanan" />
    </div>
  );
}
