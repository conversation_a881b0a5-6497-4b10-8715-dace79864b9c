# Dokumentasi Frontend Cashbox System

## Overview

Dokumentasi ini menjelaskan implementasi frontend untuk sistem cashbox pada aplikasi laundry. Sistem ini memungkinkan pengelolaan cashbox (kas) untuk setiap outlet dengan fitur CRUD lengkap dan manajemen saldo.

## Struktur File

### 1. API Layer (`lib/api/cashbox.ts`)

File ini berisi semua fungsi untuk berkomunikasi dengan backend API cashbox.

**Interfaces:**

- `Cashbox`: Model data cashbox
- `CreateCashboxRequest`: Request untuk membuat cashbox baru
- `UpdateCashboxRequest`: Request untuk update cashbox
- `GetCashboxesParams`: Parameter untuk filter cashbox
- `CashboxBalance`: Model data saldo cashbox
- `AdjustBalanceRequest`: Request untuk penyesuaian saldo
- `AdjustBalanceResponse`: Response penyesuaian saldo

**API Methods:**

- `getCashboxes()`: Mengambil semua cashbox untuk outlet
- `getCashbox()`: Mengambil cashbox berdasarkan ID
- `createCashbox()`: Membuat cashbox baru
- `updateCashbox()`: Update cashbox
- `deleteCashbox()`: Hapus cashbox
- `getCashboxBalance()`: Mengambil saldo cashbox
- `adjustCashboxBalance()`: Sesuaikan saldo cashbox

### 2. Hooks Layer (`hooks/useCashbox.ts`)

File ini berisi React Query hooks untuk state management dan caching.

**Hooks:**

- `useCashboxes()`: Hook untuk mengambil daftar cashbox
- `useCashbox()`: Hook untuk mengambil detail cashbox
- `useCashboxBalance()`: Hook untuk mengambil saldo cashbox
- `useCreateCashbox()`: Hook untuk membuat cashbox baru
- `useUpdateCashbox()`: Hook untuk update cashbox
- `useDeleteCashbox()`: Hook untuk hapus cashbox
- `useAdjustCashboxBalance()`: Hook untuk penyesuaian saldo

**Helper Functions:**

- `getCashboxErrorMessage()`: Extract error message dari API response
- `getCashboxFieldErrors()`: Extract field errors untuk form validation
- `formatCurrency()`: Format mata uang Indonesia
- `getCashboxTypeBadge()`: Format badge untuk tipe cashbox
- `getCashboxStatusBadge()`: Format badge untuk status cashbox

### 3. Pages Layer

#### a. Main Cashbox Page (`app/akun/cashbox/page.tsx`)

Halaman utama untuk menampilkan daftar cashbox dengan fitur:

**Features:**

- **Dashboard Stats**: Total saldo, jumlah cashbox, cashbox aktif
- **Search & Filter**: Pencarian nama dan filter berdasarkan tipe/status
- **Tabs**: Filter berdasarkan kategori (Semua, Tunai, Non Tunai, Aktif, Nonaktif)
- **Card View**: Tampilan card untuk setiap cashbox dengan informasi lengkap
- **Actions**: View detail, edit, dan hapus cashbox
- **Real-time Data**: Auto refresh dan loading states

**Components Used:**

- Cards untuk stats dan cashbox list
- Tabs untuk filtering
- Search input dengan debouncing
- Dropdown menu untuk actions
- Dialog untuk konfirmasi delete
- Badge untuk status dan tipe

#### b. Add Cashbox Page (`app/akun/cashbox/add/page.tsx`)

Halaman untuk menambah cashbox baru dengan fitur:

**Form Fields:**

- **Nama Cashbox**: Input text dengan validasi
- **Tipe Cashbox**: Radio button (TUNAI/NON_TUNAI)
- **Saldo Awal**: Input number (opsional, default 0)
- **Status Aktif**: Switch toggle

**Validation:**

- Nama wajib diisi (2-100 karakter)
- Saldo tidak boleh negatif
- Real-time validation dengan error messages
- Form reset setelah submit berhasil

#### c. Cashbox Detail Page (`app/akun/cashbox/[id]/page.tsx`)

Halaman detail cashbox dengan fitur:

**Information Display:**

- **Basic Info**: Nama, tipe, status, tanggal dibuat
- **Balance Info**: Saldo saat ini dengan timestamp terakhir update
- **Quick Actions**: Edit, refresh, adjust balance

**Balance Adjustment:**

- Modal dialog untuk penyesuaian saldo
- Input amount (positif/negatif)
- Alasan penyesuaian (wajib)
- Referensi (opsional)
- Validasi dan error handling

#### d. Edit Cashbox Page (`app/akun/cashbox/edit/[id]/page.tsx`)

Halaman edit cashbox dengan fitur:

**Editable Fields:**

- Nama cashbox
- Tipe cashbox
- Status aktif/nonaktif

**Features:**

- Form pre-filled dengan data existing
- Info saldo saat ini (read-only)
- Validasi sama dengan add page
- Redirect ke detail setelah update berhasil

## Fitur Utama

### 1. Dashboard Statistics

```typescript
// Menampilkan ringkasan cashbox
- Total Saldo: Sum dari semua cashbox
- Total Cashbox: Jumlah cashbox
- Cashbox Aktif: Jumlah cashbox yang aktif
```

### 2. Search & Filter System

```typescript
// Filter berdasarkan:
- Nama cashbox (search query)
- Tipe (TUNAI/NON_TUNAI)
- Status (Aktif/Nonaktif)
- Tab categories
```

### 3. Balance Management

```typescript
// Penyesuaian saldo dengan:
- Amount adjustment (+ atau -)
- Reason (wajib)
- Reference (opsional)
- Audit trail
```

### 4. Error Handling

```typescript
// Comprehensive error handling:
- Field validation errors
- API error messages
- Network error handling
- Authentication errors
```

### 5. Loading States

```typescript
// Loading indicators untuk:
- Data fetching
- Form submissions
- Balance adjustments
- Delete operations
```

## Validasi Form

### Create/Update Cashbox

- **Nama**: Required, 2-100 karakter
- **Tipe**: Required, TUNAI atau NON_TUNAI
- **Saldo Awal**: Optional, >= 0
- **Status**: Boolean

### Balance Adjustment

- **Amount**: Required, tidak boleh 0
- **Reason**: Required, 5-500 karakter
- **Reference**: Optional, max 100 karakter

## State Management

### React Query Configuration

```typescript
// Cache configuration:
- Cashbox list: 5 minutes stale time
- Cashbox detail: 5 minutes stale time
- Balance data: 1 minute stale time (frequent updates)
```

### Optimistic Updates

```typescript
// Automatic cache invalidation setelah:
- Create cashbox
- Update cashbox
- Delete cashbox
- Adjust balance
```

## Security Features

### Authentication

- Token-based authentication
- Automatic token refresh
- Redirect ke login jika unauthorized
- Auth sync pada component mount

### Authorization

- Outlet-based access control
- User role validation
- Protected routes

## UI/UX Features

### Responsive Design

- Mobile-first approach
- Grid layout yang adaptif
- Touch-friendly buttons
- Optimized untuk tablet dan desktop

### User Feedback

- Toast notifications untuk success/error
- Loading spinners
- Confirmation dialogs
- Real-time validation feedback

### Accessibility

- Proper ARIA labels
- Keyboard navigation
- Screen reader support
- Color contrast compliance

## Integration dengan Backend

### API Endpoints

```typescript
// Menggunakan endpoints dari backend:
GET    /outlets/:outletId/cashboxes
POST   /outlets/:outletId/cashboxes
GET    /outlets/:outletId/cashboxes/:id
PATCH  /outlets/:outletId/cashboxes/:id
DELETE /outlets/:outletId/cashboxes/:id
GET    /outlets/:outletId/cashboxes/:id/balance
PATCH  /outlets/:outletId/cashboxes/:id/balance
```

### Data Flow

1. User action → Hook → API call → Backend
2. Backend response → Hook → Cache update → UI update
3. Error handling → Toast notification → Form errors

## Performance Optimizations

### Caching Strategy

- React Query untuk server state
- Stale-while-revalidate pattern
- Background refetching
- Cache invalidation

### Code Splitting

- Lazy loading untuk pages
- Dynamic imports
- Bundle optimization

### Network Optimization

- Request deduplication
- Parallel requests where possible
- Optimistic updates

## Testing Considerations

### Unit Tests

- Hook testing dengan React Query
- Form validation testing
- Error handling testing
- Helper function testing

### Integration Tests

- API integration testing
- User flow testing
- Error scenario testing

### E2E Tests

- Complete cashbox management flow
- Cross-browser compatibility
- Mobile responsiveness

## Deployment Notes

### Environment Variables

```env
NEXT_PUBLIC_API_URL=http://localhost:3000/v1
```

### Build Configuration

- TypeScript strict mode
- ESLint configuration
- Prettier formatting
- Bundle analysis

## Future Enhancements

### Planned Features

1. **Transaction History**: Riwayat transaksi cashbox
2. **Bulk Operations**: Operasi massal untuk multiple cashbox
3. **Export/Import**: Export data ke Excel/CSV
4. **Advanced Filtering**: Filter berdasarkan tanggal, range saldo
5. **Dashboard Charts**: Grafik saldo dan transaksi
6. **Notifications**: Notifikasi untuk saldo rendah
7. **Backup/Restore**: Backup dan restore data cashbox

### Technical Improvements

1. **Offline Support**: PWA dengan offline capabilities
2. **Real-time Updates**: WebSocket untuk real-time balance updates
3. **Advanced Caching**: Service worker caching
4. **Performance Monitoring**: Error tracking dan performance metrics

## Troubleshooting

### Common Issues

1. **Auth Errors**: Clear localStorage dan login ulang
2. **Network Errors**: Check API endpoint dan koneksi
3. **Validation Errors**: Check form input dan backend validation
4. **Cache Issues**: Clear React Query cache atau refresh page

### Debug Tools

- React Query DevTools
- Browser DevTools
- Network tab untuk API calls
- Console logs untuk error tracking

## Kesimpulan

Sistem cashbox frontend telah diimplementasi dengan lengkap mengikuti best practices modern React development. Sistem ini menyediakan:

1. **User Experience** yang intuitif dan responsive
2. **Error Handling** yang comprehensive
3. **Performance** yang optimal dengan caching
4. **Security** dengan proper authentication
5. **Maintainability** dengan clean code architecture
6. **Scalability** untuk future enhancements

Implementasi ini siap untuk production dan dapat dengan mudah diperluas sesuai kebutuhan bisnis yang berkembang.
