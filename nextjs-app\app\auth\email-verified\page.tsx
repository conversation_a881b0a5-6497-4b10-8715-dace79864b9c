'use client';

import Image from 'next/image';
import Link from 'next/link';
import { CheckCircle2 } from 'lucide-react';
import { Button } from '@/components/ui/button';

export default function EmailVerifiedPage() {
  return (
    <div className="flex min-h-screen flex-col items-center justify-center">
      <div className="w-full max-w-md px-6 text-center">
        <div className="mb-6">
          <Image
            src="/logo.png"
            alt="SuperLaundry Logo"
            width={200}
            height={80}
            className="mx-auto mb-4"
          />
          <p className="text-sm mb-4">Manajemen Usaha Laundry</p>
        </div>

        <div className="bg-green-50 border border-green-200 rounded-lg p-8 mb-6">
          <CheckCircle2 className="h-16 w-16 text-green-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-center mb-4">
            Email Be<PERSON> Diverifika<PERSON>!
          </h1>
          <p className="text-center text-muted-foreground mb-6">
            Terima kasih telah memverifikasi email Anda. Akun Anda sekarang
            sudah aktif dan Anda dapat login untuk mulai menggunakan layanan
            SuperLaundry.
          </p>

          <Button asChild className="w-full">
            <Link href="/auth/login">Masuk ke Akun</Link>
          </Button>
        </div>

        <p className="text-sm text-muted-foreground">
          Jika Anda mengalami masalah, silakan{' '}
          <Link
            href="/auth/forgot-password"
            className="text-primary hover:underline"
          >
            hubungi tim dukungan kami
          </Link>
        </p>
      </div>
    </div>
  );
}
