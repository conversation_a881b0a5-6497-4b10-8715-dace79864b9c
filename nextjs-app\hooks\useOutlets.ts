import { useState, useEffect } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
  outletAPI,
  type Outlet,
  type CreateOutletRequest,
  type UpdateOutletRequest,
} from '@/lib/api/outlets';
import { toast } from 'sonner';

export function useOutlets(params?: {
  name?: string;
  city?: string;
  province?: string;
  isActive?: boolean;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortType?: 'asc' | 'desc';
}) {
  return useQuery({
    queryKey: ['outlets', params],
    queryFn: () => outletAPI.getOutlets(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useOutlet(id: number) {
  return useQuery({
    queryKey: ['outlet', id],
    queryFn: () => outletAPI.getOutlet(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  });
}

export function useCreateOutlet() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateOutletRequest) => outletAPI.createOutlet(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['outlets'] });
      toast.success('Outlet berhasil ditambahkan');
    },
    onError: (error: any) => {
      const message =
        error.response?.data?.message ||
        error.message ||
        'Gagal menambahkan outlet';
      toast.error(message);
    },
  });
}

export function useUpdateOutlet() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateOutletRequest }) =>
      outletAPI.updateOutlet(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['outlets'] });
      queryClient.invalidateQueries({ queryKey: ['outlet', id] });
      toast.success('Outlet berhasil diperbarui');
    },
    onError: (error: any) => {
      const message =
        error.response?.data?.message ||
        error.message ||
        'Gagal memperbarui outlet';
      toast.error(message);
    },
  });
}

export function useDeleteOutlet() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => outletAPI.deleteOutlet(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['outlets'] });
      toast.success('Outlet berhasil dihapus');
    },
    onError: (error: any) => {
      const message =
        error.response?.data?.message ||
        error.message ||
        'Gagal menghapus outlet';
      toast.error(message);
    },
  });
}

export function useOutletServices(id: number) {
  return useQuery({
    queryKey: ['outlet-services', id],
    queryFn: () => outletAPI.getOutletServices(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  });
}

export function useCopyOutletServices() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      targetId,
      sourceId,
    }: {
      targetId: number;
      sourceId: number;
    }) => outletAPI.copyServicesFromOutlet(targetId, sourceId),
    onSuccess: (_, { targetId }) => {
      queryClient.invalidateQueries({
        queryKey: ['outlet-services', targetId],
      });
      toast.success('Layanan berhasil disalin');
    },
    onError: (error: any) => {
      const message =
        error.response?.data?.message ||
        error.message ||
        'Gagal menyalin layanan';
      toast.error(message);
    },
  });
}

export function useOutletStats(id: number) {
  return useQuery({
    queryKey: ['outlet-stats', id],
    queryFn: () => outletAPI.getOutletStats(id),
    enabled: !!id,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}
