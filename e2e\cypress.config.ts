import { defineConfig } from 'cypress';

export default defineConfig({
  e2e: {
    baseUrl: 'http://localhost:3001',
    supportFile: 'cypress/support/e2e.ts',
    specPattern: 'cypress/e2e/**/*.cy.{js,jsx,ts,tsx}',
    viewportWidth: 1280,
    viewportHeight: 720,
    video: false,
    screenshotOnRunFailure: true,
    defaultCommandTimeout: 10000,
    requestTimeout: 10000,
    responseTimeout: 10000,
    pageLoadTimeout: 30000,

    env: {
      // Environment variables for testing
      API_URL: 'http://localhost:5000/v1',
      TEST_USER_EMAIL: '<EMAIL>',
      TEST_USER_PASSWORD: 'password123',
    },

    setupNodeEvents(on, config) {
      // implement node event listeners here

      // Task untuk database operations
      on('task', {
        // Database helpers
        async createTestUser(userData) {
          // Implementation akan ditambahkan nanti
          return userData;
        },

        async deleteTestUser(email) {
          // Implementation akan ditambahkan nanti
          return null;
        },

        async verifyUserInDatabase(email) {
          // Implementation akan ditambahkan nanti
          return true;
        },

        // Log helper
        log(message) {
          console.log(message);
          return null;
        },
      });

      return config;
    },
  },

  component: {
    devServer: {
      framework: 'next',
      bundler: 'webpack',
    },
  },
});
