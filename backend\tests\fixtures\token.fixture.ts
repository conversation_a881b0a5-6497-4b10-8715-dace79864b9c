import moment from 'moment';
import config from '../../src/config/config';
import { tokenService } from '../../src/services';
import { TokenType } from '@prisma/client';

// Generate access tokens for testing
// Note: These will be regenerated for each test with actual user IDs
export const generateUserOneAccessToken = (userId: number): string => {
  return tokenService.generateToken(
    userId,
    moment().add(config.jwt.accessExpirationMinutes, 'minutes'),
    TokenType.ACCESS
  );
};

export const generateUserTwoAccessToken = (userId: number): string => {
  return tokenService.generateToken(
    userId,
    moment().add(config.jwt.accessExpirationMinutes, 'minutes'),
    TokenType.ACCESS
  );
};

export const generateAdminAccessToken = (adminId: number): string => {
  return tokenService.generateToken(
    adminId,
    moment().add(config.jwt.accessExpirationMinutes, 'minutes'),
    TokenType.ACCESS
  );
};

// Default tokens (will be overridden in tests with actual user IDs)
export const userOneAccessToken = 'dummy-user-token';
export const userTwoAccessToken = 'dummy-user-two-token';
export const adminAccessToken = 'dummy-admin-token';
