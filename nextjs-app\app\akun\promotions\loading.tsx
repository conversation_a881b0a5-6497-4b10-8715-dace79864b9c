import { Skeleton } from "@/components/ui/skeleton"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"

export default function PromotionsLoading() {
  return (
    <div className="flex flex-col min-h-screen bg-slate-50">
      <header className="p-4 flex items-center bg-white sticky top-0 z-10 shadow-sm">
        <Skeleton className="h-5 w-5 mr-3" />
        <Skeleton className="h-7 w-40" />
      </header>

      <main className="flex-1 p-4 pb-20">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
          <Skeleton className="h-10 w-full sm:w-64" />
          <div className="flex gap-2 w-full sm:w-auto">
            <Skeleton className="h-10 w-24" />
            <Skeleton className="h-10 w-36" />
          </div>
        </div>

        <Tabs defaultValue="all" className="mb-6">
          <TabsList className="grid grid-cols-4 mb-4">
            <TabsTrigger value="all" disabled>
              Semua
            </TabsTrigger>
            <TabsTrigger value="active" disabled>
              Aktif
            </TabsTrigger>
            <TabsTrigger value="inactive" disabled>
              Tidak Aktif
            </TabsTrigger>
            <TabsTrigger value="upcoming" disabled>
              Akan Datang
            </TabsTrigger>
          </TabsList>
        </Tabs>

        <div className="space-y-4">
          {Array(3)
            .fill(0)
            .map((_, i) => (
              <Card key={i} className="overflow-hidden">
                <CardContent className="p-0">
                  <div className="p-4">
                    <div className="flex justify-between items-start">
                      <div>
                        <Skeleton className="h-6 w-48 mb-2" />
                        <Skeleton className="h-4 w-64" />
                      </div>
                      <Skeleton className="h-6 w-16" />
                    </div>

                    <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 mt-4">
                      {Array(4)
                        .fill(0)
                        .map((_, j) => (
                          <div key={j}>
                            <Skeleton className="h-3 w-16 mb-1" />
                            <Skeleton className="h-5 w-24" />
                          </div>
                        ))}
                    </div>

                    <div className="flex justify-between items-center mt-4 pt-4 border-t">
                      <Skeleton className="h-5 w-24" />
                      <div className="flex gap-2">
                        <Skeleton className="h-8 w-20" />
                        <Skeleton className="h-8 w-20" />
                        <Skeleton className="h-8 w-20" />
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
        </div>
      </main>
    </div>
  )
}
