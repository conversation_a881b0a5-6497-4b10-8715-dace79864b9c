import { NextFunction, Request, Response } from 'express';
import httpStatus from 'http-status';
import ApiError from '../utils/ApiError';
import config from '../config/config';
import { User, Role } from '@prisma/client';

// Routes yang dikecualikan dari verification requirement
const excludedRoutes = [
  // Auth routes
  '/v1/auth/login',
  '/v1/auth/register',
  '/v1/auth/logout',
  '/v1/auth/refresh-tokens',
  '/v1/auth/forgot-password',
  '/v1/auth/reset-password',

  // Verification routes
  '/v1/auth/send-verification-email',
  '/v1/auth/verify-email',
  '/v1/auth/send-phone-verification',
  '/v1/auth/verify-phone',

  // Docs
  '/v1/docs'
];

/**
 * Middleware untuk check verification requirement
 * Akan dijalankan setelah auth middleware
 */
const requireVerification = () => {
  return (req: Request, res: Response, next: NextFunction) => {
    // Skip jika verification disabled
    if (!config.verification.enabled) {
      return next();
    }

    // Skip untuk excluded routes
    if (excludedRoutes.some((route) => req.path.startsWith(route))) {
      return next();
    }

    // Skip jika tidak ada user (belum authenticated)
    const user = req.user as User;
    if (!user) {
      return next();
    }

    // Skip untuk admin (admin tidak perlu verification)
    if (user.role === Role.ADMIN) {
      return next();
    }

    // Check email verification requirement
    if (config.verification.requireEmailVerification && !user.isEmailVerified) {
      return next(
        new ApiError(httpStatus.FORBIDDEN, 'Email verification required to access this feature')
      );
    }

    // Check phone verification requirement
    if (config.verification.requirePhoneVerification && !user.isPhoneVerified) {
      return next(
        new ApiError(httpStatus.FORBIDDEN, 'Phone verification required to access this feature')
      );
    }

    // All verification requirements met
    next();
  };
};

export default requireVerification;
