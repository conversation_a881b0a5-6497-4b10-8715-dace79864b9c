"use client"

import type React from "react"

import { useState, useEffect } from "react"
import Link from "next/link"
import { ArrowLeft, Save } from "lucide-react"
import { useRouter } from "next/navigation"

import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

// Mock data for inventory items
const mockInventory = [
  {
    id: 1,
    name: "Deterjen Cair",
    category: "supplies",
    stock: 25,
    unit: "Liter",
    minStock: 10,
    price: 25000,
    supplier: "PT Supplier Bersih",
    lastRestock: "15/03/2025",
    notes: "Deterjen khusus untuk mesin cuci front loading",
  },
  {
    id: 2,
    name: "Pelembut Pakaian",
    category: "supplies",
    stock: 15,
    unit: "Liter",
    minStock: 8,
    price: 30000,
    supplier: "PT Supplier Bersih",
    lastRestock: "15/03/2025",
    notes: "",
  },
  {
    id: 3,
    name: "<PERSON><PERSON><PERSON><PERSON>",
    category: "supplies",
    stock: 5,
    unit: "Liter",
    minStock: 5,
    price: 20000,
    supplier: "PT Supplier Bersih",
    lastRestock: "10/03/2025",
    notes: "Hanya untuk pakaian putih",
  },
  {
    id: 4,
    name: "Plastik Packaging Kecil",
    category: "packaging",
    stock: 200,
    unit: "Pcs",
    minStock: 100,
    price: 500,
    supplier: "PT Plastik Jaya",
    lastRestock: "20/03/2025",
    notes: "",
  },
  {
    id: 5,
    name: "Plastik Packaging Besar",
    category: "packaging",
    stock: 150,
    unit: "Pcs",
    minStock: 100,
    price: 1000,
    supplier: "PT Plastik Jaya",
    lastRestock: "20/03/2025",
    notes: "",
  },
  {
    id: 6,
    name: "Hanger",
    category: "equipment",
    stock: 80,
    unit: "Pcs",
    minStock: 50,
    price: 2500,
    supplier: "PT Peralatan Laundry",
    lastRestock: "05/03/2025",
    notes: "",
  },
  {
    id: 7,
    name: "Setrika",
    category: "equipment",
    stock: 10,
    unit: "Pcs",
    minStock: 5,
    price: 150000,
    supplier: "PT Peralatan Laundry",
    lastRestock: "01/03/2025",
    notes: "Setrika uap premium",
  },
  {
    id: 8,
    name: "Kertas Nota",
    category: "stationery",
    stock: 3,
    unit: "Pack",
    minStock: 5,
    price: 35000,
    supplier: "PT ATK Sejahtera",
    lastRestock: "25/02/2025",
    notes: "Segera restock",
  },
]

export default function EditInventoryPage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const id = Number.parseInt(params.id)

  const [formData, setFormData] = useState({
    name: "",
    category: "",
    stock: "",
    unit: "",
    minStock: "",
    price: "",
    supplier: "",
    notes: "",
  })

  useEffect(() => {
    // Find the inventory item by ID
    const item = mockInventory.find((item) => item.id === id)

    if (item) {
      setFormData({
        name: item.name,
        category: item.category,
        stock: item.stock.toString(),
        unit: item.unit,
        minStock: item.minStock.toString(),
        price: item.price.toString(),
        supplier: item.supplier,
        notes: item.notes || "",
      })
    } else {
      // If item not found, redirect to inventory list
      router.push("/akun/inventory")
    }
  }, [id, router])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // In a real app, you would update the inventory item in your backend
    alert("Item inventaris berhasil diperbarui!")
    router.push("/akun/inventory")
  }

  return (
    <div className="flex flex-col min-h-screen bg-slate-50">
      <header className="p-4 flex justify-between items-center bg-white sticky top-0 z-10 shadow-sm">
        <div className="flex items-center">
          <Link href="/akun/inventory" className="mr-3">
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <h1 className="text-lg font-semibold">Edit Item Inventaris</h1>
        </div>
        <Button type="submit" form="edit-inventory-form" className="bg-blue-500 hover:bg-blue-600">
          <Save className="h-4 w-4 mr-2" /> Simpan
        </Button>
      </header>

      <main className="flex-1 p-4 pb-20">
        <form id="edit-inventory-form" onSubmit={handleSubmit}>
          <Card className="p-4 mb-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">Nama Item</Label>
                <Input
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  placeholder="Masukkan nama item"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="category">Kategori</Label>
                <Select value={formData.category} onValueChange={(value) => handleSelectChange("category", value)}>
                  <SelectTrigger id="category">
                    <SelectValue placeholder="Pilih kategori" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="supplies">Supplies</SelectItem>
                    <SelectItem value="packaging">Packaging</SelectItem>
                    <SelectItem value="equipment">Equipment</SelectItem>
                    <SelectItem value="stationery">Stationery</SelectItem>
                    <SelectItem value="other">Lainnya</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="stock">Stok Saat Ini</Label>
                  <Input
                    id="stock"
                    name="stock"
                    type="number"
                    value={formData.stock}
                    onChange={handleChange}
                    placeholder="0"
                    min="0"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="unit">Satuan</Label>
                  <Select value={formData.unit} onValueChange={(value) => handleSelectChange("unit", value)}>
                    <SelectTrigger id="unit">
                      <SelectValue placeholder="Pilih satuan" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Liter">Liter</SelectItem>
                      <SelectItem value="Kg">Kg</SelectItem>
                      <SelectItem value="Pcs">Pcs</SelectItem>
                      <SelectItem value="Box">Box</SelectItem>
                      <SelectItem value="Pack">Pack</SelectItem>
                      <SelectItem value="Roll">Roll</SelectItem>
                      <SelectItem value="Botol">Botol</SelectItem>
                      <SelectItem value="Galon">Galon</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="minStock">Stok Minimum</Label>
                  <Input
                    id="minStock"
                    name="minStock"
                    type="number"
                    value={formData.minStock}
                    onChange={handleChange}
                    placeholder="0"
                    min="0"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="price">Harga (Rp)</Label>
                  <Input
                    id="price"
                    name="price"
                    type="number"
                    value={formData.price}
                    onChange={handleChange}
                    placeholder="0"
                    min="0"
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="supplier">Supplier</Label>
                <Input
                  id="supplier"
                  name="supplier"
                  value={formData.supplier}
                  onChange={handleChange}
                  placeholder="Masukkan nama supplier"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="notes">Catatan (Opsional)</Label>
                <Textarea
                  id="notes"
                  name="notes"
                  value={formData.notes}
                  onChange={handleChange}
                  placeholder="Tambahkan catatan tentang item ini"
                  rows={3}
                />
              </div>
            </div>
          </Card>
        </form>
      </main>
    </div>
  )
}
