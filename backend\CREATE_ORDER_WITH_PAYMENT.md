# Create Order dengan Payment - Dokumentasi

## Overview

Fitur ini memungkinkan pembuatan order sekaligus dengan pembayaran dalam satu request API. Sangat berguna untuk flow kasir yang ingin langsung cetak struk dengan status pembayaran yang akurat.

## Endpoint

```
POST /v1/outlets/:outletId/orders
```

## Request Body

```json
{
  "customerId": number,
  "items": [
    {
      "serviceId": number,
      "quantity": number,
      "unit": string,
      "price": number,
      "subtotal": number,
      "notes": string (optional)
    }
  ],
  "notes": string (optional),
  "perfumeId": number (optional),
  "payment": {  // OPTIONAL - jika tidak ada, order dibuat dengan status UNPAID
    "amount": number,
    "method": "CASH" | "TRANSFER" | "CREDIT_CARD" | "DEBIT_CARD" | "E_WALLET" | "DEPOSIT",
    "cashboxId": number (optional, required untuk non-DEPOSIT),
    "reference": string (optional),
    "notes": string (optional)
  }
}
```

**Note**: Field `paymentMethod` di level order sudah dihapus untuk menghindari duplikasi. Gunakan `payment.method` sebagai single source of truth.

## Response

```json
{
  "success": true,
  "message": "Order created successfully",
  "data": {
    "id": number,
    "orderNumber": string,
    "status": "PENDING",
    "paymentStatus": "PAID" | "PARTIAL" | "UNPAID",
    "totalPrice": number,
    "paidAmount": number,
    "paymentMethod": string,
    "customerId": number,
    "outletId": number,
    "items": [
      {
        "id": number,
        "serviceName": string,
        "quantity": number,
        "price": number,
        "subtotal": number
      }
    ],
    "createdAt": string,
    "updatedAt": string
  }
}
```

## Payment Status Logic

- **UNPAID**: Tidak ada payment atau paidAmount = 0
- **PARTIAL**: paidAmount > 0 dan paidAmount < totalPrice
- **PAID**: paidAmount >= totalPrice

## Payment Methods

### 1. CASH, TRANSFER, CREDIT_CARD, DEBIT_CARD, E_WALLET

- Memerlukan `cashboxId` yang valid dan aktif
- Akan menambah balance cashbox sesuai amount
- Membuat record Payment di database

### 2. DEPOSIT

- Tidak memerlukan `cashboxId`
- Akan mengurangi saldo deposit customer
- Membuat record CustomerDepositTransaction dengan type PAYMENT
- Tidak mengubah balance cashbox

## Validasi

### Order Level

- Customer harus ada dan belong ke outlet yang sama
- Service harus ada dan belong ke outlet yang sama
- Perfume (jika ada) harus ada dan aktif di outlet yang sama

### Payment Level

- Amount tidak boleh melebihi totalPrice
- Untuk DEPOSIT: saldo customer harus mencukupi
- Untuk non-DEPOSIT: cashboxId harus valid dan aktif
- Cashbox harus belong ke outlet yang sama

## Contoh Request

### 1. Order dengan Pembayaran Cash

```json
{
  "customerId": 1,
  "items": [
    {
      "serviceId": 1,
      "quantity": 2.5,
      "unit": "kg",
      "price": 5000,
      "subtotal": 12500
    }
  ],
  "payment": {
    "amount": 12500,
    "method": "CASH",
    "cashboxId": 1,
    "reference": "CASH-001"
  }
}
```

### 2. Order dengan Pembayaran Deposit

```json
{
  "customerId": 1,
  "items": [
    {
      "serviceId": 1,
      "quantity": 1.5,
      "unit": "kg",
      "price": 5000,
      "subtotal": 7500
    }
  ],
  "payment": {
    "amount": 7500,
    "method": "DEPOSIT",
    "reference": "DEPOSIT-PAY-001"
  }
}
```

### 3. Order tanpa Pembayaran (UNPAID)

```json
{
  "customerId": 1,
  "items": [
    {
      "serviceId": 1,
      "quantity": 2,
      "unit": "kg",
      "price": 5000,
      "subtotal": 10000
    }
  ]
}
```

## Error Handling

### 400 Bad Request

- Customer tidak ditemukan
- Service tidak ditemukan atau tidak belong ke outlet
- Payment amount melebihi total order
- Saldo deposit tidak mencukupi
- Cashbox tidak ditemukan atau tidak aktif

### 401 Unauthorized

- User tidak terautentikasi

### 403 Forbidden

- Customer tidak belong ke outlet user

## Database Transactions

Semua operasi dilakukan dalam database transaction untuk memastikan konsistensi data:

1. Create Order
2. Create OrderStatusHistory
3. Create OrderItems
4. Process Payment (jika ada)
   - Untuk DEPOSIT: Update customer balance + create deposit transaction
   - Untuk lainnya: Create payment record + update cashbox balance

## Integration dengan Sistem Lain

### Cetak Struk

Setelah order berhasil dibuat, response akan berisi:

- `paymentStatus`: untuk menentukan status pembayaran di struk
- `paidAmount`: jumlah yang sudah dibayar
- `totalPrice`: total yang harus dibayar
- `paymentMethod`: metode pembayaran yang digunakan

### Cashbox Management

- Balance cashbox otomatis terupdate untuk payment non-DEPOSIT
- Tidak ada perubahan cashbox untuk payment DEPOSIT

### Customer Deposit

- Saldo deposit otomatis terpotong untuk payment DEPOSIT
- History transaksi deposit tercatat dengan type PAYMENT
