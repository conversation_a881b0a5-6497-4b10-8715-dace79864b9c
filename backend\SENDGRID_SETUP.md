# Setup Email Service - SMTP & SendGrid

## Switching Antara Email Provider

Aplikasi ini mendukung dua email provider: **SMTP** (nodemailer) dan **SendGrid**. Anda dapat dengan mudah beralih antara keduanya hanya dengan mengubah environment variable.

### Konfigurasi Environment Variables

Tambahkan variabel berikut ke file `.env` Anda:

```env
# Email Provider Configuration
EMAIL_PROVIDER=smtp  # Pilih 'smtp' atau 'sendgrid'

# SMTP Configuration (untuk EMAIL_PROVIDER=smtp)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
EMAIL_FROM=<EMAIL>

# SendGrid Configuration (untuk EMAIL_PROVIDER=sendgrid)
SENDGRID_API_KEY=your-sendgrid-api-key-here
SENDGRID_FROM_EMAIL=<EMAIL>
```

### Cara Memilih Provider

#### 1. Mengg<PERSON>kan SMTP (Nodemailer)

```env
EMAIL_PROVIDER=smtp
```

- Gratis untuk personal use
- Menggunakan Gmail, Outlook, atau SMTP server lainnya
- Setup mudah dengan credential email

#### 2. Menggunakan SendGrid

```env
EMAIL_PROVIDER=sendgrid
```

- Lebih reliable untuk production
- Better deliverability rate
- Dashboard analytics
- Scalable untuk volume tinggi

## Setup SendGrid

### Cara Mendapatkan SendGrid API Key

1. **Daftar/Login ke SendGrid**

   - Kunjungi https://sendgrid.com/
   - Buat akun baru atau login ke akun yang sudah ada

2. **Buat API Key**

   - Masuk ke dashboard SendGrid
   - Pergi ke Settings > API Keys
   - Klik "Create API Key"
   - Pilih "Restricted Access" dan berikan permission untuk "Mail Send"
   - Copy API key yang dihasilkan ke environment variable `SENDGRID_API_KEY`

3. **Verifikasi Sender Email**
   - Pergi ke Settings > Sender Authentication
   - Klik "Verify a Single Sender"
   - Masukkan email yang akan digunakan sebagai pengirim
   - Verifikasi email tersebut melalui email konfirmasi
   - Gunakan email yang sudah diverifikasi di environment variable `SENDGRID_FROM_EMAIL`

## Setup SMTP (Gmail)

### Cara Setup Gmail SMTP

1. **Enable 2-Factor Authentication**

   - Masuk ke Google Account settings
   - Enable 2FA untuk keamanan

2. **Generate App Password**

   - Pergi ke Google Account > Security > App passwords
   - Generate password khusus untuk aplikasi
   - Gunakan password ini di `SMTP_PASSWORD`

3. **Konfigurasi SMTP**
   ```env
   SMTP_HOST=smtp.gmail.com
   SMTP_PORT=587
   SMTP_USERNAME=<EMAIL>
   SMTP_PASSWORD=your-app-password
   EMAIL_FROM=<EMAIL>
   ```

## Penggunaan

Setelah konfigurasi selesai, Anda dapat menggunakan fungsi email yang **otomatis memilih provider**:

```typescript
import emailService from '../services/email.service';

// Email akan otomatis dikirim menggunakan provider yang dipilih di env
await emailService.sendEmail(
  '<EMAIL>',
  'Subject',
  'Text content',
  '<h1>HTML content</h1>' // optional
);

// Email reset password
await emailService.sendResetPasswordEmail('<EMAIL>', 'reset-token');

// Email verifikasi
await emailService.sendVerificationEmail('<EMAIL>', 'verification-token');

// Email welcome
await emailService.sendWelcomeEmail('<EMAIL>', 'John Doe');
```

## Fitur Smart Email Service

### Auto-Detection Provider

Email service akan otomatis mendeteksi provider yang dipilih dari environment variable dan menggunakan provider tersebut.

### Unified API

Semua fungsi email menggunakan API yang sama, tidak peduli provider mana yang digunakan:

- `sendEmail(to, subject, text, html?)` - Mengirim email umum
- `sendResetPasswordEmail(to, token)` - Email reset password
- `sendVerificationEmail(to, token)` - Email verifikasi
- `sendWelcomeEmail(to, userName?)` - Email welcome

### Backward Compatibility

Fungsi lama masih tersedia untuk compatibility:

- `sendEmailWithSendGrid()` - Khusus SendGrid
- `sendEmailWithSMTP()` - Khusus SMTP

## Error Handling & Logging

Semua fungsi email sudah dilengkapi dengan:

- Error handling yang comprehensive
- Logging provider yang digunakan
- Return value `EmailSendResult` dengan informasi success/error

```typescript
const result = await emailService.sendEmail('<EMAIL>', 'Test', 'Hello');
if (result.success) {
  console.log('Email sent successfully:', result.messageId);
} else {
  console.error('Email failed:', result.error);
}
```

## Perbandingan Provider

| Feature              | SMTP (Gmail)          | SendGrid                |
| -------------------- | --------------------- | ----------------------- |
| **Cost**             | Gratis (limit harian) | Free tier + paid plans  |
| **Setup**            | Mudah                 | Perlu verifikasi domain |
| **Reliability**      | Baik                  | Sangat baik             |
| **Deliverability**   | Baik                  | Excellent               |
| **Analytics**        | Tidak ada             | Dashboard lengkap       |
| **Volume**           | Terbatas              | Scalable                |
| **Production Ready** | Personal/small apps   | Enterprise ready        |

## Tips

1. **Development**: Gunakan SMTP untuk development lokal
2. **Production**: Gunakan SendGrid untuk production
3. **Testing**: Bisa switch provider untuk testing reliability
4. **Backup**: Setup kedua provider sebagai backup system
