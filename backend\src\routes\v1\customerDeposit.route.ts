import express from 'express';
import auth from '../../middlewares/auth';
import validate from '../../middlewares/validate';
import requireVerification from '../../middlewares/verification';
import { customerDepositValidation } from '../../validations';
import { customerDepositController } from '../../controllers';

const router = express.Router();

router
  .route('/deposit/in')
  .post(
    auth('manageCustomerDeposit'),
    requireVerification(),
    validate(customerDepositValidation.depositIn),
    customerDepositController.depositIn
  );

router
  .route('/deposit/out')
  .post(
    auth('manageCustomerDeposit'),
    requireVerification(),
    validate(customerDepositValidation.depositOut),
    customerDepositController.depositOut
  );

router
  .route('/deposit/pay')
  .post(
    auth('manageCustomerDeposit'),
    requireVerification(),
    validate(customerDepositValidation.payWithDeposit),
    customerDepositController.payWithDeposit
  );

router
  .route('/outlets/:outletId/customers/:customerId/deposit/history')
  .get(
    auth('getCustomerDeposit'),
    requireVerification(),
    validate(customerDepositValidation.getDepositHistory),
    customerDepositController.getDepositHistory
  );

router
  .route('/outlets/:outletId/customers/:customerId/deposit/balance')
  .get(
    auth('getCustomerDeposit'),
    requireVerification(),
    validate(customerDepositValidation.getDepositBalance),
    customerDepositController.getDepositBalance
  );

export default router;

/**
 * @swagger
 * tags:
 *   name: Customer Deposit
 *   description: Customer deposit management and transactions
 */

/**
 * @swagger
 * /deposit/in:
 *   post:
 *     summary: Deposit money to customer account
 *     description: Add money to a customer's deposit balance. This increases both customer balance and cashbox balance.
 *     tags: [Customer Deposit]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - customerId
 *               - outletId
 *               - amount
 *               - cashboxId
 *             properties:
 *               customerId:
 *                 type: integer
 *                 minimum: 1
 *                 description: Customer ID
 *               outletId:
 *                 type: integer
 *                 minimum: 1
 *                 description: Outlet ID
 *               amount:
 *                 type: number
 *                 minimum: 0.01
 *                 description: Amount to deposit
 *               cashboxId:
 *                 type: integer
 *                 minimum: 1
 *                 description: Cashbox ID where the physical money goes
 *               reference:
 *                 type: string
 *                 maxLength: 100
 *                 description: Reference number for the transaction
 *             example:
 *               customerId: 1
 *               outletId: 1
 *               amount: 100000
 *               cashboxId: 1
 *               reference: "DEPOSIT-IN-001"
 *     responses:
 *       "201":
 *         description: Created
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     transactionId:
 *                       type: integer
 *                     customerId:
 *                       type: integer
 *                     amount:
 *                       type: number
 *                     type:
 *                       type: string
 *                       enum: [DEPOSIT]
 *                     previousBalance:
 *                       type: number
 *                     newBalance:
 *                       type: number
 *                     cashboxId:
 *                       type: integer
 *                     reference:
 *                       type: string
 *                     createdAt:
 *                       type: string
 *                       format: date-time
 *       "400":
 *         $ref: '#/components/responses/BadRequest'
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "403":
 *         $ref: '#/components/responses/Forbidden'
 *       "404":
 *         $ref: '#/components/responses/NotFound'
 */

/**
 * @swagger
 * /deposit/out:
 *   post:
 *     summary: Withdraw money from customer account
 *     description: Withdraw money from a customer's deposit balance. This decreases both customer balance and cashbox balance.
 *     tags: [Customer Deposit]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - customerId
 *               - outletId
 *               - amount
 *               - cashboxId
 *             properties:
 *               customerId:
 *                 type: integer
 *                 minimum: 1
 *                 description: Customer ID
 *               outletId:
 *                 type: integer
 *                 minimum: 1
 *                 description: Outlet ID
 *               amount:
 *                 type: number
 *                 minimum: 0.01
 *                 description: Amount to withdraw
 *               cashboxId:
 *                 type: integer
 *                 minimum: 1
 *                 description: Cashbox ID where the physical money comes from
 *               reference:
 *                 type: string
 *                 maxLength: 100
 *                 description: Reference number for the transaction
 *             example:
 *               customerId: 1
 *               outletId: 1
 *               amount: 50000
 *               cashboxId: 1
 *               reference: "DEPOSIT-OUT-001"
 *     responses:
 *       "201":
 *         description: Created
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     transactionId:
 *                       type: integer
 *                     customerId:
 *                       type: integer
 *                     amount:
 *                       type: number
 *                     type:
 *                       type: string
 *                       enum: [WITHDRAW]
 *                     previousBalance:
 *                       type: number
 *                     newBalance:
 *                       type: number
 *                     cashboxId:
 *                       type: integer
 *                     reference:
 *                       type: string
 *                     createdAt:
 *                       type: string
 *                       format: date-time
 *       "400":
 *         description: Bad Request - Insufficient balance
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Saldo deposit tidak mencukupi"
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "403":
 *         $ref: '#/components/responses/Forbidden'
 *       "404":
 *         $ref: '#/components/responses/NotFound'
 */

/**
 * @swagger
 * /deposit/pay:
 *   post:
 *     summary: Pay for an order using customer deposit
 *     description: Use customer's deposit balance to pay for an order. This only affects customer balance, not cashbox balance.
 *     tags: [Customer Deposit]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - customerId
 *               - outletId
 *               - orderId
 *               - amount
 *             properties:
 *               customerId:
 *                 type: integer
 *                 minimum: 1
 *                 description: Customer ID
 *               outletId:
 *                 type: integer
 *                 minimum: 1
 *                 description: Outlet ID
 *               orderId:
 *                 type: integer
 *                 minimum: 1
 *                 description: Order ID to pay for
 *               amount:
 *                 type: number
 *                 minimum: 0.01
 *                 description: Payment amount
 *               reference:
 *                 type: string
 *                 maxLength: 100
 *                 description: Reference number for the payment
 *             example:
 *               customerId: 1
 *               outletId: 1
 *               orderId: 123
 *               amount: 25000
 *               reference: "DEPOSIT-PAY-001"
 *     responses:
 *       "201":
 *         description: Created
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     transactionId:
 *                       type: integer
 *                     customerId:
 *                       type: integer
 *                     orderId:
 *                       type: integer
 *                     amount:
 *                       type: number
 *                     type:
 *                       type: string
 *                       enum: [PAYMENT]
 *                     previousBalance:
 *                       type: number
 *                     newBalance:
 *                       type: number
 *                     reference:
 *                       type: string
 *                     createdAt:
 *                       type: string
 *                       format: date-time
 *       "400":
 *         description: Bad Request - Insufficient balance or invalid order
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   examples:
 *                     insufficient_balance:
 *                       value: "Saldo deposit tidak mencukupi"
 *                     invalid_order:
 *                       value: "Order tidak ditemukan atau sudah lunas"
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "403":
 *         $ref: '#/components/responses/Forbidden'
 *       "404":
 *         $ref: '#/components/responses/NotFound'
 */

/**
 * @swagger
 * /outlets/{outletId}/customers/{customerId}/deposit/history:
 *   get:
 *     summary: Get customer deposit transaction history
 *     description: Retrieve the transaction history for a customer's deposit account with pagination and filtering.
 *     tags: [Customer Deposit]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: outletId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Outlet ID
 *       - in: path
 *         name: customerId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Customer ID
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [DEPOSIT, WITHDRAW, PAYMENT, ADJUSTMENT]
 *         description: Filter by transaction type
 *       - in: query
 *         name: dateFrom
 *         schema:
 *           type: string
 *           format: date-time
 *         description: Filter transactions from this date
 *       - in: query
 *         name: dateTo
 *         schema:
 *           type: string
 *           format: date-time
 *         description: Filter transactions until this date
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         default: 20
 *         description: Maximum number of transactions
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         default: 1
 *         description: Page number
 *     responses:
 *       "200":
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     transactions:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                           type:
 *                             type: string
 *                             enum: [DEPOSIT, WITHDRAW, PAYMENT, ADJUSTMENT]
 *                           amount:
 *                             type: number
 *                           reference:
 *                             type: string
 *                           orderId:
 *                             type: integer
 *                             nullable: true
 *                           cashboxId:
 *                             type: integer
 *                             nullable: true
 *                           createdAt:
 *                             type: string
 *                             format: date-time
 *                           createdBy:
 *                             type: object
 *                             properties:
 *                               id:
 *                                 type: integer
 *                               name:
 *                                 type: string
 *                     pagination:
 *                       type: object
 *                       properties:
 *                         page:
 *                           type: integer
 *                         limit:
 *                           type: integer
 *                         totalPages:
 *                           type: integer
 *                         totalResults:
 *                           type: integer
 *                     summary:
 *                       type: object
 *                       properties:
 *                         currentBalance:
 *                           type: number
 *                         totalDeposits:
 *                           type: number
 *                         totalWithdrawals:
 *                           type: number
 *                         totalPayments:
 *                           type: number
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "403":
 *         $ref: '#/components/responses/Forbidden'
 *       "404":
 *         $ref: '#/components/responses/NotFound'
 */

/**
 * @swagger
 * /outlets/{outletId}/customers/{customerId}/deposit/balance:
 *   get:
 *     summary: Get customer deposit balance
 *     description: Retrieve the current deposit balance for a specific customer.
 *     tags: [Customer Deposit]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: outletId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Outlet ID
 *       - in: path
 *         name: customerId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Customer ID
 *     responses:
 *       "200":
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     customerId:
 *                       type: integer
 *                     customerName:
 *                       type: string
 *                     currentBalance:
 *                       type: number
 *                     lastTransaction:
 *                       type: object
 *                       nullable: true
 *                       properties:
 *                         id:
 *                           type: integer
 *                         type:
 *                           type: string
 *                           enum: [DEPOSIT, WITHDRAW, PAYMENT, ADJUSTMENT]
 *                         amount:
 *                           type: number
 *                         createdAt:
 *                           type: string
 *                           format: date-time
 *                     statistics:
 *                       type: object
 *                       properties:
 *                         totalDeposited:
 *                           type: number
 *                         totalWithdrawn:
 *                           type: number
 *                         totalSpentOnOrders:
 *                           type: number
 *                         transactionCount:
 *                           type: integer
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "403":
 *         $ref: '#/components/responses/Forbidden'
 *       "404":
 *         $ref: '#/components/responses/NotFound'
 */
