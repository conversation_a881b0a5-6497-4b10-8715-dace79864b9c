import { api } from '../auth-context';

export interface Employee {
  id: number;
  email: string;
  name: string;
  phone: string;
  role: string;
  isActive: boolean;
  outletId: number;
  createdAt: string;
  updatedAt: string;
  employeeAt?: {
    id: number;
    name: string;
  };
}

export interface CreateEmployeeRequest {
  name: string;
  email: string;
  phone: string;
  password: string;
  outletId: number;
}

export interface UpdateEmployeeRequest {
  name?: string;
  email?: string;
  phone?: string;
  password?: string;
  outletId?: number;
  isActive?: boolean;
}

export interface GetEmployeesParams {
  name?: string;
  outletId?: number;
  sortBy?: string;
  limit?: number;
  page?: number;
}

export interface GetEmployeesResponse {
  results: Employee[];
  page: number;
  limit: number;
  totalPages: number;
  totalResults: number;
}

export const employeeAPI = {
  // Get all employees with pagination and filters
  getEmployees: async (params?: GetEmployeesParams): Promise<GetEmployeesResponse> => {
    const response = await api.get('/users/employees', { params });
    return response.data;
  },

  // Get employee by ID
  getEmployee: async (id: number): Promise<Employee> => {
    const response = await api.get(`/users/employees/${id}`);
    return response.data;
  },

  // Create new employee
  createEmployee: async (data: CreateEmployeeRequest): Promise<Employee> => {
    const response = await api.post('/users/employees', data);
    return response.data;
  },

  // Update employee
  updateEmployee: async (id: number, data: UpdateEmployeeRequest): Promise<Employee> => {
    const response = await api.put(`/users/employees/${id}`, data);
    return response.data;
  },

  // Delete employee (soft delete)
  deleteEmployee: async (id: number): Promise<{ message: string }> => {
    const response = await api.delete(`/users/employees/${id}`);
    return response.data;
  },
}; 