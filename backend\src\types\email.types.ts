/**
 * TypeScript types untuk Email Service
 */

export interface EmailOptions {
  to: string;
  subject: string;
  text: string;
  html?: string;
}

export interface SendGridConfig {
  apiKey: string;
  fromEmail: string;
}

export interface SMTPConfig {
  host: string;
  port: number;
  auth: {
    user: string;
    pass: string;
  };
}

export interface EmailConfig {
  smtp: SMTPConfig;
  from: string;
}

export interface OrderItem {
  name: string;
  quantity: number;
  service: string;
}

export interface OrderEmailData {
  customerEmail: string;
  orderId: string;
  status: 'pending' | 'processing' | 'ready' | 'completed' | 'cancelled';
  items: OrderItem[];
  totalPrice: number;
  pickupDate?: Date;
  deliveryDate?: Date;
}

export interface EmailServiceInterface {
  sendEmail: (to: string, subject: string, text: string) => Promise<void>;
  sendEmailWithSendGrid: (
    to: string,
    subject: string,
    text: string,
    html?: string
  ) => Promise<void>;
  sendResetPasswordEmail: (to: string, token: string) => Promise<void>;
  sendResetPasswordEmailWithSendGrid: (to: string, token: string) => Promise<void>;
  sendVerificationEmail: (to: string, token: string) => Promise<void>;
  sendVerificationEmailWithSendGrid: (to: string, token: string) => Promise<void>;
}

export interface EmailTemplate {
  subject: string;
  text: string;
  html: string;
}

export interface EmailTemplates {
  welcome: EmailTemplate;
  resetPassword: EmailTemplate;
  verification: EmailTemplate;
  orderUpdate: EmailTemplate;
  orderComplete: EmailTemplate;
}

export type EmailProvider = 'smtp' | 'sendgrid';

export interface EmailSendResult {
  success: boolean;
  messageId?: string;
  error?: string;
}
