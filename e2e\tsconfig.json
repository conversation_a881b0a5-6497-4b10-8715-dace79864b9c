{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM"], "module": "CommonJS", "moduleResolution": "node", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "declaration": false, "outDir": "./dist", "rootDir": "./", "baseUrl": "./", "paths": {"@/*": ["./cypress/support/*"]}, "types": ["cypress", "node"]}, "include": ["cypress/**/*.ts", "cypress.config.ts"], "exclude": ["node_modules", "dist", "backend", "nextjs-app"]}