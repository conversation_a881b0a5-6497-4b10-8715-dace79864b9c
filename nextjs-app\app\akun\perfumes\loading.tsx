import { <PERSON><PERSON>ef<PERSON> } from "lucide-react"
import Link from "next/link"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"

export default function Loading() {
  return (
    <div className="flex flex-col min-h-screen bg-slate-50">
      <header className="p-4 flex justify-between items-center bg-white sticky top-0 z-10 shadow-sm">
        <div className="flex items-center">
          <Link href="/akun" className="mr-3">
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <h1 className="text-lg font-semibold">Kelola Parfum</h1>
        </div>
        <Button className="bg-blue-500 hover:bg-blue-600" size="sm" disabled>
          Tambah Parfum
        </Button>
      </header>

      <main className="flex-1 p-4 pb-20">
        <Card className="mb-4">
          <div className="p-4 space-y-4">
            <div className="h-10 bg-gray-200 rounded animate-pulse"></div>
            <div className="space-y-3">
              <div className="h-12 bg-gray-200 rounded animate-pulse"></div>
              <div className="h-12 bg-gray-200 rounded animate-pulse"></div>
              <div className="h-12 bg-gray-200 rounded animate-pulse"></div>
              <div className="h-12 bg-gray-200 rounded animate-pulse"></div>
            </div>
          </div>
        </Card>
      </main>
    </div>
  )
}
