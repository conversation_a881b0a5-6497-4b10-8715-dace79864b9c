"use client"

import { useState } from "react"
import Link from "next/link"
import { ArrowLeft, Save, Plus, Trash2, Eye, LineChart, BarChart2, <PERSON><PERSON><PERSON> } from "lucide-react"
import { useRouter } from "next/navigation"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Separator } from "@/components/ui/separator"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { DragDropContext, Droppable, Draggable } from "@hello-pangea/dnd"

// Mock data for available metrics
const availableMetrics = [
  { id: "revenue", name: "Pendapatan", category: "sales", chartType: "line" },
  { id: "orders", name: "Jumlah Order", category: "sales", chartType: "line" },
  { id: "avg_order", name: "Rata-rata Order", category: "sales", chartType: "line" },
  { id: "new_customers", name: "Pelanggan Baru", category: "customers", chartType: "bar" },
  { id: "returning_customers", name: "Pelanggan Kembali", category: "customers", chartType: "bar" },
  { id: "service_distribution", name: "Distribusi Layanan", category: "services", chartType: "pie" },
  { id: "inventory_levels", name: "Level Inventaris", category: "inventory", chartType: "bar" },
  { id: "employee_performance", name: "Performa Pegawai", category: "employees", chartType: "bar" },
  { id: "payment_methods", name: "Metode Pembayaran", category: "sales", chartType: "pie" },
  { id: "order_status", name: "Status Order", category: "operations", chartType: "pie" },
]

export default function CustomReportPage() {
  const router = useRouter()
  const [reportName, setReportName] = useState("Laporan Kustom Baru")
  const [reportDescription, setReportDescription] = useState("Deskripsi laporan kustom")
  const [dateRange, setDateRange] = useState("this-month")
  const [selectedMetrics, setSelectedMetrics] = useState<string[]>(["revenue", "orders", "new_customers"])
  const [showRawData, setShowRawData] = useState(true)
  const [autoRefresh, setAutoRefresh] = useState(false)
  const [refreshInterval, setRefreshInterval] = useState("daily")
  const [activeTab, setActiveTab] = useState("design")

  // Handle adding a metric to the report
  const handleAddMetric = (metricId: string) => {
    if (!selectedMetrics.includes(metricId)) {
      setSelectedMetrics([...selectedMetrics, metricId])
    }
  }

  // Handle removing a metric from the report
  const handleRemoveMetric = (metricId: string) => {
    setSelectedMetrics(selectedMetrics.filter((id) => id !== metricId))
  }

  // Handle drag and drop reordering
  const handleDragEnd = (result: any) => {
    if (!result.destination) return

    const items = Array.from(selectedMetrics)
    const [reorderedItem] = items.splice(result.source.index, 1)
    items.splice(result.destination.index, 0, reorderedItem)

    setSelectedMetrics(items)
  }

  // Handle saving the report
  const handleSaveReport = () => {
    // In a real app, you would save the report configuration to your backend
    alert("Laporan kustom berhasil disimpan!")
    router.push("/laporan")
  }

  // Get chart icon based on chart type
  const getChartIcon = (chartType: string) => {
    switch (chartType) {
      case "line":
        return <LineChart className="h-4 w-4" />
      case "bar":
        return <BarChart2 className="h-4 w-4" />
      case "pie":
        return <PieChart className="h-4 w-4" />
      default:
        return <LineChart className="h-4 w-4" />
    }
  }

  return (
    <div className="flex flex-col min-h-screen bg-slate-50">
      <header className="p-4 flex justify-between items-center bg-white sticky top-0 z-10 shadow-sm">
        <div className="flex items-center">
          <Link href="/laporan" className="mr-3">
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <h1 className="text-lg font-semibold">Buat Laporan Kustom</h1>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={() => router.push("/laporan")}>
            Batal
          </Button>
          <Button onClick={handleSaveReport} className="bg-blue-500 hover:bg-blue-600">
            <Save className="h-4 w-4 mr-2" /> Simpan
          </Button>
        </div>
      </header>

      <main className="flex-1 p-4 pb-20">
        <Tabs defaultValue="design" className="mb-4" onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-3 w-full">
            <TabsTrigger value="design">Desain</TabsTrigger>
            <TabsTrigger value="preview">Pratinjau</TabsTrigger>
            <TabsTrigger value="settings">Pengaturan</TabsTrigger>
          </TabsList>

          <TabsContent value="design" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Informasi Laporan</CardTitle>
                <CardDescription>Masukkan detail dasar laporan kustom Anda</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="report-name">Nama Laporan</Label>
                  <Input
                    id="report-name"
                    value={reportName}
                    onChange={(e) => setReportName(e.target.value)}
                    placeholder="Masukkan nama laporan"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="report-description">Deskripsi</Label>
                  <Input
                    id="report-description"
                    value={reportDescription}
                    onChange={(e) => setReportDescription(e.target.value)}
                    placeholder="Masukkan deskripsi laporan"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="date-range">Rentang Waktu Default</Label>
                  <Select value={dateRange} onValueChange={setDateRange}>
                    <SelectTrigger id="date-range">
                      <SelectValue placeholder="Pilih rentang waktu" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="today">Hari Ini</SelectItem>
                      <SelectItem value="yesterday">Kemarin</SelectItem>
                      <SelectItem value="this-week">Minggu Ini</SelectItem>
                      <SelectItem value="this-month">Bulan Ini</SelectItem>
                      <SelectItem value="this-year">Tahun Ini</SelectItem>
                      <SelectItem value="custom">Kustom</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle>Metrik yang Tersedia</CardTitle>
                  <CardDescription>Pilih metrik untuk ditambahkan ke laporan</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <Input placeholder="Cari metrik..." className="mb-2" />
                    <div className="space-y-2 max-h-[400px] overflow-y-auto">
                      {availableMetrics
                        .filter((metric) => !selectedMetrics.includes(metric.id))
                        .map((metric) => (
                          <div
                            key={metric.id}
                            className="flex items-center justify-between p-2 border rounded-md hover:bg-gray-50"
                          >
                            <div className="flex items-center">
                              {getChartIcon(metric.chartType)}
                              <div className="ml-2">
                                <p className="text-sm font-medium">{metric.name}</p>
                                <p className="text-xs text-gray-500">Kategori: {metric.category}</p>
                              </div>
                            </div>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleAddMetric(metric.id)}
                              className="h-8 w-8 p-0"
                            >
                              <Plus className="h-4 w-4" />
                            </Button>
                          </div>
                        ))}
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Metrik yang Dipilih</CardTitle>
                  <CardDescription>Seret untuk mengatur ulang</CardDescription>
                </CardHeader>
                <CardContent>
                  <DragDropContext onDragEnd={handleDragEnd}>
                    <Droppable droppableId="selected-metrics">
                      {(provided) => (
                        <div
                          {...provided.droppableProps}
                          ref={provided.innerRef}
                          className="space-y-2 max-h-[400px] overflow-y-auto"
                        >
                          {selectedMetrics.length === 0 ? (
                            <div className="text-center py-8 text-gray-500">
                              <p>Belum ada metrik yang dipilih</p>
                              <p className="text-sm">Pilih metrik dari daftar di sebelah kiri</p>
                            </div>
                          ) : (
                            selectedMetrics.map((metricId, index) => {
                              const metric = availableMetrics.find((m) => m.id === metricId)
                              if (!metric) return null
                              return (
                                <Draggable key={metric.id} draggableId={metric.id} index={index}>
                                  {(provided) => (
                                    <div
                                      ref={provided.innerRef}
                                      {...provided.draggableProps}
                                      {...provided.dragHandleProps}
                                      className="flex items-center justify-between p-2 border rounded-md bg-white"
                                    >
                                      <div className="flex items-center">
                                        {getChartIcon(metric.chartType)}
                                        <div className="ml-2">
                                          <p className="text-sm font-medium">{metric.name}</p>
                                          <p className="text-xs text-gray-500">Kategori: {metric.category}</p>
                                        </div>
                                      </div>
                                      <div className="flex items-center gap-1">
                                        <Button variant="ghost" size="sm" onClick={() => {}} className="h-8 w-8 p-0">
                                          <Eye className="h-4 w-4" />
                                        </Button>
                                        <Button
                                          variant="ghost"
                                          size="sm"
                                          onClick={() => handleRemoveMetric(metric.id)}
                                          className="h-8 w-8 p-0 text-red-500 hover:text-red-600"
                                        >
                                          <Trash2 className="h-4 w-4" />
                                        </Button>
                                      </div>
                                    </div>
                                  )}
                                </Draggable>
                              )
                            })
                          )}
                          {provided.placeholder}
                        </div>
                      )}
                    </Droppable>
                  </DragDropContext>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="preview" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>{reportName}</CardTitle>
                <CardDescription>{reportDescription}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {selectedMetrics.length === 0 ? (
                    <div className="text-center py-8 text-gray-500">
                      <p>Belum ada metrik yang dipilih</p>
                      <p className="text-sm">Pilih metrik di tab Desain untuk melihat pratinjau</p>
                    </div>
                  ) : (
                    selectedMetrics.map((metricId) => {
                      const metric = availableMetrics.find((m) => m.id === metricId)
                      if (!metric) return null
                      return (
                        <div key={metric.id} className="border rounded-md p-4">
                          <h3 className="text-lg font-medium mb-2">{metric.name}</h3>
                          <div className="h-[200px] bg-gray-100 rounded-md flex items-center justify-center">
                            {getChartIcon(metric.chartType)}
                            <span className="ml-2 text-gray-500">Pratinjau {metric.name}</span>
                          </div>
                        </div>
                      )
                    })
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="settings" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Pengaturan Laporan</CardTitle>
                <CardDescription>Konfigurasi pengaturan tambahan untuk laporan Anda</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="show-raw-data" className="cursor-pointer">
                    <div>Tampilkan Data Mentah</div>
                    <p className="text-sm text-gray-500">Sertakan tabel data mentah di bawah grafik</p>
                  </Label>
                  <Switch id="show-raw-data" checked={showRawData} onCheckedChange={setShowRawData} />
                </div>

                <Separator />

                <div className="flex items-center justify-between">
                  <Label htmlFor="auto-refresh" className="cursor-pointer">
                    <div>Penyegaran Otomatis</div>
                    <p className="text-sm text-gray-500">Perbarui laporan secara otomatis</p>
                  </Label>
                  <Switch id="auto-refresh" checked={autoRefresh} onCheckedChange={setAutoRefresh} />
                </div>

                {autoRefresh && (
                  <div className="space-y-2 pl-6">
                    <Label htmlFor="refresh-interval">Interval Penyegaran</Label>
                    <Select value={refreshInterval} onValueChange={setRefreshInterval}>
                      <SelectTrigger id="refresh-interval">
                        <SelectValue placeholder="Pilih interval" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="hourly">Setiap Jam</SelectItem>
                        <SelectItem value="daily">Harian</SelectItem>
                        <SelectItem value="weekly">Mingguan</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                )}

                <Separator />

                <div className="space-y-2">
                  <Label>Opsi Ekspor</Label>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <input type="checkbox" id="export-pdf" className="rounded" defaultChecked />
                      <Label htmlFor="export-pdf">PDF</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <input type="checkbox" id="export-csv" className="rounded" defaultChecked />
                      <Label htmlFor="export-csv">CSV</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <input type="checkbox" id="export-excel" className="rounded" defaultChecked />
                      <Label htmlFor="export-excel">Excel</Label>
                    </div>
                  </div>
                </div>

                <Separator />

                <div className="space-y-2">
                  <Label>Jadwal Pengiriman</Label>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <input type="checkbox" id="schedule-email" className="rounded" />
                      <Label htmlFor="schedule-email">Kirim melalui Email</Label>
                    </div>
                    <Input placeholder="<EMAIL>, <EMAIL>" disabled />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </main>
    </div>
  )
}
